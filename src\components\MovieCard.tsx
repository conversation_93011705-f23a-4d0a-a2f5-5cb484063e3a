
import React, { useState, useRef, useEffect } from 'react';
import { Movie } from '../types';
import PlayIcon from './PlayIcon';
import ImageWithFallback from './ImageWithFallback';
import { LuCopy, LuDisc3, LuUsers, LuFilm, LuCloud, LuCheck, LuHardDrive, LuTrash2, LuMoreVertical } from 'react-icons/lu';
import { useAppSettings } from '../hooks/useAppSettings';
import { toast } from 'react-hot-toast';
import { ActorLink } from './common/ActorLink';

interface MovieCardProps {
  movie: Movie;
  onCardClick?: (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => void; 
  appDefaultCover?: string | null;
}

const getTagStyle = (tagText: string): string => {
  const lowerTag = tagText.toLowerCase();
  if (lowerTag.includes('4k')) return 'bg-amber-500 text-black';
  if (lowerTag.includes('vr')) return 'bg-purple-600 text-white';
  if (lowerTag.includes('bd原盘')) return 'bg-sky-700 text-white';
  if (lowerTag.includes('中文字幕')) return 'bg-red-600 text-white';
  if (lowerTag.includes('破解版')) return 'bg-orange-500 text-black';
  if (lowerTag.includes('外挂字幕')) return 'bg-green-600 text-white';
  if (lowerTag.includes('流出版')) return 'bg-neutral-500 text-white';
  return 'bg-slate-600 text-white'; // Default
};


const MovieCard: React.FC<MovieCardProps> = ({ movie, onCardClick, appDefaultCover }) => {
  const { appSettings } = useAppSettings();
  const cardWidth = 'w-[200px]';
  const cardHeight = 'h-[300px]';

  // 右键菜单状态
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const contextMenuRef = useRef<HTMLDivElement>(null);

  // 【重构】严格按照"A区优先，旧数据兜底"原则获取数据
  const title = movie.displayData?.title || movie.title || movie.fileName;
  const displayId = movie.displayData?.display_id || movie.nfoId || '';
  const movieType = movie.displayData?.type || 'other';
  const versionCount = movie.displayData?.version_count || movie.versionCount || 0;
  const primaryActress = movie.displayData?.actresses?.[0]?.name ||
                        (movie.actors && movie.actors.length > 0 ? movie.actors[0] : null);

  // 影片类型徽章映射
  const getTypeLabel = (type: string): string | null => {
    switch (type) {
      case 'uncensored': return '无码';
      case 'chinese': return '国产';
      case 'western': return '欧美';
      case 'vr': return 'VR';
      case 'other': return '其他';
      case 'censored':
      default:
        return null; // 有码类型不显示徽章
    }
  };

  const typeLabel = getTypeLabel(movieType);

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setContextMenuPosition({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);
  };

  // 处理回收整部影片
  const handleRecycleMovie = async () => {
    setShowContextMenu(false);

    try {
      if (!movie.nfoId) {
        toast.error('无法获取影片番号，回收失败');
        return;
      }

      // 严重警告确认对话框
      const confirmed = window.confirm(
        `⚠️ 危险操作警告 ⚠️\n\n` +
        `您即将回收整部影片："${title}"\n\n` +
        `此操作将：\n` +
        `🗑️ 将整个影片文件夹移动到回收站\n` +
        `📁 包括所有版本、封面、预览图等文件\n` +
        `🚫 标记所有版本为"待删除"状态\n` +
        `⚡ 此操作影响范围较大，请谨慎确认\n\n` +
        `回收后可以在回收站中管理或恢复。\n\n` +
        `确定要继续吗？`
      );

      if (!confirmed) {
        return;
      }

      // 二次确认
      const doubleConfirmed = window.confirm(
        `最后确认：\n\n` +
        `影片番号：${displayId}\n` +
        `影片标题：${title}\n\n` +
        `确定要回收整部影片吗？\n` +
        `（此操作不可轻易撤销）`
      );

      if (!doubleConfirmed) {
        return;
      }

      toast.loading('正在回收整部影片...', { id: 'recycle-movie' });

      // 调用回收API
      const result = await window.sfeElectronAPI.recycleMovie(movie.nfoId);

      if (result.success) {
        toast.success(
          `整部影片回收成功！\n` +
          `影片：${displayId}\n` +
          `版本数：${result.versionsCount}\n` +
          `文件夹已移动到：${result.recycleBinPath}`,
          {
            id: 'recycle-movie',
            duration: 8000
          }
        );

        // 可以触发页面刷新或更新影片列表
        // 这里可以添加回调函数来通知父组件更新数据

      } else {
        toast.error(`整部影片回收失败：${result.error}`, { id: 'recycle-movie' });
      }

    } catch (error) {
      console.error('回收整部影片时发生错误:', error);
      toast.error('回收整部影片时发生错误，请重试', { id: 'recycle-movie' });
    }
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setShowContextMenu(false);
      }
    };

    if (showContextMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showContextMenu]);


  const isMultiVersion = versionCount > 1;
  const isMultiCD = (movie.multiCdCountForNfoId || 0) > 1;
  

  const placeholderContent = appSettings.imagesGloballyVisible || !appSettings.customSfwPlaceholderDataUrl ? (
    <div className="w-full h-full flex items-center justify-center bg-[#333333]">
      <PlayIcon className="w-12 h-12 text-white opacity-20 group-hover:opacity-50 transition-opacity duration-300" />
    </div>
  ) : (
    <img 
      src={appSettings.customSfwPlaceholderDataUrl} 
      alt="SFW占位符" 
      className="w-full h-full object-cover"
    />
  );

  return (
    <div
      className={`
        ${cardWidth} ${cardHeight}
        bg-[#2c2c2c] rounded-lg border border-[#444444] 
        shadow-lg hover:shadow-xl hover:shadow-[#B8860B]/20
        overflow-hidden 
        transform hover:scale-[1.03] transition-all duration-300 ease-in-out 
        group cursor-pointer relative flex flex-col 
      `}
      aria-label={`影片: ${title}${isMultiVersion ? ` (${versionCount} 版本)` : ''}${isMultiCD ? ` (${movie.multiCdCountForNfoId} CD)` : ''}`}
      onClick={() => movie && onCardClick?.(movie, isMultiVersion, isMultiCD)}
      onContextMenu={handleContextMenu}
    >
      {/* Image Area - Now takes full card height */}
      <div className="relative w-full h-full">
        <ImageWithFallback
          primarySrc={movie.coverDataUrl}
          secondarySrc={movie.posterUrl}
          tertiarySrc={movie.coverUrl}
          appDefaultCoverDataUrl={appDefaultCover}
          alt={`封面: ${title}`}
          className="absolute inset-0 w-full h-full object-cover object-center"
          placeholder={placeholderContent}
          forceShowPlaceholder={!appSettings.imagesGloballyVisible}
        />
      </div>

      {/* 左上角：版本数量徽章 */}
      <div className="absolute top-1.5 left-1.5 flex flex-col space-y-1 pointer-events-none z-10">
        {isMultiVersion && (
          <div
            className="badge-app bg-amber-500 text-black"
            title={`${versionCount} 个版本可用`}
          >
            <LuCopy size={10} className="mr-1" />
            {versionCount} 版本
          </div>
        )}
        {isMultiCD && (
          <div
            className="badge-app bg-teal-500 text-black"
            title={`${movie.multiCdCountForNfoId} 个CD分集`}
          >
            <LuDisc3 size={10} className="mr-1" />
            {movie.multiCdCountForNfoId}-CD
          </div>
        )}
      </div>

      {/* Top-right Asset Status Icon */}
      <div className="absolute top-2 right-2 z-10">
        {movie.asset_status === 'VIRTUAL' && (
          <LuCloud
            className="w-5 h-5 text-white bg-purple-600/80 p-0.5 rounded-sm shadow-md"
            title="虚拟资产 (待获取)"
          />
        )}
        {movie.asset_status === 'AVAILABLE' && (
          <LuCheck
            className="w-5 h-5 text-white bg-green-600/80 p-0.5 rounded-sm shadow-md"
            title="本地已拥有"
          />
        )}
        {movie.asset_status === 'MISSING' && (
          <LuHardDrive
            className="w-5 h-5 text-white bg-red-600/80 p-0.5 rounded-sm shadow-md"
            title="文件缺失"
          />
        )}
      </div>

      {/* Top-right Special Tags */}
      {movie.specialTags && movie.specialTags.length > 0 && (
        <div className="absolute top-8 right-1.5 flex flex-col items-end space-y-1 pointer-events-none z-10">
          {movie.specialTags.slice(0, 4).map(tag => (
            <span
              key={tag}
              className={`text-[9px] font-semibold px-1.5 py-0.5 rounded-sm shadow-md ${getTagStyle(tag)}`}
              title={tag}
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      {/* 底部信息蒙层区 */}
      <div className="absolute bottom-0 left-0 right-0 pointer-events-none">
        {/* 主要信息区 */}
        <div className="p-2 bg-black/60 backdrop-blur-sm">
          {/* 第一行：类型徽章 + 番号 */}
          <div className="flex items-center gap-2 text-[11px] font-medium text-neutral-100 mb-1">
            {typeLabel && (
              <span className="px-1.5 py-0.5 bg-red-600 text-white rounded text-[10px] font-bold">
                {typeLabel}
              </span>
            )}
            {displayId && (
              <span className="flex items-center truncate" title={`番号: ${displayId}`}>
                <LuFilm size={12} className="mr-1 opacity-90 text-sky-300" />
                <span className="truncate">{displayId}</span>
              </span>
            )}
          </div>

          {/* 第二行：主要女优名 */}
          {primaryActress && (
            <div className="text-[11px] text-pink-300 mb-1 truncate" title={`演员: ${primaryActress}`}>
              <LuUsers size={12} className="inline mr-1 opacity-90" />
              <ActorLink
                actorName={primaryActress}
                variant="subtle"
                className="text-pink-300 hover:text-[#B8860B] text-[11px] truncate"
              />
            </div>
          )}

          {/* 第三行：影片标题 */}
          <h3
            title={title}
            className="text-sm font-semibold text-white group-hover:text-amber-300
                       transition-colors duration-200 leading-tight truncate w-full text-shadow-md"
          >
            {title}
          </h3>
        </div>

        {/* 最底部属性标签栏 */}
        <div className="bg-black/80 px-2 py-1 flex flex-wrap gap-1">
          {movie.displayData?.has_subtitles && (
            <span className="text-[9px] px-1 py-0.5 bg-green-600 text-white rounded font-bold">
              字幕
            </span>
          )}
          {movie.displayData?.is_uncensored_cracked && (
            <span className="text-[9px] px-1 py-0.5 bg-orange-500 text-black rounded font-bold">
              破解
            </span>
          )}
          {movie.displayData?.is_leaked && (
            <span className="text-[9px] px-1 py-0.5 bg-gray-500 text-white rounded font-bold">
              流出
            </span>
          )}
          {movie.displayData?.has_4k && (
            <span className="text-[9px] px-1 py-0.5 bg-amber-500 text-black rounded font-bold">
              4K
            </span>
          )}
          {movie.displayData?.has_bluray && (
            <span className="text-[9px] px-1 py-0.5 bg-sky-700 text-white rounded font-bold">
              原盘
            </span>
          )}
        </div>
      </div>

      {/* 右键菜单 */}
      {showContextMenu && (
        <div
          ref={contextMenuRef}
          className="fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-lg py-2 min-w-[160px]"
          style={{
            left: `${contextMenuPosition.x}px`,
            top: `${contextMenuPosition.y}px`,
          }}
        >
          <button
            onClick={handleRecycleMovie}
            className="w-full px-4 py-2 text-left text-red-400 hover:bg-gray-700 flex items-center gap-2 transition-colors"
          >
            <LuTrash2 className="h-4 w-4" />
            回收整部影片
          </button>
        </div>
      )}
    </div>
  );
};

export default MovieCard;