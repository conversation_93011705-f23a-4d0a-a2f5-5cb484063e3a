// main_process/services/localAssetService.js
const fs = require('fs').promises;
const path = require('path');
const log = require('electron-log');
const NodeNfoParser = require('./nodeNfoParser');
const scraperManager = require('./scraperManager');
const pathResolverService = require('./pathResolverService');
const mediaDownloadService = require('./mediaDownloadService');
const databaseService = require('./databaseService');

/**
 * 本地资产服务 - 负责将本地视频文件代理化为完整的媒体资产
 */

/**
 * 从本地文件创建完整的媒体资产
 * @param {string} localFilePath - 本地视频文件路径
 * @returns {Promise<Object>} 创建结果
 */
async function createAssetFromLocalFile(localFilePath) {
  log.info(`[本地资产服务] 开始从本地文件创建资产: ${localFilePath}`);
  
  try {
    // 1. 验证文件存在性
    await validateLocalFile(localFilePath);
    
    // 2. 从文件名提取番号
    const nfoId = extractNfoIdFromFile(localFilePath);
    if (!nfoId) {
      throw new Error('无法从文件名中识别番号');
    }
    
    log.info(`[本地资产服务] 识别到番号: ${nfoId}`);
    
    // 3. 刮削元数据
    const scrapedData = await scrapeMetadata(nfoId);
    
    // 4. 计算标准化路径
    const assetPaths = await calculateAssetPaths(nfoId, scrapedData);
    
    // 5. 创建目录结构
    await createDirectoryStructure(assetPaths);
    
    // 6. 下载媒体资产
    const downloadResult = await downloadMediaAssets(scrapedData, assetPaths);
    
    // 7. 生成 STRM 文件
    const strmResult = await createStrmFile(localFilePath, nfoId, assetPaths);
    
    // 8. 入库到数据库
    const dbResult = await saveToDatabase(nfoId, scrapedData, assetPaths, strmResult, downloadResult);
    
    const result = {
      success: true,
      nfoId: nfoId,
      message: '本地资产代理化完成',
      strmPath: strmResult.strmPath,
      details: {
        scraped: !!scrapedData,
        mediaDownloaded: downloadResult.success,
        strmCreated: strmResult.success,
        databaseUpdated: dbResult.success
      }
    };
    
    log.info(`[本地资产服务] ${nfoId} 代理化成功`);
    return result;
    
  } catch (error) {
    log.error(`[本地资产服务] 创建本地资产失败: ${localFilePath}`, error.message);
    return {
      success: false,
      error: error.message,
      filePath: localFilePath
    };
  }
}

/**
 * 验证本地文件的有效性
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
async function validateLocalFile(filePath) {
  try {
    const stats = await fs.stat(filePath);
    
    if (!stats.isFile()) {
      throw new Error('指定路径不是文件');
    }
    
    // 检查文件扩展名
    const ext = path.extname(filePath).toLowerCase();
    const supportedExtensions = [
      '.mp4', '.mkv', '.avi', '.wmv', '.mov', '.flv', '.webm', '.m4v',
      '.ts', '.m2ts', '.mts', '.vob', '.iso', '.rmvb', '.rm', '.asf'
    ];
    
    if (!supportedExtensions.includes(ext)) {
      throw new Error(`不支持的文件格式: ${ext}`);
    }
    
    // 检查文件大小（至少1MB）
    if (stats.size < 1024 * 1024) {
      throw new Error('文件太小，可能不是有效的视频文件');
    }
    
    log.debug(`[本地资产服务] 文件验证通过: ${filePath} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    
  } catch (error) {
    throw new Error(`文件验证失败: ${error.message}`);
  }
}

/**
 * 从文件路径提取番号
 * @param {string} filePath - 文件路径
 * @returns {string|null} 提取的番号
 */
function extractNfoIdFromFile(filePath) {
  const fileName = path.basename(filePath);
  const nfoId = NodeNfoParser.extractJavIdFromFilename(fileName);
  
  if (nfoId) {
    log.debug(`[本地资产服务] 从文件名提取番号: ${fileName} -> ${nfoId}`);
  } else {
    log.warn(`[本地资产服务] 无法从文件名提取番号: ${fileName}`);
  }
  
  return nfoId;
}

/**
 * 刮削元数据
 * @param {string} nfoId - 番号
 * @returns {Promise<Object|null>} 刮削结果
 */
async function scrapeMetadata(nfoId) {
  try {
    log.info(`[本地资产服务] 开始刮削元数据: ${nfoId}`);
    
    const scrapedData = await scraperManager.scrapeMovie(nfoId);
    
    if (scrapedData && scrapedData.success) {
      log.info(`[本地资产服务] 元数据刮削成功: ${nfoId}`);
      return scrapedData.data;
    } else {
      log.warn(`[本地资产服务] 元数据刮削失败: ${nfoId}`);
      return null;
    }
    
  } catch (error) {
    log.error(`[本地资产服务] 刮削元数据异常: ${nfoId}`, error.message);
    return null;
  }
}

/**
 * 计算资产路径
 * @param {string} nfoId - 番号
 * @param {Object} scrapedData - 刮削数据
 * @returns {Promise<Object>} 路径信息
 */
async function calculateAssetPaths(nfoId, scrapedData) {
  try {
    const movieData = {
      nfoId: nfoId,
      title: scrapedData?.title || nfoId,
      studio: scrapedData?.studio || null,
      series: scrapedData?.series || null
    };
    
    const paths = await pathResolverService.resolveAssetPaths(movieData);
    
    log.debug(`[本地资产服务] 计算资产路径完成: ${nfoId}`);
    return paths;
    
  } catch (error) {
    log.error(`[本地资产服务] 计算资产路径失败: ${nfoId}`, error.message);
    throw new Error(`计算资产路径失败: ${error.message}`);
  }
}

/**
 * 创建目录结构
 * @param {Object} assetPaths - 资产路径信息
 * @returns {Promise<void>}
 */
async function createDirectoryStructure(assetPaths) {
  try {
    // 创建主目录
    await fs.mkdir(assetPaths.movieRootPath, { recursive: true });
    
    // 创建预览图目录
    if (assetPaths.previewsPath) {
      await fs.mkdir(assetPaths.previewsPath, { recursive: true });
    }
    
    log.debug(`[本地资产服务] 目录结构创建完成: ${assetPaths.movieRootPath}`);
    
  } catch (error) {
    log.error(`[本地资产服务] 创建目录结构失败: ${assetPaths.movieRootPath}`, error.message);
    throw new Error(`创建目录结构失败: ${error.message}`);
  }
}

/**
 * 下载媒体资产
 * @param {Object} scrapedData - 刮削数据
 * @param {Object} assetPaths - 资产路径信息
 * @returns {Promise<Object>} 下载结果
 */
async function downloadMediaAssets(scrapedData, assetPaths) {
  try {
    if (!scrapedData) {
      log.warn('[本地资产服务] 没有刮削数据，跳过媒体下载');
      return { success: false, reason: 'no_scraped_data' };
    }
    
    log.info(`[本地资产服务] 开始下载媒体资产...`);
    
    const downloadTasks = [];
    
    // 下载封面
    if (scrapedData.posterUrl && assetPaths.posterPath) {
      downloadTasks.push(
        mediaDownloadService.downloadFile(scrapedData.posterUrl, assetPaths.posterPath)
          .catch(error => ({ success: false, type: 'poster', error: error.message }))
      );
    }
    
    // 下载背景图
    if (scrapedData.fanartUrl && assetPaths.fanartPath) {
      downloadTasks.push(
        mediaDownloadService.downloadFile(scrapedData.fanartUrl, assetPaths.fanartPath)
          .catch(error => ({ success: false, type: 'fanart', error: error.message }))
      );
    }
    
    // 下载预览图
    if (scrapedData.previewUrls && Array.isArray(scrapedData.previewUrls) && assetPaths.previewsPath) {
      scrapedData.previewUrls.forEach((url, index) => {
        const previewPath = path.join(assetPaths.previewsPath, `${String(index + 1).padStart(2, '0')}.jpg`);
        downloadTasks.push(
          mediaDownloadService.downloadFile(url, previewPath)
            .catch(error => ({ success: false, type: 'preview', index, error: error.message }))
        );
      });
    }
    
    if (downloadTasks.length === 0) {
      log.warn('[本地资产服务] 没有可下载的媒体资产');
      return { success: false, reason: 'no_media_urls' };
    }
    
    // 执行所有下载任务
    const results = await Promise.all(downloadTasks);
    
    const successCount = results.filter(r => r.success !== false).length;
    const totalCount = results.length;
    
    log.info(`[本地资产服务] 媒体下载完成: ${successCount}/${totalCount} 成功`);
    
    return {
      success: successCount > 0,
      successCount: successCount,
      totalCount: totalCount,
      results: results
    };
    
  } catch (error) {
    log.error('[本地资产服务] 下载媒体资产失败:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 创建 STRM 文件
 * @param {string} localFilePath - 本地文件路径
 * @param {string} nfoId - 番号
 * @param {Object} assetPaths - 资产路径信息
 * @returns {Promise<Object>} 创建结果
 */
async function createStrmFile(localFilePath, nfoId, assetPaths) {
  try {
    const strmFileName = `${nfoId}.strm`;
    const strmPath = path.join(assetPaths.movieRootPath, strmFileName);
    
    // 写入本地文件路径到 STRM 文件
    await fs.writeFile(strmPath, localFilePath, 'utf8');
    
    log.info(`[本地资产服务] STRM 文件创建成功: ${strmPath}`);
    
    return {
      success: true,
      strmPath: strmPath,
      localFilePath: localFilePath
    };
    
  } catch (error) {
    log.error(`[本地资产服务] 创建 STRM 文件失败: ${nfoId}`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 保存到数据库
 * @param {string} nfoId - 番号
 * @param {Object} scrapedData - 刮削数据
 * @param {Object} assetPaths - 资产路径信息
 * @param {Object} strmResult - STRM 创建结果
 * @param {Object} downloadResult - 下载结果
 * @returns {Promise<Object>} 保存结果
 */
async function saveToDatabase(nfoId, scrapedData, assetPaths, strmResult, downloadResult) {
  try {
    // 检查数据库中是否已存在该记录
    const existingMovie = await databaseService.getMovieByNfoId(nfoId);
    
    const movieData = {
      nfoId: nfoId,
      title: scrapedData?.title || nfoId,
      filePath: strmResult.success ? strmResult.strmPath : null,
      fileName: strmResult.success ? path.basename(strmResult.strmPath) : null,
      localCoverPath: downloadResult.success && assetPaths.posterPath ? assetPaths.posterPath : null,
      asset_status: 'AVAILABLE',
      lastScanned: new Date().toISOString(),
      watched: false,
      personalRating: null
    };
    
    if (existingMovie) {
      // 更新现有记录
      const updated = await databaseService.updateMovie(existingMovie.db_id, movieData);
      
      log.info(`[本地资产服务] 数据库记录已更新: ${nfoId}`);
      return { success: true, action: 'updated', movieId: existingMovie.db_id };
      
    } else {
      // 创建新记录
      const newMovie = await databaseService.addMovie(movieData);
      
      log.info(`[本地资产服务] 数据库记录已创建: ${nfoId}`);
      return { success: true, action: 'created', movieId: newMovie.db_id };
    }
    
  } catch (error) {
    log.error(`[本地资产服务] 保存到数据库失败: ${nfoId}`, error.message);
    return { success: false, error: error.message };
  }
}

module.exports = {
  createAssetFromLocalFile,
  validateLocalFile,
  extractNfoIdFromFile,
  scrapeMetadata,
  calculateAssetPaths,
  createDirectoryStructure,
  downloadMediaAssets,
  createStrmFile,
  saveToDatabase
};
