// main_process/services/scrapers/javbusProvider.js
const { getBrowser } = require('../browserManager');
const cheerio = require('cheerio');
const log = require('electron-log');
const settingsService = require('../settingsService');

const PROVIDER_NAME = 'javbus';
const PROVIDER_VERSION = '2.0.0';

/**
 * 【新增】获取真实URL - 基于对标软件的智能策略
 * @param {string} number - 番号
 * @param {string} baseUrl - 基础URL
 * @param {Object} page - Playwright页面对象
 * @param {string} cookie - Cookie
 * @returns {Promise<string>} 真实URL
 */
async function getRealUrl(number, baseUrl, page, cookie) {
    try {
        // 【优化】基于对标软件的特殊番号处理
        let processedNumber = number;

        // 处理CWP和LAF系列的特殊格式
        if (number.toUpperCase().startsWith('CWP') || number.toUpperCase().startsWith('LAF')) {
            let tempNumber = number.replace('-0', '-');
            if (tempNumber.slice(-2, -1) === '-') {
                tempNumber = tempNumber.replace('-', '-0');
            }
            processedNumber = tempNumber;
        }

        // 【优化】基于对标软件的直接拼接策略
        let directUrl = `${baseUrl}/${processedNumber}`;

        // 欧美影片处理
        if (number.includes('.') || /[-_]\d{2}[-_]\d{2}[-_]\d{2}/.test(number)) {
            processedNumber = number.replace(/-/g, '.').replace(/_/g, '.');
            return await searchRealUrl(processedNumber, 'us', baseUrl, page, cookie);
        }

        log.info(`[JavBusProvider] 尝试直接访问: ${directUrl}`);

        // 先尝试直接访问
        try {
            await page.goto(directUrl, { timeout: 15000, waitUntil: 'domcontentloaded' });
            const content = await page.content();

            if (!content.includes('404 Page Not Found') && !content.includes('lostpasswd')) {
                return directUrl;
            }
        } catch (error) {
            log.warn(`[JavBusProvider] 直接访问失败: ${error.message}`);
        }

        // 直接访问失败，尝试搜索
        log.info(`[JavBusProvider] 直接访问失败，开始搜索...`);
        return await searchRealUrl(number, 'censored', baseUrl, page, cookie);

    } catch (error) {
        log.error(`[JavBusProvider] 获取真实URL失败: ${error.message}`);
        throw error;
    }
}

/**
 * 【新增】搜索真实URL - 基于对标软件的搜索逻辑
 * @param {string} number - 番号
 * @param {string} type - 类型 (censored/uncensored/us)
 * @param {string} baseUrl - 基础URL
 * @param {Object} page - Playwright页面对象
 * @param {string} cookie - Cookie
 * @returns {Promise<string>} 搜索到的URL
 */
async function searchRealUrl(number, type, baseUrl, page, cookie) {
    try {
        // 【优化】基于对标软件的搜索URL构建
        const searchUrl = `${baseUrl}/search/${encodeURIComponent(number)}&type=${type}`;
        log.info(`[JavBusProvider] 搜索URL: ${searchUrl}`);

        await page.goto(searchUrl, { timeout: 15000, waitUntil: 'domcontentloaded' });
        const content = await page.content();
        const $ = cheerio.load(content);

        // 【优化】基于对标软件的搜索结果解析
        const searchResults = [];
        $('.movie-box').each((index, element) => {
            const $element = $(element);
            const href = $element.attr('href') || '';
            const title = $element.find('.photo-info span').text().trim();
            const date = $element.find('.photo-info date').text().trim();

            if (href && title) {
                searchResults.push({ href, title, date });
            }
        });

        log.info(`[JavBusProvider] 找到 ${searchResults.length} 个搜索结果`);

        // 【优化】基于对标软件的精确匹配逻辑
        for (const result of searchResults) {
            if (result.title.toUpperCase() === number.toUpperCase()) {
                log.info(`[JavBusProvider] 精确匹配找到: ${result.href}`);
                return result.href;
            }
        }

        // 包含匹配
        for (const result of searchResults) {
            if (result.title.toUpperCase().includes(number.toUpperCase())) {
                log.info(`[JavBusProvider] 包含匹配找到: ${result.href}`);
                return result.href;
            }
        }

        // 如果没有找到，返回第一个结果
        if (searchResults.length > 0) {
            log.warn(`[JavBusProvider] 使用第一个搜索结果: ${searchResults[0].href}`);
            return searchResults[0].href;
        }

        throw new Error(`未找到番号 ${number} 的搜索结果`);

    } catch (error) {
        log.error(`[JavBusProvider] 搜索失败: ${error.message}`);
        throw error;
    }
}

/**
 * 【优化】从 JavBus 刮削数据 - 基于对标软件优化
 * @param {string} nfoId
 * @returns {Promise<ScrapedMovieData>}
 */
async function scrape(nfoId) {
    const browser = await getBrowser();
    const settings = settingsService.getSettings();
    const context = await browser.newContext({
        extraHTTPHeaders: {
            // JavBus 需要 Cookie 才能正常访问
            'Cookie': settings.javbusCookie || '' // 从设置中读取 Cookie
        }
    });
    const page = await context.newPage();
    const baseUrl = settings.javbusBaseUrl || 'https://www.javbus.com';

    // 【优化】基于对标软件的智能URL构建策略
    let realUrl = await getRealUrl(nfoId, baseUrl, page, settings.javbusCookie);

    try {
        log.info(`[JavBusProvider] 开始刮削 ${nfoId}，URL: ${realUrl}`);

        await page.goto(realUrl, { timeout: 20000, waitUntil: 'domcontentloaded' });

        const content = await page.content();

        // 【优化】基于对标软件的登录检测
        if (content.includes('lostpasswd')) {
            if (settings.javbusCookie) {
                throw new Error('Cookie 无效！请重新填写 Cookie 或更新节点！');
            } else {
                throw new Error('当前节点需要填写 Cookie 才能刮削！请到设置-网络填写 Cookie 或更换节点！');
            }
        }

        if (content.includes('404 Page Not Found')) {
            throw new Error('页面返回 404 Not Found');
        }

        const $ = cheerio.load(content);

        // 【现代化改造】应采尽采模式 - 榨干 JavBus 页面的每一滴信息
        log.info(`[JavBusProvider] 开始深度数据采集 (应采尽采模式)...`);

        // === 基础信息采集 ===
        // 【优化】基于对标软件的标题获取逻辑
        let title = $('h3').first().text().trim();

        // 【优化】基于对标软件的标题清理逻辑
        if (title && nfoId) {
            // 移除番号前缀
            if (title.toUpperCase().startsWith(nfoId.toUpperCase())) {
                title = title.substring(nfoId.length).trim();
            }
            // 移除开头的空格和短横线
            title = title.replace(/^\s*[-\s]+/, '').trim();
        }

        if (!title) throw new Error('未找到标题，页面可能加载不完整');

        // 高清封面图采集 - 确保获取未压缩版本
        let posterUrl = $('.bigImage').attr('href') || '';
        let coverUrl = $('.bigImage img').attr('src') || '';

        // 将相对路径转换为完整URL
        if (posterUrl && !posterUrl.startsWith('http')) {
            posterUrl = new URL(posterUrl, url).href;
        }
        if (coverUrl && !coverUrl.startsWith('http')) {
            coverUrl = new URL(coverUrl, url).href;
        }

        // 尝试获取更高清的封面版本
        const hdCoverUrl = posterUrl.replace('_b.jpg', '_b.jpg'); // 保持原始大图
        const thumbUrl = posterUrl.replace('/cover/', '/thumb/').replace('_b.jpg', '.jpg');

        const infoContainer = $('.info');

        // === 详细信息采集 ===
        const releaseDate = infoContainer.find('span.header:contains("發行日期:")').next().text().trim();
        const runtime = infoContainer.find('span.header:contains("長度:")').next().text().replace('分鐘', '').trim();

        // 导演信息 - 完整采集
        const directorElement = infoContainer.find('a[href*="/director/"]');
        const director = directorElement.text().trim();
        const directorUrl = directorElement.attr('href') ? new URL(directorElement.attr('href'), url).href : '';

        // 制作商信息 - 完整采集
        const studioElement = infoContainer.find('a[href*="/studio/"]');
        const studio = studioElement.text().trim();
        const studioUrl = studioElement.attr('href') ? new URL(studioElement.attr('href'), url).href : '';

        // 发行商信息 - 完整采集
        const publisherElement = infoContainer.find('a[href*="/label/"]');
        const publisher = publisherElement.text().trim() || studio;
        const publisherUrl = publisherElement.attr('href') ? new URL(publisherElement.attr('href'), url).href : '';

        // 系列信息 - 完整采集
        const seriesElement = infoContainer.find('a[href*="/series/"]');
        const series = seriesElement.text().trim();
        const seriesUrl = seriesElement.attr('href') ? new URL(seriesElement.attr('href'), url).href : '';

        // === 演员信息采集 - 完整列表 ===
        // 【优化】基于对标软件的演员信息采集
        const actors = [];
        const actorsDetailed = [];
        $('.star-name').each((i, el) => {
            const actorLink = $(el).find('a');
            const actorName = actorLink.text().trim();
            const actorUrl = actorLink.attr('href') ? new URL(actorLink.attr('href'), realUrl).href : '';
            const actorImage = $(el).siblings('.photo-frame').find('img').attr('src') || '';

            if (actorName && actorName !== '♀') {  // 【优化】过滤无效演员名
                actors.push(actorName);
                actorsDetailed.push({
                    name: actorName,
                    url: actorUrl,
                    image: actorImage ? new URL(actorImage, realUrl).href : ''
                });
            }
        });

        // === 标签/类别采集 - 完整列表 ===
        const tags = [];
        const tagsDetailed = [];
        $('span.genre a').each((i, el) => {
            const tagName = $(el).text().trim();
            const tagUrl = $(el).attr('href') ? new URL($(el).attr('href'), url).href : '';

            if (tagName) {
                tags.push(tagName);
                tagsDetailed.push({
                    name: tagName,
                    url: tagUrl
                });
            }
        });

        // === 预览图采集 - 所有样品图像 ===
        const previewImages = [];
        const previewImagesDetailed = [];
        $('#sample-waterfall a.sample-box').each((i, el) => {
            const href = $(el).attr('href');
            const thumbSrc = $(el).find('img').attr('src');

            if (href) {
                const fullUrl = href.startsWith('http') ? href : new URL(href, url).href;
                const thumbUrl = thumbSrc ? (thumbSrc.startsWith('http') ? thumbSrc : new URL(thumbSrc, url).href) : '';

                previewImages.push(fullUrl);
                previewImagesDetailed.push({
                    full: fullUrl,
                    thumb: thumbUrl,
                    index: i
                });
            }
        });

        // === 影片类型和马赛克信息 ===
        const mosaicText = $('li.active a').text();
        const mosaic = mosaicText.includes('無碼') ? '无码' : '有码';
        const movieType = mosaic === '无码' ? 'uncensored' : 'censored';

        // === 额外信息采集 ===

        // 页面元数据采集
        const pageTitle = $('title').text().trim();
        const metaDescription = $('meta[name="description"]').attr('content') || '';
        const metaKeywords = $('meta[name="keywords"]').attr('content') || '';

        // 评分信息采集（如果存在）
        const ratingElement = $('.rating, .score, .star-rating');
        let rating = null;
        if (ratingElement.length > 0) {
            const ratingText = ratingElement.text().trim();
            const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
            if (ratingMatch) {
                rating = {
                    score: parseFloat(ratingMatch[1]),
                    source: 'javbus',
                    text: ratingText
                };
            }
        }

        // 相关作品采集
        const relatedMovies = [];
        $('.related-movie, .movie-list a, .similar-movie').each((i, el) => {
            const movieLink = $(el).attr('href');
            const movieTitle = $(el).find('img').attr('alt') || $(el).text().trim();
            const movieImage = $(el).find('img').attr('src');

            if (movieLink && movieTitle) {
                relatedMovies.push({
                    title: movieTitle,
                    url: movieLink.startsWith('http') ? movieLink : new URL(movieLink, url).href,
                    image: movieImage ? (movieImage.startsWith('http') ? movieImage : new URL(movieImage, url).href) : ''
                });
            }
        });

        // 磁力链接采集（如果页面包含）
        const magnetLinks = [];
        $('a[href^="magnet:"]').each((i, el) => {
            const magnetUrl = $(el).attr('href');
            const magnetText = $(el).text().trim();

            if (magnetUrl) {
                magnetLinks.push({
                    link: magnetUrl,
                    title: magnetText,
                    source: 'javbus'
                });
            }
        });

        // 技术信息采集
        const technicalInfo = {};
        infoContainer.find('span.header').each((i, el) => {
            const header = $(el).text().trim().replace(':', '');
            const value = $(el).next().text().trim();

            if (header && value) {
                technicalInfo[header] = value;
            }
        });

        // 【优化】组装兼容对标软件的返回数据 - 应采尽采
        const scrapedData = {
            // === 基础信息 ===
            nfoId: nfoId,
            number: nfoId,  // 对标软件字段
            title: title,
            originaltitle: title,  // 对标软件字段
            plot: '', // JavBus 详情页通常没有剧情简介
            outline: '',  // 对标软件字段
            originalplot: '',  // 对标软件字段
            releaseDate: releaseDate,
            release: releaseDate,  // 对标软件字段
            year: releaseDate ? releaseDate.substring(0, 4) : '',
            runtime: runtime ? parseInt(runtime) || 0 : 0,

            // === 人员信息 (详细版) ===
            director: director,
            directorUrl: directorUrl,
            studio: studio,
            studioUrl: studioUrl,
            publisher: publisher,
            publisherUrl: publisherUrl,
            series: series,
            seriesUrl: seriesUrl,

            // === 演员信息 (兼容旧格式 + 新详细格式) ===
            actors: actors, // 保持向后兼容
            actor: actors.join(','),  // 对标软件字段
            actor_photo: getActorPhoto(actorsDetailed),  // 对标软件字段
            actorsDetailed: actorsDetailed, // 新增详细信息

            // === 标签信息 (兼容旧格式 + 新详细格式) ===
            tags: tags, // 保持向后兼容
            tag: tags.join(','),  // 对标软件字段
            tagsDetailed: tagsDetailed, // 新增详细信息

            // === 图像信息 (多版本) ===
            coverUrl: hdCoverUrl, // 高清封面
            thumb: hdCoverUrl,  // 对标软件字段
            poster: hdCoverUrl,  // 对标软件字段
            posterUrl: thumbUrl, // 缩略图
            originalCoverUrl: posterUrl, // 原始链接
            previewImages: previewImages, // 保持向后兼容
            extrafanart: previewImages,  // 对标软件字段
            previewImagesDetailed: previewImagesDetailed, // 新增详细信息

            // === 分类和类型信息 ===
            mosaic: mosaic,
            movieType: movieType,

            // === 评分和社区信息 ===
            rating: rating,
            score: rating || '',  // 对标软件字段
            relatedMovies: relatedMovies,
            magnetLinks: magnetLinks,

            // === 元数据信息 ===
            pageTitle: pageTitle,
            metaDescription: metaDescription,
            metaKeywords: metaKeywords,
            technicalInfo: technicalInfo,

            // === 对标软件标准字段 ===
            trailer: '', // JavBus 很少直接提供预告片链接
            image_download: !!hdCoverUrl,
            image_cut: 'center',
            wanted: '',

            // === 来源信息 ===
            trailerUrl: '', // JavBus 很少直接提供预告片链接
            sourceUrl: realUrl,
            website: realUrl,  // 对标软件字段
            scrapedAt: new Date().toISOString(),
            provider: PROVIDER_NAME,
            version: PROVIDER_VERSION,

            // === 统计信息 ===
            dataRichness: {
                actorsCount: actors.length,
                tagsCount: tags.length,
                previewImagesCount: previewImages.length,
                relatedMoviesCount: relatedMovies.length,
                magnetLinksCount: magnetLinks.length,
                hasRating: !!rating,
                hasSeries: !!series,
                hasDirector: !!director
            }
        };

        log.info(`[JavBusProvider] 🎉 深度刮削完成 ${nfoId}！数据丰富度: 演员${actors.length}个, 标签${tags.length}个, 预览图${previewImages.length}张, 相关作品${relatedMovies.length}部`);
        return scrapedData;

    } finally {
        await page.close();
        await context.close();
    }
}

/**
 * 【新增】获取演员头像映射 - 参考对标软件
 * @param {Array} actorsDetailed - 详细演员数组
 * @returns {Object} 演员头像映射
 */
function getActorPhoto(actorsDetailed) {
    const actorPhoto = {};

    if (Array.isArray(actorsDetailed)) {
        actorsDetailed.forEach(actor => {
            if (actor.name) {
                actorPhoto[actor.name] = actor.image || '';
            }
        });
    }

    return actorPhoto;
}

module.exports = {
    name: PROVIDER_NAME,
    scrape,
    version: PROVIDER_VERSION
};
