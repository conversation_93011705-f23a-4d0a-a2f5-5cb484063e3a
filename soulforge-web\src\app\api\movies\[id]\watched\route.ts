import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // First get the current movie
    const currentMovie = await prisma.movie.findUnique({
      where: { id: params.id },
    });

    if (!currentMovie) {
      return NextResponse.json(
        {
          success: false,
          error: 'Movie not found',
        },
        { status: 404 }
      );
    }

    // Toggle the watched status
    const updatedMovie = await prisma.movie.update({
      where: { id: params.id },
      data: {
        watched: !currentMovie.watched,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        id: updatedMovie.id,
        filePath: updatedMovie.filePath,
        fileName: updatedMovie.fileName,
        title: updatedMovie.title,
        originalTitle: updatedMovie.originalTitle,
        nfoId: updatedMovie.nfoId,
        year: updatedMovie.year,
        releaseDate: updatedMovie.releaseDate,
        runtime: updatedMovie.runtime,
        plot: updatedMovie.plot,
        plotJa: updatedMovie.plotJa,
        plotZh: updatedMovie.plotZh,
        studio: updatedMovie.studio,
        series: updatedMovie.series,
        director: updatedMovie.director,
        trailerUrl: updatedMovie.trailerUrl,
        posterUrl: updatedMovie.posterUrl,
        coverUrl: updatedMovie.coverUrl,
        localCoverPath: updatedMovie.localCoverPath,
        watched: updatedMovie.watched,
        personalRating: updatedMovie.personalRating,
        actors: updatedMovie.actors ? JSON.parse(updatedMovie.actors) : [],
        genres: updatedMovie.genres ? JSON.parse(updatedMovie.genres) : [],
        tags: updatedMovie.tags ? JSON.parse(updatedMovie.tags) : [],
        lastScanned: updatedMovie.lastScanned,
        nfoLastModified: updatedMovie.nfoLastModified,
        resolution: updatedMovie.resolution,
        fileSize: updatedMovie.fileSize,
        videoCodec: updatedMovie.videoCodec,
        audioCodec: updatedMovie.audioCodec,
        preferredStatus: updatedMovie.preferredStatus,
        customFileTags: updatedMovie.customFileTags ? JSON.parse(updatedMovie.customFileTags) : [],
        versionCategories: updatedMovie.versionCategories ? JSON.parse(updatedMovie.versionCategories) : [],
        autoDetectedFileNameTags: updatedMovie.autoDetectedFileNameTags ? JSON.parse(updatedMovie.autoDetectedFileNameTags) : [],
        fps: updatedMovie.fps,
        videoCodecFull: updatedMovie.videoCodecFull,
        videoBitrate: updatedMovie.videoBitrate,
        audioCodecFull: updatedMovie.audioCodecFull,
        audioChannelsDesc: updatedMovie.audioChannelsDesc,
        audioSampleRate: updatedMovie.audioSampleRate,
        audioBitrate: updatedMovie.audioBitrate,
        videoHeight: updatedMovie.videoHeight,
        aiAnalyzedTags: updatedMovie.aiAnalyzedTags ? JSON.parse(updatedMovie.aiAnalyzedTags) : [],
        aiRecommendationType: updatedMovie.aiRecommendationType,
        aiRecommendationScore: updatedMovie.aiRecommendationScore,
        aiRecommendationJustification: updatedMovie.aiRecommendationJustification,
        hasExternalSubtitles: updatedMovie.hasExternalSubtitles,
        cdPartInfo: updatedMovie.cdPartInfo,
        createdAt: updatedMovie.createdAt,
        updatedAt: updatedMovie.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error toggling watched status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to toggle watched status',
      },
      { status: 500 }
    );
  }
}
