import React, { useEffect } from 'react';

interface ImagePreviewModalProps {
  isOpen: boolean;
  imageUrl: string | null;
  altText?: string;
  onClose: () => void;
}

const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({ isOpen, imageUrl, altText, onClose }) => {
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !imageUrl) return null;

  return (
    <div
      className="fixed inset-0 z-[80] flex items-center justify-center p-4 bg-black/90 backdrop-blur-md"
      onClick={onClose}
      aria-modal="true"
      role="dialog"
      aria-labelledby="image-preview-title"
    >
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-white hover:text-neutral-300 transition-colors z-[81]"
        aria-label="关闭预览"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
      
      <div 
        className="relative w-auto h-auto max-w-[95vw] max-h-[95vh] flex items-center justify-center" 
        onClick={(e) => e.stopPropagation()}
      > 
        <img
          src={imageUrl}
          alt={altText || 'Enlarged preview'}
          className="block max-w-full max-h-full object-contain rounded-lg shadow-2xl cursor-pointer"
          onClick={onClose} 
        />
      </div>
      {altText && <h2 id="image-preview-title" className="sr-only">{altText}</h2>}
    </div>
  );
};

export default ImagePreviewModal;
