// soul-forge-electron/src/components/settings/GeneralSettingsTab.tsx
import React from 'react';
import { AppSettings, SortableMovieField, ViewMode } from '../../types';

interface GeneralSettingsTabProps {
  settings: Partial<AppSettings>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  setSettings: React.Dispatch<React.SetStateAction<Partial<AppSettings>>>;
  pathSep: string;
}

const sortableFieldsOptions: { value: SortableMovieField; label: string }[] = [
  { value: 'releaseDate', label: '发行日期' }, { value: 'year', label: '年份' },
  { value: 'personalRating', label: '个人评分'}, { value: 'title', label: '标题' },
  { value: 'nfoId', label: '番号/ID' }, { value: 'fileName', label: '文件名' },
  { value: 'runtime', label: '时长'}, { value: 'studio', label: '制作商'},
  { value: 'series', label: '系列'}, { value: 'db_id', label: '添加顺序 (DB ID)'},
  { value: 'lastScanned', label: '扫描日期'}, { value: 'nfoLastModified', label: 'NFO修改日期'},
  { value: 'fileSize', label: '文件大小'}, { value: 'fps', label: '帧率 (FPS)'}, 
  { value: 'videoBitrate', label: '视频码率'}, { value: 'versionCount', label: '版本数量'},
  { value: 'aiRecommendationScore', label: 'AI推荐分' }
];

const viewModeOptions: { value: ViewMode; label: string }[] = [
  { value: 'card', label: '卡片模式' },
  { value: 'compactList', label: '紧凑列表' },
  { value: 'detailedList', label: '详细列表' },
  { value: 'waterfall', label: '海报瀑布流' },
];

const GeneralSettingsTab: React.FC<GeneralSettingsTabProps> = ({
  settings,
  handleInputChange,
  setSettings,
  pathSep,
}) => {
  const [newScanPath, setNewScanPath] = React.useState('');

  const handleAddScanPath = async () => {
    if (newScanPath.trim() && !settings.defaultScanPaths?.includes(newScanPath.trim())) {
      setSettings(prev => ({
        ...prev,
        defaultScanPaths: [...(prev.defaultScanPaths || []), newScanPath.trim()],
      }));
      setNewScanPath('');
    } else if (settings.defaultScanPaths?.includes(newScanPath.trim())) {
      alert('该扫描路径已存在。');
    }
  };

  const handleBrowseScanPath = async () => {
    try {
      const selectedPathsArray = await window.sfeElectronAPI.selectDirectory(); // returns string[] | null
      if (selectedPathsArray && selectedPathsArray.length > 0) {
        setNewScanPath(selectedPathsArray[0]); // Use the first selected path
      }
    } catch (err) { console.error("Error browsing directory for scan path:", err); alert("选择扫描目录失败。"); }
  };

  const handleRemoveScanPath = (pathToRemove: string) => {
    setSettings(prev => ({ ...prev, defaultScanPaths: prev.defaultScanPaths?.filter(p => p !== pathToRemove) }));
  };
  
  const handleBrowseFileForInput = async (settingName: keyof AppSettings) => {
    try {
        const selectedPaths = await window.sfeElectronAPI.selectDirectory(); 
        if (selectedPaths && selectedPaths.length > 0) { // Check if array and has elements
            alert(`请在路径后手动添加可执行文件名 (例如 ${pathSep}python.exe)`);
            setSettings(prev => ({ ...prev, [settingName]: selectedPaths[0] + pathSep })); // Use the first path
        }
    } catch (err) { console.error(`Error browsing for ${settingName}:`, err); alert("选择文件/目录失败。"); }
  };


  return (
    <div className="settings-group-content space-y-4">
      <div>
        <label className="settings-label">默认扫描路径</label>
        {(settings.defaultScanPaths || []).map(p => (
          <div key={p} className="flex items-center justify-between bg-[#2d2d2d] p-2 rounded-md border border-[#4f4f4f] mb-1 text-sm">
            <span className="text-neutral-100 truncate" title={p}>{p}</span>
            <button onClick={() => handleRemoveScanPath(p)} className="text-red-400 hover:text-red-300 font-semibold ml-2 p-1 text-xs">移除</button>
          </div>
        ))}
        {(!settings.defaultScanPaths || settings.defaultScanPaths.length === 0) && <p className="text-xs text-neutral-500 italic mb-1">暂无默认扫描路径。</p>}
        <div className="flex items-center gap-2 mt-1">
          <input type="text" value={newScanPath} onChange={(e) => setNewScanPath(e.target.value)} placeholder="输入或浏览新扫描路径..." className="form-input-app flex-grow"/>
          <button onClick={handleBrowseScanPath} className="button-secondary-app px-3 py-2 text-sm">浏览</button>
          <button onClick={handleAddScanPath} className="button-primary-app px-3 py-2 text-sm" disabled={!newScanPath.trim()}>添加</button>
        </div>
      </div>
      <div>
        <label htmlFor="pythonExecutablePath" className="settings-label mt-3">Python 解释器路径 (可选)</label>
        <div className="flex items-center gap-2">
            <input type="text" id="pythonExecutablePath" name="pythonExecutablePath" value={settings.pythonExecutablePath || ''} onChange={handleInputChange} placeholder="例如: C:\\Python39\\python.exe" className="form-input-app flex-grow"/>
            <button onClick={() => handleBrowseFileForInput('pythonExecutablePath')} className="button-secondary-app px-3 py-2 text-sm">浏览</button>
        </div>
        <p className="settings-description">留空则自动查找 'python' 或 'python3'。</p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-3">
        <div><label htmlFor="defaultSortField" className="settings-label">默认排序字段</label><select id="defaultSortField" name="defaultSortField" value={settings.defaultSortField || 'releaseDate'} onChange={handleInputChange} className="form-select-app">{sortableFieldsOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}</select></div>
        <div><label htmlFor="defaultSortOrder" className="settings-label">默认排序顺序</label><select id="defaultSortOrder" name="defaultSortOrder" value={settings.defaultSortOrder || 'desc'} onChange={handleInputChange} className="form-select-app"><option value="asc">升序</option><option value="desc">降序</option></select></div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-3">
        <div><label htmlFor="defaultPageSize" className="settings-label">默认每页数量</label><input type="number" id="defaultPageSize" name="defaultPageSize" value={settings.defaultPageSize || 50} onChange={handleInputChange} min="10" max="200" step="10" className="form-input-app"/></div>
        <div><label htmlFor="defaultViewMode" className="settings-label">默认视图模式</label><select id="defaultViewMode" name="defaultViewMode" value={settings.defaultViewMode || 'card'} onChange={handleInputChange} className="form-select-app">{viewModeOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}</select></div>
      </div>
    </div>
  );
};

export default GeneralSettingsTab;