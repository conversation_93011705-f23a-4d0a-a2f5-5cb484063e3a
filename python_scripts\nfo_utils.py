# soul-forge-electron/python_scripts/nfo_utils.py
import argparse
import json
import os
import sys
from xml.etree import ElementTree as ET
from xml.dom import minidom

# 确保以 UTF-8 编码输出到 stdout，尤其是在 Windows 上
sys.stdout.reconfigure(encoding='utf-8')

# 定义在NFO中标记已处理剧情的特定标签名
PROCESSED_MARKER_TAG_NAME = "soulforge_plot_polished_v2_nfo_tool"

def parse_nfo(nfo_file_path):
    """解析NFO文件并提取所需信息。"""
    try:
        tree = ET.parse(nfo_file_path)
        root = tree.getroot()
        
        data = {
            "title": root.findtext("title"),
            "originaltitle": root.findtext("originaltitle"),
            "plot": root.findtext("plot"),
            "plot_ja": None, # 初始化为 None
            "plot_zh": None, # 初始化为 None
            "year": root.findtext("year"),
            "premiered": root.findtext("premiered"), # 发行日期
            "releasedate": root.findtext("releasedate"), # 有些NFO使用这个
            "id": root.findtext("id"), # 番号
            "uniqueid": [uid.text for uid in root.findall("uniqueid") if uid.get("type") == "SOD"], # 唯一ID，例如来自 JavDB
            "studio": root.findtext("studio"),
            "director": root.findtext("director"),
            "maker": root.findtext("maker"), # 制作商，有时用 maker
            "publisher": root.findtext("publisher"), # 发行商
            "series": root.findtext("series"), # 系列
            "runtime": root.findtext("runtime"), # 时长
            "genres": [genre.text for genre in root.findall("genre")],
            "tags": [tag.text for tag in root.findall("tag")],
            "actors": [{"name": actor.findtext("name"), "thumb": actor.findtext("thumb")} for actor in root.findall("actor")]
        }

        # 尝试解析中文和日文剧情简介 (通常在 <plot> 标签内，用特定格式或通过属性区分)
        # 假设这里没有标准格式，如果它们和主剧情简介是分开的，需要特定逻辑
        # 例如，如果它们存储在具有 lang 属性的 plot 标签中:
        # <plot lang="ja">日文剧情</plot>
        # <plot lang="zh">中文剧情</plot>
        # 或者更常见的是，主要plot是中文，需要AI识别或用户手动提供日文剧情
        
        # Kodi 通常只使用一个 <plot> 标签。这里的 plot_ja, plot_zh 更多是为了 AI 润色工具
        # 如果 NFO 中有特殊方式存储，这里需要适配
        # 暂时假设，如果需要日文或纯中文剧情，它们需要被外部工具处理或已存在特定标签
        # 为了NFO Plot Polisher工具，我们假设 <originalplot> 存储了原始（可能是日文）剧情
        # 而 <plot> 存储了处理后的（可能是中文）剧情
        # 在NFO Plot Polisher的场景下，我们通常认为 <plot> 是中文剧情
        original_plot_element = root.find("originalplot")
        if original_plot_element is not None and original_plot_element.text:
            data["plot_ja"] = original_plot_element.text # 将 <originalplot> 视为日文剧情参考

        # 智能判断剧情语言（一个非常简化的版本）
        # 这里假设：如果NFO中只有一个plot，且包含大量中文字符，则视为中文剧情。
        # 如果有originalplot，则将其视为日文，主plot视为中文。
        # 这个逻辑在实际应用中可能需要更复杂的处理。
        
        main_plot_text = data.get("plot")
        if main_plot_text:
            # 简单的中文检测：计算中文字符比例
            chinese_chars = sum(1 for char in main_plot_text if '\u4e00' <= char <= '\u9fff')
            if chinese_chars / len(main_plot_text) > 0.5: # 假设超过50%是中文字符，则认为是中文剧情
                 data["plot_zh"] = main_plot_text
            elif data.get("plot_ja") and not main_plot_text.strip() == data.get("plot_ja").strip(): # 如果有日文剧情且主剧情不同
                 data["plot_zh"] = main_plot_text # 假设主剧情是中文
            # 如果 plot_ja 没有，并且主剧情不像中文，那它可能是日文或者英文
            # 这种情况下，如果工具需要明确的日文和中文，则需要其他处理

        # 如果 plot_zh 还是空的，但 plot_ja 存在，并且主 plot 看起来不像日文，那么主 plot 可能是中文
        if not data.get("plot_zh") and data.get("plot_ja") and main_plot_text and not contains_significant_japanese(main_plot_text):
            data["plot_zh"] = main_plot_text


        return {"success": True, "data": data}
    except Exception as e:
        return {"success": False, "error": str(e)}

def contains_significant_japanese(text):
    if not text: return False
    # 简易日文检测：平假名、片假名
    japanese_chars = sum(1 for char in text if '\u3040' <= char <= '\u30ff') # Hiragana and Katakana
    return (japanese_chars / len(text)) > 0.3 # 30% threshold

def update_nfo_plot(nfo_file_path, new_plot_zh_content, add_processed_marker=True):
    """更新NFO文件中的中文剧情简介 (<plot>)，并可选择添加处理标记。"""
    try:
        tree = ET.parse(nfo_file_path)
        root = tree.getroot()

        plot_element = root.find("plot")
        if plot_element is None:
            plot_element = ET.SubElement(root, "plot")
        
        original_content = plot_element.text
        plot_element.text = new_plot_zh_content

        if add_processed_marker:
            # 检查是否已存在标记，避免重复添加
            existing_tags = {tag.text for tag in root.findall("tag")}
            if PROCESSED_MARKER_TAG_NAME not in existing_tags:
                tag_element = ET.SubElement(root, "tag")
                tag_element.text = PROCESSED_MARKER_TAG_NAME
        
        # 美化XML输出
        xml_str = ET.tostring(root, encoding='utf-8')
        pretty_xml_str = minidom.parseString(xml_str).toprettyxml(indent="  ", encoding='utf-8')
        
        with open(nfo_file_path, 'wb') as f: # 以二进制写入，因为minidom输出的是带编码声明的bytes
            f.write(pretty_xml_str)
            
        return {"success": True, "message": "NFO文件已成功更新。", "original_plot": original_content, "new_plot": new_plot_zh_content}
    except Exception as e:
        return {"success": False, "error": str(e)}

def check_processed_marker(nfo_file_path):
    """检查NFO文件是否包含处理标记。"""
    try:
        tree = ET.parse(nfo_file_path)
        root = tree.getroot()
        is_processed = any(tag.text == PROCESSED_MARKER_TAG_NAME for tag in root.findall("tag"))
        return {"success": True, "is_processed": is_processed}
    except Exception as e:
        return {"success": False, "error": str(e), "is_processed": False}


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="NFO文件工具：解析、更新剧情、检查标记。")
    subparsers = parser.add_subparsers(dest="command", required=True, help="可用的命令")

    parse_parser = subparsers.add_parser("parse", help="解析NFO文件并以JSON格式输出其内容。")
    parse_parser.add_argument("nfo_file", help="要解析的NFO文件路径。")

    update_parser = subparsers.add_parser("update", help="更新NFO文件中的中文剧情简介 (<plot>)。")
    update_parser.add_argument("nfo_file", help="要更新的NFO文件路径。")
    update_parser.add_argument("--new_plot_zh", required=True, help="新的中文剧情简介内容。")
    update_parser.add_argument("--no_marker", action="store_true", help="不添加处理标记。")

    check_marker_parser = subparsers.add_parser("check_marker", help="检查NFO文件是否包含处理标记。")
    check_marker_parser.add_argument("nfo_file", help="要检查的NFO文件路径。")

    args = parser.parse_args()
    result = {}

    try:
        if args.command == "parse":
            parse_result = parse_nfo(args.nfo_file)
            if parse_result["success"]:
                result = {"success": True, **parse_result["data"]} # 将解析出的数据直接放到顶层
            else:
                result = parse_result
        
        elif args.command == "update":
            result = update_nfo_plot(args.nfo_file, args.new_plot_zh, not args.no_marker)
        
        elif args.command == "check_marker":
            result = check_processed_marker(args.nfo_file)
            
    except Exception as e:
        result = {"success": False, "error": f"执行命令 '{args.command}' 时发生意外错误: {str(e)}"}

    print(json.dumps(result, ensure_ascii=False, indent=2))