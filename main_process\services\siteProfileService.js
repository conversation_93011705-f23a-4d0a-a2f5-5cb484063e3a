// SoulForge 站点配置文件服务
// 用于加载和管理 site-profiles.json 配置文件

const fs = require('fs');
const path = require('path');

let log;
let siteProfiles = null;
let configPath = null;

/**
 * 初始化站点配置服务
 * @param {Object} logger - 日志服务实例
 * @param {string} projectRoot - 项目根目录路径
 */
function initializeSiteProfileService(logger, projectRoot) {
  log = logger;
  configPath = path.join(projectRoot, 'site-profiles.json');
  log.info(`[站点配置服务] 初始化，配置文件路径: ${configPath}`);
}

/**
 * 加载站点配置文件
 * @returns {Object} 包含成功状态和配置数据的对象
 */
function loadSiteProfiles() {
  try {
    log.info('[站点配置服务] 开始加载站点配置文件...');
    
    // 检查配置文件是否存在
    if (!fs.existsSync(configPath)) {
      const error = `配置文件不存在: ${configPath}`;
      log.error(`[站点配置服务] ${error}`);
      return { success: false, error, profiles: null };
    }
    
    // 读取并解析配置文件
    const configContent = fs.readFileSync(configPath, 'utf8');
    siteProfiles = JSON.parse(configContent);
    
    // 验证配置文件结构
    const validationResult = validateSiteProfiles(siteProfiles);
    if (!validationResult.success) {
      log.error(`[站点配置服务] 配置文件验证失败: ${validationResult.error}`);
      return { success: false, error: validationResult.error, profiles: null };
    }
    
    // 排除 chromeUserDataPath，只计算论坛配置数量
    const forumKeys = Object.keys(siteProfiles).filter(key => key !== 'chromeUserDataPath');
    const forumCount = forumKeys.length;
    log.info(`[站点配置服务] 站点配置文件加载成功，包含 ${forumCount} 个论坛配置`);
    
    return { success: true, profiles: siteProfiles };
    
  } catch (error) {
    const errorMsg = `加载配置文件失败: ${error.message}`;
    log.error(`[站点配置服务] ${errorMsg}`);
    return { success: false, error: errorMsg, profiles: null };
  }
}

/**
 * 验证站点配置文件结构
 * @param {Object} profiles - 站点配置对象
 * @returns {Object} 验证结果
 */
function validateSiteProfiles(profiles) {
  try {
    if (!profiles || typeof profiles !== 'object') {
      return { success: false, error: '配置文件格式无效，必须是JSON对象' };
    }
    
    const requiredTopLevelFields = ['name', 'loginUrl', 'config'];
    const requiredConfigFields = [
      'postContainerSelector',
      'postLinkSelector',
      'postDateSelector',
      'postTitleSelectorOnPage',
      'previewImageSelector',
      'magnetLinkSelector',
      'ed2kLinkSelector',
      'attachmentUrlSelector',
      'passwordSelector',
      'nextPageSelector'
    ];

    // 排除 chromeUserDataPath，只验证论坛配置
    const forumKeys = Object.keys(profiles).filter(key => key !== 'chromeUserDataPath');
    if (forumKeys.length === 0) {
      return { success: false, error: '配置文件中没有论坛配置' };
    }

    // 验证每个论坛配置
    for (const forumKey of forumKeys) {
      // 跳过 chromeUserDataPath 配置项
      if (forumKey === 'chromeUserDataPath') {
        continue;
      }

      const forumConfig = profiles[forumKey];

      if (!forumConfig || typeof forumConfig !== 'object') {
        return { success: false, error: `论坛 "${forumKey}" 的配置格式无效` };
      }

      // 检查顶级必需字段
      for (const field of requiredTopLevelFields) {
        if (!forumConfig.hasOwnProperty(field)) {
          return { success: false, error: `论坛 "${forumKey}" 缺少必需字段: ${field}` };
        }
      }

      // 检查config对象
      if (!forumConfig.config || typeof forumConfig.config !== 'object') {
        return { success: false, error: `论坛 "${forumKey}" 的config字段必须是对象` };
      }

      // 检查config内的必需字段
      for (const field of requiredConfigFields) {
        if (!forumConfig.config.hasOwnProperty(field)) {
          return { success: false, error: `论坛 "${forumKey}" 的config缺少必需字段: ${field}` };
        }

        if (typeof forumConfig.config[field] !== 'string') {
          return { success: false, error: `论坛 "${forumKey}" 的config字段 "${field}" 必须是字符串` };
        }
      }
    }
    
    return { success: true };
    
  } catch (error) {
    return { success: false, error: `验证过程中出错: ${error.message}` };
  }
}

/**
 * 获取所有站点配置
 * @returns {Object} 站点配置对象
 */
function getAllSiteProfiles() {
  if (!siteProfiles) {
    const loadResult = loadSiteProfiles();
    if (!loadResult.success) {
      return null;
    }
  }
  return siteProfiles;
}

/**
 * 获取指定论坛的配置
 * @param {string} forumKey - 论坛标识符
 * @returns {Object|null} 论坛配置对象或null
 */
function getSiteProfile(forumKey) {
  const profiles = getAllSiteProfiles();
  if (!profiles) {
    log.error(`[站点配置服务] 无法获取站点配置`);
    return null;
  }

  if (!profiles.hasOwnProperty(forumKey)) {
    log.error(`[站点配置服务] 论坛 "${forumKey}" 的配置不存在`);
    return null;
  }

  const forumConfig = profiles[forumKey];

  // 为了保持向后兼容性，将config对象的内容扁平化到顶级
  const flattenedConfig = {
    key: forumKey,
    name: forumConfig.name,
    loginUrl: forumConfig.loginUrl,
    boards: forumConfig.boards || {},
    ...forumConfig.config // 将config内的所有字段展开到顶级
  };

  return flattenedConfig;
}

/**
 * 获取所有可用的论坛列表
 * @returns {Array} 论坛信息数组
 */
function getAvailableForums() {
  const profiles = getAllSiteProfiles();
  if (!profiles) {
    return [];
  }
  
  // 排除 chromeUserDataPath，只返回论坛配置
  return Object.keys(profiles)
    .filter(key => key !== 'chromeUserDataPath')
    .map(forumKey => ({
      key: forumKey,
      name: profiles[forumKey].name
    }));
}

/**
 * 重新加载配置文件
 * @returns {Object} 加载结果
 */
function reloadSiteProfiles() {
  log.info('[站点配置服务] 重新加载站点配置文件...');
  siteProfiles = null;
  return loadSiteProfiles();
}

/**
 * 获取Chrome用户配置文件路径
 * @returns {string|null} Chrome用户配置文件路径或null
 */
function getChromeUserDataPath() {
  const profiles = getAllSiteProfiles();
  if (!profiles) {
    log.error(`[站点配置服务] 无法获取站点配置`);
    return null;
  }

  if (!profiles.hasOwnProperty('chromeUserDataPath')) {
    log.error(`[站点配置服务] 配置文件中未找到 chromeUserDataPath`);
    return null;
  }

  const userDataPath = profiles.chromeUserDataPath;

  if (!userDataPath || userDataPath.includes('TODO')) {
    log.error(`[站点配置服务] Chrome用户配置文件路径未正确设置: ${userDataPath}`);
    return null;
  }

  // 检查路径是否存在
  if (!fs.existsSync(userDataPath)) {
    log.error(`[站点配置服务] Chrome用户配置文件路径不存在: ${userDataPath}`);
    return null;
  }

  log.info(`[站点配置服务] Chrome用户配置文件路径: ${userDataPath}`);
  return userDataPath;
}

/**
 * 检查配置文件是否存在
 * @returns {boolean} 文件是否存在
 */
function configFileExists() {
  return fs.existsSync(configPath);
}

module.exports = {
  initializeSiteProfileService,
  loadSiteProfiles,
  getAllSiteProfiles,
  getSiteProfile,
  getAvailableForums,
  reloadSiteProfiles,
  getChromeUserDataPath,
  configFileExists,
  validateSiteProfiles
};
