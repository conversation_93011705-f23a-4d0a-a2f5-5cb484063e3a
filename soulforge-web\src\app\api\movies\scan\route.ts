import { NextRequest, NextResponse } from 'next/server';
import { FileScanner } from '@/lib/services/file-scanner';
import { MovieService } from '@/lib/services/movie-service';
import { VideoProcessor } from '@/lib/services/video-processor';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { directories, libraryId } = body;

    if (!directories || !Array.isArray(directories) || directories.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Directories array is required',
        },
        { status: 400 }
      );
    }

    // Create scanner with progress callback
    const scanner = new FileScanner((progress) => {
      // In a real implementation, you might want to use WebSockets
      // or Server-Sent Events to send progress updates to the client
      console.log(`Scanning progress: ${progress.current}/${progress.total} - ${progress.currentFile}`);
    });

    // Scan directories
    const scanResult = await scanner.scanDirectories(directories);

    // Process and save movies
    const savedMovies = [];
    const errors = [...scanResult.errors];

    for (const movieData of scanResult.movies) {
      try {
        // Get additional video information
        if (movieData.filePath) {
          try {
            const videoInfo = await VideoProcessor.getVideoInfo(movieData.filePath);
            movieData.runtime = movieData.runtime || Math.floor(videoInfo.duration / 60);
            movieData.resolution = movieData.resolution || `${videoInfo.height}p`;
            movieData.videoCodec = videoInfo.videoCodec;
            movieData.audioCodec = videoInfo.audioCodec;
            movieData.fps = videoInfo.fps;
            movieData.fileSize = movieData.fileSize || videoInfo.fileSize;
          } catch (videoError) {
            console.warn(`Failed to get video info for ${movieData.filePath}:`, videoError);
          }
        }

        // Save movie to database
        const savedMovie = await MovieService.createMovie(movieData);
        savedMovies.push(savedMovie);

        // Link to library if specified
        if (libraryId && savedMovie.id) {
          // TODO: Implement library linking
        }
      } catch (error) {
        errors.push(`Failed to save movie ${movieData.fileName}: ${error}`);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        scannedCount: scanResult.movies.length,
        savedCount: savedMovies.length,
        errorCount: errors.length,
        movies: savedMovies,
        errors: errors,
      },
    });
  } catch (error) {
    console.error('Error scanning movies:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to scan movies',
      },
      { status: 500 }
    );
  }
}
