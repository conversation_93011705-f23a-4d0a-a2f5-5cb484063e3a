#!/usr/bin/env node

// test-javbus-provider-optimization.js - 验证 JavBus Provider 优化效果
const fs = require('fs');

function testJavbusProviderOptimization() {
  console.log('🧪 JavBus Provider 优化验证开始...\n');

  try {
    // 检查 JavBus Provider 文件是否存在
    const javbusProviderExists = fs.existsSync('./main_process/services/scrapers/javbusProvider.js');
    if (!javbusProviderExists) {
      console.log('❌ JavBus Provider 文件不存在');
      return;
    }

    const javbusContent = fs.readFileSync('./main_process/services/scrapers/javbusProvider.js', 'utf8');

    // 第一部分：验证智能URL策略
    console.log('🔍 第一部分：验证智能URL策略...');
    
    const hasGetRealUrl = javbusContent.includes('function getRealUrl');
    const hasSearchRealUrl = javbusContent.includes('function searchRealUrl');
    const hasSpecialNumberHandling = javbusContent.includes('CWP') && javbusContent.includes('LAF');
    const hasDirectAccess = javbusContent.includes('尝试直接访问');
    const hasSearchFallback = javbusContent.includes('直接访问失败，开始搜索');
    const hasLoginDetection = javbusContent.includes('lostpasswd');
    
    console.log(`✅ 智能URL策略检查:`);
    console.log(`   getRealUrl函数: ${hasGetRealUrl ? '✅' : '❌'}`);
    console.log(`   searchRealUrl函数: ${hasSearchRealUrl ? '✅' : '❌'}`);
    console.log(`   特殊番号处理(CWP/LAF): ${hasSpecialNumberHandling ? '✅' : '❌'}`);
    console.log(`   直接访问策略: ${hasDirectAccess ? '✅' : '❌'}`);
    console.log(`   搜索降级策略: ${hasSearchFallback ? '✅' : '❌'}`);
    console.log(`   登录状态检测: ${hasLoginDetection ? '✅' : '❌'}`);

    // 第二部分：验证搜索优化
    console.log('\n🔍 第二部分：验证搜索优化...');
    
    const hasSearchUrl = javbusContent.includes('/search/') && javbusContent.includes('&type=');
    const hasMovieBoxSelector = javbusContent.includes('.movie-box');
    const hasPhotoInfoSelector = javbusContent.includes('.photo-info span');
    const hasExactMatching = javbusContent.includes('toUpperCase() === number.toUpperCase()');
    const hasContainsMatching = javbusContent.includes('toUpperCase().includes(number.toUpperCase())');
    
    console.log(`✅ 搜索优化检查:`);
    console.log(`   搜索URL构建: ${hasSearchUrl ? '✅' : '❌'}`);
    console.log(`   movie-box选择器: ${hasMovieBoxSelector ? '✅' : '❌'}`);
    console.log(`   photo-info选择器: ${hasPhotoInfoSelector ? '✅' : '❌'}`);
    console.log(`   精确匹配逻辑: ${hasExactMatching ? '✅' : '❌'}`);
    console.log(`   包含匹配逻辑: ${hasContainsMatching ? '✅' : '❌'}`);

    // 第三部分：验证数据优化
    console.log('\n🔍 第三部分：验证数据优化...');
    
    const hasTitleCleaning = javbusContent.includes('移除番号前缀');
    const hasActorFiltering = javbusContent.includes("actorName !== '♀'");
    const hasUrlCorrection = javbusContent.includes('realUrl');
    const hasVersionField = javbusContent.includes('PROVIDER_VERSION');
    
    console.log(`✅ 数据优化检查:`);
    console.log(`   标题清理逻辑: ${hasTitleCleaning ? '✅' : '❌'}`);
    console.log(`   演员过滤(♀): ${hasActorFiltering ? '✅' : '❌'}`);
    console.log(`   URL修正: ${hasUrlCorrection ? '✅' : '❌'}`);
    console.log(`   版本字段: ${hasVersionField ? '✅' : '❌'}`);

    // 第四部分：验证数据标准化
    console.log('\n🔍 第四部分：验证数据标准化...');
    
    const hasNumberField = javbusContent.includes('number: nfoId');
    const hasOriginaltitleField = javbusContent.includes('originaltitle: title');
    const hasOutlineField = javbusContent.includes("outline: ''");
    const hasReleaseField = javbusContent.includes('release: releaseDate');
    const hasActorField = javbusContent.includes('actor: actors.join');
    const hasActorPhotoField = javbusContent.includes('actor_photo: getActorPhoto');
    const hasTagField = javbusContent.includes('tag: tags.join');
    const hasThumbField = javbusContent.includes('thumb: hdCoverUrl');
    const hasPosterField = javbusContent.includes('poster: hdCoverUrl');
    const hasExtrafanartField = javbusContent.includes('extrafanart: previewImages');
    const hasScoreField = javbusContent.includes('score: rating');
    const hasWebsiteField = javbusContent.includes('website: realUrl');
    const hasImageDownloadField = javbusContent.includes('image_download: !!hdCoverUrl');
    const hasImageCutField = javbusContent.includes("image_cut: 'center'");
    const hasTrailerField = javbusContent.includes("trailer: ''");
    
    console.log(`✅ 数据标准化检查:`);
    console.log(`   number字段: ${hasNumberField ? '✅' : '❌'}`);
    console.log(`   originaltitle字段: ${hasOriginaltitleField ? '✅' : '❌'}`);
    console.log(`   outline字段: ${hasOutlineField ? '✅' : '❌'}`);
    console.log(`   release字段: ${hasReleaseField ? '✅' : '❌'}`);
    console.log(`   actor字段: ${hasActorField ? '✅' : '❌'}`);
    console.log(`   actor_photo字段: ${hasActorPhotoField ? '✅' : '❌'}`);
    console.log(`   tag字段: ${hasTagField ? '✅' : '❌'}`);
    console.log(`   thumb字段: ${hasThumbField ? '✅' : '❌'}`);
    console.log(`   poster字段: ${hasPosterField ? '✅' : '❌'}`);
    console.log(`   extrafanart字段: ${hasExtrafanartField ? '✅' : '❌'}`);
    console.log(`   score字段: ${hasScoreField ? '✅' : '❌'}`);
    console.log(`   website字段: ${hasWebsiteField ? '✅' : '❌'}`);
    console.log(`   image_download字段: ${hasImageDownloadField ? '✅' : '❌'}`);
    console.log(`   image_cut字段: ${hasImageCutField ? '✅' : '❌'}`);
    console.log(`   trailer字段: ${hasTrailerField ? '✅' : '❌'}`);

    // 第五部分：验证辅助函数
    console.log('\n🔍 第五部分：验证辅助函数...');
    
    const hasGetActorPhoto = javbusContent.includes('function getActorPhoto');
    const hasActorPhotoLogic = javbusContent.includes('actor.name') && javbusContent.includes('actor.image');
    const hasArrayCheck = javbusContent.includes('Array.isArray(actorsDetailed)');
    const hasModuleExports = javbusContent.includes('module.exports');
    
    console.log(`✅ 辅助函数检查:`);
    console.log(`   getActorPhoto函数: ${hasGetActorPhoto ? '✅' : '❌'}`);
    console.log(`   演员头像逻辑: ${hasActorPhotoLogic ? '✅' : '❌'}`);
    console.log(`   数组检查: ${hasArrayCheck ? '✅' : '❌'}`);
    console.log(`   模块导出: ${hasModuleExports ? '✅' : '❌'}`);

    // 第六部分：验证错误处理
    console.log('\n🔍 第六部分：验证错误处理...');
    
    const hasTryCatchBlocks = javbusContent.includes('try {') && javbusContent.includes('catch');
    const hasErrorLogging = javbusContent.includes('log.error');
    const hasTimeoutHandling = javbusContent.includes('timeout:');
    const has404Handling = javbusContent.includes('404 Page Not Found');
    const hasCookieErrorHandling = javbusContent.includes('Cookie 无效');
    
    console.log(`✅ 错误处理检查:`);
    console.log(`   异常处理: ${hasTryCatchBlocks ? '✅' : '❌'}`);
    console.log(`   错误日志: ${hasErrorLogging ? '✅' : '❌'}`);
    console.log(`   超时处理: ${hasTimeoutHandling ? '✅' : '❌'}`);
    console.log(`   404处理: ${has404Handling ? '✅' : '❌'}`);
    console.log(`   Cookie错误处理: ${hasCookieErrorHandling ? '✅' : '❌'}`);

    // 第七部分：验证模块加载
    console.log('\n🔍 第七部分：验证模块加载...');
    
    try {
      const javbusProvider = require('./main_process/services/scrapers/javbusProvider.js');
      const hasName = !!javbusProvider.name;
      const hasScrape = typeof javbusProvider.scrape === 'function';
      const hasVersion = !!javbusProvider.version;
      
      console.log(`✅ 模块加载检查:`);
      console.log(`   name属性: ${hasName ? '✅' : '❌'}`);
      console.log(`   scrape函数: ${hasScrape ? '✅' : '❌'}`);
      console.log(`   version属性: ${hasVersion ? '✅' : '❌'}`);
      
      if (hasName) {
        console.log(`   Provider名称: ${javbusProvider.name}`);
      }
      if (hasVersion) {
        console.log(`   Provider版本: ${javbusProvider.version}`);
      }
      
    } catch (error) {
      console.log(`❌ 模块加载失败: ${error.message}`);
    }

    // 总结优化结果
    console.log('\n📊 优化结果总结:');
    
    const optimizationChecks = [
      hasGetRealUrl, hasSearchRealUrl, hasSpecialNumberHandling, hasDirectAccess,
      hasSearchUrl, hasMovieBoxSelector, hasExactMatching, hasContainsMatching,
      hasTitleCleaning, hasActorFiltering, hasUrlCorrection, hasVersionField,
      hasNumberField, hasActorField, hasTagField, hasThumbField, hasWebsiteField,
      hasGetActorPhoto, hasActorPhotoLogic, hasArrayCheck,
      hasTryCatchBlocks, hasErrorLogging, hasTimeoutHandling, has404Handling
    ];
    
    const passedOptimizations = optimizationChecks.filter(Boolean).length;
    const totalOptimizations = optimizationChecks.length;
    const optimizationRate = (passedOptimizations / totalOptimizations * 100).toFixed(1);
    
    console.log(`   优化完成度: ${passedOptimizations}/${totalOptimizations} (${optimizationRate}%)`);
    console.log(`   智能URL策略: ${hasGetRealUrl && hasSearchRealUrl ? '✅' : '❌'}`);
    console.log(`   搜索优化: ${hasSearchUrl && hasExactMatching ? '✅' : '❌'}`);
    console.log(`   数据标准化: ${hasNumberField && hasActorField ? '✅' : '❌'}`);
    console.log(`   辅助函数: ${hasGetActorPhoto && hasActorPhotoLogic ? '✅' : '❌'}`);
    console.log(`   错误处理: ${hasTryCatchBlocks && hasErrorLogging ? '✅' : '❌'}`);

    console.log('\n🎉 JavBus Provider 优化验证完成!');
    console.log('\n📋 优化总结:');
    console.log('1. ✅ 基于对标软件实现智能URL策略');
    console.log('2. ✅ 优化搜索和匹配逻辑');
    console.log('3. ✅ 标准化数据字段，兼容对标软件格式');
    console.log('4. ✅ 增强特殊番号处理能力');
    console.log('5. ✅ 完善错误处理和登录检测');
    console.log('6. ✅ 添加演员头像映射功能');
    console.log('\n💡 优化后的 JavBus Provider 更加智能和健壮！');

  } catch (error) {
    console.error('💥 优化验证过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testJavbusProviderOptimization();
}

module.exports = { testJavbusProviderOptimization };
