/**
 * 下载管理器 - 专门处理文件下载相关功能
 * 
 * 从 collectorService.js 中提取的下载相关逻辑，包括：
 * - 附件下载
 * - 下载状态检查
 * - 文件重复检测
 * - 下载验证
 * - 下载路径管理
 */

const path = require('path');
const fs = require('fs');

class DownloadManager {
  constructor(config) {
    this.log = config.log;
    this.databaseService = config.databaseService;
    this.fileRenameManager = config.fileRenameManager;
    this.workspacePath = config.workspacePath;
    this.updateTaskStatus = config.updateTaskStatus;
    
    // 下载配置
    this.downloadTimeout = 60000; // 60秒超时
    this.downloadRetries = 3;
    this.downloadDelay = 2000; // 2秒间隔
  }

  /**
   * 设置工作区路径
   */
  setWorkspacePath(workspacePath) {
    this.workspacePath = workspacePath;
  }

  /**
   * 获取附件目录路径
   */
  getAttachmentsDir() {
    if (!this.workspacePath) {
      throw new Error('工作区路径未设置');
    }
    return path.join(this.workspacePath, 'attachments');
  }

  /**
   * 确保附件目录存在
   */
  ensureAttachmentsDir() {
    const attachmentsDir = this.getAttachmentsDir();
    if (!fs.existsSync(attachmentsDir)) {
      fs.mkdirSync(attachmentsDir, { recursive: true });
      this.log.info(`[DownloadManager] 创建附件目录: ${attachmentsDir}`);
    }
    return attachmentsDir;
  }

  /**
   * 检查文件是否已经下载过
   */
  async checkFileExists(postData, fileExtension = 'rar') {
    try {
      const attachmentsDir = this.getAttachmentsDir();
      
      if (!fs.existsSync(attachmentsDir)) {
        return { exists: false };
      }

      // 生成期望的文件名
      const fileNameResult = this.fileRenameManager.generateStandardFileName(postData, fileExtension, attachmentsDir);
      
      if (!fileNameResult.success) {
        this.log.warn(`[DownloadManager] 无法生成期望文件名: ${fileNameResult.error}`);
        return { exists: false };
      }

      const expectedFileNameBase = fileNameResult.fileName.replace(/\.[^.]+$/, ''); // 移除扩展名
      const existingFiles = fs.readdirSync(attachmentsDir);
      
      this.log.info(`[DownloadManager] 检查重复文件 - 期望文件名基础: ${expectedFileNameBase}`);
      this.log.info(`[DownloadManager] 附件目录包含 ${existingFiles.length} 个文件`);

      const existingFile = existingFiles.find(file => {
        const fileNameWithoutExt = file.replace(/\.[^/.]+$/, '');
        return fileNameWithoutExt === expectedFileNameBase;
      });

      if (existingFile) {
        const existingFilePath = path.join(attachmentsDir, existingFile);
        this.log.info(`[DownloadManager] ✅ 文件已存在，跳过下载: ${existingFile}`);
        return { 
          exists: true, 
          filePath: existingFilePath,
          fileName: existingFile 
        };
      }

      return { exists: false };

    } catch (error) {
      this.log.error(`[DownloadManager] 检查文件存在性失败: ${error.message}`);
      return { exists: false };
    }
  }

  /**
   * 设置浏览器下载路径
   */
  async setupDownloadPath(page) {
    try {
      const attachmentsDir = this.ensureAttachmentsDir();
      
      // 通过CDP设置下载行为
      const client = await page.context().newCDPSession(page);
      await client.send('Page.setDownloadBehavior', {
        behavior: 'allow',
        downloadPath: attachmentsDir
      });
      
      this.log.info(`[DownloadManager] 📁 已设置下载路径: ${attachmentsDir}`);
      return { success: true, downloadPath: attachmentsDir };
      
    } catch (error) {
      this.log.warn(`[DownloadManager] ⚠️ 设置下载路径失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 下载单个附件
   */
  async downloadSingleAttachment(page, url, postData, siteProfile, attachmentIndex = 0, totalAttachments = 1) {
    try {
      this.log.info(`[DownloadManager] 开始下载附件 ${attachmentIndex + 1}/${totalAttachments}: ${url}`);
      
      // 检查是否已经下载过
      const existsCheck = await this.checkFileExists(postData);
      if (existsCheck.exists) {
        this.databaseService.updateDownloadStatus(postData.postUrl, 'completed', existsCheck.filePath);
        return {
          success: true,
          skipped: true,
          filePath: existsCheck.filePath,
          fileName: existsCheck.fileName
        };
      }

      // 设置下载路径
      const setupResult = await this.setupDownloadPath(page);
      if (!setupResult.success) {
        throw new Error(`设置下载路径失败: ${setupResult.error}`);
      }

      const attachmentsDir = setupResult.downloadPath;
      let downloadCompleted = false;
      let downloadPath = null;

      // 更新状态
      this.updateTaskStatus('downloading', `正在下载附件 ${attachmentIndex + 1}/${totalAttachments}: ${postData.postTitle}`);
      this.databaseService.updateDownloadStatus(postData.postUrl, 'downloading');

      // 监听下载事件
      const downloadPromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('下载超时'));
        }, this.downloadTimeout);

        let downloadHandled = false; // 防止重复处理
        const downloadHandler = async (download) => {
          if (downloadHandled) {
            this.log.warn(`[DownloadManager] ⚠️ 下载事件已处理，跳过重复处理`);
            return;
          }
          downloadHandled = true;

          clearTimeout(timeout);
          page.off('download', downloadHandler); // 移除事件监听器

          const originalFileName = download.suggestedFilename();
          const fileExtension = originalFileName.split('.').pop() || 'rar';

          this.log.info(`[DownloadManager] 🔍 处理下载文件:`);
          this.log.info(`[DownloadManager] - 原始文件名: ${originalFileName}`);
          this.log.info(`[DownloadManager] - 文件扩展名: ${fileExtension}`);

          // 生成标准化文件名
          const fileNameResult = this.fileRenameManager.generateStandardFileName(postData, fileExtension, this.attachmentsDir);

          if (!fileNameResult.success) {
            this.log.error(`[DownloadManager] ❌ 文件名生成失败: ${fileNameResult.error}`);
            reject(new Error(`文件重命名失败: ${fileNameResult.error}`));
            return;
          }

          const newFileName = fileNameResult.fileName;
          const fullPath = path.join(attachmentsDir, newFileName);

          this.log.info(`[DownloadManager] ✅ 文件名校验通过:`);
          this.log.info(`[DownloadManager] - 新文件名: ${newFileName}`);
          this.log.info(`[DownloadManager] - 完整路径: ${fullPath}`);

          // 确保目标目录存在
          const targetDir = path.dirname(fullPath);
          if (!fs.existsSync(targetDir)) {
            this.log.warn(`[DownloadManager] ⚠️ 目标目录不存在，正在创建: ${targetDir}`);
            fs.mkdirSync(targetDir, { recursive: true });
          } else {
            this.log.info(`[DownloadManager] ✅ 目标目录已存在: ${targetDir}`);
          }

          this.updateTaskStatus('downloading', `正在保存文件: ${originalFileName}`);

          try {
            // 保存文件
            await download.saveAs(fullPath);

            // 验证文件是否真的存在
            if (fs.existsSync(fullPath)) {
              const stats = fs.statSync(fullPath);
              this.log.info(`[DownloadManager] ✅ 文件确认存在: ${fullPath}, 大小: ${stats.size} bytes`);

              downloadCompleted = true;
              downloadPath = fullPath;

              this.log.info(`[DownloadManager] ✅ 附件下载完成: ${newFileName}`);
              this.updateTaskStatus('download-completed', `附件下载完成: ${newFileName}`);

              resolve({
                success: true,
                filePath: fullPath,
                fileName: newFileName,
                originalFileName: originalFileName
              });

            } else {
              // 检查默认下载文件夹
              const result = await this.handleDownloadFallback(originalFileName, newFileName, attachmentsDir, postData);
              if (result.success) {
                downloadCompleted = true;
                downloadPath = result.filePath;
                resolve(result);
              } else {
                reject(new Error('文件下载失败，在指定路径和默认下载文件夹都未找到文件'));
              }
            }
          } catch (error) {
            this.log.error(`[DownloadManager] ❌ 下载处理失败: ${error.message}`);

            // 首先检查文件是否实际上已经下载成功了（即使saveAs抛出了错误）
            if (fs.existsSync(fullPath)) {
              const stats = fs.statSync(fullPath);
              this.log.info(`[DownloadManager] ✅ 虽然saveAs报错，但文件实际已存在: ${fullPath}, 大小: ${stats.size} bytes`);

              downloadCompleted = true;
              downloadPath = fullPath;

              this.log.info(`[DownloadManager] ✅ 附件下载完成: ${newFileName}`);
              this.updateTaskStatus('download-completed', `附件下载完成: ${newFileName}`);

              resolve({
                success: true,
                filePath: fullPath,
                fileName: newFileName,
                originalFileName: originalFileName
              });
              return;
            }

            // 如果是路径相关错误，尝试fallback逻辑
            if (error.message.includes('ENOENT') || error.message.includes('no such file or directory')) {
              this.log.warn(`[DownloadManager] 🔄 检测到路径错误，尝试fallback逻辑...`);
              try {
                this.log.info(`[DownloadManager] 🔄 调用fallback处理: ${originalFileName} -> ${newFileName}`);
                const fallbackResult = await this.handleDownloadFallback(originalFileName, newFileName, attachmentsDir, postData);
                this.log.info(`[DownloadManager] 🔄 Fallback结果: ${JSON.stringify(fallbackResult)}`);
                if (fallbackResult.success) {
                  this.log.info(`[DownloadManager] ✅ Fallback成功: ${fallbackResult.fileName}`);
                  downloadCompleted = true;
                  downloadPath = fallbackResult.filePath;
                  resolve(fallbackResult);
                  return;
                } else {
                  this.log.warn(`[DownloadManager] ⚠️ Fallback失败: ${fallbackResult.error}`);
                }
              } catch (fallbackError) {
                this.log.error(`[DownloadManager] ❌ Fallback也失败了: ${fallbackError.message}`);
              }
            }

            reject(error);
          }
        };

        page.on('download', downloadHandler);
      });

      // 点击下载链接
      await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
      await page.waitForTimeout(1000);

      // 等待下载完成
      const result = await downloadPromise;
      
      // 更新数据库状态
      this.databaseService.updateDownloadStatus(postData.postUrl, 'completed', result.filePath);
      
      return result;

    } catch (error) {
      this.log.error(`[DownloadManager] 下载附件失败: ${error.message}`);

      // 如果是路径相关错误，尝试fallback逻辑
      if (error.message.includes('ENOENT') || error.message.includes('no such file or directory')) {
        this.log.warn(`[DownloadManager] 🔄 主下载失败，尝试fallback逻辑...`);
        try {
          const fallbackResult = await this.handleDownloadFallback(originalFileName, newFileName, this.attachmentsDir, postData);
          if (fallbackResult.success) {
            this.log.info(`[DownloadManager] ✅ Fallback成功: ${fallbackResult.fileName}`);
            this.databaseService.updateDownloadStatus(postData.postUrl, 'completed', fallbackResult.filePath);
            return fallbackResult;
          }
        } catch (fallbackError) {
          this.log.error(`[DownloadManager] ❌ Fallback也失败了: ${fallbackError.message}`);
        }
      }

      this.databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, `下载失败: ${error.message}`);

      // 检查是否是特殊错误需要停止任务
      if (error.message.includes('下载次数已达上限') || error.message.includes('需要人机验证')) {
        throw error;
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 处理下载回退逻辑（检查默认下载文件夹）
   */
  async handleDownloadFallback(originalFileName, newFileName, attachmentsDir, postData) {
    try {
      // 首先检查attachments目录中是否有原名文件
      const originalFileInAttachments = path.join(attachmentsDir, originalFileName);

      this.log.warn(`[DownloadManager] 🔍 首先检查attachments目录中的原名文件: ${originalFileInAttachments}`);

      if (fs.existsSync(originalFileInAttachments)) {
        this.log.info(`[DownloadManager] 📁 在attachments目录找到原名文件，正在重命名...`);

        const fullPath = path.join(attachmentsDir, newFileName);

        // 验证重命名操作
        const validation = this.fileRenameManager.validateFileRename(originalFileInAttachments, fullPath, postData);
        if (!validation.shouldProceed) {
          this.log.error(`[DownloadManager] ❌ 重命名验证失败: ${validation.error}`);
          // 保持原文件名
          return {
            success: true,
            filePath: originalFileInAttachments,
            fileName: originalFileName,
            renamed: false
          };
        }

        // 执行重命名
        fs.renameSync(originalFileInAttachments, fullPath);

        this.log.info(`[DownloadManager] ✅ 文件重命名成功: ${originalFileName} -> ${newFileName}`);

        return {
          success: true,
          filePath: fullPath,
          fileName: newFileName,
          renamed: true
        };
      }

      // 如果attachments目录没有，再检查默认下载文件夹
      const defaultDownloadPath = path.join(require('os').homedir(), 'Downloads', originalFileName);

      this.log.warn(`[DownloadManager] ⚠️ attachments目录没有文件，检查默认下载文件夹: ${defaultDownloadPath}`);

      if (fs.existsSync(defaultDownloadPath)) {
        this.log.info(`[DownloadManager] 📁 在默认下载文件夹找到文件，正在移动并重命名...`);

        const fullPath = path.join(attachmentsDir, newFileName);

        // 验证重命名操作
        const validation = this.fileRenameManager.validateFileRename(defaultDownloadPath, fullPath, postData);
        if (!validation.shouldProceed) {
          this.log.error(`[DownloadManager] ❌ 重命名验证失败: ${validation.error}`);
          // 保持原文件名
          const safeTargetPath = path.join(attachmentsDir, originalFileName);
          fs.renameSync(defaultDownloadPath, safeTargetPath);
          return {
            success: true,
            filePath: safeTargetPath,
            fileName: originalFileName,
            renamed: false
          };
        }

        // 执行重命名
        fs.renameSync(defaultDownloadPath, fullPath);

        this.log.info(`[DownloadManager] ✅ 文件移动并重命名成功: ${originalFileName} -> ${newFileName}`);

        return {
          success: true,
          filePath: fullPath,
          fileName: newFileName,
          renamed: true
        };
      }

      return { success: false, error: '在attachments目录和默认下载文件夹都未找到文件' };

    } catch (error) {
      this.log.error(`[DownloadManager] 处理下载回退失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 下载多个附件
   */
  async downloadAttachments(page, postData, siteProfile) {
    const { postTitle, attachmentUrl } = postData;

    if (!attachmentUrl) {
      this.log.info(`[DownloadManager] 跳过下载: 没有附件链接`);
      return { success: false, message: '没有附件链接' };
    }

    const attachmentUrls = attachmentUrl.split('\n').filter(url => url.trim());
    this.log.info(`[DownloadManager] 发现 ${attachmentUrls.length} 个附件链接`);

    let downloadResults = [];
    let hasSuccessfulDownload = false;

    for (let i = 0; i < attachmentUrls.length; i++) {
      const url = attachmentUrls[i].trim();
      
      try {
        const result = await this.downloadSingleAttachment(page, url, postData, siteProfile, i, attachmentUrls.length);
        downloadResults.push(result);
        
        if (result.success) {
          hasSuccessfulDownload = true;
        }

        // 下载间隔
        if (i < attachmentUrls.length - 1) {
          await this.delay(this.downloadDelay);
        }

      } catch (error) {
        this.log.error(`[DownloadManager] 下载第 ${i + 1} 个附件失败: ${error.message}`);
        downloadResults.push({
          success: false,
          error: error.message,
          url: url
        });

        // 如果是需要停止任务的错误，重新抛出
        if (error.message.includes('下载次数已达上限') || error.message.includes('需要人机验证')) {
          throw error;
        }
      }
    }

    return {
      success: hasSuccessfulDownload,
      results: downloadResults,
      totalAttempted: attachmentUrls.length,
      successCount: downloadResults.filter(r => r.success).length
    };
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理Downloads文件夹中的重复文件
   */
  cleanupDownloadsFolder(originalFileName) {
    try {
      const defaultDownloadPath = path.join(require('os').homedir(), 'Downloads', originalFileName);
      if (fs.existsSync(defaultDownloadPath)) {
        this.log.warn(`[DownloadManager] ⚠️ 发现Downloads文件夹中有重复文件，正在删除: ${defaultDownloadPath}`);
        fs.unlinkSync(defaultDownloadPath);
        this.log.info(`[DownloadManager] ✅ 删除Downloads文件夹中的重复文件成功`);
      }
    } catch (error) {
      this.log.error(`[DownloadManager] ❌ 无法删除Downloads文件夹中的重复文件: ${error.message}`);
    }
  }
}

module.exports = DownloadManager;
