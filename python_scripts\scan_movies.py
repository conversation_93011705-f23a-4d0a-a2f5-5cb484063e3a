import os
import json
import argparse
import xml.etree.ElementTree as ET
import subprocess
import re
from datetime import datetime
import sys
import io

# --- Start of UTF-8 fix for Python's own stdout/stderr ---
# Ensure stdout and stderr are UTF-8
# This helps if the script is called in an environment where default IO encoding is not UTF-8
if hasattr(sys.stdout, 'reconfigure'):
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    except Exception as e:
        try:
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        except Exception:
            pass
else:
    try:
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    except (AttributeError, ValueError):
        pass

if hasattr(sys.stderr, 'reconfigure'):
    try:
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except Exception as e:
        try:
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
        except Exception:
            pass
else:
    try:
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
    except (AttributeError, ValueError):
        pass
# --- End of UTF-8 fix ---


VIDEO_EXTENSIONS = ('.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg', '.m4v', '.ts', '.iso', '.strm')
SUBTITLE_EXTENSIONS = ('.srt', '.ass', '.ssa', '.sub', '.idx', '.vtt')
IMAGE_EXTENSIONS = ('.jpg', '.jpeg', '.png', '.webp')


def get_file_size(file_path):
    try:
        if os.path.islink(file_path): # Follow symlinks
            actual_path = os.path.realpath(file_path)
            return os.path.getsize(actual_path)
        return os.path.getsize(file_path)
    except OSError:
        return None

def get_nfo_last_modified(nfo_path):
    try:
        return int(os.path.getmtime(nfo_path))
    except OSError:
        return None

def parse_strm_content(strm_file_path):
    """
    Parses a .strm file to extract the URL and attempt to derive a filename.
    Returns a tuple (url, derived_filename) or (None, None) if failed.
    """
    try:
        with open(strm_file_path, 'r', encoding='utf-8') as f:
            url = f.readline().strip()
            if not url:
                return None, None

            # Attempt to derive a filename from the URL
            # Example: http://*************:9527/d/ex3ezze57780ezhu5?/[BBI-002]...mp4
            # We want to get "[BBI-002]...mp4"
            derived_filename = None
            try:
                # Try to get the last part after '?' or '/'
                if '?' in url:
                    query_part = url.split('?')[-1]
                    if '/' in query_part: # Handles cases like ?/filename.mp4
                        derived_filename = query_part.split('/')[-1]
                    elif query_part: # Handles ?filename.mp4 (less common but possible)
                         derived_filename = query_part
                if not derived_filename:
                    # Fallback to last part after '/' if no '?' or no filename after '?'
                    derived_filename = url.split('/')[-1]
                
                # Further clean up if it's still a weird name (e.g., just a hash)
                if derived_filename and not any(derived_filename.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                    # If it doesn't look like a video filename, try to be smarter if possible
                    # This part can be expanded if there are more complex URL structures
                    pass
                
                # Basic check if it looks like a valid filename part
                if derived_filename and len(derived_filename) > 3 and '.' in derived_filename : # basic check for extension
                    pass
                else: # if still no good filename, maybe use strm filename itself as base
                     derived_filename = os.path.basename(strm_file_path)


            except Exception:
                # If any error in deriving, fallback to strm filename
                derived_filename = os.path.basename(strm_file_path)
            
            return url, derived_filename
    except Exception:
        return None, None


def find_local_cover_for_item(item_base_name_no_ext, item_dir, video_filename_if_available=None):
    """
    Finds a local cover image based on item base name and common patterns.
    `item_base_name_no_ext`: The filename without extension (e.g., movie_nfo or movie_video).
    `item_dir`: The directory where the item (NFO or video) is located.
    `video_filename_if_available`: The actual video filename (with ext) if known, for more precise matching.
    """
    
    # Priority 1: Exact match with "-poster" or similar suffixes
    # e.g., moviename-poster.jpg, moviename.poster.jpg
    suffixes_to_try = ["-poster", ".poster", "-cover", ".cover", "-thumb", ".thumb", ""] # Empty for direct name match
    
    for suffix in suffixes_to_try:
        for ext in IMAGE_EXTENSIONS:
            # Try with item_base_name_no_ext (could be NFO name or video name w/o ext)
            candidate_name = f"{item_base_name_no_ext}{suffix}{ext}"
            potential_path = os.path.join(item_dir, candidate_name)
            if os.path.exists(potential_path):
                return potential_path
            
            # If video_filename_if_available is different and suffix is empty (direct match)
            # this is to cover cases where NFO is Movie.nfo but cover is Movie-CD1.jpg for Movie-CD1.mp4
            if video_filename_if_available and suffix == "":
                video_base_no_ext = os.path.splitext(video_filename_if_available)[0]
                if video_base_no_ext != item_base_name_no_ext:
                    candidate_name_video = f"{video_base_no_ext}{ext}"
                    potential_path_video = os.path.join(item_dir, candidate_name_video)
                    if os.path.exists(potential_path_video):
                        return potential_path_video


    # Priority 2: Generic folder/poster images in the item's directory
    generic_names = ["poster", "folder", "cover", "thumb", "movie"]
    for generic_name in generic_names:
        for ext in IMAGE_EXTENSIONS:
            potential_path = os.path.join(item_dir, f"{generic_name}{ext}")
            if os.path.exists(potential_path):
                return potential_path
                
    return None


def parse_nfo(nfo_path, video_file_name_for_cover_search=None):
    movie_data = {}
    movie_data['localCoverPath'] = None 

    try:
        with open(nfo_path, 'r', encoding='utf-8', errors='replace') as f_xml:
            tree = ET.parse(f_xml) 
        root = tree.getroot()

        def get_text(element_name, parent=root):
            el = parent.find(element_name)
            return el.text.strip() if el is not None and el.text else None

        def get_multiple_texts(element_name_plural, element_name_singular=None, parent=root):
            elements = parent.findall(element_name_plural) 
            if not elements and element_name_singular:
                 elements = parent.findall(element_name_singular)
            
            values = []
            for el in elements:
                if el.tag == 'actor': # Kodi NFO style for actor
                    name_el = el.find('name')
                    if name_el is not None and name_el.text and name_el.text.strip():
                        values.append(name_el.text.strip())
                elif el.text and el.text.strip(): # General case for simple text lists like <genre>, <tag>
                    values.append(el.text.strip())
            return values if values else None

        movie_data['title'] = get_text('title')
        movie_data['originalTitle'] = get_text('originaltitle')
        movie_data['plot'] = get_text('plot')
        movie_data['year'] = int(get_text('year')) if get_text('year') and get_text('year').isdigit() else None
        movie_data['releaseDate'] = get_text('releasedate') 
        if not movie_data['releaseDate']:
             movie_data['releaseDate'] = get_text('premiered')

        movie_data['runtime'] = int(get_text('runtime')) if get_text('runtime') and get_text('runtime').isdigit() else None

        nfo_id_val = get_text('id')
        if not nfo_id_val: # Try uniqueid tags
            uniqueid_tags = root.findall('uniqueid')
            for uid_tag in uniqueid_tags:
                # Prioritize specific known ID types
                if uid_tag.get('type', '').lower() in ['imdb', 'tmdb', 'tvdb', 'sht', 'javdb', 'javlibrary', 'javbus'] and uid_tag.text:
                    nfo_id_val = uid_tag.text.strip()
                    break
            if not nfo_id_val and uniqueid_tags and uniqueid_tags[0].text: # Fallback to first uniqueid if specific types not found
                nfo_id_val = uniqueid_tags[0].text.strip()

        if not nfo_id_val : # Try to extract from filenameandpath if ID is conventional (e.g., JAV IDs)
            id_from_filename_tag = root.find(".//filenameandpath") # Common in Emby/Jellyfin NFOs
            if id_from_filename_tag is not None and id_from_filename_tag.text:
                # Regex for common JAV ID patterns (e.g., ABC-123, AB-1234, ABCD-12345)
                match = re.search(r'([A-Z]{2,5}-\d{3,5})', id_from_filename_tag.text.upper())
                if match:
                    nfo_id_val = match.group(1)
        movie_data['nfoId'] = nfo_id_val

        movie_data['studio'] = get_text('studio')
        
        # --- Revised Series/Set parsing based on user feedback ---
        series_text = get_text('series') # Attempt to get from <series> first

        if not series_text: # If <series> was not found or was empty
            set_elements = root.findall('set')
            set_names = []
            if set_elements:
                for set_el in set_elements:
                    name_in_set_el = set_el.find('name')
                    if name_in_set_el is not None and name_in_set_el.text and name_in_set_el.text.strip():
                        set_names.append(name_in_set_el.text.strip())
                    elif set_el.text and set_el.text.strip(): # If <set> directly contains the name
                        set_names.append(set_el.text.strip())
            
            if set_names: # If any names were collected from <set> tags
                # Heuristic: if multiple <set> names, pick the longest one.
                # This helps differentiate from short set names that might be actor names (e.g., user's NFO example).
                series_text = "" 
                if set_names: # Ensure set_names is not empty before calling max
                    series_text = max(set_names, key=len) 
        
        if series_text: # If series_text was populated from either <series> or <set>
            movie_data['series'] = series_text
        # --- End of Revised Series/Set parsing ---

        directors = get_multiple_texts('director') 
        if directors: movie_data['director'] = directors[0] if directors else None
        
        movie_data['trailerUrl'] = get_text('trailer')
        
        # --- Local Cover Search using the new function ---
        nfo_dir = os.path.dirname(nfo_path)
        base_filename_no_ext_nfo = os.path.splitext(os.path.basename(nfo_path))[0]
        
        movie_data['localCoverPath'] = find_local_cover_for_item(base_filename_no_ext_nfo, nfo_dir, video_file_name_for_cover_search)
        
        if not movie_data['localCoverPath'] and video_file_name_for_cover_search:
            video_base_name_no_ext = os.path.splitext(video_file_name_for_cover_search)[0]
            if video_base_name_no_ext != base_filename_no_ext_nfo :
                 movie_data['localCoverPath'] = find_local_cover_for_item(video_base_name_no_ext, nfo_dir)


        # --- Network Poster/Cover URL Parsing (remains largely the same) ---
        movie_data['posterUrl'] = None
        movie_data['coverUrl'] = None
        
        thumb_elements = root.findall('.//thumb') 
        for thumb in thumb_elements:
            if thumb.text:
                thumb_url = thumb.text.strip()
                aspect = thumb.get('aspect')

                if aspect == 'poster': 
                    if not movie_data['posterUrl']: movie_data['posterUrl'] = thumb_url
                elif aspect == 'fanart' or aspect == 'backdrop':
                     if not movie_data['coverUrl']: movie_data['coverUrl'] = thumb_url
                elif not aspect and not movie_data['posterUrl']: 
                     movie_data['posterUrl'] = thumb_url
                elif not aspect and not movie_data['coverUrl'] and movie_data['posterUrl'] != thumb_url: 
                    movie_data['coverUrl'] = thumb_url

        if not movie_data['posterUrl']: 
            top_level_poster = get_text('poster')
            if top_level_poster: movie_data['posterUrl'] = top_level_poster
        
        if not movie_data['coverUrl']: 
            fanart_tag = root.find('fanart') 
            if fanart_tag is not None and fanart_tag.text and fanart_tag.text.strip():
                 movie_data['coverUrl'] = fanart_tag.text.strip()
            elif not movie_data['coverUrl']: 
                top_level_fanart_text = get_text('fanart') 
                if top_level_fanart_text: movie_data['coverUrl'] = top_level_fanart_text

        movie_data['actors'] = get_multiple_texts('actor') # Actor parsing is specific in get_multiple_texts
        movie_data['genres'] = get_multiple_texts('genre')
        movie_data['tags'] = get_multiple_texts('tag')
        
        movie_data['nfoLastModified'] = get_nfo_last_modified(nfo_path)

    except ET.ParseError as e:
        pass
    except Exception as e:
        pass
    return movie_data

def get_ffprobe_info(video_path, ffprobe_exec):
    media_info = {}
    
    if video_path.lower().endswith('.strm'):
        pass

    try:
        command = [
            ffprobe_exec,
            '-v', 'error',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_path 
        ]
        result = subprocess.run(command, capture_output=True, check=False, timeout=45) 

        stdout_str = result.stdout.decode('utf-8', errors='replace')
        
        if result.returncode != 0:
            if not stdout_str.strip():
                return media_info 
        
        if not stdout_str.strip():
            return media_info

        ffprobe_data = json.loads(stdout_str)
        
        if 'format' in ffprobe_data:
            fmt = ffprobe_data['format']
            if 'duration' in fmt and fmt['duration'] and fmt['duration'] != 'N/A':
                try:
                    media_info['runtime'] = int(float(fmt['duration']) / 60) 
                except ValueError: pass 
            if 'size' in fmt and fmt['size']:
                 try:
                    media_info['fileSize'] = int(fmt['size'])
                 except ValueError: pass
            if 'bit_rate' in fmt and fmt['bit_rate']:
                media_info['videoBitrate'] = fmt['bit_rate']

        video_stream = next((s for s in ffprobe_data.get('streams', []) if s.get('codec_type') == 'video'), None)
        audio_stream = next((s for s in ffprobe_data.get('streams', []) if s.get('codec_type') == 'audio'), None)

        if video_stream:
            media_info['videoCodec'] = video_stream.get('codec_name')
            media_info['videoCodecFull'] = video_stream.get('codec_long_name')
            if 'width' in video_stream and 'height' in video_stream:
                media_info['resolution'] = f"{video_stream['width']}x{video_stream['height']}"
                media_info['videoHeight'] = str(video_stream['height'])
            if 'avg_frame_rate' in video_stream and '/' in video_stream['avg_frame_rate']:
                parts = video_stream['avg_frame_rate'].split('/')
                if len(parts) == 2:
                    try:
                        num, den = map(int, parts)
                        if den != 0: media_info['fps'] = round(num / den, 2)
                    except ValueError: pass
            if 'bit_rate' in video_stream and video_stream['bit_rate']: 
                 media_info['videoBitrate'] = video_stream['bit_rate']

        if audio_stream:
            media_info['audioCodec'] = audio_stream.get('codec_name')
            media_info['audioCodecFull'] = audio_stream.get('codec_long_name')
            media_info['audioChannelsDesc'] = audio_stream.get('channel_layout')
            if not media_info['audioChannelsDesc'] and 'channels' in audio_stream:
                 media_info['audioChannelsDesc'] = str(audio_stream['channels']) + 'ch'
            if 'sample_rate' in audio_stream:
                try:
                    media_info['audioSampleRate'] = int(audio_stream['sample_rate'])
                except ValueError: pass
            if 'bit_rate' in audio_stream and audio_stream['bit_rate']:
                media_info['audioBitrate'] = audio_stream['bit_rate']
    
    except subprocess.TimeoutExpired:
        pass 
    except json.JSONDecodeError as e:
        pass
    except FileNotFoundError: 
        raise 
    except Exception as e:
        pass
    return media_info

def apply_filename_suffix_rules(filename, rules_list):
    detected_tags = []
    if not rules_list:
        return detected_tags
    
    for rule in rules_list:
        try:
            if rule.get('suffix', '').lower() in filename.lower():
                tags_to_add = rule.get('tags', [])
                if isinstance(tags_to_add, list):
                    detected_tags.extend(t for t in tags_to_add if isinstance(t, str))
        except Exception:
            continue 
    return list(set(detected_tags)) 

def check_external_subtitles(video_path_or_strm_path):
    base, _ = os.path.splitext(video_path_or_strm_path)
    for ext in SUBTITLE_EXTENSIONS:
        if os.path.exists(base + ext):
            return True
    return False

def extract_cd_part_info(filename):
    patterns = [
        r'(CD|DISK|DISC|PART|PT)[\s._-]*([A-D1-9])\b', 
        r'CD_(\d+)', 
        r'DISC(\d)',
        r'FILE[\s._-]*(\d+)', 
        r'(\d+)[\s._-]*OF[\s._-]*(\d+)', 
        r'[\s._-]([A-D])$', 
        r'[\s._-](\d)$' 
    ]
    filename_upper = filename.upper()
    base_filename_no_ext, _ = os.path.splitext(filename_upper) 

    for pattern in patterns:
        match = re.search(pattern, base_filename_no_ext)
        if match:
            if pattern == r'(CD|DISK|DISC|PART|PT)[\s._-]*([A-D1-9])\b':
                return f"{match.group(1).strip()}{match.group(2).strip()}"
            elif pattern == r'CD_(\d+)':
                return f"CD{match.group(1)}"
            elif pattern == r'DISC(\d)':
                 return f"Disc{match.group(1)}"
            elif pattern == r'FILE[\s._-]*(\d+)':
                return f"File{match.group(1)}"
            elif pattern == r'(\d+)[\s._-]*OF[\s._-]*(\d+)':
                return f"Part{match.group(1)}_Total{match.group(2)}"
            elif pattern == r'[\s._-]([A-D])$' or pattern == r'[\s._-](\d)$':
                if len(base_filename_no_ext) > len(match.group(0)) + 2 : 
                    return f"Part{match.group(1)}"
    return None


def scan_single_path(target_path, ffprobe_path_exec, suffix_rules_list):
    movies_found = []
    files_to_process_tuples = [] 
    
    try:
        is_symlink = os.path.islink(target_path)
        actual_target_path = os.path.realpath(target_path) if is_symlink else target_path

        if not os.path.exists(actual_target_path):
            return []

        if os.path.isfile(actual_target_path):
            file_lower = actual_target_path.lower()
            if file_lower.endswith(VIDEO_EXTENSIONS):
                if file_lower.endswith('.strm'):
                    url, derived_name = parse_strm_content(actual_target_path)
                    if url:
                        files_to_process_tuples.append((actual_target_path, derived_name or os.path.basename(actual_target_path), actual_target_path))
                else:
                    files_to_process_tuples.append((actual_target_path, os.path.basename(actual_target_path), None))
        elif os.path.isdir(actual_target_path):
            for root, _, files in os.walk(actual_target_path, followlinks=True): 
                for file in files:
                    file_lower = file.lower()
                    if file_lower.endswith(VIDEO_EXTENSIONS):
                        try:
                            full_file_path = os.path.join(root, file)
                            actual_file_path_in_dir = os.path.realpath(full_file_path)
                            
                            if file_lower.endswith('.strm'):
                                url, derived_name = parse_strm_content(actual_file_path_in_dir)
                                if url:
                                    files_to_process_tuples.append((actual_file_path_in_dir, derived_name or file, actual_file_path_in_dir))
                            else:
                                files_to_process_tuples.append((actual_file_path_in_dir, file, None))
                        except Exception as e_file: 
                            pass 
        else:
            return [] 
    except Exception as e_path:
        return []


    nfo_info_from_parse = {} # Define to ensure it's available in broader scope

    for video_file_path_for_ffprobe, display_file_name, strm_path_original in files_to_process_tuples:
        movie_data = {
            'filePath': strm_path_original if strm_path_original else video_file_path_for_ffprobe, 
            'fileName': display_file_name, 
            'title': os.path.splitext(display_file_name)[0], 
            'originalTitle': None, 'nfoId': None, 'year': None, 'releaseDate': None,
            'runtime': None, 'plot': None, 'studio': None, 'series': None, 'director': None,
            'trailerUrl': None, 'posterUrl': None, 'coverUrl': None,
            'localCoverPath': None, 
            'actors': [], 'genres': [], 'tags': [],
            'nfoLastModified': None, 'resolution': None, 'videoHeight': None, 
            'fileSize': get_file_size(video_file_path_for_ffprobe) if not strm_path_original else None, 
            'videoCodec': None, 'audioCodec': None, 'autoDetectedFileNameTags': [],
            'fps': None, 'videoCodecFull': None, 'videoBitrate': None,
            'audioCodecFull': None, 'audioChannelsDesc': None, 'audioSampleRate': None, 'audioBitrate': None,
            'hasExternalSubtitles': False, 'cdPartInfo': None,
            'plotJa': None, 'plotZh': None, 
            'watched': False, 'personalRating': None, 'aiAnalyzedTags': [],         
            'aiRecommendationType': None, 'aiRecommendationScore': None, 'aiRecommendationJustification': None, 
            'preferredStatus': None, 'customFileTags': [], 'versionCategories': [],      
        }

        base_name_for_nfo, _ = os.path.splitext(display_file_name)
        current_item_dir = os.path.dirname(movie_data['filePath'])
        nfo_file_path = os.path.join(current_item_dir, base_name_for_nfo + '.nfo')
        
        nfo_info_from_parse.clear() # Clear for each movie

        if os.path.exists(nfo_file_path):
            nfo_info_temp = parse_nfo(nfo_file_path, display_file_name) 
            nfo_info_from_parse.update(nfo_info_temp) # Store parsed NFO to use its title later
            for key, value in nfo_info_temp.items():
                if value is not None: movie_data[key] = value
        else: 
            if strm_path_original and os.path.basename(strm_path_original) != display_file_name :
                base_name_strm, _ = os.path.splitext(os.path.basename(strm_path_original))
                nfo_file_path_strm = os.path.join(current_item_dir, base_name_strm + '.nfo')
                if os.path.exists(nfo_file_path_strm):
                    nfo_info_temp = parse_nfo(nfo_file_path_strm, display_file_name)
                    nfo_info_from_parse.update(nfo_info_temp)
                    for key, value in nfo_info_temp.items():
                        if value is not None: movie_data[key] = value
        
        if not movie_data['localCoverPath']:
            movie_data['localCoverPath'] = find_local_cover_for_item(base_name_for_nfo, current_item_dir, display_file_name)


        if ffprobe_path_exec:
            ffprobe_info = get_ffprobe_info(video_file_path_for_ffprobe, ffprobe_path_exec)
            for key, value in ffprobe_info.items():
                 if value is not None: movie_data[key] = value

        movie_data['autoDetectedFileNameTags'] = apply_filename_suffix_rules(movie_data['fileName'], suffix_rules_list)
        movie_data['hasExternalSubtitles'] = check_external_subtitles(movie_data['filePath'])
        movie_data['cdPartInfo'] = extract_cd_part_info(movie_data['fileName'])
        
        if movie_data['title'] == base_name_for_nfo and nfo_info_from_parse and nfo_info_from_parse.get('title'):
            movie_data['title'] = nfo_info_from_parse.get('title')

        movies_found.append(movie_data)
        
    return movies_found

def main():
    parser = argparse.ArgumentParser(description="Scan movie files, parse NFOs, and get media info.")
    parser.add_argument('command', choices=['parse'], help="The command to execute (currently only 'parse').")
    parser.add_argument('target_paths', nargs='+', help="One or more paths to video files or directories to scan.")
    parser.add_argument('--ffprobe-path', default='ffprobe', help="Path to the ffprobe executable. Defaults to 'ffprobe' (uses system PATH).")
    parser.add_argument('--suffix-rules', default='[]', help="JSON string of filename suffix rules.")
    
    args = parser.parse_args()

    if args.command == 'parse':
        try:
            suffix_rules = json.loads(args.suffix_rules)
            if not isinstance(suffix_rules, list):
                suffix_rules = []
        except json.JSONDecodeError:
            suffix_rules = [] 

        all_movie_data = []
        for target_path in args.target_paths:
            movies_in_path = scan_single_path(target_path, args.ffprobe_path, suffix_rules)
            all_movie_data.extend(movies_in_path)
        
        json_output = json.dumps(all_movie_data, indent=None, ensure_ascii=False, separators=(',', ':'))
        
        try:
            sys.stdout.buffer.write(json_output.encode('utf-8'))
            sys.stdout.flush() 
        except Exception as e:
            print(json_output)


if __name__ == '__main__':
    main()