// src/components/common/ActorLink.tsx
// 可交互的演员姓名链接组件

import React from 'react';
import { LuUser } from 'react-icons/lu';

interface ActorLinkProps {
  actorName: string;
  className?: string;
  showIcon?: boolean;
  variant?: 'default' | 'subtle' | 'bold';
  onActorClick?: (actorName: string) => void;
}

export const ActorLink: React.FC<ActorLinkProps> = ({
  actorName,
  className = '',
  showIcon = false,
  variant = 'default',
  onActorClick
}) => {
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onActorClick) {
      onActorClick(actorName);
    } else {
      // 触发全局事件，让父组件处理
      // 这样可以避免在这个组件中直接依赖状态管理
      const event = new CustomEvent('openActorProfile', {
        detail: { actorName },
        bubbles: true
      });
      document.dispatchEvent(event);
    }
  };

  // 根据variant选择样式
  const getVariantStyles = () => {
    switch (variant) {
      case 'subtle':
        return 'text-gray-300 hover:text-[#B8860B] hover:underline';
      case 'bold':
        return 'text-white font-medium hover:text-[#B8860B] hover:underline';
      default:
        return 'text-blue-400 hover:text-[#B8860B] hover:underline';
    }
  };

  const baseStyles = 'cursor-pointer transition-colors duration-200 inline-flex items-center gap-1';
  const variantStyles = getVariantStyles();
  const combinedClassName = `${baseStyles} ${variantStyles} ${className}`;

  return (
    <span
      className={combinedClassName}
      onClick={handleClick}
      title={`查看 ${actorName} 的详细档案`}
    >
      {showIcon && <LuUser className="h-3 w-3" />}
      {actorName}
    </span>
  );
};

// 用于处理演员列表的辅助组件
interface ActorListProps {
  actors: string[];
  separator?: string;
  className?: string;
  variant?: 'default' | 'subtle' | 'bold';
  showIcons?: boolean;
  onActorClick?: (actorName: string) => void;
}

export const ActorList: React.FC<ActorListProps> = ({
  actors,
  separator = ', ',
  className = '',
  variant = 'default',
  showIcons = false,
  onActorClick
}) => {
  if (!actors || actors.length === 0) {
    return <span className="text-gray-500">未知</span>;
  }

  return (
    <span className={className}>
      {actors.map((actor, index) => (
        <React.Fragment key={actor}>
          <ActorLink
            actorName={actor}
            variant={variant}
            showIcon={showIcons}
            onActorClick={onActorClick}
          />
          {index < actors.length - 1 && (
            <span className="text-gray-400">{separator}</span>
          )}
        </React.Fragment>
      ))}
    </span>
  );
};

export default ActorLink;
