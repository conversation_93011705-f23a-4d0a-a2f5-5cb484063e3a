'use client';

import React from 'react';
import { useMovieStore } from '@/lib/stores/movie-store';
import { useUIStore } from '@/lib/stores/ui-store';
import { AppLayout } from '@/components/layout/app-layout';
import { MovieGrid } from '@/components/movie/movie-grid';
import { MovieFilters } from '@/components/movie/movie-filters';
import { VersionSelectionModal } from '@/components/movie/version-selection-modal';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Movie } from '@/lib/types';
import { Plus, RefreshCw } from 'lucide-react';

export default function MoviesPage() {
  const {
    movies,
    loading,
    error,
    filters,
    sortField,
    sortOrder,
    viewMode,
    pagination,
    setFilters,
    clearFilters,
    setSortField,
    setSortOrder,
    setViewMode,
    setPage,
    fetchMovies,
    toggleWatchedAsync,
    deleteMovieAsync,
    scanMoviesAsync,
  } = useMovieStore();

  const { addToast } = useUIStore();

  // Version selection modal state
  const [versionModalOpen, setVersionModalOpen] = React.useState(false);
  const [selectedMovieForVersions, setSelectedMovieForVersions] = React.useState<Movie | null>(null);

  React.useEffect(() => {
    // Load movies on component mount and when dependencies change
    fetchMovies();
  }, [filters, sortField, sortOrder, pagination.page, fetchMovies]);

  const handleMoviePlay = (movie: Movie) => {
    // TODO: Implement movie playback
    addToast({
      type: 'info',
      title: '播放电影',
      description: `正在播放: ${movie.title || movie.fileName}`,
    });
  };

  const handleShowVersions = (movie: Movie) => {
    setSelectedMovieForVersions(movie);
    setVersionModalOpen(true);
  };

  const handleVersionSelect = (version: Movie) => {
    // Play the selected version
    handleMoviePlay(version);
    setVersionModalOpen(false);
    setSelectedMovieForVersions(null);
  };

  const handleVersionModalClose = () => {
    setVersionModalOpen(false);
    setSelectedMovieForVersions(null);
  };

  const handleMovieToggleWatched = async (movie: Movie) => {
    try {
      await toggleWatchedAsync(movie.id);
      addToast({
        type: 'success',
        title: movie.watched ? '标记为未观看' : '标记为已观看',
        description: movie.title || movie.fileName,
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: '操作失败',
        description: '无法更新观看状态',
      });
    }
  };

  const handleMovieToggleFavorite = async (movie: Movie) => {
    try {
      // TODO: Implement API call
      addToast({
        type: 'success',
        title: '收藏状态已更新',
        description: movie.title || movie.fileName,
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: '操作失败',
        description: '无法更新收藏状态',
      });
    }
  };

  const handleMovieEdit = (movie: Movie) => {
    // TODO: Open edit modal
    addToast({
      type: 'info',
      title: '编辑电影',
      description: movie.title || movie.fileName,
    });
  };

  const handleScanMovies = async () => {
    // For demo purposes, scan a default directory
    // In a real app, this would open a dialog to select directories
    try {
      await scanMoviesAsync(['/path/to/movies']); // Placeholder path
      addToast({
        type: 'success',
        title: '扫描完成',
        description: '电影扫描已完成',
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: '扫描失败',
        description: '无法扫描电影目录',
      });
    }
  };

  const handleRefresh = () => {
    fetchMovies();
  };

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-lg font-semibold text-destructive mb-2">
              加载错误
            </h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">电影库</h1>
          <p className="text-muted-foreground">
            共 {pagination.total} 部电影
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button onClick={handleScanMovies}>
            <Plus className="h-4 w-4 mr-2" />
            扫描电影
          </Button>
        </div>
      </div>

      {/* Filters */}
      <MovieFilters
        filters={filters}
        viewMode={viewMode}
        sortField={sortField}
        sortOrder={sortOrder}
        onFiltersChange={setFilters}
        onViewModeChange={setViewMode}
        onSortChange={(field, order) => {
          setSortField(field);
          setSortOrder(order);
        }}
        onClearFilters={clearFilters}
      />

      {/* Movie Grid */}
      <MovieGrid
        movies={movies}
        viewMode={viewMode}
        loading={loading}
        onMoviePlay={handleMoviePlay}
        onMovieToggleFavorite={handleMovieToggleFavorite}
        onMovieToggleWatched={handleMovieToggleWatched}
        onMovieEdit={handleMovieEdit}
        onShowVersions={handleShowVersions}
      />

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            disabled={pagination.page === 1}
            onClick={() => setPage(pagination.page - 1)}
          >
            上一页
          </Button>
          <span className="flex items-center px-4 text-sm text-muted-foreground">
            第 {pagination.page} 页，共 {pagination.totalPages} 页
          </span>
          <Button
            variant="outline"
            disabled={pagination.page === pagination.totalPages}
            onClick={() => setPage(pagination.page + 1)}
          >
            下一页
          </Button>
        </div>
      )}
      </div>

      {/* Version Selection Modal */}
      <VersionSelectionModal
        isOpen={versionModalOpen}
        onClose={handleVersionModalClose}
        movie={selectedMovieForVersions}
        onSelectVersion={handleVersionSelect}
      />
    </AppLayout>
  );
}
