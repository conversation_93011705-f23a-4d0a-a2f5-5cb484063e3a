
// soul-forge-electron/preload.js

const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

contextBridge.exposeInMainWorld('sfeElectronAPI', {
  // Scan and Movie Data
  triggerScan: (pathsToScan, libraryId) => ipcRenderer.send('select-folder-and-scan', { pathsToScan, libraryId }),
  onScanComplete: (callback) => { 
    const channel = 'scan-complete';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onScanStatusUpdate: (callback) => {
    const channel = 'scan-status-update';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onScanProgressUpdate: (callback) => {
    const channel = 'scan-progress-update';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onScanError: (callback) => {
    const channel = 'scan-error';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  getMovies: async (params) => ipcRenderer.invoke('get-movies', params),
  playVideo: (params) => ipcRenderer.send('play-video', params), // params: { filePath, title, strmUrl }
  onPlayStrmUrl: (callback) => { // New listener for STRM URLs
    const channel = 'play-strm-url';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  openExternalUrl: (url) => ipcRenderer.send('open-external-url', url),
  saveNfoData: async (videoFilePath, movieData) => ipcRenderer.invoke('save-nfo-data', { videoFilePath, movieData }),
  
  // AI Functions
  generatePlotSummary: async (params) => ipcRenderer.invoke('generate-plot-summary-with-ai', params),
  translatePlotWithAI: async (params) => ipcRenderer.invoke('translate-plot-with-ai', params),
  embellishPlotWithAI: async (params) => ipcRenderer.invoke('embellish-plot-with-ai', params), // Options might be passed within params if needed for specific calls
  analyzeMovieTagsWithAI: async (params) => ipcRenderer.invoke('analyze-movie-tags-with-ai', params),
  suggestCoverFromSnapshotsWithAI: async (params) => ipcRenderer.invoke('suggest-cover-from-snapshots-with-ai', params),
  analyzeMovieRecommendationIndex: async (params) => ipcRenderer.invoke('analyze-movie-recommendation-index', params),
  getCleanupSuggestionsFromAI: async () => ipcRenderer.invoke('get-cleanup-suggestions-from-ai'),
  
  invokeLinLuoChat: async (userInput) => ipcRenderer.invoke('linluo-chat', userInput),
  onLinLuoChatChunk: (callback) => {
    const channel = 'linluo-chat-chunk';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onLinLuoChatEnd: (callback) => {
    const channel = 'linluo-chat-end';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onLinLuoChatError: (callback) => {
    const channel = 'linluo-chat-error';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  saveChatHistory: async (params) => ipcRenderer.invoke('save-chat-history', params),

  // File and Image Utilities
  downloadNfoCover: async (videoFilePath, imageUrl, desiredLocalFilename) => ipcRenderer.invoke('download-nfo-cover', { videoFilePath, imageUrl, desiredLocalFilename }),
  browseLocalCover: async (videoFilePath, desiredLocalFilename) => ipcRenderer.invoke('browse-local-cover', { videoFilePath, desiredLocalFilenameTemplate: desiredLocalFilename }),
  browseImageForDataUrl: async () => ipcRenderer.invoke('browse-image-for-dataurl'),
  imagePathToDataUrl: (filePath) => ipcRenderer.invoke('image-path-to-data-url', filePath), 
  
  // Settings
  getSettings: async () => ipcRenderer.invoke('get-settings'),
  saveSettings: async (settings) => ipcRenderer.invoke('save-settings', settings),
  selectDirectory: async () => ipcRenderer.invoke('select-directory'),
  dialogSelectDirectory: async () => ipcRenderer.invoke('dialog:select-directory'),
  dialogSelectFile: async (options) => ipcRenderer.invoke('dialog:select-file', options),
  getPathSep: () => ipcRenderer.invoke('get-path-sep'),
  testAiConnection: async (provider, config) => ipcRenderer.invoke('test-ai-connection', provider, config),

  // Actor Avatars
  getActorAvatarDetails: async (actorName) => ipcRenderer.invoke('get-actor-avatar-details', actorName),
  scrapeActorAvatars: async () => ipcRenderer.invoke('scrape-actor-avatars'),
  onScrapeAvatarsProgress: (callback) => {
    const channel = 'scrape-avatars-progress';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onScrapeAvatarsComplete: (callback) => {
    const channel = 'scrape-avatars-complete';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
   onScrapeAvatarsError: (callback) => {
    const channel = 'scrape-avatars-error';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },

  // 刮削器服务
  scrapeMovie: async (nfoId) => ipcRenderer.invoke('scraper-scrape-movie', nfoId),

  // 即时番号侦察服务
  archiveScrapeAndCreate: async (nfoId) => ipcRenderer.invoke('archive:scrape-and-create-virtual-asset', nfoId),
  onArchiveScrapeProgress: (callback) => {
    const channel = 'archive-scrape-progress';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },

  // 下载中转站服务
  stagingScan: async () => ipcRenderer.invoke('staging:scan'),
  stagingProcessFile: async (filePath, nfoId, targetSeriesFolder) => ipcRenderer.invoke('staging:process-file', filePath, nfoId, targetSeriesFolder),
  stagingGetSeriesFolders: async () => ipcRenderer.invoke('staging:get-series-folders'),

  // 本地资产代理化服务
  createAssetFromLocalFile: async (filePath) => ipcRenderer.invoke('asset:create-from-local-file', filePath),

  // 资产回收站服务
  recycleVersion: async (versionDbId) => ipcRenderer.invoke('asset:recycle-version', versionDbId),
  recycleMovie: async (nfoId) => ipcRenderer.invoke('asset:recycle-movie', nfoId),
  getRecycledItems: async () => ipcRenderer.invoke('asset:get-recycled-items'),
  generateDeletionList: async () => ipcRenderer.invoke('asset:generate-deletion-list'),
  openFileLocation: async (filePath) => ipcRenderer.invoke('asset:open-file-location', filePath),

  // 历史回溯扫描器服务
  startDailyScan: async () => ipcRenderer.invoke('daily-scan:start'),
  startHistoryScan: async (options) => ipcRenderer.invoke('history-scan:start', options),
  getDailyScanStatus: async () => ipcRenderer.invoke('daily-scan:get-status'),
  stopDailyScan: async () => ipcRenderer.invoke('daily-scan:stop'),

  // 未来新品扫描服务
  scanUpcomingReleases: async () => ipcRenderer.invoke('upcoming-scan:start'),

  // 演员档案服务
  getActorProfile: async (actorName) => ipcRenderer.invoke('actor:get-profile', actorName),
  getAllActorProfiles: async (limit, offset) => ipcRenderer.invoke('actor:get-all-profiles', limit, offset),
  deleteActorProfile: async (actorName) => ipcRenderer.invoke('actor:delete-profile', actorName),
  getMoviesByActor: async (actorName) => ipcRenderer.invoke('movies:get-by-actor', actorName),
  getActorCompleteFilmography: async (actorName) => ipcRenderer.invoke('actor:get-complete-filmography', actorName),

  // NFO 铸造厂服务
  exportNfo: async (nfoId) => ipcRenderer.invoke('nfo:export', nfoId),
  importNfo: async (nfoFilePath) => ipcRenderer.invoke('nfo:import', nfoFilePath),

  // 每日简报扫描进度监听
  onDailyScanProgress: (callback) => {
    const listener = (event, data) => callback(data);
    ipcRenderer.on('daily-scan-progress', listener);
    return () => ipcRenderer.removeListener('daily-scan-progress', listener);
  },

  // 未来新品扫描进度监听
  onUpcomingScanProgress: (callback) => {
    const listener = (event, data) => callback(data);
    ipcRenderer.on('upcoming-scan-progress', listener);
    return () => ipcRenderer.removeListener('upcoming-scan-progress', listener);
  },

  // Movie Versions and Snapshots
  getMovieVersions: async (nfoId) => ipcRenderer.invoke('get-movie-versions', nfoId),
  getUnifiedVersions: async (nfoId) => ipcRenderer.invoke('movie:get-unified-versions', nfoId),
  getMovieCdParts: async (nfoId) => {
    if (typeof nfoId !== 'string' || !nfoId) return { success: false, error: '提供给 preload 的 nfoId (CD Parts) 无效', cdParts: [] };
    return ipcRenderer.invoke('get-movie-cd-parts', nfoId);
  },
  updateMovieNfoId: async (movieId, nfoId) => ipcRenderer.invoke('update-movie-nfo-id', { movieId, nfoId }),

  // Node.js NFO 修复功能
  batchFixNfoIds: async () => ipcRenderer.invoke('batch-fix-nfo-ids'),
  analyzeNfoIdStatus: async () => ipcRenderer.invoke('analyze-nfo-id-status'),
  validateNfoIds: async () => ipcRenderer.invoke('validate-nfo-ids'),

  // Collector 模块验证
  verifyCollectedLinksTable: async () => ipcRenderer.invoke('verify-collected-links-table'),

  // Collector 服务 API
  collectorStartTask: async (siteKey, targetUrl, options) => ipcRenderer.invoke('collector-start-task', { siteKey, targetUrl, options }),
  collectorStopTask: async () => ipcRenderer.invoke('collector-stop-task'),
  collectorForceStopTask: async () => ipcRenderer.invoke('collector-force-stop-task'),
  collectorGetStatus: async () => ipcRenderer.invoke('collector-get-status'),
  collectorGetForums: async () => ipcRenderer.invoke('collector-get-forums'),
  collectorGetData: async (options) => ipcRenderer.invoke('collector-get-data', options),
  collectorDeleteLink: async (id) => ipcRenderer.invoke('collector-delete-link', id),
  collectorDeleteLinks: async (ids) => ipcRenderer.invoke('collector-delete-links', ids),
  collectorPurgeLinks: async (ids) => ipcRenderer.invoke('collector-purge-links', ids),
  collectorUpdateMdPath: async (id, mdDocumentPath) => ipcRenderer.invoke('collector-update-md-path', id, mdDocumentPath),
  collectorAnalyzeWithAi: async (recordId) => ipcRenderer.invoke('collector-analyze-with-ai', recordId),
  collectorConfigureDownload: async (downloadConfig) => ipcRenderer.invoke('collector-configure-download', downloadConfig),

  // AI分类管理
  aiCategoriesGetAll: async () => ipcRenderer.invoke('ai-categories:get-all'),
  aiCategoriesAdd: async (name) => ipcRenderer.invoke('ai-categories:add', name),
  aiCategoriesUpdate: async (id, newName) => ipcRenderer.invoke('ai-categories:update', { id, newName }),
  aiCategoriesDelete: async (id) => ipcRenderer.invoke('ai-categories:delete', id),

  // 历史档案复杂查询
  archiveComplexQuery: async (filters) => ipcRenderer.invoke('archive:complex-query', filters),

  // 获取所有符合条件的链接
  archiveGetAllLinksForQuery: async (filters) => ipcRenderer.invoke('archive:get-all-links-for-query', filters),

  // 批量 AI 分析
  archiveBatchAnalyzeAI: async (recordIds) => ipcRenderer.invoke('archive:batch-analyze-ai', recordIds),
  onBatchAIProgress: (callback) => {
    const channel = 'batch-ai-progress';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },

  // 文件操作
  fileExists: async (filePath) => ipcRenderer.invoke('file-exists', filePath),
  openFileInDefaultApp: async (filePath) => ipcRenderer.invoke('open-file-in-default-app', filePath),
  selectFile: async (options) => ipcRenderer.invoke('select-file', options),
  selectFolder: async () => ipcRenderer.invoke('select-folder'),

  // WhatsLink 预览服务
  queryWhatsLink: async (magnetUrl) => ipcRenderer.invoke('misc:query-whatslink', magnetUrl),

  // Dashboard 看板服务
  getRecentActivity: async () => ipcRenderer.invoke('dashboard:get-activity'),
  getDashboardStats: async () => ipcRenderer.invoke('dashboard:get-stats'),

  // IPC Renderer for event listening
  ipcRenderer: {
    on: (channel, listener) => ipcRenderer.on(channel, listener),
    removeListener: (channel, listener) => ipcRenderer.removeListener(channel, listener),
  },

  // Collector 状态更新监听
  onCollectorStatusUpdate: (callback) => {
    const listener = (event, statusUpdate) => callback(statusUpdate);
    ipcRenderer.on('collector-status-update', listener);
    return () => ipcRenderer.removeListener('collector-status-update', listener);
  },

  // Collector 任务完成监听
  onCollectorTaskCompleted: (callback) => {
    const listener = (event, taskResult) => callback(taskResult);
    ipcRenderer.on('collector-task-completed', listener);
    return () => ipcRenderer.removeListener('collector-task-completed', listener);
  },
  generateThumbnails: async (params) => ipcRenderer.invoke('generate-thumbnails', params),
  getExistingSnapshots: async (params) => ipcRenderer.invoke('get-existing-snapshots', params),
  getAllSnapshotsForNfoId: async (nfoId) => ipcRenderer.invoke('movie:get-all-snapshots-for-nfoId', nfoId),
  saveVersionMarks: async (params) => ipcRenderer.invoke('save-version-marks', params),
  
  // File Management
  renameFilesByNfoId: async (nfoId) => ipcRenderer.invoke('rename-files-by-nfo-id', nfoId),
  moveToRecycleBin: async (filePath) => ipcRenderer.invoke('move-to-recycle-bin', filePath),

  // Database Management
  backupDatabase: async () => ipcRenderer.invoke('backup-database'),
  restoreDatabase: async () => ipcRenderer.invoke('restore-database'),
  onDatabaseRestored: (callback) => {
    const channel = 'database-restored';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },

  // Favorites
  toggleFavorite: async (itemType, itemValue) => ipcRenderer.invoke('toggle-favorite', { itemType, itemValue }),
  isFavorite: async (itemType, itemValue) => ipcRenderer.invoke('is-favorite', { itemType, itemValue }),
  batchCheckFavorites: async (items) => ipcRenderer.invoke('batch-check-favorites', items),
  getFavorites: async (itemType) => ipcRenderer.invoke('get-favorites', itemType),

  // Privacy Mode
  getPrivacyModeState: async () => ipcRenderer.invoke('get-privacy-mode-state'),
  attemptUnlockPrivacyMode: async (password) => ipcRenderer.invoke('attempt-unlock-privacy-mode', password),
  togglePrivacyModeNoPassword: async () => ipcRenderer.invoke('toggle-privacy-mode-no-password'),
  onPrivacySettingsChanged: (callback) => {
    const channel = 'privacy-settings-changed';
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  
  // Recommendations
  getInitialRecommendations: async (params) => ipcRenderer.invoke('get-initial-recommendations', params),
  formatRecommendationsAsAiMessage: async (params) => ipcRenderer.invoke('format-recommendations-as-ai-message', params),

  // NFO Plot Polisher Tool - Updated for two-step process
  nfoPolishToolScanDirectories: (directories) => ipcRenderer.send('nfo-polish-tool:scan-directories', directories),
  onNfoPolishToolScanProgress: (callback) => {
    const channel = 'nfo-polish-tool:scan-progress';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onNfoPolishToolScanComplete: (callback) => {
    const channel = 'nfo-polish-tool:scan-complete';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  nfoPolishToolProcessFiles: (nfoFilePaths) => ipcRenderer.send('nfo-polish-tool:process-files', nfoFilePaths),
  onNfoPolishToolProcessProgress: (callback) => {
    const channel = 'nfo-polish-tool:process-progress';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onNfoPolishToolProcessComplete: (callback) => {
    const channel = 'nfo-polish-tool:process-complete';
    ipcRenderer.on(channel, (_event, summary) => callback(summary));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  nfoPolishToolCancel: () => ipcRenderer.send('nfo-polish-tool:cancel-operation'), // Added cancel operation
  // Common listeners (retained)
  onNfoPolishToolLog: (callback) => {
    const channel = 'nfo-polish-tool:log';
    ipcRenderer.on(channel, (_event, message) => callback(message));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onNfoPolishToolError: (callback) => {
    const channel = 'nfo-polish-tool:error';
    ipcRenderer.on(channel, (_event, errorData) => callback(errorData));
    return () => ipcRenderer.removeAllListeners(channel);
  },

  // Movie Libraries
  manageMovieLibrary: (params) => ipcRenderer.invoke('manage-movie-library', params),
  getMovieLibraries: () => ipcRenderer.invoke('get-movie-libraries'),
  getRecentMovies: (fetchType, limit) => ipcRenderer.invoke('get-recent-movies', { fetchType, limit }),

  // Smart Libraries
  getSmartLibraries: () => ipcRenderer.invoke('get-smart-libraries'),
  createSmartLibrary: (libraryData) => ipcRenderer.invoke('create-smart-library', libraryData),
  updateSmartLibrary: (id, updateData) => ipcRenderer.invoke('update-smart-library', id, updateData),
  deleteSmartLibrary: (id) => ipcRenderer.invoke('delete-smart-library', id),

  // Ingest Center (情报中心)
  ingestSelectWorkspace: () => ipcRenderer.invoke('ingest-select-workspace'),
  ingestStartScan: (workspacePath) => ipcRenderer.invoke('ingest-start-scan', workspacePath),
  ingestGetFileInfo: (filePath) => ipcRenderer.invoke('ingest-get-file-info', filePath),
  ingestValidateWorkspace: (workspacePath) => ipcRenderer.invoke('ingest-validate-workspace', workspacePath),
  ingestStartWorkflow: (workspacePath, mdFiles) => ipcRenderer.invoke('ingest-start-workflow', workspacePath, mdFiles),
  onIngestScanProgress: (callback) => {
    const channel = 'ingest-scan-progress';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },
  onIngestWorkflowProgress: (callback) => {
    const channel = 'ingest-workflow-progress';
    ipcRenderer.on(channel, (_event, data) => callback(data));
    return () => ipcRenderer.removeAllListeners(channel);
  },
});
