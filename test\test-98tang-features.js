// 测试98堂论坛新功能的脚本
const path = require('path');

// 模拟日期解析函数测试
function parseForumDate(dateText) {
  if (!dateText) return null;

  // 清理文本
  const cleanText = dateText.trim();

  // 匹配 YYYY-MM-DD 格式
  const match = cleanText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
  if (match) {
    return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
  }

  // 匹配其他常见格式，如 YYYY/MM/DD
  const match2 = cleanText.match(/(\d{4})\/(\d{1,2})\/(\d{1,2})/);
  if (match2) {
    return new Date(parseInt(match2[1]), parseInt(match2[2]) - 1, parseInt(match2[3]));
  }

  // 匹配 MM-DD 格式（假设是当年）
  const match3 = cleanText.match(/(\d{1,2})-(\d{1,2})/);
  if (match3) {
    const currentYear = new Date().getFullYear();
    return new Date(currentYear, parseInt(match3[1]) - 1, parseInt(match3[2]));
  }

  // 解析相对日期格式
  const now = new Date();
  
  // 匹配 "X小时前"
  const hoursMatch = cleanText.match(/(\d+)\s*小时前/);
  if (hoursMatch) {
    const hours = parseInt(hoursMatch[1]);
    return new Date(now.getTime() - hours * 60 * 60 * 1000);
  }

  // 匹配 "X分钟前"
  const minutesMatch = cleanText.match(/(\d+)\s*分钟前/);
  if (minutesMatch) {
    const minutes = parseInt(minutesMatch[1]);
    return new Date(now.getTime() - minutes * 60 * 1000);
  }

  // 匹配 "X天前"
  const daysMatch = cleanText.match(/(\d+)\s*天前/);
  if (daysMatch) {
    const days = parseInt(daysMatch[1]);
    return new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
  }

  // 匹配 "昨天"
  if (cleanText.includes('昨天')) {
    return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // 匹配 "前天"
  if (cleanText.includes('前天')) {
    return new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
  }

  // 匹配 "今天"
  if (cleanText.includes('今天')) {
    return new Date(now.getFullYear(), now.getMonth(), now.getDate());
  }

  // 如果无法解析，返回当前日期作为备用
  console.warn(`无法解析日期格式: "${dateText}"，使用当前日期作为备用`);
  return new Date();
}

// 测试密码提取函数
function testPasswordExtraction(text) {
  const passwordPatterns = [
    // 标准格式
    /密码[：:]\s*([^\s\n\r]+)/i,
    /解压密码[：:]\s*([^\s\n\r]+)/i,
    /password[：:]\s*([^\s\n\r]+)/i,
    /pwd[：:]\s*([^\s\n\r]+)/i,
    /pass[：:]\s*([^\s\n\r]+)/i,
    
    // 带括号的格式
    /【解压密码】[：:]\s*([^\s\n\r]+)/i,
    /【密码】[：:]\s*([^\s\n\r]+)/i,
    /\[解压密码\][：:]\s*([^\s\n\r]+)/i,
    /\[密码\][：:]\s*([^\s\n\r]+)/i,
    
    // 其他常见格式
    /解压码[：:]\s*([^\s\n\r]+)/i,
    /压缩密码[：:]\s*([^\s\n\r]+)/i,
    /提取码[：:]\s*([^\s\n\r]+)/i,
    /访问密码[：:]\s*([^\s\n\r]+)/i,
    
    // 英文格式
    /extract\s*password[：:]\s*([^\s\n\r]+)/i,
    /archive\s*password[：:]\s*([^\s\n\r]+)/i,
    /unzip\s*password[：:]\s*([^\s\n\r]+)/i,
    
    // 简化格式（无冒号）
    /密码\s+([^\s\n\r]+)/i,
    /解压密码\s+([^\s\n\r]+)/i,
    /password\s+([^\s\n\r]+)/i
  ];

  for (const pattern of passwordPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      let password = match[1].trim();
      // 清理可能的多余字符
      password = password.replace(/[，。！？；：""''（）【】\[\]]/g, '');
      return password;
    }
  }
  return null;
}

// 测试链接提取函数
function testLinkExtraction(text) {
  const magnetMatch = text.match(/magnet:\?[^\s\n\r]+/);
  const ed2kMatch = text.match(/ed2k:\/\/[^\s\n\r]+/);
  
  return {
    magnet: magnetMatch ? magnetMatch[0] : null,
    ed2k: ed2kMatch ? ed2kMatch[0] : null
  };
}

// 运行测试
function runTests() {
  console.log('🧪 开始测试98堂论坛新功能...\n');
  
  // 测试日期解析
  console.log('📅 测试日期解析功能:');
  const dateTests = [
    '2024-01-15',
    '2024/01/15',
    '01-15',
    '14 小时前',
    '30 分钟前',
    '3 天前',
    '昨天',
    '前天',
    '今天',
    '无效日期'
  ];
  
  dateTests.forEach(dateText => {
    const result = parseForumDate(dateText);
    console.log(`  "${dateText}" -> ${result ? result.toISOString().split('T')[0] : 'null'}`);
  });
  
  // 测试密码提取
  console.log('\n🔑 测试密码提取功能:');
  const passwordTests = [
    '解压密码：abc123',
    '【解压密码】：xyz789',
    '[密码]: test123',
    '提取码: 1234',
    'password: mypass',
    '解压密码 secret',
    '无密码内容'
  ];
  
  passwordTests.forEach(text => {
    const result = testPasswordExtraction(text);
    console.log(`  "${text}" -> ${result || '未找到'}`);
  });
  
  // 测试链接提取
  console.log('\n🔗 测试链接提取功能:');
  const linkTests = [
    'magnet:?xt=urn:btih:1234567890abcdef&dn=test',
    'ed2k://|file|test.avi|123456|abcdef|/',
    '这里有磁力链接 magnet:?xt=urn:btih:abcdef1234567890 和其他内容',
    '包含ed2k链接: ed2k://|file|movie.mp4|987654321|fedcba|/ 的文本',
    '无链接的普通文本'
  ];
  
  linkTests.forEach(text => {
    const result = testLinkExtraction(text);
    console.log(`  "${text.substring(0, 50)}..." -> 磁力:${result.magnet ? '✓' : '✗'} ED2K:${result.ed2k ? '✓' : '✗'}`);
  });
  
  console.log('\n✅ 所有功能测试完成！');
}

// 运行测试
runTests();
