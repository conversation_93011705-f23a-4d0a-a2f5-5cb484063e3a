// main_process/services/pathResolverService.js
const path = require('path');
const fs = require('fs').promises;
const log = require('electron-log');
const settingsService = require('./settingsService');

/**
 * 路径解析服务 - 负责根据影片元数据计算标准化存储路径
 */

/**
 * 根据影片元数据解析三位一体分离式存储的所有相关路径
 * @param {Object} movieData - 影片元数据对象
 * @param {string} movieData.nfoId - 番号
 * @param {string[]} movieData.tags - 标签数组
 * @param {string} movieData.mosaic - 马赛克类型 ('有码' | '无码' | '其他')
 * @param {string} movieData.studio - 制作商
 * @returns {Promise<Object>} 包含三位一体分离式存储路径的对象
 */
async function resolveAssetPaths(movieData) {
  if (!movieData || !movieData.nfoId) {
    throw new Error('影片数据无效：缺少必要的 nfoId 字段');
  }

  log.info(`[路径解析服务] 开始为 ${movieData.nfoId} 解析三位一体分离式存储路径...`);

  try {
    // 获取三位一体的仓库设置
    const settings = settingsService.getSettings();
    const assetsPath = settings.assetsPath;      // 元数据仓库
    const trailersPath = settings.trailersPath;  // 预告片仓库
    const mediaPath = settings.mediaPath;        // 正片仓库
    const categoryRules = JSON.parse(settings.categoryDirectoryRules || '{}');

    // 验证三个仓库路径都已设置
    if (!assetsPath) {
      throw new Error('元数据仓库路径未设置');
    }
    if (!trailersPath) {
      throw new Error('预告片仓库路径未设置');
    }
    if (!mediaPath) {
      throw new Error('正片仓库路径未设置');
    }

    // 1. 确定分类目录
    const categoryDir = determineCategoryDirectory(movieData, categoryRules);

    // 2. 提取番号头（用于子目录分组）
    const nfoPrefix = extractNfoPrefix(movieData.nfoId);

    // 3. 构建三个仓库的基础路径
    const assetsBasePath = path.join(assetsPath, categoryDir, nfoPrefix, movieData.nfoId);
    const trailersBasePath = path.join(trailersPath, categoryDir, nfoPrefix);
    const mediaBasePath = path.join(mediaPath, categoryDir, nfoPrefix);

    // 4. 生成三位一体分离式存储的所有路径
    const assetPaths = {
      // === 元数据仓库 (Assets) ===
      assetsRootPath: assetsBasePath,
      posterPath: path.join(assetsBasePath, 'poster.jpg'),
      fanartPath: path.join(assetsBasePath, 'fanart.jpg'),
      coverPath: path.join(assetsBasePath, 'cover.jpg'),
      previewsDir: path.join(assetsBasePath, 'previews'),
      metaFilePath: path.join(assetsBasePath, `${movieData.nfoId}.meta.json`),
      nfoFilePath: path.join(assetsBasePath, `${movieData.nfoId}.nfo`),
      strmFilePath: path.join(assetsBasePath, `${movieData.nfoId}.strm`),

      // === 预告片仓库 (Trailers) ===
      trailersRootPath: trailersBasePath,
      trailerPath: path.join(trailersBasePath, `${movieData.nfoId}-trailer.mp4`),

      // === 正片仓库 (Media) ===
      mediaRootPath: mediaBasePath,
      videoPath: path.join(mediaBasePath, `${movieData.nfoId}.mkv`),

      // === 通用信息 ===
      categoryDir: categoryDir,
      nfoPrefix: nfoPrefix,

      // === 向后兼容 (废弃，但保留以避免破坏现有代码) ===
      movieRootPath: assetsBasePath,  // 指向元数据仓库
      trailerPath_legacy: path.join(assetsBasePath, 'trailer.mp4')  // 旧的预告片路径
    };

    // 5. 确保三个仓库的目录结构存在
    await ensureDirectoryExists(assetsBasePath);
    await ensureDirectoryExists(assetPaths.previewsDir);
    await ensureDirectoryExists(trailersBasePath);
    await ensureDirectoryExists(mediaBasePath);

    log.info(`[路径解析服务] ${movieData.nfoId} 三位一体路径解析完成:`);
    log.info(`  元数据仓库: ${assetsBasePath}`);
    log.info(`  预告片仓库: ${trailersBasePath}`);
    log.info(`  正片仓库: ${mediaBasePath}`);

    return assetPaths;

  } catch (error) {
    log.error(`[路径解析服务] 解析 ${movieData.nfoId} 三位一体路径失败: ${error.message}`);
    throw error;
  }
}

/**
 * 根据影片数据和分类规则确定分类目录
 * @param {Object} movieData - 影片元数据
 * @param {Object} categoryRules - 分类规则对象
 * @returns {string} 分类目录名
 */
function determineCategoryDirectory(movieData, categoryRules) {
  // 优先检查马赛克类型
  if (movieData.mosaic) {
    if (movieData.mosaic === '无码' && categoryRules.jav_uncensored) {
      return 'jav_uncensored';
    }
    if (movieData.mosaic === '有码' && categoryRules.jav_censored) {
      return 'jav_censored';
    }
  }

  // 检查标签匹配
  if (movieData.tags && Array.isArray(movieData.tags)) {
    for (const [categoryKey, keywords] of Object.entries(categoryRules)) {
      for (const keyword of keywords) {
        if (movieData.tags.some(tag => tag.includes(keyword))) {
          return categoryKey;
        }
      }
    }
  }

  // 检查制作商或其他字段
  if (movieData.studio) {
    // 可以根据制作商进一步细分类别
    // 这里可以添加更多的制作商匹配逻辑
  }

  // 默认分类
  return 'uncategorized';
}

/**
 * 从番号中提取前缀（用于目录分组）
 * @param {string} nfoId - 番号
 * @returns {string} 番号前缀
 */
function extractNfoPrefix(nfoId) {
  if (!nfoId) return 'UNKNOWN';

  // 移除常见的分隔符和数字，提取字母前缀
  const match = nfoId.match(/^([A-Za-z]+)/);
  if (match) {
    return match[1].toUpperCase();
  }

  // 如果没有匹配到字母前缀，使用前3个字符
  return nfoId.substring(0, 3).toUpperCase();
}

/**
 * 确保指定路径的目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 * @returns {Promise<void>}
 */
async function ensureDirectoryExists(dirPath) {
  try {
    await fs.mkdir(dirPath, { recursive: true });
    log.debug(`[路径解析服务] 目录已确保存在: ${dirPath}`);
  } catch (error) {
    if (error.code !== 'EEXIST') {
      log.error(`[路径解析服务] 创建目录失败: ${dirPath}`, error);
      throw error;
    }
  }
}

/**
 * 获取分类规则的可读描述
 * @returns {Object} 分类规则描述
 */
function getCategoryRulesDescription() {
  const settings = settingsService.getSettings();
  const categoryRules = JSON.parse(settings.categoryDirectoryRules || '{}');
  
  return {
    rules: categoryRules,
    description: '根据影片的马赛克类型、标签等信息自动分类到对应目录'
  };
}

/**
 * 生成预览图的具体文件路径
 * @param {string} previewsDir - 预览图目录路径
 * @param {number} index - 预览图索引（从0开始）
 * @param {string} extension - 文件扩展名（默认为 .jpg）
 * @returns {string} 预览图文件路径
 */
function generatePreviewPath(previewsDir, index, extension = '.jpg') {
  const fileName = `${String(index + 1).padStart(2, '0')}${extension}`;
  return path.join(previewsDir, fileName);
}

/**
 * 验证并修复路径（处理特殊字符、长度限制等）
 * @param {string} filePath - 原始路径
 * @returns {string} 修复后的路径
 */
function sanitizePath(filePath) {
  // 移除或替换不安全的字符
  let sanitized = filePath
    .replace(/[<>:"|?*]/g, '_')  // 替换 Windows 不允许的字符
    .replace(/\s+/g, '_')        // 替换空格为下划线
    .replace(/_{2,}/g, '_');     // 合并多个下划线

  // 处理路径长度限制（Windows 260字符限制）
  if (sanitized.length > 200) {
    const ext = path.extname(sanitized);
    const base = path.basename(sanitized, ext);
    const dir = path.dirname(sanitized);
    
    // 截断文件名但保留扩展名
    const truncatedBase = base.substring(0, 150);
    sanitized = path.join(dir, truncatedBase + ext);
  }

  return sanitized;
}

/**
 * 生成 STRM 文件内容，用于链接到正片仓库中的视频文件
 * @param {string} nfoId - 番号
 * @param {string} mediaPath - 正片仓库中的视频文件路径
 * @returns {string} STRM 文件内容
 */
function generateStrmContent(nfoId, mediaPath) {
  // STRM 文件内容就是视频文件的路径
  // 可以是本地路径或网络URL
  return mediaPath;
}

/**
 * 根据番号和仓库设置生成完整的 STRM 链接路径
 * @param {string} nfoId - 番号
 * @returns {Promise<string>} 正片仓库中的视频文件路径
 */
async function resolveMediaPath(nfoId) {
  try {
    const settings = settingsService.getSettings();
    const mediaPath = settings.mediaPath;

    if (!mediaPath) {
      throw new Error('正片仓库路径未设置');
    }

    // 构建基本的影片数据用于路径解析
    const movieData = {
      nfoId: nfoId,
      tags: [],
      mosaic: '有码',  // 默认值
      studio: '未知'
    };

    // 解析路径
    const paths = await resolveAssetPaths(movieData);
    return paths.videoPath;

  } catch (error) {
    log.error(`[路径解析服务] 解析 ${nfoId} 正片路径失败: ${error.message}`);
    throw error;
  }
}

/**
 * 创建 STRM 文件，用于 Kodi/Jellyfin 等媒体服务器
 * @param {string} strmFilePath - STRM 文件的保存路径
 * @param {string} mediaFilePath - 正片仓库中的视频文件路径
 * @returns {Promise<boolean>} 创建是否成功
 */
async function createStrmFile(strmFilePath, mediaFilePath) {
  try {
    const strmContent = generateStrmContent(path.basename(strmFilePath, '.strm'), mediaFilePath);

    // 确保目录存在
    await ensureDirectoryExists(path.dirname(strmFilePath));

    // 写入 STRM 文件
    await fs.writeFile(strmFilePath, strmContent, 'utf8');

    log.info(`[路径解析服务] STRM 文件创建成功: ${strmFilePath} -> ${mediaFilePath}`);
    return true;

  } catch (error) {
    log.error(`[路径解析服务] 创建 STRM 文件失败: ${strmFilePath} - ${error.message}`);
    return false;
  }
}

module.exports = {
  resolveAssetPaths,
  ensureDirectoryExists,
  determineCategoryDirectory,
  extractNfoPrefix,
  getCategoryRulesDescription,
  generatePreviewPath,
  sanitizePath,
  generateStrmContent,
  resolveMediaPath,
  createStrmFile
};
