
// soul-forge-electron/src/components/common/Pagination.tsx
import React from 'react';
import { LuChevronLeft, LuChevronRight } from 'react-icons/lu';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  isLoading: boolean;
}

const Pagination: React.FC<PaginationProps> = ({ currentPage, totalPages, onPageChange, isLoading }) => {
  if (totalPages <= 1) return null;

  return (
    <div className="flex justify-center items-center space-x-2 my-6" role="navigation" aria-label="Pagination">
      <button
        onClick={() => onPageChange(1)}
        disabled={currentPage === 1 || isLoading}
        className="pagination-button"
        aria-label="Go to first page"
        title="第一页"
      >
        &laquo;&laquo;
      </button>
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1 || isLoading}
        className="pagination-button"
        aria-label="Go to previous page"
        title="上一页"
      >
        <LuChevronLeft size={18} />
      </button>
      <span className="text-sm text-neutral-300" aria-label={`Page ${currentPage} of ${totalPages}`}>
        第 {currentPage} / {totalPages} 页
      </span>
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages || isLoading}
        className="pagination-button"
        aria-label="Go to next page"
        title="下一页"
      >
        <LuChevronRight size={18} />
      </button>
      <button
        onClick={() => onPageChange(totalPages)}
        disabled={currentPage === totalPages || isLoading}
        className="pagination-button"
        aria-label="Go to last page"
        title="最后一页"
      >
        &raquo;&raquo;
      </button>
    </div>
  );
};

export default Pagination;
