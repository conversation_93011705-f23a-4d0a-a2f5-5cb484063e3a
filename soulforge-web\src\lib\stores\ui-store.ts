import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface Modal {
  id: string;
  type: string;
  data?: any;
}

interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  description?: string;
  duration?: number;
}

interface UIState {
  // Modals
  modals: Modal[];
  
  // Toasts
  toasts: Toast[];
  
  // Loading states
  globalLoading: boolean;
  loadingStates: Record<string, boolean>;
  
  // Sidebar
  sidebarOpen: boolean;
  
  // Theme
  theme: 'light' | 'dark' | 'system';
  
  // Actions
  openModal: (type: string, data?: any) => void;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  
  setGlobalLoading: (loading: boolean) => void;
  setLoading: (key: string, loading: boolean) => void;
  
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

let modalIdCounter = 0;
let toastIdCounter = 0;

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      // Initial State
      modals: [],
      toasts: [],
      globalLoading: false,
      loadingStates: {},
      sidebarOpen: true,
      theme: 'system',
      
      // Modal Actions
      openModal: (type, data) => {
        const id = `modal-${++modalIdCounter}`;
        set((state) => ({
          modals: [...state.modals, { id, type, data }]
        }));
      },
      
      closeModal: (id) =>
        set((state) => ({
          modals: state.modals.filter((modal) => modal.id !== id)
        })),
      
      closeAllModals: () => set({ modals: [] }),
      
      // Toast Actions
      addToast: (toast) => {
        const id = `toast-${++toastIdCounter}`;
        const newToast = { ...toast, id };
        
        set((state) => ({
          toasts: [...state.toasts, newToast]
        }));
        
        // Auto remove toast after duration
        const duration = toast.duration || 5000;
        setTimeout(() => {
          get().removeToast(id);
        }, duration);
      },
      
      removeToast: (id) =>
        set((state) => ({
          toasts: state.toasts.filter((toast) => toast.id !== id)
        })),
      
      clearToasts: () => set({ toasts: [] }),
      
      // Loading Actions
      setGlobalLoading: (loading) => set({ globalLoading: loading }),
      
      setLoading: (key, loading) =>
        set((state) => ({
          loadingStates: {
            ...state.loadingStates,
            [key]: loading
          }
        })),
      
      // Sidebar Actions
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
      
      // Theme Actions
      setTheme: (theme) => set({ theme }),
    }),
    {
      name: 'ui-store',
    }
  )
);
