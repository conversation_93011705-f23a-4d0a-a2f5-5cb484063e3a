// 独立测试刮削器的脚本
// 用于诊断 "File is not defined" 错误

const log = require('electron-log');
const path = require('path');
const os = require('os');

async function testScraperStandalone() {
  console.log('🔍 开始独立测试刮削器...');

  try {
    // 0. 初始化设置服务
    console.log('0️⃣ 初始化设置服务...');
    const settingsService = require('./main_process/services/settingsService');
    const userDataPath = path.join(os.tmpdir(), 'soulforge-test');
    settingsService.initializeSettings(log, userDataPath);
    console.log('✅ 设置服务初始化成功');

    // 1. 测试模块加载
    console.log('\n1️⃣ 测试模块加载...');
    
    console.log('加载 browserManager...');
    const browserManager = require('./main_process/services/browserManager');
    console.log('✅ browserManager 加载成功');
    
    console.log('加载 javbusProvider...');
    const javbusProvider = require('./main_process/services/scrapers/javbusProvider');
    console.log('✅ javbusProvider 加载成功');
    
    console.log('加载 scraperManager...');
    const scraperManager = require('./main_process/services/scraperManager');
    console.log('✅ scraperManager 加载成功');
    
    // 2. 测试基本功能
    console.log('\n2️⃣ 测试基本功能...');
    
    console.log('测试 browserManager.getBrowser()...');
    const browser = await browserManager.getBrowser();
    console.log('✅ 浏览器实例创建成功');
    
    console.log('测试创建浏览器上下文...');
    const context = await browserManager.createContext();
    console.log('✅ 浏览器上下文创建成功');
    
    console.log('关闭上下文...');
    await context.close();
    console.log('✅ 上下文关闭成功');
    
    // 3. 测试 JavBus Provider
    console.log('\n3️⃣ 测试 JavBus Provider...');
    
    console.log('测试 javbusProvider.scrape()...');
    const testNfoId = 'JUFE-585';
    
    try {
      const result = await javbusProvider.scrape(testNfoId);
      console.log('✅ JavBus Provider 测试成功');
      console.log('结果:', {
        nfoId: result.nfoId,
        title: result.title,
        studio: result.studio,
        actorsCount: result.actors.length,
        tagsCount: result.tags.length
      });
    } catch (providerError) {
      console.error('❌ JavBus Provider 测试失败:', providerError.message);
      console.error('错误详情:', providerError);
    }
    
    // 4. 测试 ScraperManager
    console.log('\n4️⃣ 测试 ScraperManager...');
    
    try {
      const managerResult = await scraperManager.scrapeMovieById(testNfoId);
      console.log('✅ ScraperManager 测试成功');
      console.log('结果:', {
        nfoId: managerResult.nfoId,
        title: managerResult.title,
        studio: managerResult.studio
      });
    } catch (managerError) {
      console.error('❌ ScraperManager 测试失败:', managerError.message);
      console.error('错误详情:', managerError);
    }
    
    // 5. 清理资源
    console.log('\n5️⃣ 清理资源...');
    await browserManager.closeBrowser();
    console.log('✅ 浏览器已关闭');
    
    console.log('\n🎉 独立测试完成！');
    
  } catch (error) {
    console.error('❌ 独立测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
    
    // 检查是否是 "File is not defined" 错误
    if (error.message.includes('File is not defined')) {
      console.error('\n🔍 检测到 "File is not defined" 错误');
      console.error('这通常是因为在 Node.js 环境中使用了浏览器专用的 API');
      console.error('请检查以下可能的原因:');
      console.error('1. 是否在服务端代码中使用了 File、Blob 等浏览器 API');
      console.error('2. 是否有模块在加载时执行了浏览器专用代码');
      console.error('3. 是否有 TypeScript 类型定义冲突');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testScraperStandalone().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testScraperStandalone };
