// main_process/services/physicalAssetMonitor.js
const fs = require('fs').promises;
const path = require('path');
const log = require('electron-log');
const xml2js = require('xml2js');
const NodeNfoParser = require('./nodeNfoParser');
const databaseService = require('./databaseService');
const settingsService = require('./settingsService');
const pathResolverService = require('./pathResolverService');

/**
 * 物理资产监视器 - 负责扫描用户的媒体文件并同步到数据库
 */

// 支持的媒体文件扩展名
const MEDIA_EXTENSIONS = [
  '.mp4', '.mkv', '.avi', '.wmv', '.mov', '.flv', '.webm', '.m4v',
  '.strm', '.ts', '.m2ts', '.mts', '.vob', '.iso'
];

// 支持的元数据文件扩展名
const METADATA_EXTENSIONS = [
  '.nfo', '.meta.json'
];

// 支持的图片文件扩展名
const IMAGE_EXTENSIONS = [
  '.jpg', '.jpeg', '.png', '.webp', '.bmp', '.gif'
];

/**
 * 扫描源文件夹，识别影片文件夹（包括用户指定的源文件夹和待导入目录）
 * @param {string[]} folderPaths - 源文件夹路径数组
 * @returns {Promise<Object>} 扫描结果，包含常规影片文件夹和待导入文件夹
 */
async function scanSourceFolders(folderPaths) {
  const result = {
    regularMovieFolders: [],
    importMovieFolders: []
  };

  // 扫描用户指定的源文件夹
  if (folderPaths && Array.isArray(folderPaths) && folderPaths.length > 0) {
    log.info(`[物理资产监视器] 开始扫描 ${folderPaths.length} 个用户源文件夹...`);

    for (const folderPath of folderPaths) {
      try {
        log.info(`[物理资产监视器] 扫描用户源文件夹: ${folderPath}`);

        // 检查文件夹是否存在
        const stats = await fs.stat(folderPath);
        if (!stats.isDirectory()) {
          log.warn(`[物理资产监视器] 路径不是文件夹: ${folderPath}`);
          continue;
        }

        // 递归扫描文件夹
        const foundFolders = await scanDirectoryRecursively(folderPath);
        result.regularMovieFolders.push(...foundFolders);

        log.info(`[物理资产监视器] 在 ${folderPath} 中找到 ${foundFolders.length} 个影片文件夹`);

      } catch (error) {
        log.error(`[物理资产监视器] 扫描用户源文件夹失败: ${folderPath}`, error.message);
      }
    }
  }

  // 扫描待导入目录
  const settings = settingsService.getSettings();
  const importPath = settings.importPath;

  if (importPath) {
    try {
      log.info(`[物理资产监视器] 开始扫描待导入目录: ${importPath}`);

      // 检查待导入目录是否存在
      const stats = await fs.stat(importPath);
      if (stats.isDirectory()) {
        // 扫描待导入目录中的影片文件夹
        const importFolders = await scanImportDirectory(importPath);
        result.importMovieFolders.push(...importFolders);

        log.info(`[物理资产监视器] 在待导入目录中找到 ${importFolders.length} 个影片文件夹`);
      } else {
        log.warn(`[物理资产监视器] 待导入路径不是文件夹: ${importPath}`);
      }
    } catch (error) {
      log.error(`[物理资产监视器] 扫描待导入目录失败: ${importPath}`, error.message);
    }
  } else {
    log.debug('[物理资产监视器] 未配置待导入目录');
  }

  const totalFolders = result.regularMovieFolders.length + result.importMovieFolders.length;
  log.info(`[物理资产监视器] 扫描完成，总共找到 ${totalFolders} 个影片文件夹 (常规: ${result.regularMovieFolders.length}, 待导入: ${result.importMovieFolders.length})`);

  return result;
}

/**
 * 扫描待导入目录，寻找完整的影片文件夹
 * @param {string} importPath - 待导入目录路径
 * @returns {Promise<string[]>} 找到的影片文件夹路径数组
 */
async function scanImportDirectory(importPath) {
  try {
    const entries = await fs.readdir(importPath, { withFileTypes: true });
    const movieFolders = [];

    for (const entry of entries) {
      if (entry.isDirectory()) {
        const folderPath = path.join(importPath, entry.name);

        // 跳过隐藏文件夹和系统文件夹
        if (entry.name.startsWith('.') ||
            entry.name.toLowerCase() === 'system volume information' ||
            entry.name.toLowerCase() === '$recycle.bin') {
          continue;
        }

        // 检查是否是完整的影片文件夹（包含.strm或.nfo文件）
        if (await isCompleteMovieFolder(folderPath)) {
          movieFolders.push(folderPath);
          log.debug(`[物理资产监视器] 发现完整影片文件夹: ${folderPath}`);
        }
      }
    }

    return movieFolders;

  } catch (error) {
    log.error(`[物理资产监视器] 扫描待导入目录失败: ${importPath}`, error.message);
    return [];
  }
}

/**
 * 判断一个目录是否是完整的影片文件夹（包含元数据文件）
 * @param {string} dirPath - 目录路径
 * @returns {Promise<boolean>} 是否是完整的影片文件夹
 */
async function isCompleteMovieFolder(dirPath) {
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    let hasMetadata = false;
    let hasMediaOrStrm = false;

    for (const entry of entries) {
      if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase();

        // 检查是否包含元数据文件
        if (ext === '.nfo' || entry.name.endsWith('.meta.json')) {
          hasMetadata = true;
        }

        // 检查是否包含媒体文件或STRM文件
        if (MEDIA_EXTENSIONS.includes(ext) || ext === '.strm') {
          hasMediaOrStrm = true;
        }
      }
    }

    // 完整的影片文件夹应该包含元数据文件和媒体文件/STRM文件
    return hasMetadata && hasMediaOrStrm;

  } catch (error) {
    log.error(`[物理资产监视器] 检查完整影片文件夹失败: ${dirPath}`, error.message);
    return false;
  }
}

/**
 * 递归扫描目录，寻找影片文件夹
 * @param {string} dirPath - 目录路径
 * @param {number} depth - 当前递归深度（用于防止过深递归）
 * @returns {Promise<string[]>} 找到的影片文件夹路径数组
 */
async function scanDirectoryRecursively(dirPath, depth = 0) {
  const MAX_DEPTH = 10; // 最大递归深度
  
  if (depth > MAX_DEPTH) {
    log.warn(`[物理资产监视器] 达到最大递归深度，跳过: ${dirPath}`);
    return [];
  }

  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    
    // 检查当前目录是否是影片文件夹
    if (await isMovieFolder(dirPath, entries)) {
      log.debug(`[物理资产监视器] 发现影片文件夹: ${dirPath}`);
      return [dirPath]; // 找到影片文件夹，不再深入扫描其子目录
    }

    // 如果不是影片文件夹，继续扫描子目录
    const movieFolders = [];
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const subDirPath = path.join(dirPath, entry.name);
        
        // 跳过隐藏文件夹和系统文件夹
        if (entry.name.startsWith('.') || 
            entry.name.toLowerCase() === 'system volume information' ||
            entry.name.toLowerCase() === '$recycle.bin') {
          continue;
        }

        const subFolders = await scanDirectoryRecursively(subDirPath, depth + 1);
        movieFolders.push(...subFolders);
      }
    }

    return movieFolders;
    
  } catch (error) {
    log.error(`[物理资产监视器] 读取目录失败: ${dirPath}`, error.message);
    return [];
  }
}

/**
 * 判断一个目录是否是影片文件夹
 * @param {string} dirPath - 目录路径
 * @param {fs.Dirent[]} entries - 目录条目列表
 * @returns {Promise<boolean>} 是否是影片文件夹
 */
async function isMovieFolder(dirPath, entries) {
  // 影片文件夹的定义：包含至少一个媒体文件或元数据文件
  
  for (const entry of entries) {
    if (entry.isFile()) {
      const ext = path.extname(entry.name).toLowerCase();
      
      // 检查是否包含媒体文件
      if (MEDIA_EXTENSIONS.includes(ext)) {
        log.debug(`[物理资产监视器] 在 ${dirPath} 中发现媒体文件: ${entry.name}`);
        return true;
      }
      
      // 检查是否包含元数据文件
      if (METADATA_EXTENSIONS.includes(ext)) {
        log.debug(`[物理资产监视器] 在 ${dirPath} 中发现元数据文件: ${entry.name}`);
        return true;
      }
    }
  }

  return false;
}

/**
 * 获取目录中的所有文件，按类型分类
 * @param {string} dirPath - 目录路径
 * @returns {Promise<Object>} 分类后的文件信息
 */
async function categorizeFilesInDirectory(dirPath) {
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    
    const result = {
      mediaFiles: [],
      metadataFiles: [],
      imageFiles: [],
      otherFiles: []
    };

    for (const entry of entries) {
      if (entry.isFile()) {
        const filePath = path.join(dirPath, entry.name);
        const ext = path.extname(entry.name).toLowerCase();
        const stats = await fs.stat(filePath);
        
        const fileInfo = {
          name: entry.name,
          path: filePath,
          extension: ext,
          size: stats.size,
          modified: stats.mtime
        };

        if (MEDIA_EXTENSIONS.includes(ext)) {
          result.mediaFiles.push(fileInfo);
        } else if (METADATA_EXTENSIONS.includes(ext) || entry.name.endsWith('.meta.json')) {
          result.metadataFiles.push(fileInfo);
        } else if (IMAGE_EXTENSIONS.includes(ext)) {
          result.imageFiles.push(fileInfo);
        } else {
          result.otherFiles.push(fileInfo);
        }
      }
    }

    // 按文件大小排序媒体文件（大文件优先）
    result.mediaFiles.sort((a, b) => b.size - a.size);
    
    return result;
    
  } catch (error) {
    log.error(`[物理资产监视器] 分类文件失败: ${dirPath}`, error.message);
    return {
      mediaFiles: [],
      metadataFiles: [],
      imageFiles: [],
      otherFiles: []
    };
  }
}

/**
 * 检查路径是否有效且可访问
 * @param {string} folderPath - 文件夹路径
 * @returns {Promise<boolean>} 路径是否有效
 */
async function isValidPath(folderPath) {
  try {
    const stats = await fs.stat(folderPath);
    return stats.isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * 获取扫描统计信息
 * @param {string[]} movieFolders - 影片文件夹路径数组
 * @returns {Promise<Object>} 统计信息
 */
async function getScanStatistics(movieFolders) {
  const stats = {
    totalFolders: movieFolders.length,
    totalMediaFiles: 0,
    totalSize: 0,
    fileTypes: {},
    errors: 0
  };

  for (const folderPath of movieFolders) {
    try {
      const files = await categorizeFilesInDirectory(folderPath);
      stats.totalMediaFiles += files.mediaFiles.length;
      
      for (const file of files.mediaFiles) {
        stats.totalSize += file.size;
        const ext = file.extension;
        stats.fileTypes[ext] = (stats.fileTypes[ext] || 0) + 1;
      }
    } catch (error) {
      stats.errors++;
      log.error(`[物理资产监视器] 统计文件夹失败: ${folderPath}`, error.message);
    }
  }

  return stats;
}

/**
 * 证据链身份识别 - 按优先级从多个来源识别 nfoId
 * @param {string} movieFolderPath - 影片文件夹路径
 * @returns {Promise<Object>} 识别结果
 */
async function identifyNfoId(movieFolderPath) {
  log.info(`[物理资产监视器] 开始为 ${movieFolderPath} 进行身份识别...`);

  try {
    // 获取文件夹中的所有文件
    const files = await categorizeFilesInDirectory(movieFolderPath);

    const result = {
      nfoId: null,
      source: 'none',
      confidence: 'low',
      evidence: []
    };

    // 优先级 1: 从 .meta.json 文件中获取
    for (const metaFile of files.metadataFiles) {
      if (metaFile.name.endsWith('.meta.json')) {
        const nfoId = await extractNfoIdFromMetaJson(metaFile.path);
        if (nfoId) {
          result.nfoId = nfoId;
          result.source = 'meta_json';
          result.confidence = 'high';
          result.evidence.push(`从 ${metaFile.name} 中提取`);
          log.info(`[物理资产监视器] 从 .meta.json 识别到 nfoId: ${nfoId}`);
          return result;
        }
      }
    }

    // 优先级 2: 从 .nfo 文件中获取
    for (const metaFile of files.metadataFiles) {
      if (metaFile.name.endsWith('.nfo')) {
        const nfoId = await extractNfoIdFromNfoFile(metaFile.path);
        if (nfoId) {
          result.nfoId = nfoId;
          result.source = 'nfo_file';
          result.confidence = 'high';
          result.evidence.push(`从 ${metaFile.name} 中提取`);
          log.info(`[物理资产监视器] 从 .nfo 文件识别到 nfoId: ${nfoId}`);
          return result;
        }
      }
    }

    // 优先级 3: 从媒体文件名中提取（选择最大的文件）
    if (files.mediaFiles.length > 0) {
      const primaryMediaFile = files.mediaFiles[0]; // 已按大小排序，第一个是最大的
      const nfoId = NodeNfoParser.extractJavIdFromFilename(primaryMediaFile.name);
      if (nfoId) {
        result.nfoId = nfoId;
        result.source = 'filename';
        result.confidence = 'medium';
        result.evidence.push(`从主媒体文件 ${primaryMediaFile.name} 中提取`);
        log.info(`[物理资产监视器] 从文件名识别到 nfoId: ${nfoId}`);
        return result;
      }
    }

    // 优先级 4: 从文件夹名称中提取
    const folderName = path.basename(movieFolderPath);
    const nfoIdFromFolder = NodeNfoParser.extractJavIdFromFilename(folderName);
    if (nfoIdFromFolder) {
      result.nfoId = nfoIdFromFolder;
      result.source = 'folder_name';
      result.confidence = 'low';
      result.evidence.push(`从文件夹名称 ${folderName} 中提取`);
      log.info(`[物理资产监视器] 从文件夹名识别到 nfoId: ${nfoIdFromFolder}`);
      return result;
    }

    log.warn(`[物理资产监视器] 无法为 ${movieFolderPath} 识别出 nfoId`);
    return result;

  } catch (error) {
    log.error(`[物理资产监视器] 身份识别失败: ${movieFolderPath}`, error.message);
    return {
      nfoId: null,
      source: 'error',
      confidence: 'low',
      evidence: [`识别过程出错: ${error.message}`]
    };
  }
}

/**
 * 从 .meta.json 文件中提取 nfoId
 * @param {string} metaJsonPath - .meta.json 文件路径
 * @returns {Promise<string|null>} nfoId
 */
async function extractNfoIdFromMetaJson(metaJsonPath) {
  try {
    const content = await fs.readFile(metaJsonPath, 'utf8');
    const metadata = JSON.parse(content);

    // 尝试多个可能的字段
    return metadata.nfoId || metadata.id || metadata.num || null;

  } catch (error) {
    log.error(`[物理资产监视器] 解析 .meta.json 失败: ${metaJsonPath}`, error.message);
    return null;
  }
}

/**
 * 从 .nfo 文件中提取 nfoId
 * @param {string} nfoPath - .nfo 文件路径
 * @returns {Promise<string|null>} nfoId
 */
async function extractNfoIdFromNfoFile(nfoPath) {
  try {
    const content = await fs.readFile(nfoPath, 'utf8');

    // 尝试 XML 解析
    try {
      const parser = new xml2js.Parser({ explicitArray: false });
      const result = await parser.parseStringPromise(content);

      if (result && result.movie) {
        const movieData = result.movie;

        // 按优先级尝试不同字段
        if (movieData.uniqueid && movieData.uniqueid._) {
          return movieData.uniqueid._;
        }

        if (movieData.id) {
          return movieData.id;
        }

        if (movieData.num) {
          return movieData.num;
        }

        // 从 filenameandpath 中提取
        if (movieData.filenameandpath) {
          const javMatch = movieData.filenameandpath.match(/([A-Z]{2,5}-\d{3,5})/i);
          if (javMatch) {
            return javMatch[1].toUpperCase();
          }
        }
      }
    } catch (xmlError) {
      // XML 解析失败，尝试正则表达式
      log.debug(`[物理资产监视器] XML 解析失败，尝试正则表达式: ${nfoPath}`);
    }

    // 使用正则表达式作为后备方案
    const patterns = [
      /<uniqueid[^>]*>([^<]+)<\/uniqueid>/i,
      /<id>([^<]+)<\/id>/i,
      /<num>([^<]+)<\/num>/i,
      /([A-Z]{2,5}-\d{3,5})/i
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim().toUpperCase();
      }
    }

    return null;

  } catch (error) {
    log.error(`[物理资产监视器] 读取 .nfo 文件失败: ${nfoPath}`, error.message);
    return null;
  }
}

/**
 * 将识别出的影片及其所有相关资产同步到中央数据库
 * @param {string[]} movieFolders - 影片文件夹路径数组
 * @returns {Promise<Object>} 同步结果统计
 */
async function syncMovieAssetsToDb(movieFolders) {
  log.info(`[物理资产监视器] 开始同步 ${movieFolders.length} 个影片文件夹到数据库...`);

  const syncStats = {
    total: movieFolders.length,
    success: 0,
    failed: 0,
    skipped: 0,
    updated: 0,
    created: 0,
    errors: []
  };

  for (const folderPath of movieFolders) {
    try {
      log.info(`[物理资产监视器] 处理影片文件夹: ${folderPath}`);

      // 1. 身份识别
      const identityResult = await identifyNfoId(folderPath);

      if (!identityResult.nfoId) {
        log.warn(`[物理资产监视器] 跳过无法识别身份的文件夹: ${folderPath}`);
        syncStats.skipped++;
        continue;
      }

      // 2. 收集文件夹中的所有资产
      const assetData = await collectMovieAssets(folderPath, identityResult);

      // 3. 同步到数据库
      const syncResult = await syncSingleMovieToDb(assetData);

      if (syncResult.success) {
        syncStats.success++;
        if (syncResult.action === 'created') {
          syncStats.created++;
        } else if (syncResult.action === 'updated') {
          syncStats.updated++;
        }
        log.info(`[物理资产监视器] ${identityResult.nfoId} 同步成功 (${syncResult.action})`);
      } else {
        syncStats.failed++;
        syncStats.errors.push({
          folder: folderPath,
          nfoId: identityResult.nfoId,
          error: syncResult.error
        });
        log.error(`[物理资产监视器] ${identityResult.nfoId} 同步失败: ${syncResult.error}`);
      }

    } catch (error) {
      syncStats.failed++;
      syncStats.errors.push({
        folder: folderPath,
        nfoId: null,
        error: error.message
      });
      log.error(`[物理资产监视器] 处理文件夹失败: ${folderPath}`, error.message);
    }
  }

  log.info(`[物理资产监视器] 同步完成: 成功 ${syncStats.success}, 失败 ${syncStats.failed}, 跳过 ${syncStats.skipped}`);
  return syncStats;
}

/**
 * 收集影片文件夹中的所有资产信息
 * @param {string} folderPath - 影片文件夹路径
 * @param {Object} identityResult - 身份识别结果
 * @returns {Promise<Object>} 资产数据
 */
async function collectMovieAssets(folderPath, identityResult) {
  const files = await categorizeFilesInDirectory(folderPath);

  // 选择主媒体文件（最大的文件）
  const primaryMediaFile = files.mediaFiles.length > 0 ? files.mediaFiles[0] : null;

  // 查找本地封面图片
  const localCoverPath = await findLocalCover(folderPath, files.imageFiles);

  // 查找 NFO 文件
  const nfoFile = files.metadataFiles.find(f => f.name.endsWith('.nfo'));

  // 查找 .meta.json 文件
  const metaJsonFile = files.metadataFiles.find(f => f.name.endsWith('.meta.json'));

  const assetData = {
    nfoId: identityResult.nfoId,
    folderPath: folderPath,
    identitySource: identityResult.source,
    identityConfidence: identityResult.confidence,

    // 媒体文件信息
    primaryMediaFile: primaryMediaFile,
    allMediaFiles: files.mediaFiles,
    totalMediaSize: files.mediaFiles.reduce((sum, file) => sum + file.size, 0),

    // 元数据文件
    nfoFile: nfoFile,
    metaJsonFile: metaJsonFile,

    // 图片资产
    localCoverPath: localCoverPath,
    allImageFiles: files.imageFiles,

    // 其他文件
    otherFiles: files.otherFiles,

    // 统计信息
    fileCount: files.mediaFiles.length + files.metadataFiles.length + files.imageFiles.length + files.otherFiles.length,
    lastModified: primaryMediaFile ? primaryMediaFile.modified : new Date()
  };

  return assetData;
}

/**
 * 将单个影片的资产数据同步到数据库
 * @param {Object} assetData - 资产数据
 * @returns {Promise<Object>} 同步结果
 */
async function syncSingleMovieToDb(assetData) {
  try {
    // 检查数据库中是否已存在该影片
    const existingMovie = await databaseService.getMovieByNfoId(assetData.nfoId);

    if (existingMovie) {
      // 更新现有记录
      return await updateExistingMovie(existingMovie, assetData);
    } else {
      // 创建新记录
      return await createNewMovie(assetData);
    }

  } catch (error) {
    return {
      success: false,
      action: 'error',
      error: error.message
    };
  }
}

/**
 * 更新现有的影片记录
 * @param {Object} existingMovie - 现有影片记录
 * @param {Object} assetData - 新的资产数据
 * @returns {Promise<Object>} 更新结果
 */
async function updateExistingMovie(existingMovie, assetData) {
  try {
    const updateData = {
      // 更新文件路径信息
      filePath: assetData.primaryMediaFile ? assetData.primaryMediaFile.path : existingMovie.filePath,
      fileName: assetData.primaryMediaFile ? assetData.primaryMediaFile.name : existingMovie.fileName,
      fileSize: assetData.totalMediaSize || existingMovie.fileSize,

      // 更新本地封面路径
      localCoverPath: assetData.localCoverPath || existingMovie.localCoverPath,

      // 更新资产状态为 AVAILABLE（因为找到了物理文件）
      asset_status: 'AVAILABLE',

      // 更新扫描时间
      lastScanned: new Date().toISOString(),

      // 如果没有标题，使用文件夹名称
      title: existingMovie.title || path.basename(assetData.folderPath)
    };

    await databaseService.updateMovie(existingMovie.db_id, updateData);

    return {
      success: true,
      action: 'updated',
      movieId: existingMovie.db_id
    };

  } catch (error) {
    return {
      success: false,
      action: 'update_error',
      error: error.message
    };
  }
}

/**
 * 创建新的影片记录
 * @param {Object} assetData - 资产数据
 * @returns {Promise<Object>} 创建结果
 */
async function createNewMovie(assetData) {
  try {
    const movieData = {
      nfoId: assetData.nfoId,
      title: path.basename(assetData.folderPath), // 使用文件夹名称作为默认标题
      filePath: assetData.primaryMediaFile ? assetData.primaryMediaFile.path : null,
      fileName: assetData.primaryMediaFile ? assetData.primaryMediaFile.name : null,
      fileSize: assetData.totalMediaSize,
      localCoverPath: assetData.localCoverPath,
      asset_status: 'AVAILABLE', // 物理资产
      lastScanned: new Date().toISOString(),
      watched: false,
      personalRating: null
    };

    const newMovie = await databaseService.addMovie(movieData);

    // NFO自动解析：如果存在.nfo文件但没有.meta.json文件，自动触发解析
    await tryAutoImportNfo(assetData);

    return {
      success: true,
      action: 'created',
      movieId: newMovie.db_id
    };

  } catch (error) {
    return {
      success: false,
      action: 'create_error',
      error: error.message
    };
  }
}

/**
 * 在图片文件中查找本地封面
 * @param {string} folderPath - 文件夹路径
 * @param {Array} imageFiles - 图片文件列表
 * @returns {Promise<string|null>} 封面文件路径
 */
async function findLocalCover(folderPath, imageFiles) {
  // 封面文件的常见名称模式
  const coverPatterns = [
    /^cover\./i,
    /^poster\./i,
    /^fanart\./i,
    /^folder\./i,
    /^thumb\./i
  ];

  // 首先查找明确的封面文件
  for (const pattern of coverPatterns) {
    const coverFile = imageFiles.find(file => pattern.test(file.name));
    if (coverFile) {
      return coverFile.path;
    }
  }

  // 如果没有找到明确的封面文件，返回第一个图片文件
  if (imageFiles.length > 0) {
    return imageFiles[0].path;
  }

  return null;
}

/**
 * 处理待导入文件夹，执行自动化导入和迁移
 * @param {string[]} importMovieFolders - 待导入的影片文件夹路径数组
 * @returns {Promise<Object>} 处理结果统计
 */
async function processImportFolders(importMovieFolders) {
  log.info(`[物理资产监视器] 开始处理 ${importMovieFolders.length} 个待导入文件夹...`);

  const processStats = {
    total: importMovieFolders.length,
    success: 0,
    failed: 0,
    skipped: 0,
    moved: 0,
    updated: 0,
    errors: []
  };

  for (const folderPath of importMovieFolders) {
    try {
      log.info(`[物理资产监视器] 处理待导入文件夹: ${folderPath}`);

      // 1. 身份识别
      const identityResult = await identifyNfoId(folderPath);

      if (!identityResult.nfoId) {
        log.warn(`[物理资产监视器] 跳过无法识别身份的待导入文件夹: ${folderPath}`);
        processStats.skipped++;
        continue;
      }

      // 2. 计算目标路径
      const targetPaths = await calculateImportTargetPaths(identityResult.nfoId, folderPath);

      // 3. 执行物理迁移
      const migrationResult = await migrateImportFolder(folderPath, targetPaths);

      // 4. 更新数据库索引
      const dbResult = await updateDatabaseForImport(identityResult.nfoId, targetPaths, migrationResult);

      if (migrationResult.success && dbResult.updated) {
        processStats.success++;
        processStats.moved++;
        if (dbResult.action === 'updated') {
          processStats.updated++;
        }
        log.info(`[物理资产监视器] ${identityResult.nfoId} 导入成功`);
      } else {
        processStats.failed++;
        const error = migrationResult.error || dbResult.error || '未知错误';
        processStats.errors.push({
          folder: folderPath,
          nfoId: identityResult.nfoId,
          error: error
        });
        log.error(`[物理资产监视器] ${identityResult.nfoId} 导入失败: ${error}`);
      }

    } catch (error) {
      processStats.failed++;
      processStats.errors.push({
        folder: folderPath,
        nfoId: null,
        error: error.message
      });
      log.error(`[物理资产监视器] 处理待导入文件夹失败: ${folderPath}`, error.message);
    }
  }

  log.info(`[物理资产监视器] 导入处理完成: 成功 ${processStats.success}, 失败 ${processStats.failed}, 跳过 ${processStats.skipped}`);
  return processStats;
}

/**
 * 计算导入文件夹的目标路径
 * @param {string} nfoId - 番号
 * @param {string} sourceFolderPath - 源文件夹路径
 * @returns {Promise<Object>} 目标路径信息
 */
async function calculateImportTargetPaths(nfoId, sourceFolderPath) {
  try {
    // 使用 pathResolverService 计算标准路径
    const movieData = {
      nfoId: nfoId,
      title: nfoId // 临时使用番号作为标题
    };

    const standardPaths = await pathResolverService.resolveAssetPaths(movieData);

    return {
      sourceFolder: sourceFolderPath,
      targetFolder: standardPaths.movieRootPath,
      nfoId: nfoId,
      standardPaths: standardPaths
    };

  } catch (error) {
    log.error(`[物理资产监视器] 计算导入目标路径失败: ${nfoId}`, error.message);
    throw new Error(`计算目标路径失败: ${error.message}`);
  }
}

/**
 * 执行导入文件夹的物理迁移
 * @param {string} sourceFolderPath - 源文件夹路径
 * @param {Object} targetPaths - 目标路径信息
 * @returns {Promise<Object>} 迁移结果
 */
async function migrateImportFolder(sourceFolderPath, targetPaths) {
  try {
    const { targetFolder } = targetPaths;

    // 确保目标目录的父目录存在
    await fs.mkdir(path.dirname(targetFolder), { recursive: true });

    // 检查目标文件夹是否已存在
    try {
      await fs.access(targetFolder);
      // 目标文件夹已存在，需要处理版本优先逻辑
      const mergeResult = await mergeWithExistingFolder(sourceFolderPath, targetFolder);
      return mergeResult;
    } catch (error) {
      if (error.code !== 'ENOENT') {
        throw error;
      }
      // 目标文件夹不存在，直接移动
    }

    // 执行文件夹移动
    await fs.rename(sourceFolderPath, targetFolder);

    log.info(`[物理资产监视器] 文件夹迁移成功: ${sourceFolderPath} -> ${targetFolder}`);

    return {
      success: true,
      action: 'moved',
      sourcePath: sourceFolderPath,
      targetPath: targetFolder
    };

  } catch (error) {
    log.error(`[物理资产监视器] 文件夹迁移失败: ${sourceFolderPath}`, error.message);
    return {
      success: false,
      error: error.message,
      sourcePath: sourceFolderPath
    };
  }
}

/**
 * 与现有文件夹合并（版本优先逻辑）
 * @param {string} sourceFolderPath - 源文件夹路径
 * @param {string} targetFolder - 目标文件夹路径
 * @returns {Promise<Object>} 合并结果
 */
async function mergeWithExistingFolder(sourceFolderPath, targetFolder) {
  try {
    log.info(`[物理资产监视器] 目标文件夹已存在，执行版本优先合并: ${targetFolder}`);

    // 获取源文件夹和目标文件夹的文件列表
    const sourceFiles = await categorizeFilesInDirectory(sourceFolderPath);
    const targetFiles = await categorizeFilesInDirectory(targetFolder);

    let filesUpdated = 0;
    let filesMerged = 0;

    // 处理所有源文件
    const allSourceFiles = [
      ...sourceFiles.mediaFiles,
      ...sourceFiles.metadataFiles,
      ...sourceFiles.imageFiles,
      ...sourceFiles.otherFiles
    ];

    for (const sourceFile of allSourceFiles) {
      const fileName = path.basename(sourceFile.path);
      const targetFilePath = path.join(targetFolder, fileName);

      try {
        // 检查目标文件是否存在
        const targetExists = await fs.access(targetFilePath).then(() => true).catch(() => false);

        if (targetExists) {
          // 应用版本优先逻辑
          const shouldReplace = await shouldReplaceFile(sourceFile.path, targetFilePath);

          if (shouldReplace) {
            await fs.copyFile(sourceFile.path, targetFilePath);
            filesUpdated++;
            log.debug(`[物理资产监视器] 文件已更新: ${fileName}`);
          } else {
            log.debug(`[物理资产监视器] 保留现有文件: ${fileName}`);
          }
        } else {
          // 目标文件不存在，直接复制
          await fs.copyFile(sourceFile.path, targetFilePath);
          filesMerged++;
          log.debug(`[物理资产监视器] 文件已合并: ${fileName}`);
        }
      } catch (error) {
        log.error(`[物理资产监视器] 处理文件失败: ${fileName}`, error.message);
      }
    }

    // 删除源文件夹
    await fs.rmdir(sourceFolderPath, { recursive: true });

    log.info(`[物理资产监视器] 文件夹合并完成: 更新 ${filesUpdated} 个文件, 新增 ${filesMerged} 个文件`);

    return {
      success: true,
      action: 'merged',
      sourcePath: sourceFolderPath,
      targetPath: targetFolder,
      filesUpdated: filesUpdated,
      filesMerged: filesMerged
    };

  } catch (error) {
    log.error(`[物理资产监视器] 文件夹合并失败: ${sourceFolderPath}`, error.message);
    return {
      success: false,
      error: error.message,
      sourcePath: sourceFolderPath
    };
  }
}

/**
 * 判断是否应该替换现有文件（版本优先逻辑）
 * @param {string} sourceFilePath - 源文件路径
 * @param {string} targetFilePath - 目标文件路径
 * @returns {Promise<boolean>} 是否应该替换
 */
async function shouldReplaceFile(sourceFilePath, targetFilePath) {
  try {
    const sourceStats = await fs.stat(sourceFilePath);
    const targetStats = await fs.stat(targetFilePath);

    // 版本优先逻辑：
    // 1. 如果源文件更新，则替换
    if (sourceStats.mtime > targetStats.mtime) {
      return true;
    }

    // 2. 如果源文件更大，则替换（假设更大的文件质量更好）
    if (sourceStats.size > targetStats.size) {
      return true;
    }

    // 3. 默认保留现有文件
    return false;

  } catch (error) {
    log.error(`[物理资产监视器] 比较文件版本失败: ${sourceFilePath}`, error.message);
    // 出错时保守处理，不替换
    return false;
  }
}

/**
 * 为导入的文件夹更新数据库索引
 * @param {string} nfoId - 番号
 * @param {Object} targetPaths - 目标路径信息
 * @param {Object} migrationResult - 迁移结果
 * @returns {Promise<Object>} 更新结果
 */
async function updateDatabaseForImport(nfoId, targetPaths, migrationResult) {
  try {
    if (!migrationResult.success) {
      return { updated: false, error: '迁移失败，跳过数据库更新' };
    }

    // 检查数据库中是否已存在该记录
    const existingMovie = await databaseService.getMovieByNfoId(nfoId);

    // 查找主要的媒体文件或STRM文件
    const targetFolder = migrationResult.targetPath || targetPaths.targetFolder;
    const files = await categorizeFilesInDirectory(targetFolder);

    // 优先选择STRM文件，其次选择最大的媒体文件
    let primaryFile = null;
    if (files.otherFiles.some(f => f.extension === '.strm')) {
      primaryFile = files.otherFiles.find(f => f.extension === '.strm');
    } else if (files.mediaFiles.length > 0) {
      primaryFile = files.mediaFiles[0]; // 已按大小排序
    }

    if (existingMovie) {
      // 更新现有记录
      const updateData = {
        filePath: primaryFile ? primaryFile.path : existingMovie.filePath,
        fileName: primaryFile ? primaryFile.name : existingMovie.fileName,
        asset_status: 'AVAILABLE',
        lastScanned: new Date().toISOString()
      };

      const updated = await databaseService.updateMovie(existingMovie.db_id, updateData);

      log.info(`[物理资产监视器] 数据库记录已更新: ${nfoId}`);
      return { updated: true, action: 'updated', movieId: existingMovie.db_id };

    } else {
      // 创建新记录
      const movieData = {
        nfoId: nfoId,
        title: nfoId, // 使用番号作为默认标题
        filePath: primaryFile ? primaryFile.path : null,
        fileName: primaryFile ? primaryFile.name : null,
        asset_status: 'AVAILABLE',
        lastScanned: new Date().toISOString(),
        watched: false,
        personalRating: null
      };

      const newMovie = await databaseService.addMovie(movieData);

      log.info(`[物理资产监视器] 数据库记录已创建: ${nfoId}`);
      return { updated: true, action: 'created', movieId: newMovie.db_id };
    }

  } catch (error) {
    log.error(`[物理资产监视器] 更新导入数据库失败: ${nfoId}`, error.message);
    return { updated: false, error: error.message };
  }
}

/**
 * 尝试自动导入NFO文件（冷启动）
 * @param {Object} assetData - 资产数据
 */
async function tryAutoImportNfo(assetData) {
  try {
    // 检查是否存在.nfo文件但没有.meta.json文件
    const hasNfoFile = assetData.nfoFile;
    const hasMetaJsonFile = assetData.metaJsonFile;

    if (hasNfoFile && !hasMetaJsonFile) {
      log.info(`[物理资产监视器] 发现NFO文件但缺少.meta.json，开始自动解析: ${assetData.nfoId}`);

      // 调用NFO导入服务
      const { nfoService } = require('./nfoService');
      const importResult = await nfoService.importNfo(assetData.nfoFile.path);

      if (importResult && importResult.success) {
        log.info(`[物理资产监视器] NFO自动解析成功: ${assetData.nfoId} -> ${importResult.metaJsonPath}`);
      } else {
        log.warn(`[物理资产监视器] NFO自动解析失败: ${assetData.nfoId}, 错误: ${importResult ? importResult.error : '未知错误'}`);
      }
    }
  } catch (error) {
    log.error(`[物理资产监视器] NFO自动解析过程发生异常: ${assetData.nfoId}, 错误: ${error.message}`);
  }
}

module.exports = {
  scanSourceFolders,
  scanImportDirectory,
  isCompleteMovieFolder,
  processImportFolders,
  calculateImportTargetPaths,
  migrateImportFolder,
  mergeWithExistingFolder,
  shouldReplaceFile,
  updateDatabaseForImport,
  scanDirectoryRecursively,
  isMovieFolder,
  categorizeFilesInDirectory,
  isValidPath,
  getScanStatistics,
  identifyNfoId,
  extractNfoIdFromMetaJson,
  extractNfoIdFromNfoFile,
  syncMovieAssetsToDb,
  collectMovieAssets,
  syncSingleMovieToDb,
  findLocalCover,
  MEDIA_EXTENSIONS,
  METADATA_EXTENSIONS,
  IMAGE_EXTENSIONS
};
