const fs = require('fs');

function checkBrackets(filename) {
  const content = fs.readFileSync(filename, 'utf8');
  const lines = content.split('\n');
  
  let stack = [];
  let brackets = {
    '(': ')',
    '[': ']',
    '{': '}'
  };
  
  for (let lineNum = 0; lineNum < lines.length; lineNum++) {
    const line = lines[lineNum];
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      // 跳过字符串内容
      if (char === '"' || char === "'" || char === '`') {
        let quote = char;
        i++;
        while (i < line.length && line[i] !== quote) {
          if (line[i] === '\\') i++; // 跳过转义字符
          i++;
        }
        continue;
      }

      // 跳过正则表达式
      if (char === '/' && i > 0 && /[=\(,\[!&|?:;]/.test(line[i-1])) {
        i++;
        while (i < line.length && line[i] !== '/') {
          if (line[i] === '\\') i++; // 跳过转义字符
          i++;
        }
        continue;
      }
      
      // 跳过注释
      if (char === '/' && i + 1 < line.length && line[i + 1] === '/') {
        break; // 跳过行注释
      }
      
      if (brackets[char]) {
        stack.push({ char, line: lineNum + 1, col: i + 1 });
      } else if (Object.values(brackets).includes(char)) {
        if (stack.length === 0) {
          console.log(`❌ 多余的闭合符号 '${char}' 在第 ${lineNum + 1} 行第 ${i + 1} 列`);
          return false;
        }
        
        const last = stack.pop();
        if (brackets[last.char] !== char) {
          console.log(`❌ 括号不匹配: '${last.char}' (第 ${last.line} 行) 与 '${char}' (第 ${lineNum + 1} 行)`);
          return false;
        }
      }
    }
  }
  
  if (stack.length > 0) {
    console.log(`❌ 未关闭的括号:`);
    stack.forEach(item => {
      console.log(`   '${item.char}' 在第 ${item.line} 行第 ${item.col} 列`);
    });
    return false;
  }
  
  console.log('✅ 括号匹配检查通过');
  return true;
}

// 检查main.js
checkBrackets('main.js');
