// 调试页面内容的脚本
// 用于检查页面选择器和内容

const { chromium } = require('playwright');

async function debugPageContent() {
  console.log('开始调试页面内容...');
  
  try {
    // 连接到用户的Chrome实例
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    const context = browser.contexts()[0];
    const pages = context.pages();
    
    console.log(`找到 ${pages.length} 个打开的页面`);
    
    // 找到当前活跃的页面
    let targetPage = null;
    for (const page of pages) {
      const url = page.url();
      console.log(`页面: ${url}`);
      
      // 查找论坛相关页面
      if (url.includes('ccgga') || url.includes('forum') || url.includes('viewthread') || url.includes('forumdisplay')) {
        targetPage = page;
        console.log(`✅ 找到目标页面: ${url}`);
        break;
      }
    }
    
    if (!targetPage) {
      console.log('❌ 未找到论坛页面，使用第一个页面进行调试');
      targetPage = pages[0];
    }
    
    await targetPage.bringToFront();
    
    // 获取页面基本信息
    const url = targetPage.url();
    const title = await targetPage.title();
    console.log(`\n页面信息:`);
    console.log(`URL: ${url}`);
    console.log(`标题: ${title}`);
    
    // 测试不同的帖子链接选择器
    const selectors = [
      "tbody[id^='normalthread_'] a[href*='viewthread'][href*='tid=']:not([href*='redirect']):not([href*='lastpost']):not([href*='extra='])",
      "a[href*='viewthread'][href*='tid=']",
      "a[href*='tid=']",
      ".s.xst",
      "tbody a[href*='viewthread']",
      "[id^='normalthread_'] a",
      "a[href*='thread']"
    ];
    
    console.log(`\n测试帖子链接选择器:`);
    for (const selector of selectors) {
      try {
        const elements = await targetPage.locator(selector).all();
        console.log(`${selector}: 找到 ${elements.length} 个元素`);
        
        if (elements.length > 0 && elements.length <= 5) {
          // 显示前几个链接的详细信息
          for (let i = 0; i < Math.min(elements.length, 3); i++) {
            const href = await elements[i].getAttribute('href');
            const text = await elements[i].textContent();
            console.log(`  - ${text?.trim().substring(0, 50)}... -> ${href}`);
          }
        }
      } catch (error) {
        console.log(`${selector}: 错误 - ${error.message}`);
      }
    }
    
    // 检查页面是否需要登录
    console.log(`\n检查登录状态:`);
    const loginIndicators = [
      "a[href*='action=logout']",
      "a[href*='logout']",
      ".logout",
      "[class*='user']",
      "[class*='member']"
    ];
    
    for (const selector of loginIndicators) {
      try {
        const element = await targetPage.$(selector);
        if (element) {
          const text = await element.textContent();
          console.log(`✅ 找到登录指示器 ${selector}: ${text?.trim()}`);
        }
      } catch (error) {
        // 忽略错误
      }
    }
    
    // 获取页面HTML片段用于分析
    console.log(`\n获取页面HTML片段:`);
    const bodyHTML = await targetPage.evaluate(() => {
      const body = document.body;
      return body ? body.innerHTML.substring(0, 1000) : '无法获取页面内容';
    });
    console.log(`页面内容预览: ${bodyHTML.substring(0, 200)}...`);
    
    console.log('\n调试完成！');
    
  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

// 运行调试
debugPageContent().then(() => {
  console.log('调试脚本执行完成');
}).catch(error => {
  console.error('调试脚本执行失败:', error);
});
