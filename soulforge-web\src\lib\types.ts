// Core Types for SoulForge Web Application

export interface Movie {
  id: string;
  filePath: string;
  fileName: string;
  title: string;
  originalTitle?: string;
  nfoId?: string;
  year?: number;
  releaseDate?: string;
  runtime?: number;
  plot?: string;
  plotJa?: string;
  plotZh?: string;
  studio?: string;
  series?: string;
  director?: string;
  trailerUrl?: string;
  posterUrl?: string;
  coverUrl?: string;
  localCoverPath?: string;
  watched: boolean;
  personalRating?: number;
  actors?: string[];
  genres?: string[];
  tags?: string[];
  asset_status?: 'VIRTUAL' | 'AVAILABLE' | 'MISSING'; // 资产状态
  lastScanned: Date;
  nfoLastModified?: number;
  resolution?: string;
  fileSize?: number;
  videoCodec?: string;
  audioCodec?: string;
  preferredStatus?: 'preferred' | 'normal';
  customFileTags?: string[];
  versionCategories?: string[];
  autoDetectedFileNameTags?: string[];
  fps?: number;
  videoCodecFull?: string;
  videoBitrate?: string;
  audioCodecFull?: string;
  audioChannelsDesc?: string;
  audioSampleRate?: number;
  audioBitrate?: string;
  videoHeight?: string;
  aiAnalyzedTags?: string[];
  aiRecommendationType?: string;
  aiRecommendationScore?: number;
  aiRecommendationJustification?: string;
  hasExternalSubtitles?: boolean;
  cdPartInfo?: string;
  createdAt: Date;
  updatedAt: Date;

  // Version information (computed fields)
  versionCount?: number;
  multiCdCountForNfoId?: number;
}

export interface MovieLibrary {
  id: string;
  name: string;
  paths: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ActorMetadata {
  actorName: string;
  localAvatarPath?: string;
  avatarUrlSource?: string;
  filetreeSourcePath?: string;
  lastUpdated: Date;
}

export interface Favorite {
  id: string;
  itemType: 'movie' | 'actor' | 'genre' | 'studio';
  itemValue: string;
  favoritedAt: Date;
}

export interface AppSettings {
  // AI Settings
  aiProvider: 'gemini' | 'custom-gpt' | 'grok';
  customGptEndpoint?: string;
  customGptApiKey?: string;
  customGptModel?: string;
  grokApiKey?: string;
  
  // Media Settings
  snapshotQuality: 'sd_320p' | 'hd_640p' | 'fhd_1280p_720h';
  snapshotCachePath?: string;
  
  // Privacy Settings
  privacyModeEnabled: boolean;
  privacyModePassword?: string;
  
  // Avatar Settings
  avatarDataSourceType: 'local' | 'remote';
  localFileTreePath?: string;
  remoteGfriendsFiletreeUrl?: string;
  actorAvatarLibraryPath?: string;
  
  // File Settings
  filenameRenameTemplate?: string;
}

export interface ScanProgress {
  isScanning: boolean;
  currentFile?: string;
  progress: number;
  total: number;
  libraryId?: string;
}

export interface SnapshotInfo {
  id: string;
  movieId: string;
  filePath: string;
  timestamp: number;
  quality: string;
  createdAt: Date;
}

export type ViewMode = 'grid' | 'list' | 'detailed' | 'waterfall';

export type SortField = 
  | 'title' 
  | 'year' 
  | 'releaseDate' 
  | 'lastScanned' 
  | 'fileSize' 
  | 'personalRating'
  | 'runtime';

export type SortOrder = 'asc' | 'desc';

export interface FilterOptions {
  search?: string;
  genres?: string[];
  actors?: string[];
  studios?: string[];
  years?: number[];
  watched?: boolean;
  favorited?: boolean;
  libraryId?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: PaginationInfo;
}

// AI Related Types
export interface AIAnalysisResult {
  tags?: string[];
  recommendationType?: string;
  recommendationScore?: number;
  justification?: string;
}

export interface AITranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
}

export interface AIChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}
