# 🚫 DEPRECATED - Python Scripts

## ⚠️ 重要通知

**这个目录中的所有Python脚本已被废弃，不再使用！**

软件已经完全重构，移除了所有Python依赖，改为使用JavaScript/Node.js实现所有功能。

## 📋 废弃的文件列表

### 快照生成相关
- `generate_thumbnails.py` - 已被 `main_process/services/snapshotGeneratorService.js` 替代
- `video_utils.py` - 功能已集成到JavaScript快照生成服务中

### NFO处理相关
- `nfo_utils.py` - NFO处理功能需要重构为JavaScript实现

### 其他工具
- 所有其他Python脚本 - 功能需要重构为JavaScript实现

## 🔄 迁移状态

- ✅ **快照生成功能** - 已完成JavaScript重构
- ❌ **NFO处理功能** - 待重构
- ❌ **其他Python功能** - 待重构或移除

## 🗑️ 清理计划

这些文件将在确认所有功能都已成功迁移到JavaScript后被删除。

## 📝 开发者注意事项

1. **不要修改这些Python文件** - 它们不再被使用
2. **新功能请使用JavaScript实现** - 在 `main_process/services/` 目录下
3. **如需参考旧逻辑** - 可以查看这些文件，但请用JavaScript重新实现

---

**最后更新**: 2025-07-31  
**状态**: 已废弃，等待删除
