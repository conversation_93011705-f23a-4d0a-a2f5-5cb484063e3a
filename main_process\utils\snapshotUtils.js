// soul-forge-electron/main_process/utils/snapshotUtils.js
const fs = require('node:fs');
const path = require('node:path');

let log;

function initializeSnapshotUtils(logger) {
  log = logger;
  log.info('[快照工具] 初始化。');
}

function getExistingSnapshotFilePaths(movieDbId, snapshotCacheBasePath) {
  if (movieDbId === undefined || movieDbId === null || !snapshotCacheBasePath) {
    log.warn(`[快照工具] 获取快照路径时，movieDbId 或 snapshotCacheBasePath 无效。 ID: ${movieDbId}, 路径: ${snapshotCacheBasePath}`);
    return [];
  }
  const movieSnapshotDir = path.join(snapshotCacheBasePath, String(movieDbId));
  if (!fs.existsSync(movieSnapshotDir)) {
    // log.info(`[快照工具] 快照目录不存在: ${movieSnapshotDir}`);
    return [];
  }
  try {
    const files = fs.readdirSync(movieSnapshotDir);
    return files
      .filter(file => file.startsWith('snapshot_') && (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.webp')))
      .map(file => path.join(movieSnapshotDir, file))
      .sort((a, b) => { // Sort numerically by snapshot index
        const numA = parseInt(a.match(/snapshot_(\d+)\.\w+$/)?.[1] || '0');
        const numB = parseInt(b.match(/snapshot_(\d+)\.\w+$/)?.[1] || '0');
        return numA - numB;
      });
  } catch (error) {
    log.error(`[快照工具] 读取快照目录 ${movieSnapshotDir} 失败:`, error);
    return [];
  }
}

function getExistingSnapshotsInfo(movieDbId, snapshotCacheBasePath, imagePathToDataUrlFunc) {
    if (!imagePathToDataUrlFunc || typeof imagePathToDataUrlFunc !== 'function') {
        log.error('[快照工具] getExistingSnapshotsInfo: imagePathToDataUrlFunc 未提供或无效。');
        return []; 
    }
    const filePaths = getExistingSnapshotFilePaths(movieDbId, snapshotCacheBasePath);
    if (!filePaths || filePaths.length === 0) {
        return [];
    }
    return filePaths.map(filePath => ({
        filePath: filePath,
        dataUrl: imagePathToDataUrlFunc(filePath)
    }));
}

module.exports = {
  initializeSnapshotUtils,
  getExistingSnapshotsInfo, // Renamed and modified
  getExistingSnapshotFilePaths, // Export if needed directly, though typically used by getExistingSnapshotsInfo
};