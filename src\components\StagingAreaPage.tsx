import React, { useState, useEffect } from 'react';
import { RefreshCw, Download, FileVideo, AlertCircle, Settings, Folder } from 'lucide-react';
import { ProcessStagedFileModal } from './ProcessStagedFileModal';

interface StagedFile {
  filePath: string;
  fileName: string;
  fileSize: number;
  lastModified: Date;
  nfoId: string | null;
  extension: string;
}

interface StagingAreaPageProps {
  className?: string;
}

export const StagingAreaPage: React.FC<StagingAreaPageProps> = ({ className = '' }) => {
  const [files, setFiles] = useState<StagedFile[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<StagedFile | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  // 页面加载时自动扫描
  useEffect(() => {
    handleScan();
  }, []);

  const handleScan = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await window.sfeElectronAPI.stagingScan();
      
      if (result.success) {
        setFiles(result.files || []);
      } else {
        setError(result.error || '扫描失败');
      }
    } catch (error) {
      console.error('扫描下载中转站失败:', error);
      setError('扫描失败，请检查网络连接');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProcessFile = (file: StagedFile) => {
    setSelectedFile(file);
    setIsModalOpen(true);
  };

  const handleProcessComplete = () => {
    setIsModalOpen(false);
    setSelectedFile(null);
    // 重新扫描以更新列表
    handleScan();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleString('zh-CN');
  };

  return (
    <div className={`min-h-screen bg-gray-900 text-white p-6 ${className}`}>
      {/* 页面标题 */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <Download className="h-8 w-8 text-[#B8860B]" />
          <h1 className="text-3xl font-bold">下载中转站</h1>
        </div>
        <p className="text-gray-400">
          自动监视下载完成的媒体文件，提供一键处理和入库功能
        </p>
      </div>

      {/* 操作栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={handleScan}
            disabled={isLoading}
            className="flex items-center gap-2 px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? '扫描中...' : '刷新'}
          </button>
          
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Folder className="h-4 w-4" />
            <span>找到 {files.length} 个文件</span>
          </div>
        </div>

        <button
          onClick={() => {/* TODO: 打开设置 */}}
          className="flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white border border-gray-600 rounded hover:border-gray-500"
        >
          <Settings className="h-4 w-4" />
          设置
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg flex items-center gap-3">
          <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
          <div>
            <p className="text-red-300 font-medium">扫描失败</p>
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* 文件列表 */}
      {files.length === 0 && !isLoading && !error ? (
        <div className="text-center py-12">
          <FileVideo className="h-16 w-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-400 mb-2">暂无待处理文件</h3>
          <p className="text-gray-500">
            下载完成的媒体文件将自动出现在这里
          </p>
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    文件名
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    识别番号
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    文件大小
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    修改时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {files.map((file, index) => (
                  <tr key={index} className="hover:bg-gray-700/50">
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        <FileVideo className="h-5 w-5 text-blue-400 flex-shrink-0" />
                        <div>
                          <p className="text-white font-medium truncate max-w-xs" title={file.fileName}>
                            {file.fileName}
                          </p>
                          <p className="text-gray-400 text-sm">
                            {file.extension.toUpperCase()}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {file.nfoId ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/50 text-green-300 border border-green-700">
                          {file.nfoId}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/50 text-yellow-300 border border-yellow-700">
                          未识别
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 text-gray-300">
                      {formatFileSize(file.fileSize)}
                    </td>
                    <td className="px-6 py-4 text-gray-300">
                      {formatDate(file.lastModified)}
                    </td>
                    <td className="px-6 py-4">
                      <button
                        onClick={() => handleProcessFile(file)}
                        className="px-3 py-1.5 bg-[#B8860B] text-black text-sm font-medium rounded hover:bg-[#DAA520] transition-colors"
                      >
                        处理
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 处理文件模态框 */}
      {selectedFile && (
        <ProcessStagedFileModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          file={selectedFile}
          onProcessComplete={handleProcessComplete}
        />
      )}
    </div>
  );
};
