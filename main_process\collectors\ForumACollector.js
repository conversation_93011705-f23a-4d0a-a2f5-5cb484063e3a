/**
 * 论坛A (x1080x) 采集器
 * 
 * 继承自BaseCollector，实现x1080x论坛的具体采集逻辑
 * 包含已验证的稳定功能和修复
 */

const BaseCollector = require('./BaseCollector');
const NodeNfoParser = require('../services/nodeNfoParser');
const path = require('path');
const fs = require('fs');

class ForumACollector extends BaseCollector {
  constructor(config) {
    super(config);
    this.forumName = 'x1080x';

    // 论坛A特有的配置
    this.currentBoardId = null;
    this.currentBoardConfig = null;
    this.currentPageUrl = null;
  }

  /**
   * 执行搜集任务 - 重写BaseCollector的executeTask方法
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 搜集结果
   */
  async executeTask(targetUrl, options = {}) {
    this.log.info(`[${this.forumName}] 开始执行搜集任务: ${targetUrl}`);

    this.isRunning = true;
    this.shouldStop = false;
    this.stats.startTime = new Date();

    try {
      if (this.updateTaskStatus) {
        this.updateTaskStatus('connecting', '正在连接到论坛...');
      }

      // 执行真正的搜集逻辑
      const results = await this.performActualScraping(targetUrl, options);

      this.stats.endTime = new Date();

      if (this.updateTaskStatus) {
        this.updateTaskStatus('completed', `搜集完成，共处理 ${results.length} 个项目`);
      }

      const result = {
        success: true,
        message: `搜集任务完成，共处理 ${results.length} 个项目`,
        result: {
          collectedCount: results.length,
          links: results,
          data: results
        }
      };

      this.log.info(`[${this.forumName}] 搜集任务完成: ${result.message}`);
      return result;

    } catch (error) {
      this.stats.endTime = new Date();
      this.log.error(`[${this.forumName}] 搜集任务失败: ${error.message}`);

      if (this.updateTaskStatus) {
        this.updateTaskStatus('failed', `搜集失败: ${error.message}`);
      }

      return {
        success: false,
        message: `搜集任务失败: ${error.message}`,
        error: error.message
      };
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 执行真正的搜集过程 - 包含浏览器管理
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 搜集结果
   */
  async performActualScraping(targetUrl, options) {
    const { chromium } = require('playwright');
    let browser;
    let context;
    let page;

    try {
      // 尝试连接到用户手动打开的Chrome实例
      this.log.info(`[${this.forumName}] 尝试连接到正在运行的Chrome实例 (端口: 9222)...`);

      try {
        // 连接到用户手动打开的Chrome
        browser = await chromium.connectOverCDP('http://localhost:9222');
        context = browser.contexts()[0]; // 获取默认的浏览器上下文

        this.log.info(`[${this.forumName}] 已连接到Chrome实例`);

        // 找到用户已经打开的目标页面
        const pages = context.pages();
        page = pages.find(p => p.url().includes(new URL(targetUrl).hostname));

        if (!page) {
          // 如果没有找到目标页面，创建新页面
          page = await context.newPage();
          this.log.info(`[${this.forumName}] 创建新页面进行搜集`);
        } else {
          this.log.info(`[${this.forumName}] 找到现有页面，接管进行搜集`);
        }

      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法连接到Chrome实例: ${error.message}`);
        throw new Error('请先启动Chrome浏览器并使用 --remote-debugging-port=9222 参数');
      }

      // 调用原有的executeScrapingLogic方法
      const results = await this.executeScrapingLogic(page, this.siteProfile, targetUrl, options);

      this.log.info(`[${this.forumName}] 搜集完成，共处理 ${results.length} 个帖子`);
      return results;

    } finally {
      // 注意：不要关闭browser，因为它是用户的Chrome实例
      if (page && !page.isClosed()) {
        try {
          // 可以选择关闭我们创建的页面，但保留用户原有的页面
          // await page.close();
        } catch (error) {
          this.log.warn(`[${this.forumName}] 关闭页面时出错: ${error.message}`);
        }
      }
    }
  }

  /**
   * 执行抓取逻辑 - 从已连接的页面开始抓取（支持多页面和日期限制）
   */
  async executeScrapingLogic(page, siteProfile, targetUrl, options) {
    this.log.info(`[${this.forumName}] 开始执行多页面抓取逻辑`);
    const results = [];

    try {
      // 🔧 帖子排序修正：自动添加排序参数
      let finalUrl = targetUrl;
      if (!finalUrl.includes('orderby=dateline')) {
        const separator = finalUrl.includes('?') ? '&' : '?';
        finalUrl += `${separator}filter=author&orderby=dateline`;
        this.log.info(`[${this.forumName}] 🔧 自动添加排序参数: ${finalUrl}`);
      }

      // 智能检测起始页面
      const detectedStartPage = this.detectStartPageFromUrl(finalUrl);
      const startPage = options.startPage || detectedStartPage;

      this.updateTaskStatus('refreshing', `准备从第 ${startPage} 页开始抓取...`);

      // 🔧 设置当前页面URL，用于板块名称提取
      this.currentPageUrl = finalUrl;
      this.log.info(`[${this.forumName}] 🔧 设置当前页面URL: ${this.currentPageUrl}`);

      // 🔧 智能标记与分类：从targetUrl中提取boardId
      this.extractBoardInfo(finalUrl);

      // 如果检测到的起始页面不是1，保持当前URL；否则导航到第一页
      if (startPage === 1 && detectedStartPage > 1) {
        // 用户想从第一页开始，但当前在其他页面
        let firstPageUrl = finalUrl.replace(/[&?]page=\d+/g, '').replace(/[&?]extra=[^&]*/g, '');
        // 确保第一页也有排序参数
        if (!firstPageUrl.includes('orderby=dateline')) {
          const separator = firstPageUrl.includes('?') ? '&' : '?';
          firstPageUrl += `${separator}filter=author&orderby=dateline`;
        }
        this.log.info(`[${this.forumName}] 导航到第一页: ${firstPageUrl}`);
        await page.goto(firstPageUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        await page.waitForTimeout(2000);
        // 🔧 更新当前页面URL
        this.currentPageUrl = firstPageUrl;
        this.log.info(`[${this.forumName}] 🔧 更新当前页面URL为第一页: ${this.currentPageUrl}`);
      } else {
        // 从当前页面开始抓取
        this.log.info(`[${this.forumName}] 从第 ${startPage} 页开始抓取: ${finalUrl}`);

        // 确保页面在正确的URL上
        const currentUrl = page.url();
        if (currentUrl !== finalUrl) {
          this.log.info(`[${this.forumName}] 当前页面URL不匹配，导航到目标URL: ${finalUrl}`);
          await page.goto(finalUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        }

        await page.waitForTimeout(1000); // 确保页面稳定
      }

      const maxPages = options.maxPages || 10; // 从选项获取要抓取的页面数量，默认为10
      const scrapeDays = options.scrapeDays || 0; // 获取天数设置，0为不限制

      let keepScraping = true;
      let currentPageNum = startPage;
      let pagesProcessed = 0; // 已处理的页面数量

      // 主循环，处理翻页
      while (keepScraping && pagesProcessed < maxPages) {
        this.updateTaskStatus('scraping', `正在分析第 ${currentPageNum} 页...`);

        try {
          // 步骤一：抓取当前页面的所有帖子URL
          this.log.info(`[${this.forumName}] 使用选择器: ${siteProfile.postLinkSelector}`);

          // 添加页面调试信息
          const pageInfo = await page.evaluate(() => {
            return {
              url: window.location.href,
              title: document.title,
              bodyText: document.body ? document.body.textContent.substring(0, 200) : '无body',
              hasLoginIndicator: document.querySelector('a[href*="action=logout"]') ? true : false
            };
          });
          this.log.info(`[${this.forumName}] 页面信息:`, pageInfo);

          // 🚨 检测Cloudflare人机验证挑战
          if (pageInfo.title === 'Just a moment...' ||
              pageInfo.bodyText.includes('Verify you are human') ||
              pageInfo.bodyText.includes('ccgga.me needs to review the security')) {
            this.log.error(`[${this.forumName}] 🚨🚨🚨 检测到Cloudflare人机验证挑战！🚨🚨🚨`);
            this.log.error(`[${this.forumName}] ⚠️  网站要求人机验证，需要手动处理！`);
            this.log.error(`[${this.forumName}] 📋 请手动打开浏览器完成验证：${pageInfo.url}`);
            this.log.error(`[${this.forumName}] 🔄 验证完成后，请重新启动抓取任务`);

            // 抛出特定错误，让上层知道这是人机验证问题
            throw new Error('需要手动完成Cloudflare人机验证');
          }

          // 添加HTML结构调试
          const htmlDebugInfo = await page.evaluate(() => {
            // 检查是否有tbody元素
            const tbodyElements = document.querySelectorAll('tbody');
            const tbodyInfo = Array.from(tbodyElements).map(tbody => ({
              id: tbody.id,
              className: tbody.className,
              childCount: tbody.children.length
            }));

            // 检查是否有normalthread相关的元素
            const normalthreadElements = document.querySelectorAll('[id*="normalthread"]');
            const normalthreadInfo = Array.from(normalthreadElements).map(el => ({
              tagName: el.tagName,
              id: el.id,
              className: el.className
            }));

            // 检查是否有帖子链接
            const allLinks = document.querySelectorAll('a[href*="thread"]');
            const linkInfo = Array.from(allLinks).slice(0, 5).map(link => ({
              href: link.href,
              text: link.innerText.trim(),
              className: link.className,
              parentTagName: link.parentElement?.tagName,
              parentId: link.parentElement?.id,
              parentClassName: link.parentElement?.className
            }));

            return {
              tbodyCount: tbodyElements.length,
              tbodyInfo: tbodyInfo.slice(0, 5),
              normalthreadCount: normalthreadElements.length,
              normalthreadInfo: normalthreadInfo.slice(0, 5),
              threadLinkCount: allLinks.length,
              linkInfo: linkInfo
            };
          });

          this.log.info(`[${this.forumName}] HTML结构调试:`, htmlDebugInfo);

          // 尝试多个选择器，找到正确的帖子链接，同时提取日期信息
          let postLinksData = await page.evaluate(({ originalSelector, dateSelector }) => {
            console.log(`[Browser] 原始选择器: ${originalSelector}`);
            console.log(`[Browser] 日期选择器: ${dateSelector}`);

            // 尝试不同的选择器变体
            const selectors = [
              originalSelector,  // 原始选择器
              "tbody[id^='normalthread_'] tr > th > a.xst",  // 修复：移除.s
              "tbody[id^='normalthread_'] a.xst",  // 简化版本
              "a.xst[href*='viewthread']",  // 更通用的版本
              "a[href*='viewthread'][href*='tid=']"  // 最通用的版本
            ];

            let linksData = [];
            let workingSelector = null;

            for (const selector of selectors) {
              console.log(`[Browser] 尝试选择器: ${selector}`);
              const elements = document.querySelectorAll(selector);
              console.log(`[Browser] 找到 ${elements.length} 个元素`);

              if (elements.length > 0) {
                elements.forEach((element, index) => {
                  const href = element.href;
                  if (href && href.includes('viewthread') && href.includes('tid=')) {
                    console.log(`[Browser] 元素 ${index + 1}: ${href}`);

                    // 尝试提取日期信息
                    let postDate = null;
                    try {
                      // 找到包含这个链接的行
                      const row = element.closest('tr');
                      if (row) {
                        // 尝试在行内找到日期元素
                        const dateElement = row.querySelector(dateSelector);
                        if (dateElement) {
                          postDate = dateElement.getAttribute('title') || dateElement.textContent || null;
                          console.log(`[Browser] 找到日期: ${postDate}`);
                        } else {
                          console.log(`[Browser] 未找到日期元素，选择器: ${dateSelector}`);
                        }
                      }
                    } catch (dateError) {
                      console.log(`[Browser] 提取日期失败: ${dateError.message}`);
                    }

                    linksData.push({
                      url: href,
                      date: postDate
                    });
                  }
                });

                if (linksData.length > 0) {
                  workingSelector = selector;
                  console.log(`[Browser] 成功的选择器: ${selector}, 找到 ${linksData.length} 个链接`);
                  break;
                }
              }
            }

            return { linksData, workingSelector };
          }, {
            originalSelector: siteProfile.postLinkSelector,
            dateSelector: siteProfile.postDateSelector
          });

          // 如果找到了工作的选择器，更新配置
          if (postLinksData.workingSelector && postLinksData.workingSelector !== siteProfile.postLinkSelector) {
            this.log.info(`[${this.forumName}] 发现更好的选择器: ${postLinksData.workingSelector}`);
            siteProfile.postLinkSelector = postLinksData.workingSelector;
          }

          const postLinksOnPage = postLinksData.linksData.map(item => item.url);
          const postDatesOnPage = postLinksData.linksData;

          this.log.info(`[${this.forumName}] 第 ${currentPageNum} 页发现 ${postLinksOnPage.length} 个帖子链接`);

          if (postLinksOnPage.length === 0) {
            this.log.warn(`[${this.forumName}] 第 ${currentPageNum} 页没有找到帖子链接，可能已到最后一页`);
            break;
          }

          // 步骤二：遍历每个帖子链接，抓取详细信息
          for (let i = 0; i < postLinksOnPage.length; i++) {
            if (this.shouldStop) {
              this.log.info(`[${this.forumName}] 收到停止指令，中断任务`);
              keepScraping = false;
              break;
            }

            const postUrl = postLinksOnPage[i];
            const postDateInfo = postDatesOnPage[i] || { url: postUrl, date: null };
            this.updateTaskStatus('scraping', `正在处理帖子 ${i + 1}/${postLinksOnPage.length} (第${currentPageNum}页)`);

            try {
              // 🔥 重新扫描模式：不跳过任何帖子，重新检查所有内容
              // 用户反馈：第一页内容已更新，需要重新扫描所有帖子
              this.log.info(`[${this.forumName}] 🔄 重新扫描模式：处理帖子 ${postUrl}`);

              // 检查是否已存在，但不跳过，而是记录状态
              const existingUrls = this.databaseService.getExistingUrls([postUrl]);
              if (existingUrls.has(postUrl)) {
                this.log.info(`[${this.forumName}] 📝 帖子已存在但重新检查: ${postUrl}`);
              } else {
                this.log.info(`[${this.forumName}] ✨ 发现新帖子: ${postUrl}`);
              }

              // 导航到帖子页面
              await page.goto(postUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
              await page.waitForTimeout(1000);

              // 解析帖子内容，传入预先提取的日期信息
              const postData = await this.parsePostContent(page, siteProfile, postUrl, postDateInfo);
              if (postData) {
                // 检查日期限制
                if (!this.shouldContinueCollection(postData, options)) {
                  keepScraping = false;
                  break;
                }

                // 🔧 修复：这是一个内容搜集和归档系统，主要目的是搜集帖子信息，而不是下载附件
                // 只要有帖子标题和URL，就认为是有效数据，可以进行归档
                this.log.info(`[${this.forumName}] 搜集到的内容: 标题="${postData.postTitle}", 磁力链接=${postData.magnetLink ? '有' : '无'}, ED2K链接=${postData.ed2kLink ? '有' : '无'}, 附件=${postData.attachmentUrl ? '有' : '无'}, 网盘链接=${postData.cloudLinks ? postData.cloudLinks.length : 0}个, 正文长度=${postData.postBodyText ? postData.postBodyText.length : 0}字符`);

                // 只要有帖子标题就认为是有效数据（这是一个归档系统，不是下载工具）
                const hasValidData = postData.postTitle && postData.postTitle.trim().length > 0;

                // 🔧 修复：记录所有帖子到数据库（恢复历史记录功能）
                let shouldRecord = postData.postTitle && postData.postTitle.trim().length > 0; // 只要有标题就记录

                if (!hasValidData) {
                  this.log.info(`[${this.forumName}] 无有效数据: ${postData.postTitle} - 没有找到磁力链接、ed2k链接或附件链接`);
                  // 🔧 新逻辑：即使没有有效数据，也设置状态并记录到数据库
                  postData.downloadStatus = 'no_attachment';
                  this.log.info(`[${this.forumName}] 🔧 将记录到数据库（恢复历史记录功能）`);
                } else {
                  this.log.info(`[${this.forumName}] 发现有效数据: ${postData.postTitle}`);
                  this.log.info(`[${this.forumName}] 🔧 将记录到数据库（无论下载是否成功）`);
                }

                // 尝试下载附件（如果启用了下载功能）
                if (this.workspacePath && postData.attachmentUrl) {
                  try {
                    await this.downloadAttachments(page, postData, siteProfile);
                    this.stats.successCount++;

                  } catch (downloadError) {
                    this.log.warn(`[${this.forumName}] 下载失败: ${downloadError.message}`);
                    this.stats.failedCount++;

                    // 检查是否需要停止任务
                    if (downloadError.message.includes('下载次数已达上限') ||
                        downloadError.message.includes('需要人机验证')) {
                      throw downloadError;
                    }
                  }
                }

                // 🎯 生成档案文件（无论是否记录到数据库）
                let archiveFilePath = null;
                try {
                  archiveFilePath = await this.recordManager.generatePostArchiveFile(postData);
                  if (archiveFilePath) {
                    this.log.info(`[${this.forumName}] 📄 档案文件已生成: ${archiveFilePath}`);
                    // 🎯 将md文档路径添加到postData中，用于自动关联
                    postData.mdDocumentPath = archiveFilePath;
                  }
                } catch (archiveError) {
                  this.log.error(`[${this.forumName}] ❌ 生成档案文件失败: ${archiveError.message}`);
                }

                // 🎯 保存到数据库（恢复历史记录功能）
                if (shouldRecord) {
                  try {
                    const saved = await this.saveToDatabase(postData);
                    if (saved) {
                      results.push(postData);
                      this.stats.totalProcessed++;
                      this.log.info(`[${this.forumName}] ✅ 帖子已保存到数据库: ${postData.postTitle}`);

                      // 🎯 md文档路径已在saveToDatabase中包含，无需额外更新
                      if (archiveFilePath) {
                        this.log.info(`[${this.forumName}] ✅ md文档已自动关联: ${archiveFilePath}`);
                      }
                    }
                  } catch (saveError) {
                    this.log.error(`[${this.forumName}] ❌ 保存到数据库失败: ${saveError.message}`);
                  }
                }
              }

            } catch (error) {
              this.log.warn(`[${this.forumName}] 跳过帖子 ${postUrl}: ${error.message}`);
              this.stats.failedCount++;
            }

            // 延迟
            if (options.delay && options.delay > 0) {
              await this.delay(options.delay);
            }
          }

          // 步骤三：翻页到下一页
          if (keepScraping && pagesProcessed + 1 < maxPages) {
            const hasNextPage = await this.goToNextPage(page, siteProfile);
            if (hasNextPage) {
              currentPageNum++;
              pagesProcessed++;
              await page.waitForTimeout(2000); // 等待页面加载
            } else {
              this.log.info(`[${this.forumName}] 已到最后一页，停止抓取`);
              break;
            }
          } else {
            pagesProcessed++;
            break;
          }

        } catch (error) {
          this.log.error(`[${this.forumName}] 处理第 ${currentPageNum} 页时出错: ${error.message}`);
          if (error.message.includes('下载次数已达上限') || 
              error.message.includes('需要人机验证')) {
            throw error;
          }
          break;
        }
      }

      this.log.info(`[${this.forumName}] 多页面抓取任务完成，共处理 ${results.length} 个帖子`);
      return results;

    } catch (error) {
      this.log.error(`[${this.forumName}] 抓取逻辑执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从URL中智能检测起始页面
   */
  detectStartPageFromUrl(url) {
    try {
      const pageMatch = url.match(/[&?]page=(\d+)/);
      if (pageMatch) {
        const page = parseInt(pageMatch[1]);
        this.log.info(`[${this.forumName}] 🔍 从URL检测到起始页面: ${page}`);
        return page;
      }
      return 1;
    } catch (error) {
      this.log.warn(`[${this.forumName}] 检测起始页面失败: ${error.message}`);
      return 1;
    }
  }

  /**
   * 提取板块信息
   */
  extractBoardInfo(url) {
    try {
      const fidMatch = url.match(/[&?]fid=(\d+)/);
      if (fidMatch) {
        this.currentBoardId = fidMatch[1];
        this.currentBoardConfig = this.siteProfile.boards?.[this.currentBoardId] || null;

        if (this.currentBoardConfig) {
          this.log.info(`[${this.forumName}] 🔧 检测到板块: ${this.currentBoardConfig.name} (ID: ${this.currentBoardId})`);
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 提取板块信息失败: ${error.message}`);
    }
  }

  /**
   * 解析帖子内容 - 增强版，支持全信息结构化归档
   */
  async parsePostContent(page, siteProfile, postUrl, postDateInfo = null) {
    // 🔧 关键修复：将所有变量定义移到方法最开始，避免作用域问题
    let postTitle = '';
    let postBodyText = '';
    let postBodyHtml = ''; // 确保初始化为空字符串
    let extractedMetadata = {};
    let cloudLinks = []; // 🔧 新增：网盘链接数组
    let allImages = []; // 所有图片
    let allLinks = []; // 所有链接

    try {
      this.log.info(`[${this.forumName}] 开始解析帖子内容，postDateInfo: ${JSON.stringify(postDateInfo)}`);
      // 抓取帖子标题
      try {
        await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
        postTitle = await page.locator(siteProfile.postTitleSelector).first().textContent();
        postTitle = postTitle ? postTitle.trim() : '';
      } catch (error) {
        postTitle = await page.title() || `帖子`;
      }

      // 🔧 新增：提取帖子正文内容进行元数据解析

      try {
        if (siteProfile.postContainerSelector) {
          this.log.info(`[${this.forumName}] 🔍 开始提取帖子正文内容，使用选择器: ${siteProfile.postContainerSelector}`);
          await page.waitForSelector(siteProfile.postContainerSelector, { timeout: 5000 });

          // 调试：检查选择器匹配的元素数量
          const elementCount = await page.locator(siteProfile.postContainerSelector).count();
          this.log.info(`[${this.forumName}] 🔍 选择器 ${siteProfile.postContainerSelector} 匹配到 ${elementCount} 个元素`);

          // 获取主内容区域的纯文本和HTML
          const bodyElements = await page.locator(siteProfile.postContainerSelector).all();
          if (bodyElements.length > 0) {
            postBodyText = await bodyElements[0].innerText();
            postBodyHtml = await bodyElements[0].innerHTML();
            this.log.info(`[${this.forumName}] 📄 提取到正文内容长度: ${postBodyText.length} 字符`);

            // 解析元数据
            extractedMetadata = this.extractMetadataFromText(postBodyText);
            this.log.info(`[${this.forumName}] 📊 提取的元数据:`, extractedMetadata);

            // 🔧 新增：提取网盘链接与提取码
            cloudLinks = this.extractCloudLinksWithCodes(postBodyText);
            if (cloudLinks.length > 0) {
              this.log.info(`[${this.forumName}] 🌐 提取到 ${cloudLinks.length} 个网盘链接`);
              cloudLinks.forEach((link, index) => {
                this.log.info(`[${this.forumName}] 🌐 网盘链接 ${index + 1}: ${link.url} (提取码: ${link.code})`);
              });
            }
          }
        }
      } catch (error) {
        this.log.warn(`[${this.forumName}] ⚠️ 提取正文内容失败: ${error.message}`);
        // 确保变量被正确初始化
        postBodyText = '';
        postBodyHtml = '';
        extractedMetadata = {};
        cloudLinks = [];
        allImages = [];
        allLinks = [];
      }

      // 提取NFO ID（优先使用从正文提取的品番，其次使用标题）
      let nfoId = extractedMetadata.nfoId || NodeNfoParser.extractJavIdFromFilename(postTitle);

      // 抓取磁力链接 - 支持从li标签内提取，修复截断问题
      let magnetLinks = [];
      try {
        // 调试：检查页面上是否有任何包含magnet的内容
        const pageContent = await page.content();
        const magnetMatches = pageContent.match(/magnet:/gi);
        this.log.info(`[${this.forumName}] 🧲 页面HTML中包含 ${magnetMatches ? magnetMatches.length : 0} 个 "magnet:" 字符串`);

        const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
        this.log.info(`[${this.forumName}] 🧲 查找磁力链接，使用选择器: ${siteProfile.magnetLinkSelector}`);
        this.log.info(`[${this.forumName}] 🧲 找到 ${magnetElements.length} 个磁力链接元素`);

        for (const element of magnetElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('magnet:')) {
              // 使用更强大的正则表达式提取完整磁力链接
              const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
              if (magnetMatches) {
                magnetMatches.forEach(link => {
                  const cleanLink = link.trim();
                  magnetLinks.push(cleanLink);
                  this.log.info(`[${this.forumName}] 🧲 从li标签HTML提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('magnet:')) {
              magnetLinks.push(href.trim());
              this.log.info(`[${this.forumName}] 🧲 从href属性提取磁力链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('magnet:')) {
                const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
                if (magnetMatches) {
                  magnetMatches.forEach(link => {
                    const cleanLink = link.trim();
                    magnetLinks.push(cleanLink);
                    this.log.info(`[${this.forumName}] 🧲 从HTML内容提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        this.log.info(`[${this.forumName}] 🧲 总共提取到 ${magnetLinks.length} 个磁力链接`);
      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法获取磁力链接: ${error.message}`);
      }

      // 抓取ED2K链接 - 支持从li标签内提取，修复截断问题
      let ed2kLinks = [];
      try {
        const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
        this.log.info(`[${this.forumName}] 🔗 查找ED2K链接，使用选择器: ${siteProfile.ed2kLinkSelector}`);
        this.log.info(`[${this.forumName}] 🔗 找到 ${ed2kElements.length} 个ED2K链接元素`);

        for (const element of ed2kElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('ed2k:')) {
              // 使用更强大的正则表达式提取完整ed2k链接
              const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/g);
              if (ed2kMatches) {
                ed2kMatches.forEach(link => {
                  const cleanLink = link.trim();
                  ed2kLinks.push(cleanLink);
                  this.log.info(`[${this.forumName}] 🔗 从li标签HTML提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('ed2k:')) {
              ed2kLinks.push(href.trim());
              this.log.info(`[${this.forumName}] 🔗 从href属性提取ED2K链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('ed2k:')) {
                const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/g);
                if (ed2kMatches) {
                  ed2kMatches.forEach(link => {
                    const cleanLink = link.trim();
                    ed2kLinks.push(cleanLink);
                    this.log.info(`[${this.forumName}] 🔗 从HTML内容提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        this.log.info(`[${this.forumName}] 🔗 总共提取到 ${ed2kLinks.length} 个ED2K链接`);
      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法获取ED2K链接: ${error.message}`);
      }

      // 继续其他内容...
      return this.buildPostData(postUrl, postTitle, nfoId, magnetLinks, ed2kLinks, extractedMetadata, cloudLinks, postBodyText, postBodyHtml, allImages, allLinks, page, siteProfile);

    } catch (error) {
      this.log.error(`[${this.forumName}] 解析帖子内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 构建帖子数据对象
   */
  async buildPostData(postUrl, postTitle, nfoId, magnetLinks, ed2kLinks, extractedMetadata, cloudLinks, postBodyText, postBodyHtml, allImages, allLinks, page, siteProfile) {
    // 抓取附件链接
    let attachmentUrls = [];
    try {
      // 调试：检查页面上是否有任何包含attachment的内容
      const pageContent = await page.content();
      const attachmentMatches = pageContent.match(/mod=attachment/gi);
      this.log.info(`[${this.forumName}] 📎 页面HTML中包含 ${attachmentMatches ? attachmentMatches.length : 0} 个 "mod=attachment" 字符串`);

      const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
      this.log.info(`[${this.forumName}] 📎 查找附件链接，使用选择器: ${siteProfile.attachmentUrlSelector}`);
      this.log.info(`[${this.forumName}] 📎 找到 ${attachmentElements.length} 个附件元素`);

      for (const element of attachmentElements) {
        const href = await element.getAttribute('href');
        if (href) {
          const absoluteUrl = href.startsWith('http') ? href : new URL(href, page.url()).toString();
          attachmentUrls.push(absoluteUrl);
          this.log.info(`[${this.forumName}] 📎 提取附件链接: ${absoluteUrl}`);
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 无法获取附件链接: ${error.message}`);
    }

    // 抓取解压密码
    let decompressionPassword = '';
    try {
      const passwordElements = await page.locator(siteProfile.passwordSelector).all();
      for (const element of passwordElements) {
        const text = await element.textContent();
        if (text) {
          // 密码提取逻辑
          const passwordPatterns = [
            /密码[：:]\s*([^\s\n\r]+)/i,
            /解压密码[：:]\s*([^\s\n\r]+)/i,
            /password[：:]\s*([^\s\n\r]+)/i,
            /pwd[：:]\s*([^\s\n\r]+)/i,
            /pass[：:]\s*([^\s\n\r]+)/i,
            /【解压密码】[：:]\s*([^\s\n\r]+)/i,
            /【密码】[：:]\s*([^\s\n\r]+)/i,
            /\[解压密码\][：:]\s*([^\s\n\r]+)/i,
            /\[密码\][：:]\s*([^\s\n\r]+)/i
          ];

          for (const pattern of passwordPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
              decompressionPassword = match[1].trim();
              decompressionPassword = decompressionPassword.replace(/[，。！？；：""''（）【】\[\]]/g, '');
              break;
            }
          }

          if (decompressionPassword) break;
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 无法获取解压密码: ${error.message}`);
    }

    // 抓取预览图
    let previewImageUrl = '';
    try {
      const previewElements = await page.locator(siteProfile.previewImageSelector).all();
      if (previewElements.length > 0) {
        const src = await previewElements[0].getAttribute('src');
        if (src) {
          previewImageUrl = src.startsWith('http') ? src : new URL(src, page.url()).toString();
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 获取预览图失败: ${error.message}`);
    }

    // 获取帖子日期 - 使用预先提取的日期信息或回退到页面提取
    let postDate;
    try {
      if (typeof postDateInfo !== 'undefined' && postDateInfo && postDateInfo.date) {
        try {
          postDate = this.parseForumDate(postDateInfo.date);
          this.log.info(`[${this.forumName}] 使用预先提取的日期: ${postDateInfo.date}`);
        } catch (error) {
          this.log.warn(`[${this.forumName}] 解析预先提取的日期失败: ${error.message}`);
          postDate = await this.extractPostDate(page);
        }
      } else {
        this.log.info(`[${this.forumName}] 未找到预先提取的日期，尝试从页面提取`);
        postDate = await this.extractPostDate(page);
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 日期处理出错: ${error.message}，使用当前时间`);
      postDate = new Date();
    }

    // 检测回帖可见状态
    let status = 'normal';
    try {
      const hasReplyButton = siteProfile.replyToViewSelector &&
        await page.locator(siteProfile.replyToViewSelector).count() > 0;
      const hasDownloadLinks = magnetLinks.length > 0 || ed2kLinks.length > 0 || attachmentUrls.length > 0;

      if (hasReplyButton && !hasDownloadLinks) {
        status = 'requires_reply';
        this.log.info(`[${this.forumName}] 🔒 检测到回帖可见状态：${postTitle}`);
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 检测回帖可见状态时出错: ${error.message}`);
    }

    // 构建板块信息
    const boardInfo = this.currentBoardConfig ? {
      boardId: this.currentBoardId,
      boardName: this.currentBoardConfig.name,
      boardTags: this.currentBoardConfig.tags
    } : null;

    return {
      postUrl,
      postTitle,
      nfoId,
      magnetLink: magnetLinks.join('\n'),
      ed2kLink: ed2kLinks.join('\n'),
      attachmentUrl: attachmentUrls.join('\n'),
      decompressionPassword,
      previewImageUrl,
      postDate,
      collectionDate: new Date().toISOString(),
      downloadStatus: 'pending',
      errorMessage: null,
      downloadPath: null,
      status,
      boardInfo,
      sourceForum: this.forumName || 'x1080x', // 添加论坛名称
      fileSize: extractedMetadata.fileSize || null,
      performers: extractedMetadata.performers || null,
      studio: extractedMetadata.studio || null,
      duration: extractedMetadata.duration || null,
      cloudLinks: cloudLinks,
      postBodyText: postBodyText, // 完整的正文内容
      fullPostHtml: postBodyHtml, // 完整的HTML内容
      fullPostText: postBodyText, // 完整的纯文本内容
      allImages: allImages, // 所有图片
      allLinks: allLinks, // 所有链接
      extractedMetadata: extractedMetadata // 提取的元数据
    };
  }

  /**
   * 提取帖子日期
   */
  async extractPostDate(page) {
    try {
      // 尝试从帖子页面提取日期
      const postRow = await page.locator(`a[href="${page.url()}"]`).locator('xpath=ancestor::tr[1]').first();
      const dateElement = postRow.locator(this.siteProfile.postDateSelector).first();

      // 优先尝试从title属性读取日期
      let dateText = await dateElement.getAttribute('title');

      // 如果title属性不存在，则读取元素的文本内容
      if (!dateText) {
        dateText = await dateElement.textContent();
      }

      return this.parseForumDate(dateText);
    } catch (error) {
      this.log.warn(`[${this.forumName}] 获取帖子日期失败: ${error.message}`);
      return new Date();
    }
  }

  /**
   * 解析论坛日期格式
   */
  parseForumDate(dateText) {
    if (!dateText) return new Date();

    const cleanText = dateText.trim();

    // 匹配 YYYY-MM-DD 格式
    const match = cleanText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (match) {
      return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
    }

    // 匹配其他常见格式，如 YYYY/MM/DD
    const match2 = cleanText.match(/(\d{4})\/(\d{1,2})\/(\d{1,2})/);
    if (match2) {
      return new Date(parseInt(match2[1]), parseInt(match2[2]) - 1, parseInt(match2[3]));
    }

    // 解析相对日期格式
    const now = new Date();

    const hoursMatch = cleanText.match(/(\d+)\s*小时前/);
    if (hoursMatch) {
      const hours = parseInt(hoursMatch[1]);
      return new Date(now.getTime() - hours * 60 * 60 * 1000);
    }

    const minutesMatch = cleanText.match(/(\d+)\s*分钟前/);
    if (minutesMatch) {
      const minutes = parseInt(minutesMatch[1]);
      return new Date(now.getTime() - minutes * 60 * 1000);
    }

    const daysMatch = cleanText.match(/(\d+)\s*天前/);
    if (daysMatch) {
      const days = parseInt(daysMatch[1]);
      return new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    }

    if (cleanText.includes('昨天')) {
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    if (cleanText.includes('前天')) {
      return new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
    }

    if (cleanText.includes('今天')) {
      return new Date(now.getFullYear(), now.getMonth(), now.getDate());
    }

    return new Date();
  }

  /**
   * 翻页到下一页
   */
  async goToNextPage(page, siteProfile) {
    try {
      // 查找下一页链接
      const nextPageElement = await page.locator(siteProfile.nextPageSelector).first();
      if (await nextPageElement.count() > 0) {
        await nextPageElement.click();
        await page.waitForLoadState('domcontentloaded');
        return true;
      }
      return false;
    } catch (error) {
      this.log.warn(`[${this.forumName}] 翻页失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 下载附件 - 使用基类的模块化实现
   * 注意：此方法已移除，现在使用 BaseCollector 中的 downloadAttachments 方法
   */
  // 注意：downloadAttachments 方法已移除，现在使用 BaseCollector 中的模块化实现

  /**
   * 从文本中提取元数据
   */
  extractMetadataFromText(text) {
    const metadata = {};

    // 提取文件大小
    const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(GB|MB|KB)/i);
    if (sizeMatch) {
      metadata.fileSize = `${sizeMatch[1]} ${sizeMatch[2].toUpperCase()}`;
    }

    // 提取演员信息
    const performerPatterns = [
      /演员[：:]\s*([^\n\r]+)/i,
      /主演[：:]\s*([^\n\r]+)/i,
      /女优[：:]\s*([^\n\r]+)/i
    ];

    for (const pattern of performerPatterns) {
      const match = text.match(pattern);
      if (match) {
        metadata.performers = match[1].trim();
        break;
      }
    }

    return metadata;
  }

  /**
   * 从文本中提取网盘链接与提取码
   */
  extractCloudLinksWithCodes(text) {
    const cloudLinks = [];

    const patterns = [
      /链接\d*[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
      /下载链接[：:]\s*(https?:\/\/[^\s]+)\s*密码[：:]\s*(\w+)/gi,
      /网盘[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
      /百度网盘[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const url = match[1].trim();
        const code = match[2].trim();

        try {
          new URL(url);
          cloudLinks.push({
            url: url,
            code: code,
            type: this.detectCloudType(url)
          });
        } catch (e) {
          // 无效URL，跳过
        }
      }
    }

    return cloudLinks;
  }

  /**
   * 检测网盘类型
   */
  detectCloudType(url) {
    if (url.includes('pan.baidu.com')) return '百度网盘';
    if (url.includes('cloud.189.cn')) return '天翼云盘';
    if (url.includes('lanzou.com') || url.includes('lanzous.com')) return '蓝奏云';
    return '其他网盘';
  }

  /**
   * 保存数据到数据库
   * @param {Object} postData - 帖子数据
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveToDatabase(postData) {
    try {
      if (!this.databaseService) {
        this.log.warn('[ForumACollector] 数据库服务未初始化，跳过保存');
        return false;
      }

      // 重复检查已在处理前完成，这里直接保存

      // 转换数据格式以匹配数据库结构
      const linkData = {
        postUrl: postData.postUrl || '',
        postTitle: postData.postTitle || '',
        nfoId: postData.nfoId || '',
        magnetLink: postData.magnetLink || '',
        ed2kLink: postData.ed2kLink || '',
        attachmentUrl: postData.attachmentUrl || '',
        decompressionPassword: postData.decompressionPassword || '',
        collectionDate: new Date().toISOString(),
        downloadStatus: postData.downloadStatus || 'pending',
        errorMessage: null,
        downloadPath: postData.downloadPath || '',
        downloadedDate: postData.downloadedDate || null,
        archivePath: postData.archivePath || '',
        fullPostHtml: postData.fullPostHtml || '',
        fullPostText: postData.fullPostText || '',
        postBodyText: postData.postBodyText || '',
        allImages: JSON.stringify(postData.allImages || []),
        mdDocumentPath: postData.mdDocumentPath || null, // 🎯 包含md文档路径
        allLinks: JSON.stringify(postData.allLinks || []),
        cloudLinks: JSON.stringify(postData.cloudLinks || []),
        extractedMetadata: JSON.stringify(postData.extractedMetadata || {}),
        boardInfo: JSON.stringify(postData.boardInfo || {}),
        status: postData.status || '',
        previewImageUrl: postData.previewImageUrl || '',
        postDate: postData.postDate || ''
      };

      // 确保所有字段都是字符串、数字或null
      Object.keys(linkData).forEach(key => {
        if (linkData[key] !== null && typeof linkData[key] === 'object') {
          linkData[key] = JSON.stringify(linkData[key]);
        }
      });

      // 保存到数据库
      const sourceForum = this.forumName || 'x1080x'; // 使用论坛名称
      const result = this.databaseService.insertCollectedLinks([linkData], sourceForum);

      if (result.success && result.insertedCount > 0) {
        this.log.info(`[ForumACollector] 帖子已保存到数据库: ${postData.postTitle}`);
        return true;
      } else {
        this.log.warn(`[ForumACollector] 帖子保存失败: ${result.error || '未知错误'}`);
        return false;
      }
    } catch (error) {
      this.log.error(`[ForumACollector] 保存到数据库失败: ${error.message}`);
      return false;
    }
  }
}

module.exports = ForumACollector;
