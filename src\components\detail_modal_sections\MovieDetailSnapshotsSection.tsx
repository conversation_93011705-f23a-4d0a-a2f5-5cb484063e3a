// soul-forge-electron/src/components/detail_modal_sections/MovieDetailSnapshotsSection.tsx
import React from 'react';
import { Movie, SnapshotInfo } from '../../types';
import LazyLoadWrapper from '../LazyLoadWrapper';
import { HardDrive, Play, FileVideo } from 'lucide-react';

interface SnapshotThumbnailProps {
  snapshot: SnapshotInfo;
  isSuggested: boolean;
  onViewSnapshot: () => void;
  isLoading: boolean;
}

const SnapshotThumbnail: React.FC<SnapshotThumbnailProps> = ({ snapshot, isSuggested, onViewSnapshot, isLoading }) => (
  <div
    className={`relative group border-2 rounded-lg overflow-hidden shadow-md cursor-pointer hover:shadow-lg transition-all duration-200 ${isSuggested ? 'border-purple-500' : 'border-slate-600 hover:border-slate-500'}`}
    onClick={onViewSnapshot}
    title="点击放大查看快照"
  >
    <LazyLoadWrapper placeholder={<div className="aspect-video bg-[#383838] animate-pulse rounded-lg"></div>}>
      <img src={snapshot.dataUrl || ''} alt="影片快照" className="aspect-video object-cover w-full h-full group-hover:scale-105 transition-transform duration-200" />
    </LazyLoadWrapper>
    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
      {isSuggested && <span className="absolute top-2 right-2 text-[10px] bg-purple-600 text-white px-2 py-1 rounded-full font-medium">AI推荐</span>}
      <div className="bg-black/60 text-white px-3 py-1.5 rounded-lg text-sm font-medium backdrop-blur-sm">
        🔍 点击放大
      </div>
    </div>
  </div>
);

// 【新增】版本组快照显示组件
interface VersionSnapshotGroupProps {
  versionInfo: {
    db_id: number;
    filePath: string;
    fileName: string;
    fileSize?: number;
    resolution?: string;
  };
  snapshots: SnapshotInfo[];
  isCoverActionLoading: boolean;
  aiSuggestedSnapshotIndex: number | null;
  onGenerateSnapshots?: (versionDbId: number, versionFilePath: string) => Promise<void>;
  isGeneratingSnapshots?: boolean;
}

const VersionSnapshotGroup: React.FC<VersionSnapshotGroupProps> = ({
  versionInfo,
  snapshots,
  isCoverActionLoading,
  aiSuggestedSnapshotIndex,
  onGenerateSnapshots,
  isGeneratingSnapshots = false
}) => {

  // 调试日志
  console.log("🎬 [VersionSnapshotGroup] 组件渲染", {
    versionDbId: versionInfo.db_id,
    fileName: versionInfo.fileName,
    onGenerateSnapshots: !!onGenerateSnapshots,
    onGenerateSnapshotsType: typeof onGenerateSnapshots,
    snapshotsCount: snapshots.length
  });
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    const gb = bytes / (1024 * 1024 * 1024);
    if (gb >= 1) return `${gb.toFixed(1)}GB`;
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(0)}MB`;
  };

  const getVersionIcon = (): React.ReactNode => {
    const fileSize = versionInfo.fileSize || 0;
    if (fileSize < 200 * 1024 * 1024) { // 小于 200MB
      return <Play className="w-4 h-4 text-orange-400" title="预告片" />;
    }
    return <HardDrive className="w-4 h-4 text-green-400" title="本地版本" />;
  };

  console.log("🎬 [VersionSnapshotGroup] 开始渲染组件");

  return (
    <div className="mb-8 border border-slate-600 rounded-xl p-6 bg-slate-800/50 shadow-lg">
      {/* 版本信息标题 */}
      <div className="flex items-start space-x-3 mb-4 pb-3 border-b border-slate-600">
        <div className="flex-shrink-0 mt-1">
          {getVersionIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-base font-semibold text-white mb-2 break-words" title={versionInfo.fileName}>
            {versionInfo.fileName}
          </h4>
          <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-slate-400">
            {versionInfo.resolution && (
              <span className="flex items-center space-x-1">
                <FileVideo className="w-4 h-4" />
                <span>{versionInfo.resolution}</span>
              </span>
            )}
            {versionInfo.fileSize && (
              <span className="flex items-center space-x-1">
                <span>📁</span>
                <span>{formatFileSize(versionInfo.fileSize)}</span>
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 按钮区域 */}
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm text-slate-400 font-medium">
          📸 {snapshots.length} 张快照
        </span>
        {onGenerateSnapshots && (
          <button
            onClick={() => {
              console.log("🎬 [VersionSnapshotGroup] 用户点击了生成快照按钮", {
                versionDbId: versionInfo.db_id,
                filePath: versionInfo.filePath
              });
              onGenerateSnapshots(versionInfo.db_id, versionInfo.filePath);
            }}
            disabled={isGeneratingSnapshots}
            className="button-secondary-app text-xs px-3 py-1.5 rounded-lg hover:bg-slate-600 transition-colors"
          >
            {isGeneratingSnapshots ? "⏳ 生成中..." : "🎬 生成快照"}
          </button>
        )}
      </div>

      {/* 快照横向滚动列表 */}
      <div className="overflow-x-auto rounded-lg bg-slate-900/30 p-4">
        <div className="flex space-x-5 pb-2" style={{ minWidth: 'max-content' }}>
          {snapshots.map((snapshot, index) => {
            // 在分组显示中，AI 推荐的快照索引需要重新计算
            // 暂时禁用 AI 推荐显示，因为索引可能不匹配
            const isSuggested = false; // aiSuggestedSnapshotIndex === index;
            return (
              <div key={snapshot.filePath || `snapshot-${index}`} className="flex-shrink-0 w-40">
                <SnapshotThumbnail
                  snapshot={snapshot}
                  isSuggested={isSuggested}
                  onViewSnapshot={() => {
                    // 创建一个新窗口显示放大的快照
                    if (snapshot.dataUrl) {
                      const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                      if (newWindow) {
                        newWindow.document.write(`
                          <html>
                            <head>
                              <title>快照预览</title>
                              <style>
                                body { margin: 0; padding: 20px; background: #1a1a1a; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
                                img { max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.5); }
                              </style>
                            </head>
                            <body>
                              <img src="${snapshot.dataUrl}" alt="快照预览" />
                            </body>
                          </html>
                        `);
                        newWindow.document.close();
                      }
                    }
                  }}
                  isLoading={isCoverActionLoading}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface MovieDetailSnapshotsSectionProps {
  movie: Movie;
  snapshots: SnapshotInfo[];
  isLoadingSnapshots: boolean;
  onFetchSnapshots: (forceGenerate: boolean) => Promise<void>; // Updated signature
  onSuggestCover: () => Promise<void>;
  onSetSnapshotAsCover: (snapshotFilePath: string) => Promise<void>;
  isSuggestingCover: boolean; // Keep this if it's a distinct loading state
  isCoverActionLoading: boolean;
  aiSuggestedSnapshotIndex: number | null;
  showSnapshotsSection: boolean;
  setShowSnapshotsSection: React.Dispatch<React.SetStateAction<boolean>>;
  isEditing: boolean;
  // 【新增】分组快照相关
  groupedSnapshots: Array<{
    versionInfo: {
      db_id: number;
      filePath: string;
      fileName: string;
      fileSize?: number;
      resolution?: string;
    };
    snapshots: SnapshotInfo[];
  }>;
  isLoadingGroupedSnapshots: boolean;
  refreshGroupedSnapshots?: () => Promise<void>;
  onGenerateSnapshotsForVersion?: (versionDbId: number, versionFilePath: string) => Promise<void>;
  isGeneratingSnapshotsForVersion?: boolean;
}

const MovieDetailSnapshotsSection: React.FC<MovieDetailSnapshotsSectionProps> = ({
  movie,
  snapshots,
  isLoadingSnapshots,
  onFetchSnapshots, // Use this prop from the hook
  onSuggestCover, // Use this prop from the hook
  onSetSnapshotAsCover,
  isSuggestingCover, // Can be the AI analysis loading state from the hook
  isCoverActionLoading,
  aiSuggestedSnapshotIndex,
  showSnapshotsSection,
  setShowSnapshotsSection,
  isEditing,
  // 【新增】分组快照相关
  groupedSnapshots,
  isLoadingGroupedSnapshots,
  refreshGroupedSnapshots,
  onGenerateSnapshotsForVersion,
  isGeneratingSnapshotsForVersion = false,
}) => {

  // 调试日志
  console.log("🎬 [MovieDetailSnapshotsSection] 组件渲染", {
    onGenerateSnapshotsForVersion: !!onGenerateSnapshotsForVersion,
    onGenerateSnapshotsForVersionType: typeof onGenerateSnapshotsForVersion,
    groupedSnapshotsLength: groupedSnapshots.length
  });

  const handleForceGenerateSnapshots = async () => {
    console.log("🎬 [MovieDetailSnapshotsSection] 用户点击了一键生成快照按钮", {
      movieTitle: movie?.title,
      versionsCount: groupedSnapshots.length
    });

    if (!onGenerateSnapshotsForVersion) {
      console.error("🎬 [MovieDetailSnapshotsSection] ❌ onGenerateSnapshotsForVersion 函数不可用");
      alert("快照生成功能不可用");
      return;
    }

    if (groupedSnapshots.length === 0) {
      console.error("🎬 [MovieDetailSnapshotsSection] ❌ 没有找到版本信息");
      alert("没有找到版本信息，无法生成快照");
      return;
    }

    console.log("🎬 [MovieDetailSnapshotsSection] ✅ 开始为所有版本生成快照...");

    // 为所有版本生成快照
    for (const group of groupedSnapshots) {
      const { versionInfo } = group;
      console.log(`🎬 [MovieDetailSnapshotsSection] 正在为版本 ${versionInfo.fileName} 生成快照...`);

      try {
        await onGenerateSnapshotsForVersion(versionInfo.db_id, versionInfo.filePath);
        console.log(`🎬 [MovieDetailSnapshotsSection] ✅ 版本 ${versionInfo.fileName} 快照生成完成`);
      } catch (error) {
        console.error(`🎬 [MovieDetailSnapshotsSection] ❌ 版本 ${versionInfo.fileName} 快照生成失败:`, error);
      }
    }

    // 刷新分组快照
    if (refreshGroupedSnapshots) {
      console.log("🎬 [MovieDetailSnapshotsSection] 刷新分组快照...");
      await refreshGroupedSnapshots();
    }

    if (!showSnapshotsSection) {
      console.log("🎬 [MovieDetailSnapshotsSection] 展开快照区域");
      setShowSnapshotsSection(true);
    }

    console.log("🎬 [MovieDetailSnapshotsSection] ✅ 所有版本快照生成流程完成");
  };

  // 计算总快照数量
  const totalSnapshots = groupedSnapshots.reduce((total, group) => total + group.snapshots.length, 0);

  return (
    (movie?.nfoId || groupedSnapshots.length > 0 || isLoadingGroupedSnapshots) && (
      <details
        className="mt-3 rounded-md border border-[#4f4f4f] bg-[#2d2d2d]/50 overflow-hidden"
        open={showSnapshotsSection}
    >
        <summary
          className="px-3 py-2 text-sm font-semibold text-amber-400 cursor-pointer list-none flex justify-between items-center hover:bg-[#383838]/70 transition-colors"
          onClick={(e) => {
            e.preventDefault();
            setShowSnapshotsSection(!showSnapshotsSection);
          }}
        >
          <div className="flex items-center">
            <span>
              影片快照 ({isLoadingGroupedSnapshots ? '加载中...' : `${groupedSnapshots.length} 个版本，共 ${totalSnapshots} 张`})
            </span>
            {movie?.db_id !== undefined && ( 
                <button 
                    onClick={(e) => { e.preventDefault(); e.stopPropagation(); handleForceGenerateSnapshots(); }} 
                    disabled={isLoadingSnapshots} 
                    className="button-secondary-app text-[10px] px-1.5 py-0.5 ml-2"
                >
                    {isLoadingSnapshots ? "生成中..." : "一键生成快照"}
                </button>
            )}
          </div>
          <div className="flex items-center">
            {movie?.db_id !== undefined && !isEditing && (
              <button 
                onClick={(e) => { e.preventDefault(); e.stopPropagation(); onSuggestCover(); }} 
                disabled={isSuggestingCover || isLoadingSnapshots || snapshots.length === 0} 
                className="button-secondary-app text-[10px] px-1.5 py-0.5 mr-2"
              >
                {isSuggestingCover ? "AI推荐中..." : "AI推荐封面"}
              </button>
            )}
            <span className={`transition-transform duration-200 ${showSnapshotsSection ? 'rotate-180' : ''}`}>▼</span>
          </div>
        </summary>
        <div className="p-3">
          {isLoadingGroupedSnapshots && (
            <p className="text-xs text-center text-neutral-400">正在加载快照...</p>
          )}
          {!isLoadingGroupedSnapshots && groupedSnapshots.length === 0 && (
            <div className="text-center py-8">
              <p className="text-sm text-neutral-400 mb-2">
                📸 当前没有可用的影片快照
              </p>
              <p className="text-xs text-neutral-500">
                点击"一键生成快照"或各版本的"生成快照"按钮来创建快照
              </p>
            </div>
          )}
          {!isLoadingGroupedSnapshots && groupedSnapshots.length > 0 && (
            <div className="space-y-6">
              {groupedSnapshots.map((group, index) => {
                console.log(`🎬 [MovieDetailSnapshotsSection] 渲染版本组 ${index}:`, {
                  versionDbId: group.versionInfo.db_id,
                  fileName: group.versionInfo.fileName,
                  snapshotsCount: group.snapshots.length
                });

                try {
                  return (
                    <VersionSnapshotGroup
                      key={group.versionInfo.db_id}
                      versionInfo={group.versionInfo}
                      snapshots={group.snapshots}
                      isCoverActionLoading={isCoverActionLoading}
                      aiSuggestedSnapshotIndex={aiSuggestedSnapshotIndex}
                      onGenerateSnapshots={onGenerateSnapshotsForVersion}
                      isGeneratingSnapshots={isGeneratingSnapshotsForVersion}
                    />
                  );
                } catch (error) {
                  console.error(`🎬 [MovieDetailSnapshotsSection] 版本组 ${index} 渲染错误:`, error);
                  return (
                    <div key={group.versionInfo.db_id} style={{ border: '2px solid red', margin: '10px', padding: '10px' }}>
                      <p style={{ color: 'red' }}>版本组 {index + 1} 渲染错误: {error.message}</p>
                    </div>
                  );
                }
              })}
            </div>
          )}
        </div>
      </details>
    )
  );
};

export default MovieDetailSnapshotsSection;
