// main_process/services/mediaDownloadService.js
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const log = require('electron-log');

/**
 * 媒体下载服务 - 负责从网络下载媒体文件并保存到本地
 */

/**
 * 从给定的 URL 下载文件并保存到指定路径（支持版本优先覆盖）。
 * @param {string} url - 要下载的文件的 URL。
 * @param {string} savePath - 完整的本地保存路径 (e.g., 'D:/.../poster.jpg')。
 * @param {Object} options - 下载选项
 * @param {boolean} options.forceOverwrite - 是否强制覆盖现有文件
 * @returns {Promise<boolean>} - 成功返回 true，失败返回 false。
 */
async function downloadFile(url, savePath, options = {}) {
    if (!url || !savePath) {
        log.warn('[媒体下载服务] URL 或保存路径为空，跳过下载。');
        return false;
    }

    // 检查文件是否已存在
    if (fs.existsSync(savePath)) {
        if (!options.forceOverwrite) {
            // 应用版本优先逻辑
            const shouldOverwrite = await shouldOverwriteExistingFile(url, savePath);

            if (!shouldOverwrite) {
                log.info(`[媒体下载服务] 文件已存在且无需更新，跳过下载: ${savePath}`);
                return true;
            } else {
                log.info(`[媒体下载服务] 应用版本优先逻辑，将覆盖现有文件: ${savePath}`);
            }
        } else {
            log.info(`[媒体下载服务] 强制覆盖模式，将覆盖现有文件: ${savePath}`);
        }
    }

    try {
        log.info(`[媒体下载服务] 开始下载: ${url} -> ${savePath}`);

        // 确保目标目录存在
        const dir = path.dirname(savePath);
        await fs.promises.mkdir(dir, { recursive: true });

        const writer = fs.createWriteStream(savePath);

        // 根据URL来源设置不同的请求头
        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        };

        // 为 JavBus 图片添加特殊的 Referer
        if (url.includes('javbus.com')) {
            headers['Referer'] = 'https://www.javbus.com/';
            headers['Origin'] = 'https://www.javbus.com';
        } else {
            // 对于其他网站，使用源 URL 的 origin 作为 Referer
            try {
                headers['Referer'] = new URL(url).origin;
            } catch (e) {
                // 如果 URL 解析失败，忽略 Referer
            }
        }

        const response = await axios({
            url,
            method: 'GET',
            responseType: 'stream',
            timeout: 30000, // 30秒超时
            maxRedirects: 5, // 允许最多5次重定向
            headers: headers
        });

        // 检查响应状态
        if (response.status !== 200) {
            log.error(`[媒体下载服务] HTTP 错误: ${response.status} ${response.statusText}`);
            return false;
        }

        // 检查内容类型
        const contentType = response.headers['content-type'];
        if (contentType && !contentType.startsWith('image/') && !contentType.startsWith('video/')) {
            log.warn(`[媒体下载服务] 可疑的内容类型: ${contentType}, URL: ${url}`);
        }

        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', () => {
                log.info(`[媒体下载服务] 文件成功下载到: ${savePath}`);
                resolve(true);
            });
            
            writer.on('error', (err) => {
                log.error(`[媒体下载服务] 写入文件时出错: ${savePath}`, err);
                // 尝试删除不完整的文件
                fs.unlink(savePath, () => {});
                resolve(false);
            });

            response.data.on('error', (err) => {
                log.error(`[媒体下载服务] 下载流出错: ${url}`, err);
                writer.destroy();
                // 尝试删除不完整的文件
                fs.unlink(savePath, () => {});
                resolve(false);
            });
        });

    } catch (error) {
        log.error(`[媒体下载服务] 下载文件失败: ${url}`, error.message);
        
        // 尝试删除可能创建的不完整文件
        try {
            if (fs.existsSync(savePath)) {
                fs.unlinkSync(savePath);
            }
        } catch (unlinkError) {
            log.warn(`[媒体下载服务] 清理失败文件时出错: ${savePath}`, unlinkError.message);
        }
        
        return false;
    }
}

/**
 * 批量下载多个文件
 * @param {Array<{url: string, savePath: string}>} downloads - 下载任务数组
 * @param {number} concurrency - 并发数量，默认为3
 * @returns {Promise<{success: number, failed: number, results: Array}>}
 */
async function downloadMultipleFiles(downloads, concurrency = 3) {
    if (!downloads || downloads.length === 0) {
        return { success: 0, failed: 0, results: [] };
    }

    log.info(`[媒体下载服务] 开始批量下载 ${downloads.length} 个文件，并发数: ${concurrency}`);

    const results = [];
    let successCount = 0;
    let failedCount = 0;

    // 分批处理，控制并发数
    for (let i = 0; i < downloads.length; i += concurrency) {
        const batch = downloads.slice(i, i + concurrency);
        
        const batchPromises = batch.map(async (download, index) => {
            const result = await downloadFile(download.url, download.savePath);
            return {
                url: download.url,
                savePath: download.savePath,
                success: result,
                index: i + index
            };
        });

        const batchResults = await Promise.all(batchPromises);
        
        batchResults.forEach(result => {
            results.push(result);
            if (result.success) {
                successCount++;
            } else {
                failedCount++;
            }
        });

        // 在批次之间添加小延迟，避免过于频繁的请求
        if (i + concurrency < downloads.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    log.info(`[媒体下载服务] 批量下载完成: 成功 ${successCount}, 失败 ${failedCount}`);
    
    return {
        success: successCount,
        failed: failedCount,
        results: results
    };
}

/**
 * 验证 URL 是否有效
 * @param {string} url - 要验证的 URL
 * @returns {boolean} - URL 是否有效
 */
function isValidUrl(url) {
    if (!url || typeof url !== 'string') {
        return false;
    }
    
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch (error) {
        return false;
    }
}

/**
 * 获取文件扩展名（从 URL 或 Content-Type 推断）
 * @param {string} url - 文件 URL
 * @param {string} contentType - HTTP Content-Type 头
 * @returns {string} - 文件扩展名（包含点号）
 */
function getFileExtension(url, contentType) {
    // 首先尝试从 URL 获取扩展名
    try {
        const urlPath = new URL(url).pathname;
        const ext = path.extname(urlPath);
        if (ext) {
            return ext.toLowerCase();
        }
    } catch (error) {
        // URL 解析失败，继续尝试其他方法
    }

    // 从 Content-Type 推断扩展名
    if (contentType) {
        const mimeToExt = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp',
            'video/mp4': '.mp4',
            'video/webm': '.webm',
            'video/avi': '.avi'
        };
        
        const ext = mimeToExt[contentType.toLowerCase()];
        if (ext) {
            return ext;
        }
    }

    // 默认返回 .jpg
    return '.jpg';
}

/**
 * 判断是否应该覆盖现有文件（版本优先逻辑）
 * @param {string} url - 新文件的URL
 * @param {string} existingFilePath - 现有文件路径
 * @returns {Promise<boolean>} 是否应该覆盖
 */
async function shouldOverwriteExistingFile(url, existingFilePath) {
    try {
        // 获取现有文件的信息
        const stats = fs.statSync(existingFilePath);
        const fileAge = Date.now() - stats.mtime.getTime();
        const fileSizeKB = stats.size / 1024;

        // 版本优先规则：

        // 1. 如果现有文件很小（可能是损坏的），则覆盖
        if (fileSizeKB < 1) {
            log.debug(`[媒体下载服务] 现有文件太小 (${fileSizeKB.toFixed(2)} KB)，将覆盖`);
            return true;
        }

        // 2. 如果现有文件很旧（超过30天），则尝试更新
        const thirtyDaysMs = 30 * 24 * 60 * 60 * 1000;
        if (fileAge > thirtyDaysMs) {
            log.debug(`[媒体下载服务] 现有文件较旧 (${Math.floor(fileAge / (24 * 60 * 60 * 1000))} 天)，将尝试更新`);
            return true;
        }

        // 3. 对于图片文件，如果现有文件小于100KB，可能是低质量版本
        const ext = path.extname(existingFilePath).toLowerCase();
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.bmp'];
        if (imageExtensions.includes(ext) && fileSizeKB < 100) {
            log.debug(`[媒体下载服务] 现有图片文件质量可能较低 (${fileSizeKB.toFixed(2)} KB)，将尝试更新`);
            return true;
        }

        // 4. 默认保留现有文件
        log.debug(`[媒体下载服务] 现有文件状态良好，保留不覆盖`);
        return false;

    } catch (error) {
        log.error(`[媒体下载服务] 检查文件版本优先逻辑失败: ${existingFilePath}`, error.message);
        // 出错时保守处理，不覆盖
        return false;
    }
}

module.exports = {
    downloadFile,
    downloadMultipleFiles,
    isValidUrl,
    getFileExtension,
    shouldOverwriteExistingFile
};
