import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LuHeart, LuStar, LuUser, LuBuilding, LuTag, LuCalendar } from 'react-icons/lu';

interface FavoriteItem {
  id: string;
  type: 'actor' | 'studio' | 'series' | 'director' | 'tag' | 'genre';
  value: string;
  count: number;
  lastAdded: string;
}

const FavoritesPage: React.FC = () => {
  const navigate = useNavigate();
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchFavorites = async () => {
      try {
        setIsLoading(true);
        console.log('开始获取收藏夹数据...');

        const result = await window.sfeElectronAPI.getFavorites();
        console.log('收藏夹数据获取结果:', result);

        if (result.success && result.favorites) {
          // 转换数据格式以匹配界面需求
          const formattedFavorites: FavoriteItem[] = result.favorites.map((fav: any, index: number) => ({
            id: `${index + 1}`,
            type: fav.itemType as 'actor' | 'studio' | 'series' | 'director' | 'tag' | 'genre',
            value: fav.itemValue,
            count: fav.count || 1,
            lastAdded: fav.lastAdded || new Date().toISOString().split('T')[0]
          }));

          setFavorites(formattedFavorites);
          console.log(`成功加载 ${formattedFavorites.length} 个收藏项`);
        } else {
          console.warn('收藏夹数据为空或获取失败:', result.error);
          setFavorites([]);
        }
      } catch (error) {
        console.error('获取收藏夹失败:', error);
        setFavorites([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFavorites();
  }, []);

  const getIcon = (type: string) => {
    switch (type) {
      case 'actor': return <LuUser className="h-5 w-5" />;
      case 'studio': return <LuBuilding className="h-5 w-5" />;
      case 'series': return <LuStar className="h-5 w-5" />;
      case 'director': return <LuUser className="h-5 w-5" />;
      case 'tag': return <LuTag className="h-5 w-5" />;
      case 'genre': return <LuTag className="h-5 w-5" />;
      default: return <LuHeart className="h-5 w-5" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'actor': return '演员';
      case 'studio': return '制作商';
      case 'series': return '系列';
      case 'director': return '导演';
      case 'tag': return '标签';
      case 'genre': return '类型';
      default: return '未知';
    }
  };

  const handleFavoriteClick = (favorite: FavoriteItem) => {
    // 导航到影片墙并应用筛选
    navigate(`/library?filter=${favorite.type}&value=${encodeURIComponent(favorite.value)}`);
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
            <LuHeart className="h-8 w-8 text-red-500" />
            收藏夹
          </h1>
          <p className="text-gray-400">管理您收藏的演员、制作商、系列等</p>
        </div>

        {/* 收藏夹内容 */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-700 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-700 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-700 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        ) : favorites.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {favorites.map((favorite) => (
              <div
                key={favorite.id}
                onClick={() => handleFavoriteClick(favorite)}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-[#B8860B] transition-all duration-200 cursor-pointer hover:scale-105 hover:shadow-xl"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-[#B8860B]/20 rounded-lg text-[#B8860B]">
                    {getIcon(favorite.type)}
                  </div>
                  <div>
                    <span className="text-xs text-gray-400 uppercase tracking-wide">
                      {getTypeLabel(favorite.type)}
                    </span>
                    <h3 className="text-white font-semibold">{favorite.value}</h3>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">影片数量</span>
                    <span className="text-white font-medium">{favorite.count}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">最后添加</span>
                    <span className="text-gray-300 text-sm">{favorite.lastAdded}</span>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <button className="w-full text-[#B8860B] text-sm font-medium hover:text-amber-400 transition-colors">
                    查看相关影片 →
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <LuHeart className="h-16 w-16 mx-auto mb-4 text-gray-600" />
            <h3 className="text-xl font-semibold text-white mb-2">暂无收藏</h3>
            <p className="text-gray-400 mb-6">开始收藏您喜欢的演员、制作商或系列</p>
            <button
              onClick={() => navigate('/library')}
              className="px-6 py-3 bg-[#B8860B] text-white rounded-lg hover:bg-amber-600 transition-colors"
            >
              浏览影片库
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;
