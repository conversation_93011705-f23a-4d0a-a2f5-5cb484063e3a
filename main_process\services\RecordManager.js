/**
 * 记录管理器 - 专门处理数据记录相关功能
 * 
 * 从 collectorService.js 中提取的数据记录相关逻辑，包括：
 * - 数据库操作
 * - 记录状态更新
 * - 数据验证
 * - 结果保存
 * - 自动导出
 */

const path = require('path');
const fs = require('fs');

class RecordManager {
  constructor(config) {
    this.log = config.log;
    this.databaseService = config.databaseService;
    this.workspacePath = config.workspacePath;
    this.updateTaskStatus = config.updateTaskStatus;
  }

  /**
   * 设置工作区路径
   */
  setWorkspacePath(workspacePath) {
    this.workspacePath = workspacePath;
  }

  /**
   * 保存结果到数据库
   * @param {Array} results - 搜集结果数组
   * @param {Object} siteProfile - 站点配置
   * @returns {Object} 保存结果
   */
  async saveResults(results, siteProfile) {
    let dbResult = null;
    
    if (!results || results.length === 0) {
      this.log.warn('[RecordManager] 没有数据需要保存');
      return {
        success: true,
        message: '没有数据需要保存',
        savedCount: 0,
        dbResult: null
      };
    }

    this.updateTaskStatus('saving', '正在保存数据到数据库...');

    try {
      // 数据验证
      const validResults = this.validateResults(results);
      if (validResults.length === 0) {
        this.log.warn('[RecordManager] 所有数据验证失败，没有有效数据保存');
        return {
          success: false,
          error: '所有数据验证失败',
          savedCount: 0,
          dbResult: null
        };
      }

      if (validResults.length < results.length) {
        this.log.warn(`[RecordManager] ${results.length - validResults.length} 条数据验证失败，将保存 ${validResults.length} 条有效数据`);
      }

      // 保存到数据库
      dbResult = await this.databaseService.insertCollectedLinks(validResults, siteProfile.key);

      if (dbResult && dbResult.success) {
        this.log.info(`[RecordManager] ✅ 数据保存成功: ${validResults.length} 条记录`);
        return {
          success: true,
          message: `成功保存 ${validResults.length} 条记录`,
          savedCount: validResults.length,
          dbResult: dbResult
        };
      } else {
        const errorMsg = dbResult ? dbResult.error : '未知数据库错误';
        this.log.error(`[RecordManager] ❌ 数据保存失败: ${errorMsg}`);
        return {
          success: false,
          error: errorMsg,
          savedCount: 0,
          dbResult: dbResult
        };
      }

    } catch (error) {
      this.log.error(`[RecordManager] 保存数据时发生异常: ${error.message}`);
      return {
        success: false,
        error: error.message,
        savedCount: 0,
        dbResult: null
      };
    }
  }

  /**
   * 验证结果数据
   * @param {Array} results - 原始结果数组
   * @returns {Array} 验证后的有效结果数组
   */
  validateResults(results) {
    if (!Array.isArray(results)) {
      this.log.error('[RecordManager] 结果数据不是数组');
      return [];
    }

    const validResults = [];

    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const validation = this.validateSingleResult(result, i);
      
      if (validation.valid) {
        validResults.push(validation.data);
      } else {
        this.log.warn(`[RecordManager] 第 ${i + 1} 条数据验证失败: ${validation.error}`);
      }
    }

    return validResults;
  }

  /**
   * 验证单条结果数据
   * @param {Object} result - 单条结果数据
   * @param {number} index - 数据索引
   * @returns {Object} 验证结果
   */
  validateSingleResult(result, index) {
    if (!result || typeof result !== 'object') {
      return {
        valid: false,
        error: '数据不是对象',
        data: null
      };
    }

    // 必需字段检查
    const requiredFields = ['postUrl', 'postTitle'];
    for (const field of requiredFields) {
      if (!result[field] || typeof result[field] !== 'string' || result[field].trim() === '') {
        return {
          valid: false,
          error: `缺少必需字段: ${field}`,
          data: null
        };
      }
    }

    // URL格式验证
    try {
      new URL(result.postUrl);
    } catch (error) {
      return {
        valid: false,
        error: 'postUrl格式无效',
        data: null
      };
    }

    // 数据清理和标准化
    const cleanedResult = {
      postUrl: result.postUrl.trim(),
      postTitle: result.postTitle.trim(),
      nfoId: result.nfoId || null,
      magnetLink: this.cleanLinkData(result.magnetLink),
      ed2kLink: this.cleanLinkData(result.ed2kLink),
      attachmentUrl: this.cleanLinkData(result.attachmentUrl),
      decompressionPassword: result.decompressionPassword || null,
      previewImageUrl: result.previewImageUrl || null,
      postDate: result.postDate || null,
      collectionDate: result.collectionDate || new Date().toISOString(),
      downloadStatus: result.downloadStatus || 'pending',
      errorMessage: result.errorMessage || null,
      downloadPath: result.downloadPath || null,
      status: result.status || 'normal',
      boardInfo: result.boardInfo || null,
      fileSize: result.fileSize || null,
      performers: result.performers || null,
      studio: result.studio || null,
      duration: result.duration || null,
      cloudLinks: Array.isArray(result.cloudLinks) ? result.cloudLinks : [],
      postBodyText: result.postBodyText || null
    };

    return {
      valid: true,
      error: null,
      data: cleanedResult
    };
  }

  /**
   * 清理链接数据
   * @param {string} linkData - 原始链接数据
   * @returns {string} 清理后的链接数据
   */
  cleanLinkData(linkData) {
    if (!linkData || typeof linkData !== 'string') {
      return null;
    }

    const cleaned = linkData.trim();
    return cleaned === '' ? null : cleaned;
  }

  /**
   * 自动导出搜集结果
   * @param {Array} results - 搜集结果
   * @param {Object} siteProfile - 站点配置
   * @param {Object} taskInfo - 任务信息
   * @returns {string|null} 导出文件路径
   */
  async autoExportResults(results, siteProfile, taskInfo) {
    if (!this.workspacePath) {
      this.log.warn('[RecordManager] 工作区路径未设置，跳过自动导出');
      return null;
    }

    try {
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
      const fileName = `搜集日志_${siteProfile.name || 'Unknown'}_${dateStr}_${timeStr}.md`;
      const filePath = path.join(this.workspacePath, fileName);

      // 生成导出内容
      const content = this.generateExportContent(results, siteProfile, taskInfo, now);

      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');

      this.log.info(`[RecordManager] 自动导出完成: ${filePath}`);
      this.updateTaskStatus('export-completed', `搜集日志已自动导出: ${fileName}`);

      return filePath;

    } catch (error) {
      this.log.error(`[RecordManager] 自动导出失败: ${error.message}`);
      this.updateTaskStatus('export-failed', `自动导出失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 生成导出内容
   * @param {Array} results - 搜集结果
   * @param {Object} siteProfile - 站点配置
   * @param {Object} taskInfo - 任务信息
   * @param {Date} exportTime - 导出时间
   * @returns {string} 导出内容
   */
  generateExportContent(results, siteProfile, taskInfo, exportTime) {
    let content = `# ${siteProfile.name || 'Unknown'} 搜集日志\n\n`;

    // 任务信息
    content += `## 任务信息\n\n`;
    content += `- **论坛**: ${siteProfile.name || 'Unknown'}\n`;
    content += `- **目标URL**: ${taskInfo.targetUrl || 'Unknown'}\n`;
    content += `- **开始时间**: ${taskInfo.startTime ? new Date(taskInfo.startTime).toLocaleString('zh-CN') : 'Unknown'}\n`;
    content += `- **结束时间**: ${taskInfo.endTime ? new Date(taskInfo.endTime).toLocaleString('zh-CN') : 'Unknown'}\n`;
    content += `- **处理页数**: ${taskInfo.pages || 'Unknown'}\n`;
    content += `- **任务状态**: ${taskInfo.status || 'Unknown'}\n\n`;

    // 统计信息
    content += `## 搜集统计\n\n`;
    content += `- **总计帖子**: ${results.length} 个\n`;

    const withMagnet = results.filter(r => r.magnetLink).length;
    const withEd2k = results.filter(r => r.ed2kLink).length;
    const withAttachment = results.filter(r => r.attachmentUrl).length;
    const withPassword = results.filter(r => r.decompressionPassword).length;
    const downloadCompleted = results.filter(r => r.downloadStatus === 'completed').length;

    content += `- **包含磁力链接**: ${withMagnet} 个\n`;
    content += `- **包含ED2K链接**: ${withEd2k} 个\n`;
    content += `- **包含附件**: ${withAttachment} 个\n`;
    content += `- **包含解压密码**: ${withPassword} 个\n`;
    content += `- **下载完成**: ${downloadCompleted} 个\n\n`;

    // 详细记录
    content += `## 详细记录\n\n`;

    results.forEach((result, index) => {
      content += `### ${index + 1}. ${result.postTitle}\n\n`;
      content += `- **帖子链接**: ${result.postUrl}\n`;
      
      if (result.nfoId) {
        content += `- **NFO ID**: ${result.nfoId}\n`;
      }
      
      if (result.magnetLink) {
        content += `- **磁力链接**: \`${result.magnetLink}\`\n`;
      }
      
      if (result.ed2kLink) {
        content += `- **ED2K链接**: \`${result.ed2kLink}\`\n`;
      }
      
      if (result.attachmentUrl) {
        content += `- **附件链接**: ${result.attachmentUrl}\n`;
      }
      
      if (result.decompressionPassword) {
        content += `- **解压密码**: \`${result.decompressionPassword}\`\n`;
      }
      
      if (result.downloadStatus) {
        content += `- **下载状态**: ${result.downloadStatus}\n`;
      }
      
      if (result.downloadPath) {
        content += `- **下载路径**: ${result.downloadPath}\n`;
      }
      
      if (result.postDate) {
        content += `- **发布日期**: ${result.postDate}\n`;
      }

      // 网盘链接
      if (result.cloudLinks && result.cloudLinks.length > 0) {
        content += `- **网盘链接**:\n`;
        result.cloudLinks.forEach((link, linkIndex) => {
          content += `  ${linkIndex + 1}. ${link.url} (提取码: ${link.code || '无'})\n`;
        });
      }

      content += `\n`;
    });

    // 导出信息
    content += `${'='.repeat(80)}\n`;
    content += `**导出时间**: ${exportTime.toLocaleString('zh-CN')}\n`;
    content += `**导出工具**: SoulForge v1.0\n`;

    return content;
  }

  /**
   * 更新下载状态
   * @param {string} postUrl - 帖子URL
   * @param {string} status - 下载状态
   * @param {string} filePath - 文件路径
   * @param {string} errorMessage - 错误信息
   */
  updateDownloadStatus(postUrl, status, filePath = null, errorMessage = null) {
    try {
      this.databaseService.updateDownloadStatus(postUrl, status, filePath, errorMessage);
      this.log.info(`[RecordManager] 更新下载状态: ${postUrl} -> ${status}`);
    } catch (error) {
      this.log.error(`[RecordManager] 更新下载状态失败: ${error.message}`);
    }
  }

  /**
   * 批量更新下载状态
   * @param {Array} updates - 更新列表 [{postUrl, status, filePath, errorMessage}]
   */
  batchUpdateDownloadStatus(updates) {
    let successCount = 0;
    let failedCount = 0;

    for (const update of updates) {
      try {
        this.updateDownloadStatus(update.postUrl, update.status, update.filePath, update.errorMessage);
        successCount++;
      } catch (error) {
        this.log.error(`[RecordManager] 批量更新失败: ${update.postUrl} - ${error.message}`);
        failedCount++;
      }
    }

    this.log.info(`[RecordManager] 批量更新完成: 成功 ${successCount}, 失败 ${failedCount}`);
  }

  /**
   * 生成帖子档案文件
   * @param {Object} postData - 帖子数据
   * @returns {string|null} 档案文件路径
   */
  async generatePostArchiveFile(postData) {
    if (!this.workspacePath) {
      this.log.warn('[RecordManager] 工作区路径未设置，跳过档案文件生成');
      return null;
    }

    try {
      // 按照文档要求：/[工作区]/[论坛名称]/[板块名称]/
      let boardInfo = {};
      try {
        // 处理boardInfo可能是对象或字符串的情况
        if (typeof postData.boardInfo === 'string') {
          boardInfo = JSON.parse(postData.boardInfo || '{}');
        } else if (typeof postData.boardInfo === 'object' && postData.boardInfo !== null) {
          boardInfo = postData.boardInfo;
        }
      } catch (parseError) {
        this.log.warn(`[RecordManager] boardInfo解析失败: ${parseError.message}`);
        boardInfo = {};
      }

      // 设置论坛名称，优先使用传入的sourceForum
      const forumName = postData.sourceForum || 'x1080x';
      const boardName = boardInfo.boardName || boardInfo.board_name || 'unknown';

      const archiveDir = path.join(this.workspacePath, forumName, boardName);
      if (!fs.existsSync(archiveDir)) {
        fs.mkdirSync(archiveDir, { recursive: true });
      }

      // 生成标准化文件名
      const standardFileName = this.generateStandardFileName(postData);
      const filePath = path.join(archiveDir, `${standardFileName}.md`);

      // 生成档案内容
      const content = this.generateArchiveContent(postData);

      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');

      this.log.info(`[RecordManager] 档案文件已生成: ${filePath}`);
      return filePath;

    } catch (error) {
      this.log.error(`[RecordManager] 生成档案文件失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 生成标准化文件名
   * @param {Object} postData - 帖子数据
   * @returns {string} 标准化文件名
   */
  generateStandardFileName(postData) {
    try {
      const title = postData.postTitle || 'untitled';

      // 提取番号（如果有）
      const nfoId = this.extractNfoId(title);

      // 清理标题 - 🔧 简单修复：去掉论坛后缀
      let cleanedTitle = this.cleanPostTitle(title, nfoId);

      // 🔧 简单修复：移除常见的论坛后缀 - 使用更宽松的匹配
      cleanedTitle = cleanedTitle
        .replace(/ - 4K超清 - x1080x\.com - Powered by Discuz!?$/i, '')
        .replace(/ - 高清有码 - x1080x\.com - Powered by Discuz!?$/i, '')
        .replace(/ - x1080x\.com - Powered by Discuz!?$/i, '')
        .replace(/ - Powered by Discuz!?$/i, '')
        .replace(/ - 4K超清$/i, '')
        .replace(/ - 高清有码$/i, '')
        .trim();

      // 检查是否有解压密码
      const hasPassword = postData.decompressionPassword && postData.decompressionPassword.trim();

      // 构建文件名
      let fileName = '';
      if (nfoId) {
        fileName = `[${nfoId}] ${cleanedTitle}`;
      } else {
        fileName = cleanedTitle;
      }

      if (hasPassword) {
        fileName += '[PW]';
      }

      // 清理文件名中的非法字符
      return this.sanitizeFileName(fileName);

    } catch (error) {
      this.log.warn(`[RecordManager] 生成标准化文件名失败: ${error.message}`);
      return this.sanitizeFileName(postData.postTitle || 'untitled');
    }
  }

  /**
   * 提取番号
   * @param {string} title - 帖子标题
   * @returns {string|null} 番号
   */
  extractNfoId(title) {
    // 常见番号格式匹配
    const patterns = [
      /([A-Z]{2,6}-\d{3,4})/i,  // ABC-123, ABCD-1234
      /([A-Z]{2,6}\d{3,4})/i,   // ABC123, ABCD1234
      /(\d{6}-\d{3})/,          // 123456-789
    ];

    for (const pattern of patterns) {
      const match = title.match(pattern);
      if (match) {
        return match[1].toUpperCase();
      }
    }

    return null;
  }

  /**
   * 清理帖子标题
   * @param {string} title - 原始标题
   * @param {string} nfoId - 番号
   * @returns {string} 清理后的标题
   */
  cleanPostTitle(title, nfoId) {
    let cleaned = title;

    // 移除番号
    if (nfoId) {
      const nfoPattern = new RegExp(nfoId.replace(/[-]/g, '[-]?'), 'gi');
      cleaned = cleaned.replace(nfoPattern, '');
    }

    // 移除常见的板块标识
    const boardPatterns = [
      /\[.*?\]/g,  // 移除方括号内容
      /【.*?】/g,  // 移除中文方括号内容
      /\(.*?\)/g,  // 移除圆括号内容
      /（.*?）/g,  // 移除中文圆括号内容
    ];

    for (const pattern of boardPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }

    // 清理多余的空格和特殊字符
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    cleaned = cleaned.replace(/^[-\s]+|[-\s]+$/g, '');

    return cleaned || 'untitled';
  }

  /**
   * 生成档案内容
   * @param {Object} postData - 帖子数据
   * @returns {string} 档案内容
   */
  generateArchiveContent(postData) {
    // YAML Front Matter 头部 - 按照要求添加关键元数据
    let content = '---\n';
    content += `title: "${(postData.postTitle || '').replace(/"/g, '\\"')}"\n`;
    content += `url: "${postData.postUrl || ''}"\n`;
    content += `collection_date: "${postData.collectionDate || new Date().toISOString()}"\n`;

    // 2.2. 帖子发布日期 (Post Date)
    if (postData.postDate) {
      content += `post_date: "${postData.postDate}"\n`;
    }

    // 2.1. 帖子标题已在title中
    if (postData.nfoId) {
      content += `nfo_id: "${postData.nfoId}"\n`;
    }

    // 2.3. 预览图 (Preview Image)
    if (postData.previewImageUrl) {
      content += `preview_image: "${postData.previewImageUrl.replace(/"/g, '\\"')}"\n`;
    }

    // 2.4. 文件大小 (File Size)
    if (postData.fileSize) {
      content += `file_size: "${postData.fileSize}"\n`;
    }

    // 2.5. 解压密码 (Password)
    if (postData.decompressionPassword) {
      content += `decompression_password: "${postData.decompressionPassword}"\n`;
    }

    // 2.6. 下载链接 (Download Links)
    if (postData.magnetLink) {
      content += `magnet_link: "${postData.magnetLink.replace(/"/g, '\\"')}"\n`;
    }

    if (postData.ed2kLink) {
      content += `ed2k_link: "${postData.ed2kLink.replace(/"/g, '\\"')}"\n`;
    }

    if (postData.attachmentUrl) {
      content += `attachment_url: "${postData.attachmentUrl.replace(/"/g, '\\"')}"\n`;
    }

    if (postData.downloadStatus) {
      content += `download_status: "${postData.downloadStatus}"\n`;
    }

    if (postData.downloadPath) {
      content += `download_path: "${postData.downloadPath.replace(/"/g, '\\"')}"\n`;
    }

    if (postData.boardInfo) {
      let boardInfo = {};
      try {
        // 处理boardInfo可能是对象或字符串的情况
        if (typeof postData.boardInfo === 'string') {
          boardInfo = JSON.parse(postData.boardInfo || '{}');
        } else if (typeof postData.boardInfo === 'object' && postData.boardInfo !== null) {
          boardInfo = postData.boardInfo;
        }
      } catch (parseError) {
        this.log.warn(`[RecordManager] generateArchiveContent boardInfo解析失败: ${parseError.message}`);
        boardInfo = {};
      }
      content += `board_id: "${boardInfo.boardId || boardInfo.board_id || ''}"\n`;
      content += `board_name: "${boardInfo.boardName || boardInfo.board_name || ''}"\n`;
    }

    if (postData.cloudLinks && postData.cloudLinks.length > 0) {
      content += 'cloud_links:\n';
      postData.cloudLinks.forEach(link => {
        content += `  - url: "${link.url || ''}"\n`;
        content += `    password: "${link.password || ''}"\n`;
      });
    }

    content += '---\n\n';

    // 主体部分：顶楼发帖者的全部内容（纯文本格式）
    content += '# 帖子内容\n\n';
    if (postData.fullPostText || postData.postBodyText) {
      content += '## 正文内容\n\n';
      content += (postData.fullPostText || postData.postBodyText || '无内容');
      content += '\n\n';
    }

    // 如果需要保留原始HTML用于调试
    if (postData.fullPostHtml) {
      content += '## 原始HTML（调试用）\n\n';
      content += '```html\n';
      content += postData.fullPostHtml;
      content += '\n```\n\n';
    }

    if (postData.cloudLinks && postData.cloudLinks.length > 0) {
      content += `### 网盘链接\n`;
      postData.cloudLinks.forEach((link, index) => {
        content += `${index + 1}. ${link.url} (提取码: ${link.code || '无'})\n`;
      });
      content += `\n`;
    }

    if (postData.downloadStatus) {
      content += `## 下载信息\n\n`;
      content += `- **下载状态**: ${postData.downloadStatus}\n`;
      
      if (postData.downloadPath) {
        content += `- **下载路径**: ${postData.downloadPath}\n`;
      }
      
      if (postData.errorMessage) {
        content += `- **错误信息**: ${postData.errorMessage}\n`;
      }
    }

    return content;
  }

  /**
   * 清理文件名中的非法字符
   * @param {string} fileName - 原始文件名
   * @returns {string} 清理后的文件名
   */
  sanitizeFileName(fileName) {
    if (!fileName) {
      return `unnamed_${Date.now()}`;
    }

    return fileName
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/[\x00-\x1f\x80-\x9f]/g, '')
      .replace(/^\.+/, '')
      .replace(/\.+$/, '')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 100); // 限制长度
  }
}

module.exports = RecordManager;
