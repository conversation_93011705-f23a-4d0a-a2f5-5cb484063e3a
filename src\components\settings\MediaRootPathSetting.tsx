import React, { useState, useEffect } from 'react';
import { FolderOpen, Check, AlertCircle } from 'lucide-react';

interface MediaRootPathSettingProps {
  className?: string;
}

export function MediaRootPathSetting({ className = '' }: MediaRootPathSettingProps) {
  const [currentPath, setCurrentPath] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
    timestamp: number;
  } | null>(null);

  // 加载当前设置
  useEffect(() => {
    loadCurrentPath();
  }, []);

  const loadCurrentPath = async () => {
    try {
      const settings = await window.sfeElectronAPI.getSettings();
      setCurrentPath(settings.mediaAssetsRootPath || '');
    } catch (error) {
      console.error('加载媒体资产根目录设置失败:', error);
      setSaveStatus({
        type: 'error',
        message: '加载设置失败',
        timestamp: Date.now()
      });
    }
  };

  const handleBrowseFolder = async () => {
    setIsLoading(true);
    try {
      const selectedPath = await window.sfeElectronAPI.dialogSelectDirectory();
      
      if (selectedPath) {
        // 保存新路径
        const settings = await window.sfeElectronAPI.getSettings();
        const updatedSettings = {
          ...settings,
          mediaAssetsRootPath: selectedPath
        };
        
        const result = await window.sfeElectronAPI.saveSettings(updatedSettings);
        
        if (result.success) {
          setCurrentPath(selectedPath);
          setSaveStatus({
            type: 'success',
            message: '媒体资产根目录已更新',
            timestamp: Date.now()
          });
        } else {
          setSaveStatus({
            type: 'error',
            message: result.error || '保存设置失败',
            timestamp: Date.now()
          });
        }
      }
    } catch (error) {
      console.error('选择文件夹失败:', error);
      setSaveStatus({
        type: 'error',
        message: '选择文件夹失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 自动清除状态消息
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 标题和说明 */}
      <div>
        <h3 className="text-lg font-medium text-white mb-2">媒体资产主目录 (元数据层)</h3>
        <p className="text-sm text-gray-400 mb-4">
          所有由 SoulForge 自动下载的封面、预览图、NFO 和 .meta.json 等轻量级元数据文件，都将按照标准结构存放在此目录。
        </p>
      </div>

      {/* 路径显示和选择 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          当前路径
        </label>
        
        <div className="flex items-center gap-3">
          <input
            type="text"
            value={currentPath}
            readOnly
            placeholder="未设置媒体资产根目录"
            className="flex-1 px-3 py-2 bg-[#1a1a1a] border border-[#444] rounded text-white placeholder-gray-500 focus:border-[#B8860B] focus:outline-none"
          />
          
          <button
            onClick={handleBrowseFolder}
            disabled={isLoading}
            className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[100px] justify-center"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
                选择中...
              </>
            ) : (
              <>
                <FolderOpen className="h-4 w-4" />
                浏览...
              </>
            )}
          </button>
        </div>
      </div>

      {/* 状态反馈 */}
      {saveStatus && (
        <div className={`p-3 rounded-lg flex items-center gap-2 ${
          saveStatus.type === 'success' 
            ? 'bg-green-900/20 border border-green-500/30' 
            : 'bg-red-900/20 border border-red-500/30'
        }`}>
          {saveStatus.type === 'success' ? (
            <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
          )}
          <span className={`text-sm ${
            saveStatus.type === 'success' ? 'text-green-300' : 'text-red-300'
          }`}>
            {saveStatus.message}
          </span>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-[#1a1a1a] border border-[#333] rounded-lg">
        <h4 className="text-sm font-medium text-white mb-2">目录结构说明</h4>
        <div className="text-xs text-gray-400 space-y-1">
          <p>系统将在选定的根目录下自动创建以下结构：</p>
          <div className="ml-4 space-y-1 font-mono">
            <p>├── jav_censored/    # 有码影片</p>
            <p>├── jav_uncensored/  # 无码影片</p>
            <p>├── jav_vr/         # VR 影片</p>
            <p>├── chinese/        # 国产影片</p>
            <p>├── western/        # 欧美影片</p>
            <p>└── uncategorized/  # 未分类影片</p>
          </div>
          <p className="mt-2">每个分类下会按番号前缀进一步分组，如：jav_censored/JUR/JUR-001/</p>
        </div>
      </div>
    </div>
  );
}
