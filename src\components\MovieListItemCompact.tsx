
import React from 'react';
import { Movie } from '../types';
import ImageWithFallback from './ImageWithFallback';
import PlayIcon from './PlayIcon'; // Placeholder for thumbnail

interface MovieListItemCompactProps {
  movie: Movie;
  onCardClick?: (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => void;
  appDefaultCover?: string | null;
}

const MovieListItemCompact: React.FC<MovieListItemCompactProps> = ({ movie, onCardClick, appDefaultCover }) => {
  const displayTitle = movie.title || movie.fileName;
  const isMultiVersion = (movie.versionCount || 0) > 1;
  const isMultiCD = (movie.multiCdCountForNfoId || 0) > 1;

  const thumbnailPlaceholder = (
    <div className="w-full h-full flex items-center justify-center bg-[#333333] text-neutral-500">
      <PlayIcon className="w-5 h-5 opacity-50" />
    </div>
  );

  return (
    <div
      className="flex items-center p-2 bg-[#2c2c2c] rounded-md border border-[#444444] hover:bg-[#383838] transition-colors duration-150 cursor-pointer group"
      onClick={() => onCardClick?.(movie, isMultiVersion, isMultiCD)}
      aria-label={`影片: ${displayTitle}`}
    >
      <div className="flex-shrink-0 w-12 h-16 mr-3 rounded overflow-hidden border border-[#4f4f4f]">
        <ImageWithFallback
          primarySrc={movie.coverDataUrl}
          secondarySrc={movie.posterUrl}
          tertiarySrc={movie.coverUrl}
          appDefaultCoverDataUrl={appDefaultCover}
          alt={`封面: ${displayTitle}`}
          className="w-full h-full object-cover"
          placeholder={thumbnailPlaceholder}
        />
      </div>
      <div className="flex-grow overflow-hidden">
        <h3 className="text-sm font-medium text-neutral-100 group-hover:text-amber-400 truncate" title={displayTitle}>
          {displayTitle}
        </h3>
        <p className="text-xs text-neutral-400">
          {movie.year && <span>{movie.year}</span>}
          {isMultiVersion && <span className="ml-2 text-[10px] bg-black/50 px-1 py-0.5 rounded-sm">{movie.versionCount} 版本</span>}
          {isMultiCD && <span className="ml-2 text-[10px] bg-black/50 px-1 py-0.5 rounded-sm">{movie.multiCdCountForNfoId} CD</span>}
        </p>
      </div>
    </div>
  );
};

export default MovieListItemCompact;