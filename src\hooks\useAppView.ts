// soul-forge-electron/src/hooks/useAppView.ts
import { useState, useCallback } from 'react'; // Added useCallback
import { FavoriteItemType, DetailFilterType, MovieLibrary } from '../types'; 

export type AppView = 'mainWall' | 'favoritesView' | 'libraryWall' | 'collectorView' | 'ingestCenter' | 'stagingArea' | 'recycleBin' | 'intelligenceCenter';

export function useAppView(initialView: AppView = 'mainWall') {
  const [currentAppView, setCurrentAppView] = useState<AppView>(initialView);
  const [activeFilterTag, setActiveFilterTag] = useState<{ type: DetailFilterType; value: string } | null>(null);
  const [activeLibrary, setActiveLibrary] = useState<MovieLibrary | null>(null); 

  const navigateToMainWallWithFilter = useCallback((type: DetailFilterType, value: string) => {
    setActiveFilterTag({ type, value });
    setActiveLibrary(null); 
    setCurrentAppView('mainWall');
  }, []); // Dependencies for useCallback

  const navigateToLibraryWall = useCallback((library: MovieLibrary) => {
    setActiveLibrary(library);
    setActiveFilterTag(null); 
    setCurrentAppView('mainWall'); // Keep as mainWall, content will change based on activeLibrary
  }, []); // Dependencies for useCallback

  const clearActiveFilterTag = useCallback(() => {
    setActiveFilterTag(null);
  }, []); // Dependencies for useCallback
  
  const navigateToMainWall = useCallback(() => {
    setActiveLibrary(null); 
    setActiveFilterTag(null);
    setCurrentAppView('mainWall');
  }, []); // Dependencies for useCallback

  return {
    currentAppView,
    setCurrentAppView,
    activeFilterTag,
    navigateToMainWallWithFilter,
    clearActiveFilterTag,
    activeLibrary,
    setActiveLibrary, 
    navigateToLibraryWall,
    navigateToMainWall,
  };
}
