// soul-forge-electron/main_process/services/nfoPlotPolisherService.js
const fs = require('node:fs').promises; // Use promise-based fs for async operations
const path = require('node:path');

let log;
let pythonRunnerServiceInstance;
let aiServiceInstance;
let settingsServiceInstance; 
let webContentsSenderRef = null;
let globalAbortController = null; // Controller for the current NFO polishing operation


const PROCESSED_MARKER_TAG_NAME = "soulforge_plot_polished_v2_nfo_tool"; 
const BATCH_SIZE_FOR_AI_POLISHING = 20; 

function initializeNfoPlotPolisherService(logger, pythonRunner, aiService, settingsService) {
  log = logger;
  pythonRunnerServiceInstance = pythonRunner;
  aiServiceInstance = aiService;
  settingsServiceInstance = settingsService;
  log.info('[NFO剧情润色服务] 初始化。');
}

function cancelCurrentOperation() {
  if (globalAbortController) {
    log.info('[NFO剧情润色服务] 收到取消操作请求，正在中止进行中的任务。');
    globalAbortController.abort();
    if (webContentsSenderRef) {
      webContentsSenderRef.send('nfo-polish-tool:log', '操作取消请求已收到，正在尝试优雅停止...');
    }
  } else {
    log.info('[NFO剧情润色服务] 收到取消操作请求，但当前没有可中止的任务控制器。');
  }
}

async function scanDirectoriesForNfoFiles(directories, webContentsSender) {
  globalAbortController = new AbortController(); // New controller for this operation
  const abortSignal = globalAbortController.signal;
  webContentsSenderRef = webContentsSender;
  log.info(`[NFO剧情润色服务] 开始扫描目录: ${directories.join(', ')}`);
  webContentsSender.send('nfo-polish-tool:log', `正在启动NFO文件扫描...`);
  
  let allNfoFiles = [];
  let filesFoundInCurrentRootDirIteration = 0;

  for (let i = 0; i < directories.length; i++) {
    if (abortSignal.aborted) {
      log.info('[NFO剧情润色服务] 扫描操作已取消。');
      webContentsSender.send('nfo-polish-tool:log', '扫描已取消。');
      break;
    }
    const dir = directories[i];
    filesFoundInCurrentRootDirIteration = 0; 

    webContentsSender.send('nfo-polish-tool:scan-progress', {
      currentDirectory: dir,
      totalDirectories: directories.length,
      currentDirIndex: i + 1,
      nfoFilesFoundInCurrentDir: 0, 
      nfoFilesFoundTotal: allNfoFiles.length,
      status: 'scanning_dir',
    });
    webContentsSender.send('nfo-polish-tool:log', `正在扫描目录: ${dir} (${i+1}/${directories.length})`);
    
    try {
      const filesInDir = await findNfoFilesRecursive(dir, webContentsSender, dir, abortSignal, (filePath, isNfo) => {});
      if (abortSignal.aborted) {
          log.info('[NFO剧情润色服务] 目录扫描在 findNfoFilesRecursive 后被取消。');
          webContentsSender.send('nfo-polish-tool:log', `目录 ${path.basename(dir)} 的扫描已取消。`);
          break; 
      }
      allNfoFiles = allNfoFiles.concat(filesInDir);
      webContentsSender.send('nfo-polish-tool:log', `目录 ${path.basename(dir)} 扫描完成，找到 ${filesInDir.length} 个NFO文件。`);
    } catch (error) { 
       log.error(`[NFO剧情润色服务] 扫描根目录 ${dir} 时发生无法恢复的错误: ${error.message}`);
       webContentsSender.send('nfo-polish-tool:error', { filePath: dir, error: `扫描根目录 ${path.basename(dir)} 失败: ${error.message}` });
    }
  }

  const wasCancelled = abortSignal.aborted;
  if (wasCancelled) {
     log.info('[NFO剧情润色服务] 扫描操作已在完成前被用户取消。');
     webContentsSender.send('nfo-polish-tool:log', '扫描操作已被用户取消。');
  } else {
    log.info(`[NFO剧情润色服务] 所有目录扫描完成. 总共找到 ${allNfoFiles.length} 个NFO文件.`);
  }
  webContentsSender.send('nfo-polish-tool:scan-complete', {
    totalNfoFilesFound: allNfoFiles.length,
    nfoFilePaths: allNfoFiles,
    cancelled: wasCancelled 
  });
  
  if (globalAbortController && globalAbortController.signal === abortSignal) {
      globalAbortController = null; // Clear if this was the controller we managed
  }
  return allNfoFiles;
}

async function findNfoFilesRecursive(currentPath, webContentsSender, rootScanDir, abortSignal, fileScanCallback) {
  if (abortSignal.aborted) return []; 

  let nfoFiles = [];
  try {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });
    for (const entry of entries) {
      if (abortSignal.aborted) break; 

      const fullPath = path.join(currentPath, entry.name);
      if (fileScanCallback) fileScanCallback(fullPath, entry.isFile() && entry.name.toLowerCase().endsWith('.nfo'));

      if (entry.isDirectory()) {
        const subDirNfos = await findNfoFilesRecursive(fullPath, webContentsSender, rootScanDir, abortSignal, fileScanCallback);
        nfoFiles = nfoFiles.concat(subDirNfos);
      } else if (entry.isFile() && entry.name.toLowerCase().endsWith('.nfo')) {
        nfoFiles.push(fullPath);
        webContentsSender.send('nfo-polish-tool:scan-progress', {
            currentDirectory: rootScanDir, 
            nfoFilesFoundInCurrentDir: nfoFiles.filter(f => path.dirname(f).startsWith(rootScanDir)).length, 
            nfoFilesFoundTotal: nfoFiles.length, 
            status: 'finding_nfo',
            currentFileScanning: fullPath,
        });
      }
    }
  } catch (error) {
    if (!abortSignal.aborted) { // Only log/send error if not due to abort
        log.error(`[NFO剧情润色服务] 访问路径 ${currentPath} 出错: ${error.message}. 跳过此目录/文件.`);
        webContentsSender.send('nfo-polish-tool:error', { filePath: currentPath, error: `扫描子项 ${path.basename(currentPath)} 失败: ${error.message}` });
    }
    return []; 
  }
  return nfoFiles;
}


async function processBatchWithAI(batch, webContentsSender, appSettings, abortSignal) {
  if (abortSignal.aborted) {
    webContentsSender.send('nfo-polish-tool:log', `AI批量处理已取消，跳过批次。`);
    return batch.map(item => ({ ...item, error: '操作已取消', aiProcessed: true, cancelled: true }));
  }
  if (batch.length === 0) return [];

  webContentsSender.send('nfo-polish-tool:log', `开始为 ${batch.length} 个NFO文件调用AI批量润色...`);
  
  const plotsToEmbellish = batch.map(item => ({
    id: item.nfoPath, 
    title: item.nfoData.title || path.basename(item.nfoPath, '.nfo'),
    currentPlot: item.nfoData.plot_zh, 
    plotJa: item.nfoData.plot_ja || null,
    plotZh: item.nfoData.plot_zh, 
    actors: item.nfoData.actors || [],
    genres: item.nfoData.genres || [],
  }));

  const aiResults = await aiServiceInstance.embellishMultiplePlotsWithAI(
    settingsServiceInstance.getStore(),
    plotsToEmbellish,
    { requestStyle: 'grok_direct_bold_for_nfo_tool' },
    abortSignal // Pass the abort signal
  );

  if (abortSignal.aborted || aiResults.cancelled) { // Check signal again and result's cancelled flag
     webContentsSender.send('nfo-polish-tool:log', `AI批量处理在请求后被取消。`);
     return batch.map(item => ({ ...item, error: '操作已取消', aiProcessed: true, cancelled: true }));
  }

  if (!aiResults.success || !aiResults.results) {
    const errorMessage = `AI批量润色失败: ${aiResults.error || 'AI未返回有效结果。'}`;
    log.error(`[NFO剧情润色服务] ${errorMessage}`);
    webContentsSender.send('nfo-polish-tool:error', { filePath: '批量处理', error: errorMessage });
    return batch.map(item => ({ ...item, error: errorMessage, aiProcessed: true }));
  }

  const processedBatch = batch.map(item => {
    const aiResultForItem = aiResults.results.find(res => res.id === item.nfoPath);
    if (aiResultForItem && aiResultForItem.polishedPlot && !aiResultForItem.error) {
      return { ...item, newPlotZhContent: aiResultForItem.polishedPlot, error: null, aiProcessed: true };
    } else if (aiResultForItem && aiResultForItem.error) {
      const itemError = `AI润色时返回错误 for ${path.basename(item.nfoPath)}: ${aiResultForItem.error}`;
      log.warn(`[NFO剧情润色服务] ${itemError}`);
      webContentsSender.send('nfo-polish-tool:error', { filePath: item.nfoPath, error: itemError });
      return { ...item, error: itemError, aiProcessed: true };
    }
    const defaultError = `AI未返回 ${path.basename(item.nfoPath)} 的润色结果。`;
    log.warn(`[NFO剧情润色服务] ${defaultError}`);
    return { ...item, error: defaultError, aiProcessed: true };
  });
  webContentsSender.send('nfo-polish-tool:log', `AI批量润色完成 ${batch.length} 个文件。`);
  return processedBatch;
}


async function processNfoFiles(nfoFilePaths, webContentsSender) {
  globalAbortController = new AbortController(); // New controller for this operation
  const abortSignal = globalAbortController.signal;
  webContentsSenderRef = webContentsSender;
  log.info(`[NFO剧情润色服务] 开始处理 ${nfoFilePaths.length} 个NFO文件.`);
  webContentsSender.send('nfo-polish-tool:log', `准备开始润色 ${nfoFilePaths.length} 个NFO文件...`);

  let totalFilesProcessedCounter = 0; 
  let totalSuccessfullyUpdated = 0;
  let totalSkippedAsProcessedMarker = 0;
  let totalSkippedForOtherReasons = 0; 
  let totalErrors = 0;
  
  const appSettings = settingsServiceInstance.getSettings();
  let currentBatch = [];

  for (let i = 0; i < nfoFilePaths.length; i++) {
    if (abortSignal.aborted) {
      log.info('[NFO剧情润色服务] 文件处理操作已取消。');
      webContentsSender.send('nfo-polish-tool:log', '文件处理已取消。');
      break;
    }
    const nfoPath = nfoFilePaths[i];
    totalFilesProcessedCounter++;
    webContentsSender.send('nfo-polish-tool:process-progress', {
      totalFilesToProcess: nfoFilePaths.length,
      filesProcessedSoFar: totalFilesProcessedCounter -1,
      currentNfoFileProcessing: path.basename(nfoPath),
      statusMessage: `检查文件 ${totalFilesProcessedCounter}/${nfoFilePaths.length}: ${path.basename(nfoPath)}`,
    });

    try {
      const markerCheckResult = await pythonRunnerServiceInstance.runPythonScript(
        'nfo_utils.py',
        ['check_marker', nfoPath],
        appSettings.pythonExecutablePath
      );
      let isAlreadyProcessedByMarker = false;
      if (markerCheckResult.success && markerCheckResult.data) {
        const markerData = JSON.parse(markerCheckResult.data);
        if (markerData.success && markerData.is_processed) {
          isAlreadyProcessedByMarker = true;
        } else if (!markerData.success) {
            log.warn(`[NFO剧情润色服务] 检查NFO标记时Python脚本报告错误 for ${nfoPath}: ${markerData.error}. 将尝试处理.`);
        }
      } else if (!markerCheckResult.success) {
         log.warn(`[NFO剧情润色服务] 检查NFO标记失败 for ${nfoPath}: ${markerCheckResult.error}. 将尝试处理.`);
      }

      if (isAlreadyProcessedByMarker) {
        log.info(`[NFO剧情润色服务] 跳过已处理的NFO文件 (标记存在): ${nfoPath}`);
        webContentsSender.send('nfo-polish-tool:log', `跳过 (标记存在): ${path.basename(nfoPath)}`);
        totalSkippedAsProcessedMarker++;
        continue;
      }
      
      const parseResult = await pythonRunnerServiceInstance.runPythonScript(
        'nfo_utils.py',
        ['parse', nfoPath],
        appSettings.pythonExecutablePath
      );

      if (!parseResult.success || !parseResult.data) {
        throw new Error(`解析NFO失败: ${parseResult.error || 'Python脚本无输出或错误.'}`);
      }
      
      const nfoData = JSON.parse(parseResult.data);
      if (!nfoData.success) {
          throw new Error(`NFO解析脚本报告错误: ${nfoData.error}`);
      }
      
      if (!nfoData.plot_zh || !nfoData.plot_zh.trim()) {
        log.warn(`[NFO剧情润色服务] 跳过 ${nfoPath} 因为中文剧情 (<originalplot>) 为空.`);
        webContentsSender.send('nfo-polish-tool:log', `跳过 (中文剧情为空): ${path.basename(nfoPath)}`);
        totalSkippedForOtherReasons++;
        continue;
      }
      
      currentBatch.push({ nfoPath, nfoData });

      if (currentBatch.length >= BATCH_SIZE_FOR_AI_POLISHING || i === nfoFilePaths.length - 1) {
        if (abortSignal.aborted) { 
            webContentsSender.send('nfo-polish-tool:log', `批处理前检测到取消请求，跳过此批次。`);
            totalErrors += currentBatch.length; 
            currentBatch = [];
            break; 
        }
        webContentsSender.send('nfo-polish-tool:log', `准备处理批次，数量: ${currentBatch.length}`);
        const processedBatchItems = await processBatchWithAI(currentBatch, webContentsSender, appSettings, abortSignal);
        
        for (const item of processedBatchItems) {
          if (abortSignal.aborted || item.cancelled) { 
              webContentsSender.send('nfo-polish-tool:log', `处理批次内项目 ${path.basename(item.nfoPath)} 时检测到取消。`);
              totalErrors++; 
              continue; 
          }
          if (item.error) { 
            totalErrors++;
            continue;
          }
          if (!item.newPlotZhContent) { 
            log.warn(`[NFO剧情润色服务] AI未返回 ${path.basename(item.nfoPath)} 的润色剧情，跳过更新。`);
            totalErrors++; 
            continue;
          }

          webContentsSender.send('nfo-polish-tool:log', `保存润色后的剧情到 ${path.basename(item.nfoPath)}...`);
          try {
            const updateResult = await pythonRunnerServiceInstance.runPythonScript(
              'nfo_utils.py',
              ['update', item.nfoPath, '--new_plot_zh', item.newPlotZhContent],
              appSettings.pythonExecutablePath
            );

            if (!updateResult.success || !updateResult.data) {
              throw new Error(`更新NFO失败: ${updateResult.error || 'Python脚本无输出或错误.'}`);
            }
            const updateData = JSON.parse(updateResult.data);
            if (!updateData.success) {
                throw new Error(`NFO更新脚本报告错误: ${updateData.error}`);
            }
            log.info(`[NFO剧情润色服务] 成功处理并更新 ${item.nfoPath}`);
            webContentsSender.send('nfo-polish-tool:log', `成功更新 ${path.basename(item.nfoPath)}`);
            totalSuccessfullyUpdated++;
          } catch(updateError) {
            log.error(`[NFO剧情润色服务] 更新NFO文件 ${item.nfoPath} 失败:`, updateError);
            webContentsSender.send('nfo-polish-tool:error', { filePath: item.nfoPath, error: `更新NFO失败: ${updateError.message}` });
            totalErrors++;
          }
        }
        currentBatch = []; 
      }

    } catch (error) {
      if (!abortSignal.aborted) {
        log.error(`[NFO剧情润色服务] 处理文件 ${nfoPath} 时出错:`, error);
        webContentsSender.send('nfo-polish-tool:error', { filePath: nfoPath, error: error.message });
        totalErrors++;
      }
    }
  } 

  const wasCancelled = abortSignal.aborted;
  const statusMessage = wasCancelled ? "操作被用户取消。" : "所有文件处理完成。";
  log.info(`[NFO剧情润色服务] ${statusMessage} 总文件数: ${nfoFilePaths.length}, 成功更新: ${totalSuccessfullyUpdated}, 跳过(标记): ${totalSkippedAsProcessedMarker}, 跳过(其他): ${totalSkippedForOtherReasons}, 错误: ${totalErrors}`);
  webContentsSender.send('nfo-polish-tool:process-complete', {
    totalFilesToProcess: nfoFilePaths.length,
    totalFilesProcessed: totalFilesProcessedCounter,
    totalSuccessfullyUpdated: totalSuccessfullyUpdated,
    totalSkipped: totalSkippedAsProcessedMarker + totalSkippedForOtherReasons,
    totalErrors: totalErrors,
    cancelled: wasCancelled,
  });
  
  if (globalAbortController && globalAbortController.signal === abortSignal) {
      globalAbortController = null; // Clear if this was the controller we managed
  }
}


module.exports = {
  initializeNfoPlotPolisherService,
  scanDirectoriesForNfoFiles,
  processNfoFiles,
  cancelCurrentOperation,
};