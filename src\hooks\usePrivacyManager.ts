
// soul-forge-electron/src/hooks/usePrivacyManager.ts
import { useState, useEffect, useCallback } from 'react';
import { PrivacyModeState } from '../types';

export function usePrivacyManager() {
  const [privacyState, setPrivacyState] = useState<PrivacyModeState>({ isEnabled: false, isLocked: false });
  const [isPrivacyUnlockModalOpen, setIsPrivacyUnlockModalOpen] = useState(false);
  const [isPrivacyUnlockedThisSession, setIsPrivacyUnlockedThisSession] = useState(false);

  const refreshPrivacyState = useCallback(async () => {
    try {
      const state = await window.sfeElectronAPI.getPrivacyModeState();
      setPrivacyState(state);
      if (state.isEnabled && state.isLocked && !isPrivacyUnlockedThisSession) {
        setIsPrivacyUnlockModalOpen(true);
      } else if (!state.isEnabled) {
        setIsPrivacyUnlockedThisSession(false); // Lock if privacy mode is disabled entirely
      }
    } catch (error) {
      console.error("Failed to refresh privacy state:", error);
      // Handle error appropriately, maybe set a default error state
    }
  }, [isPrivacyUnlockedThisSession]);

  useEffect(() => {
    refreshPrivacyState();
    const removeListener = window.sfeElectronAPI.onPrivacySettingsChanged(() => {
      refreshPrivacyState();
    });
    return removeListener;
  }, [refreshPrivacyState]);

  const attemptUnlockPrivacy = async (password: string): Promise<boolean> => {
    const result = await window.sfeElectronAPI.attemptUnlockPrivacyMode(password);
    if (result.success) {
      setIsPrivacyUnlockedThisSession(true);
      setIsPrivacyUnlockModalOpen(false);
      return true;
    }
    return false;
  };

  const handleTogglePrivacyLockIcon = async () => {
    if (privacyState.isEnabled) {
      if (privacyState.isLocked && !isPrivacyUnlockedThisSession) {
        setIsPrivacyUnlockModalOpen(true);
      } else if (privacyState.isLocked && isPrivacyUnlockedThisSession) {
        // Lock it again for the session
        setIsPrivacyUnlockedThisSession(false);
        alert("隐私模式已重新锁定。"); // Or some less obtrusive notification
      } else if (!privacyState.isLocked) { // Is enabled but has no password
        const result = await window.sfeElectronAPI.togglePrivacyModeNoPassword();
        if (!result.success) {
          alert(`切换隐私模式失败: ${result.error}`);
        }
        // refreshPrivacyState will be called by the IPC event listener
      }
    } else { // Privacy mode is currently disabled
      if (!privacyState.isLocked) { // And has no password set
         const result = await window.sfeElectronAPI.togglePrivacyModeNoPassword();
         if (!result.success) {
            alert(`启用隐私模式失败: ${result.error}`);
         }
         // refreshPrivacyState will be called by the IPC event listener
      } else {
        // Has a password but is disabled - user should enable it via settings if they want to use the password
        alert("隐私模式已设置密码，请在设置中启用。");
      }
    }
  };


  return {
    privacyState,
    isPrivacyUnlockModalOpen,
    isPrivacyUnlockedThisSession,
    refreshPrivacyState,
    attemptUnlockPrivacy,
    setIsPrivacyUnlockModalOpen,
    setIsPrivacyUnlockedThisSession,
    handleTogglePrivacyLockIcon,
  };
}
