// Node.js NFO 解析器 - 替代 Python 脚本
const fs = require('fs').promises;
const path = require('path');
const { parseString } = require('xml2js');

class NodeNfoParser {
  static async parseNfoFile(nfoPath) {
    try {
      const content = await fs.readFile(nfoPath, 'utf-8');
      return await this.parseNfoContent(content);
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async parseNfoContent(content) {
    try {
      // 清理内容
      const cleanContent = content
        .replace(/^\uFEFF/, '') // 移除 BOM
        .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;'); // 修复未转义的 &

      // 尝试 XML 解析
      const result = await this.parseXml(cleanContent);
      if (result.success) {
        return result;
      }

      // 回退到正则表达式解析
      return this.parseWithRegex(cleanContent);
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static parseXml(content) {
    return new Promise((resolve) => {
      parseString(content, { 
        explicitArray: false,
        ignoreAttrs: false,
        trim: true,
      }, (err, result) => {
        if (err) {
          resolve({ success: false, error: err.message });
          return;
        }

        try {
          const movieData = result.movie || result;
          const parsed = this.extractMovieData(movieData);
          resolve({ success: true, data: parsed });
        } catch (parseError) {
          resolve({ success: false, error: parseError.message });
        }
      });
    });
  }

  static extractMovieData(movieData) {
    const data = {};

    // 基本信息
    data.title = this.getText(movieData.title);
    data.originalTitle = this.getText(movieData.originaltitle);
    data.year = this.getNumber(movieData.year);
    data.releaseDate = this.getText(movieData.premiered) || this.getText(movieData.releasedate);
    data.runtime = this.getNumber(movieData.runtime);
    data.plot = this.getText(movieData.plot);
    data.studio = this.getText(movieData.studio);
    data.director = this.getText(movieData.director);
    data.trailerUrl = this.getText(movieData.trailer);

    // ID 提取 - 优先级顺序
    data.id = this.extractId(movieData);

    // 海报和封面
    data.posterUrl = this.getText(movieData.thumb) || this.getText(movieData.poster);
    data.coverUrl = this.getText(movieData.fanart);

    // 演员信息
    data.actors = this.extractActors(movieData.actor);

    // 类型信息
    data.genres = this.extractGenres(movieData.genre);

    // 标签信息
    data.tags = this.extractTags(movieData.tag);

    // 评分信息
    data.rating = this.getNumber(movieData.rating);
    data.votes = this.getNumber(movieData.votes);

    return data;
  }

  static extractId(movieData) {
    // 1. 尝试 uniqueid 标签
    if (movieData.uniqueid) {
      const uniqueids = Array.isArray(movieData.uniqueid) ? movieData.uniqueid : [movieData.uniqueid];
      
      // 查找默认或主要 ID
      for (const uid of uniqueids) {
        if (typeof uid === 'object' && uid.$) {
          const type = uid.$.type?.toLowerCase();
          const isDefault = uid.$.default === 'true';
          const isPrimary = type === 'primary';
          
          if ((isDefault || isPrimary) && uid._) {
            return uid._;
          }
        }
      }

      // 查找特定类型的 ID
      for (const uid of uniqueids) {
        if (typeof uid === 'object' && uid.$) {
          const type = uid.$.type?.toLowerCase();
          if (['imdb', 'tmdb', 'tvdb', 'javdb', 'javlibrary', 'javbus'].includes(type) && uid._) {
            return uid._;
          }
        } else if (typeof uid === 'string') {
          return uid;
        }
      }
    }

    // 2. 尝试简单的 id 标签
    if (movieData.id) {
      return this.getText(movieData.id);
    }

    // 3. 尝试 num 标签
    if (movieData.num) {
      return this.getText(movieData.num);
    }

    // 4. 从 filenameandpath 中提取
    if (movieData.filenameandpath) {
      const javMatch = movieData.filenameandpath.match(/([A-Z]{2,5}-\d{3,5})/i);
      if (javMatch) {
        return javMatch[1].toUpperCase();
      }
    }

    return null;
  }

  static extractActors(actorData) {
    if (!actorData) return [];
    
    const actors = Array.isArray(actorData) ? actorData : [actorData];
    return actors.map(actor => {
      if (typeof actor === 'string') {
        return { name: actor };
      } else if (typeof actor === 'object') {
        return {
          name: this.getText(actor.name),
          role: this.getText(actor.role),
          thumb: this.getText(actor.thumb),
        };
      }
      return null;
    }).filter(Boolean);
  }

  static extractGenres(genreData) {
    if (!genreData) return [];
    
    const genres = Array.isArray(genreData) ? genreData : [genreData];
    return genres.map(genre => this.getText(genre)).filter(Boolean);
  }

  static extractTags(tagData) {
    if (!tagData) return [];
    
    const tags = Array.isArray(tagData) ? tagData : [tagData];
    return tags.map(tag => this.getText(tag)).filter(Boolean);
  }

  static parseWithRegex(content) {
    const data = {};

    // 基本字段的正则表达式
    const patterns = {
      title: /<title[^>]*>(.*?)<\/title>/i,
      originalTitle: /<originaltitle[^>]*>(.*?)<\/originaltitle>/i,
      year: /<year[^>]*>(\d{4})<\/year>/i,
      plot: /<plot[^>]*>(.*?)<\/plot>/is,
      studio: /<studio[^>]*>(.*?)<\/studio>/i,
      director: /<director[^>]*>(.*?)<\/director>/i,
      runtime: /<runtime[^>]*>(\d+)<\/runtime>/i,
      id: /<id[^>]*>(.*?)<\/id>/i,
      num: /<num[^>]*>(.*?)<\/num>/i,
      poster: /<thumb[^>]*>(.*?)<\/thumb>/i,
      rating: /<rating[^>]*>([\d.]+)<\/rating>/i,
    };

    // 提取基本字段
    for (const [key, pattern] of Object.entries(patterns)) {
      const match = content.match(pattern);
      if (match) {
        if (key === 'year' || key === 'runtime') {
          data[key] = parseInt(match[1]);
        } else if (key === 'rating') {
          data[key] = parseFloat(match[1]);
        } else {
          data[key] = match[1].trim();
        }
      }
    }

    // 提取演员
    const actorMatches = content.match(/<actor[^>]*>.*?<\/actor>/gis);
    if (actorMatches) {
      data.actors = actorMatches.map(actorXml => {
        const nameMatch = actorXml.match(/<name[^>]*>(.*?)<\/name>/i);
        const roleMatch = actorXml.match(/<role[^>]*>(.*?)<\/role>/i);
        const thumbMatch = actorXml.match(/<thumb[^>]*>(.*?)<\/thumb>/i);
        
        return {
          name: nameMatch ? nameMatch[1].trim() : null,
          role: roleMatch ? roleMatch[1].trim() : null,
          thumb: thumbMatch ? thumbMatch[1].trim() : null,
        };
      }).filter(actor => actor.name);
    }

    // 提取类型
    const genreMatches = content.match(/<genre[^>]*>(.*?)<\/genre>/gi);
    if (genreMatches) {
      data.genres = genreMatches.map(match => {
        const genreMatch = match.match(/<genre[^>]*>(.*?)<\/genre>/i);
        return genreMatch ? genreMatch[1].trim() : null;
      }).filter(Boolean);
    }

    return { success: true, data };
  }

  static getText(value) {
    if (typeof value === 'string') {
      return value.trim() || null;
    }
    if (typeof value === 'object' && value._) {
      return value._.trim() || null;
    }
    return null;
  }

  static getNumber(value) {
    if (typeof value === 'number') {
      return value;
    }
    if (typeof value === 'string') {
      const num = parseInt(value);
      return isNaN(num) ? null : num;
    }
    if (typeof value === 'object' && value._) {
      const num = parseInt(value._);
      return isNaN(num) ? null : num;
    }
    return null;
  }

  // 从文件名中提取 JAV ID
  static extractJavIdFromFilename(filename) {
    const baseName = filename.replace(/\.[^.]+$/, '');

    const patterns = [
      /(No\d{3,5})/i,           // Mywife格式：No1544
      /([A-Z]{2,5}-\d{3,5})/i,  // 标准格式：ABC-123
      /([A-Z]{2,4}\d{3,5})/i,   // 无连字符：ABC123
      /([A-Z]+[-_]\d+)/i,       // 通用格式：ABC_123
      /\((\d{3,5})\)/i,         // 🔧 新增：括号内纯数字格式：(5695)
    ];

    for (const pattern of patterns) {
      const match = baseName.match(pattern);
      if (match) {
        let id = match[1];

        // 对于No开头的ID，保持原格式
        if (id.toLowerCase().startsWith('no')) {
          return id; // 保持原始大小写
        }

        // 🔧 对于纯数字ID，直接返回
        if (/^\d+$/.test(id)) {
          return id; // 保持纯数字格式
        }

        // 其他格式标准化处理
        id = id.toUpperCase();
        if (!/[-_]/.test(id)) {
          id = id.replace(/([A-Z]+)(\d+)/, '$1-$2');
        } else {
          id = id.replace('_', '-');
        }
        return id;
      }
    }

    return null;
  }
}

module.exports = NodeNfoParser;
