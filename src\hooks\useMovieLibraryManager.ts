// soul-forge-electron/src/hooks/useMovieLibraryManager.ts
import { useState, useEffect, useCallback } from 'react';
import { MovieLibrary, ManageMovieLibraryParams, AppSettings, SettingsResult } from '../types';

type ManageLibraryHookData = Partial<Omit<MovieLibrary, 'createdAt' | 'updatedAt'>>;

export function useMovieLibraryManager(
  appSettings: AppSettings, 
  saveSettings: (settings: Partial<AppSettings>) => Promise<SettingsResult>,
  activeLibraryProp: MovieLibrary | null, // Receive activeLibrary as a prop
  setActiveLibraryProp: (library: MovieLibrary | null) => void // Receive setter as a prop
) {
  const [movieLibraries, setMovieLibraries] = useState<MovieLibrary[]>([]);
  const [isLoadingLibraries, setIsLoadingLibraries] = useState(true);
  const [isLibraryCreationModalOpen, setIsLibraryCreationModalOpen] = useState(false);
  // activeLibrary state is now managed by useAppView and passed as a prop

  const refreshLibraries = useCallback(async () => {
    setIsLoadingLibraries(true);
    try {
      const result = await window.sfeElectronAPI.getMovieLibraries();
      if (result.success) {
        setMovieLibraries(result.libraries);
        
        const currentActiveIdInSettings = appSettings.activeLibraryId;
        let libraryFromSettings: MovieLibrary | null = null;

        if (currentActiveIdInSettings) {
          libraryFromSettings = result.libraries.find(lib => lib.id === currentActiveIdInSettings) || null;
        }
        
        // Only update activeLibraryProp if the settings-derived library is different
        // or if activeLibraryProp is currently set but settings dictate it should be null.
        if (activeLibraryProp?.id !== libraryFromSettings?.id) {
           setActiveLibraryProp(libraryFromSettings); 
        }

        // If after checking settings, libraryFromSettings is null (meaning no valid active library in settings)
        // AND currentActiveIdInSettings was actually set (meaning it was an invalid ID that's now cleared), clear it from settings.
        if (!libraryFromSettings && currentActiveIdInSettings) {
           await saveSettings({ activeLibraryId: null });
        }
        // If there was no activeLibraryId in settings to begin with, and we're on home (libraryFromSettings is null), no need to save null again.
        
      } else {
        console.error("Failed to fetch movie libraries:", result.error);
        setMovieLibraries([]);
        setActiveLibraryProp(null); // Clear active library in parent on error
      }
    } catch (e) {
      console.error("Error fetching movie libraries:", e);
      setMovieLibraries([]);
      setActiveLibraryProp(null); // Clear active library in parent on error
    } finally {
      setIsLoadingLibraries(false);
    }
  }, [appSettings.activeLibraryId, saveSettings, activeLibraryProp, setActiveLibraryProp]); 

  useEffect(() => {
    refreshLibraries();
  }, [refreshLibraries]);
  
  // This effect is to synchronize if appSettings.activeLibraryId changes externally
  // and the local activeLibrary (via prop) isn't matching yet.
  useEffect(() => {
    if (isLoadingLibraries) return; // Don't run if still loading initial libraries

    const settingsActiveId = appSettings.activeLibraryId;
    if (settingsActiveId !== activeLibraryProp?.id) {
        const foundLibrary = movieLibraries.find(lib => lib.id === settingsActiveId);
        setActiveLibraryProp(foundLibrary || null);
    }
  }, [appSettings.activeLibraryId, movieLibraries, activeLibraryProp, setActiveLibraryProp, isLoadingLibraries]);


  const manageMovieLibrary = async (
    data: ManageLibraryHookData,
    operation: 'create' | 'update' | 'delete'
  ) => {
    let apiParams: ManageMovieLibraryParams | { id: string, delete: true };

    if (operation === 'delete') {
      if (!data.id) {
        alert('删除操作需要提供片库ID。');
        return { success: false, error: '删除操作需要ID。' };
      }
      apiParams = { id: data.id, delete: true };
    } else { 
      if (!data.name || !data.paths) {
        alert('创建或更新片库需要名称和路径。');
        return { success: false, error: '名称和路径为必填项。' };
      }
      apiParams = { name: data.name, paths: data.paths };
      if (operation === 'update' && data.id) {
        apiParams.id = data.id;
      } else if (operation === 'update' && !data.id) {
        alert('更新操作需要提供片库ID。');
        return { success: false, error: '更新操作需要ID。' };
      }
    }

    try {
      const result = await window.sfeElectronAPI.manageMovieLibrary(apiParams);
      if (result.success) {
        await refreshLibraries(); // This will re-evaluate and set active library if needed
        
        // After refresh, explicitly set the newly created or updated library as active if applicable
        if (operation === 'create' && result.library) {
           setActiveLibraryProp(result.library); 
           if (appSettings.activeLibraryId !== result.library.id) {
            await saveSettings({ activeLibraryId: result.library.id });
           }
        } else if (operation === 'delete' && activeLibraryProp && activeLibraryProp.id === data.id) {
          // refreshLibraries should handle selecting a new default if current active is deleted
          // (or setting to null if no libraries remain / no default in settings)
        } else if (operation === 'update' && result.library && activeLibraryProp && activeLibraryProp.id === result.library.id) {
          // If the currently active library was updated, ensure its details are refreshed in the parent state
          setActiveLibraryProp(result.library); 
        }
      } else {
        alert(`操作片库失败: ${result.error}`);
      }
      return result;
    } catch (e: any) {
      alert(`操作片库时发生前端错误: ${e.message}`);
      return { success: false, error: e.message };
    }
  };

  const triggerLibraryScan = (libraryToScan: MovieLibrary) => { // Renamed parameter for clarity
    if (libraryToScan && libraryToScan.paths && libraryToScan.paths.length > 0) {
      window.sfeElectronAPI.triggerScan(libraryToScan.paths, libraryToScan.id);
    } else {
      alert("此片库没有配置扫描路径。");
    }
  };


  return {
    movieLibraries,
    isLoadingLibraries,
    isLibraryCreationModalOpen,
    setIsLibraryCreationModalOpen,
    manageMovieLibrary,
    refreshLibraries,
    // activeLibrary is now managed by useAppView, no longer returned from here
    // setActiveLibrary is now managed by useAppView, no longer returned from here
    triggerLibraryScan,
  };
}