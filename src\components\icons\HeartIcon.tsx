import React from 'react';

interface HeartIconProps {
  className?: string;
  filled?: boolean;
}

const HeartIcon: React.FC<HeartIconProps> = ({ className, filled = false }) => {
  if (filled) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
        className={className}
        aria-hidden="true"
      >
        <path
          fillRule="evenodd"
          d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
          clipRule="evenodd"
        />
      </svg>
    );
  }
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 20 20" // Adjusted viewBox to match typical 20x20 for consistency
      strokeWidth={1.5} // Common stroke width
      stroke="currentColor"
      className={className}
      aria-hidden="true"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        // Standard heart outline path (adjust if you have a specific one)
        d="M10 17.657l-6.828-6.829a4 4 0 010-5.656L4.343 3.515A4.002 4.002 0 0110 3.515a4.002 4.002 0 015.657 1.657l1.171 1.171a4 4 0 010 5.656L10 17.657z"
      />
    </svg>
  );
};

export default HeartIcon;