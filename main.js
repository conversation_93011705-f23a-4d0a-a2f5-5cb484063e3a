
// 在最顶部添加 File 对象 polyfill（解决 "File is not defined" 错误）
if (typeof global.File === 'undefined') {
  // 为 Electron 主进程提供 File 对象的简单 polyfill
  global.File = class File {
    constructor(fileBits, fileName, options = {}) {
      this.name = fileName;
      this.type = options.type || '';
      this.lastModified = options.lastModified || Date.now();
      this.size = 0;
      if (Array.isArray(fileBits)) {
        this.size = fileBits.reduce((total, bit) => {
          if (typeof bit === 'string') return total + bit.length;
          if (bit instanceof ArrayBuffer) return total + bit.byteLength;
          return total;
        }, 0);
      }
    }
  };
}

// 优先在最顶部初始化 electron-log
const log = require('electron-log');
const nodePathModule = require('node:path'); // Renamed to avoid conflict

// 配置 electron-log
try {
    log.transports.file.resolvePathFn = () => nodePathModule.join(require('electron').app.getPath('userData'), 'logs', 'main.log');
    log.transports.file.level = 'info';
    log.transports.console.level = 'info';
    log.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}';

    log.errorHandler.startCatching({ // Updated from log.catchErrors
      showDialog: false, 
      onError: (error) => {
        log.error("electron-log.errorHandler 捕获到未处理的主进程错误:");
        log.error(error); 
      }
    });
    Object.assign(console, log.functions);
    log.info('麟琅秘府主进程 V1.6 (多片库支持) 启动中...'); // Version Update for Multi-Library
    log.info(`初始 __dirname: ${__dirname}`);
    log.info(`初始 process.cwd(): ${process.cwd()}`);
} catch (e) { console.error("致命错误: 初始化 electron-log 或获取 userData 路径失败:", e); }


const { app, BrowserWindow, ipcMain, dialog, shell, session } = require('electron');
log.info('Electron 模块 (app, BrowserWindow 等) 已成功加载。');

const path = require('node:path'); // Node.js path module
const http = require('http');
const { spawn } = require('child_process');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

let googleGenAiModule;
try {
  googleGenAiModule = require('@google/genai');
  log.info('@google/genai 已成功加载并暂存。');
} catch (error) {
  log.error('加载 @google/genai 失败。AI 功能可能受影响。', error);
  googleGenAiModule = null; 
}

// Load services and handlers
const settingsService = require('./main_process/services/settingsService');
const databaseService = require('./main_process/services/databaseService');
// 【废弃】Python相关功能已移除，改用JavaScript实现
// const pythonRunnerService = require('./main_process/services/pythonRunnerService');
const snapshotGeneratorService = require('./main_process/services/snapshotGeneratorService'); // 新的JavaScript快照生成服务
const fileUtils = require('./main_process/utils/fileUtils');
const httpClient = require('./main_process/utils/httpClient'); // Import httpClient
const snapshotUtils = require('./main_process/utils/snapshotUtils');
const aiService = require('./main_process/services/aiService');
const aiAnalysisService = require('./main_process/services/aiAnalysisService');
const aiCategoryService = require('./main_process/services/aiCategoryService');
const avatarFileTreeService = require('./main_process/services/avatarFileTreeService');
const scanHandler = require('./main_process/ipcHandlers/scanHandler');
const ingestHandler = require('./main_process/ipcHandlers/ingestHandler');
const nfoPlotPolisherService = require('./main_process/services/nfoPlotPolisherService');
const collectorService = require('./main_process/services/collectorService');
log.info('自定义服务和处理器模块已加载。');


let mainWindow;
// Use app.isPackaged for a more reliable way to determine if the app is in development or production
const isDev = !app.isPackaged;
log.info(`是否为开发模式 (isDev using !app.isPackaged): ${isDev}`);

let userDataPath, dbPath, defaultThumbnailCacheBasePathInternal;

try {
    userDataPath = app.getPath('userData');
    settingsService.initializeSettings(log, userDataPath);
    log.info(`用户数据路径 (userDataPath): ${userDataPath}`);
    
    const currentSettingsForPaths = settingsService.getSettings();
    dbPath = path.join(userDataPath, 'soul-forge-library.sqlite'); 
    defaultThumbnailCacheBasePathInternal = currentSettingsForPaths.snapshotCachePath || path.join(userDataPath, 'thumbnail_cache');
    
    log.info(`数据库路径 (dbPath): ${dbPath}`);
    log.info(`缩略图缓存基础路径 (最终确定): ${defaultThumbnailCacheBasePathInternal}`);

} catch (error) {
    log.error('获取应用路径 (userData 等) 或初始化设置服务失败。这是一个严重错误。', error);
    app.quit(); 
}

try {
    databaseService.initializeDatabaseService(log, dbPath, defaultThumbnailCacheBasePathInternal, isDev);
    // 【废弃】Python相关功能已移除
    // pythonRunnerService.initializePythonRunner(log, isDev, __dirname, process.resourcesPath);
    snapshotGeneratorService.initializeSnapshotGenerator(log); // 新的JavaScript快照生成服务
    fileUtils.initializeFileUtils(log);
    httpClient.initializeHttpClient(log); // Initialize httpClient
    snapshotUtils.initializeSnapshotUtils(log);

    // 初始化AI分析服务
    aiAnalysisService.initializeAiAnalysisService(log, googleGenAiModule);

    // aiService.initializeAiService will now also init provider modules & httpClient if they need it
    aiService.initializeAiService(log, settingsService.getStore(), googleGenAiModule, app, databaseService);
    avatarFileTreeService.initializeAvatarFileTreeService(log, settingsService, fileUtils, databaseService, app); // Pass app instance
    // 【废弃】scanHandler依赖Python，需要重构
    // scanHandler.initializeScanHandler(log, pythonRunnerService, databaseService, settingsService, fileUtils);
    // 【废弃】nfoPlotPolisherService依赖Python，需要重构
    // nfoPlotPolisherService.initializeNfoPlotPolisherService(log, pythonRunnerService, aiService, settingsService);
    collectorService.initializeCollectorService(log, __dirname);
    log.info('所有服务初始化完成。');
} catch (error) {
    log.error('服务初始化过程中发生错误:', error);
}

// 暂时删除新添加的函数来排查语法错误

/**
 * 自动启动Vite服务器并等待就绪
 * @param {BrowserWindow} window - Electron窗口实例
 */
async function startViteServerAndLoad(window) {
  const viteUrl = 'http://localhost:5173';
  const maxAttempts = 60; // 最多等待60秒
  const retryInterval = 1000; // 每秒重试一次
  let viteProcess = null;

  log.info('[启动] 🚀 正在启动SoulForge...');

  // 首先快速检查服务器是否已经运行
  try {
    await checkViteServer(viteUrl);
    log.info('[启动] ✅ Vite服务器已在运行，直接加载页面');
    await window.loadURL(viteUrl);
    log.info('[启动] 🎉 应用启动成功！');
    return;
  } catch (error) {
    log.info('[启动] 🔧 Vite服务器未运行，正在自动启动...');
  }

  // 显示启动中的页面
  await showStartupPage(window);

  // 自动启动Vite服务器
  try {
    viteProcess = spawn('npm', ['run', 'dev'], {
      cwd: process.cwd(),
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });

    log.info('[启动] 🔧 正在启动Vite开发服务器...');

    // 监听输出以确定服务器何时就绪
    viteProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Local:') && output.includes('5173')) {
        log.info('[启动] ✅ Vite服务器启动成功！');
      }
    });

    viteProcess.stderr.on('data', (data) => {
      const output = data.toString();
      if (!output.includes('WARN') && !output.includes('deprecated')) {
        log.warn(`[启动] Vite服务器信息: ${output.trim()}`);
      }
    });

    viteProcess.on('error', (error) => {
      log.error(`[启动] ❌ Vite进程启动失败: ${error.message}`);
    });

  } catch (spawnError) {
    log.error(`[启动] ❌ 无法启动Vite服务器: ${spawnError.message}`);
    await showErrorPage(window, spawnError.message);
    return;
  }

  log.info('[启动] ⏳ 等待Vite开发服务器就绪...');

  // 等待服务器就绪
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      await checkViteServer(viteUrl);

      // 服务器响应成功，加载页面
      log.info(`[启动] 🎯 Vite服务器已就绪，正在加载应用... (尝试 ${attempt}/${maxAttempts})`);
      await window.loadURL(viteUrl);
      log.info('[启动] 🎉 SoulForge启动成功！');
      return;

    } catch (error) {
      if (attempt <= 10) {
        // 前10次尝试不显示警告，因为服务器需要时间启动
        log.info(`[启动] ⏳ 等待服务器启动... (${attempt}/${maxAttempts})`);
      } else {
        log.warn(`[启动] ⏳ 服务器仍在启动中... (${attempt}/${maxAttempts})`);
      }

      if (attempt === maxAttempts) {
        // 最后一次尝试失败，清理进程并显示错误页面
        if (viteProcess && !viteProcess.killed) {
          log.info('[启动] 🧹 清理Vite进程...');
          viteProcess.kill();
        }
        log.error('[启动] ❌ Vite服务器启动超时');
        await showErrorPage(window, 'Vite服务器启动超时，请检查Node.js和npm是否正确安装');
        return;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, retryInterval));
    }
  }
}

/**
 * 检查Vite服务器是否可用
 * @param {string} url - Vite服务器URL
 */
function checkViteServer(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      resolve(res);
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(2000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

/**
 * 显示启动中的页面
 * @param {BrowserWindow} window - Electron窗口实例
 */
async function showStartupPage(window) {
  const startupHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>SoulForge - 启动中</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
          color: #ffffff;
          margin: 0;
          padding: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
        }
        .startup-container {
          text-align: center;
          max-width: 600px;
          background: rgba(255, 255, 255, 0.05);
          padding: 40px;
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .startup-icon {
          font-size: 64px;
          margin-bottom: 20px;
          animation: pulse 2s infinite;
        }
        @keyframes pulse {
          0% { opacity: 0.6; }
          50% { opacity: 1; }
          100% { opacity: 0.6; }
        }
        h1 {
          color: #4CAF50;
          margin-bottom: 20px;
        }
        .progress {
          width: 100%;
          height: 4px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
          overflow: hidden;
          margin: 20px 0;
        }
        .progress-bar {
          height: 100%;
          background: linear-gradient(90deg, #4CAF50, #45a049);
          animation: loading 2s infinite;
        }
        @keyframes loading {
          0% { width: 0%; }
          50% { width: 70%; }
          100% { width: 100%; }
        }
      </style>
    </head>
    <body>
      <div class="startup-container">
        <div class="startup-icon">🚀</div>
        <h1>SoulForge 启动中</h1>
        <p>正在启动前端服务器，请稍候...</p>
        <div class="progress">
          <div class="progress-bar"></div>
        </div>
        <p><small>首次启动可能需要较长时间</small></p>
      </div>
    </body>
    </html>
  `;

  await window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(startupHtml)}`);
}

/**
 * 显示错误页面
 * @param {BrowserWindow} window - Electron窗口实例
 * @param {string} errorMessage - 错误信息
 */
async function showErrorPage(window, errorMessage) {
  const errorHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>SoulForge - 启动失败</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
          color: #ffffff;
          margin: 0;
          padding: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
        }
        .error-container {
          text-align: center;
          max-width: 600px;
          background: rgba(255, 255, 255, 0.05);
          padding: 40px;
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .error-icon { font-size: 64px; margin-bottom: 20px; }
        h1 { color: #ff6b6b; margin-bottom: 20px; }
        .solution {
          background: rgba(255, 255, 255, 0.1);
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          text-align: left;
        }
        .command {
          background: #000;
          color: #00ff00;
          padding: 10px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          margin: 10px 0;
        }
        .retry-btn {
          background: #4CAF50;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 20px;
        }
        .retry-btn:hover { background: #45a049; }
      </style>
    </head>
    <body>
      <div class="error-container">
        <div class="error-icon">❌</div>
        <h1>启动失败</h1>
        <p>SoulForge无法自动启动前端服务器。</p>
        <p><strong>错误信息：</strong> ${errorMessage}</p>

        <div class="solution">
          <h3>解决方案：</h3>
          <p>1. 确保Node.js和npm已正确安装</p>
          <p>2. 打开命令行窗口，导航到项目目录</p>
          <p>3. 手动运行以下命令：</p>
          <div class="command">npm install</div>
          <div class="command">npm run dev</div>
          <p>4. 等待服务器启动后重启SoulForge</p>
        </div>

        <button class="retry-btn" onclick="location.reload()">重试启动</button>
      </div>
    </body>
    </html>
  `;

  await window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(errorHtml)}`);
}

async function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 720,
    minWidth: 940,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      spellcheck: false,
      devTools: isDev,
    },
    icon: path.join(__dirname, '..', 'build', process.platform === 'win32' ? 'icon.ico' : 'icon.png'),
    show: false,
  });

  // 设置CollectorService的主窗口引用，用于发送事件
  collectorService.setMainWindow(mainWindow);

  if (isDev) {
    // 开发模式：自动启动Vite服务器并等待就绪
    await startViteServerAndLoad(mainWindow);
    // 🔧 修复：确保窗口显示
    mainWindow.show();
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'dist', 'index.html'));
    // 生产模式仍使用ready-to-show事件
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
    });
  }

  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self';" +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://esm.sh;" + 
          "style-src 'self' 'unsafe-inline';" + 
          "img-src 'self' data: https: http: file:;" + 
          "font-src 'self';" +
          "connect-src 'self' https: http: file:;"  // Added file: for strm content from local files
        ].join(' ')
      }
    });
  });
}

// --- IPC Handlers ---
ipcMain.handle('get-settings', async () => {
  const settings = settingsService.getSettings();
  // Do not send defaultScanPaths anymore, renderer will use libraries
  const { defaultScanPaths, ...settingsForRenderer } = settings;
  return settingsForRenderer;
});


ipcMain.handle('save-settings', async (_event, newSettings) => {
  const oldSettings = settingsService.getLastSettings();
  const result = settingsService.saveSettings(newSettings);
  if (result.success && result.newSettings) {
    
    const aiConfigChanged = 
      oldSettings.aiProvider !== result.newSettings.aiProvider ||
      oldSettings.customGptEndpoint !== result.newSettings.customGptEndpoint ||
      oldSettings.customGptApiKey !== result.newSettings.customGptApiKey ||
      oldSettings.customGptModel !== result.newSettings.customGptModel ||
      oldSettings.grokApiKey !== result.newSettings.grokApiKey;

    if (aiConfigChanged) {
      log.info("[主进程] AI 配置已更改，正在重新初始化 AI 客户端...");
      try {
        // Pass true for forceReinitialize to ensure new settings are picked up
        await aiService.getAiClient(settingsService.getStore(), true); 
      } catch (aiError) {
        log.error("[主进程] 重新初始化 AI 客户端失败:", aiError);
      }
    }

    const avatarConfigChanged = 
      oldSettings.avatarDataSourceType !== result.newSettings.avatarDataSourceType ||
      oldSettings.localFileTreePath !== result.newSettings.localFileTreePath ||
      oldSettings.remoteGfriendsFiletreeUrl !== result.newSettings.remoteGfriendsFiletreeUrl ||
      oldSettings.actorAvatarLibraryPath !== result.newSettings.actorAvatarLibraryPath;
    
    if (avatarConfigChanged) {
        log.info("[主进程] 演员头像配置已更改。Filetree.json 将在下次刮削时按需加载。");
        // REMOVED: avatarFileTreeService.loadAndCacheFiletree().catch(...) - No longer reloaded on settings change.
    }
     _event.sender.send('privacy-settings-changed'); 
     _event.sender.send('settings-updated', result.newSettings); // Notify renderer of general settings update
  }
  return result;
});

ipcMain.handle('select-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory', 'multiSelections'] // Allow multiple selections for library paths
  });
  if (result.canceled) return null;
  return result.filePaths; // Return array of paths
});

// 单选文件夹选择器（用于媒体资产根目录设置）
ipcMain.handle('dialog:select-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  if (result.canceled || result.filePaths.length === 0) {
    return null;
  }
  return result.filePaths[0]; // Return single path
});

// 文件选择对话框
ipcMain.handle('dialog:select-file', async (_event, options = {}) => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    title: options.title || '选择文件',
    filters: options.filters || [
      { name: '所有文件', extensions: ['*'] }
    ]
  });

  if (result.canceled || result.filePaths.length === 0) {
    return null;
  }

  return result.filePaths[0]; // 返回单个文件路径
});

// 下载中转站 IPC 处理器
ipcMain.handle('staging:scan', async (_event) => {
  try {
    const downloadStagingService = require('./main_process/services/downloadStagingService');
    const files = await downloadStagingService.scanStagingDirectory();
    return { success: true, files: files };
  } catch (error) {
    log.error('[IPC] 扫描下载中转站失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('staging:process-file', async (_event, filePath, nfoId, targetSeriesFolder) => {
  try {
    const downloadStagingService = require('./main_process/services/downloadStagingService');
    const result = await downloadStagingService.processStagedFile(filePath, nfoId, targetSeriesFolder);
    return result;
  } catch (error) {
    log.error(`[IPC] 处理中转站文件失败: ${filePath}`, error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('staging:get-series-folders', async (_event) => {
  try {
    const downloadStagingService = require('./main_process/services/downloadStagingService');
    const folders = await downloadStagingService.getAvailableSeriesFolders();
    return { success: true, folders: folders };
  } catch (error) {
    log.error('[IPC] 获取系列文件夹列表失败:', error.message);
    return { success: false, error: error.message };
  }
});

// 本地资产代理化 IPC 处理器
ipcMain.handle('asset:create-from-local-file', async (_event, filePath) => {
  try {
    const localAssetService = require('./main_process/services/localAssetService');
    const result = await localAssetService.createAssetFromLocalFile(filePath);
    return result;
  } catch (error) {
    log.error(`[IPC] 从本地文件创建资产失败: ${filePath}`, error.message);
    return { success: false, error: error.message };
  }
});

// 资产回收站 IPC 处理器
ipcMain.handle('asset:recycle-version', async (_event, versionDbId) => {
  try {
    const assetRecyclingService = require('./main_process/services/assetRecyclingService');
    const result = await assetRecyclingService.recycleSingleVersion(versionDbId);
    return result;
  } catch (error) {
    log.error(`[IPC] 回收单一版本失败: ${versionDbId}`, error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('asset:recycle-movie', async (_event, nfoId) => {
  try {
    const assetRecyclingService = require('./main_process/services/assetRecyclingService');
    const result = await assetRecyclingService.recycleEntireMovie(nfoId);
    return result;
  } catch (error) {
    log.error(`[IPC] 回收整部影片失败: ${nfoId}`, error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('asset:get-recycled-items', async (_event) => {
  try {
    const assetRecyclingService = require('./main_process/services/assetRecyclingService');
    const result = await assetRecyclingService.getRecycledItems();
    return result;
  } catch (error) {
    log.error('[IPC] 获取回收站项目失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('asset:generate-deletion-list', async (_event) => {
  try {
    const assetRecyclingService = require('./main_process/services/assetRecyclingService');
    const result = await assetRecyclingService.generateDeletionList();
    return result;
  } catch (error) {
    log.error('[IPC] 生成删除清单失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('asset:open-file-location', async (_event, filePath) => {
  try {
    const { shell } = require('electron');
    await shell.showItemInFolder(filePath);
    return { success: true };
  } catch (error) {
    log.error(`[IPC] 打开文件位置失败: ${filePath}`, error.message);
    return { success: false, error: error.message };
  }
});

// 历史回溯扫描器 IPC 处理器
ipcMain.handle('daily-scan:start', async (event) => {
  try {
    const dailyScannerService = require('./main_process/services/dailyScannerService');
    const result = await dailyScannerService.startDailyScan(event);
    return result;
  } catch (error) {
    log.error('[IPC] 每日简报扫描失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('history-scan:start', async (event, options) => {
  try {
    const dailyScannerService = require('./main_process/services/dailyScannerService');
    const result = await dailyScannerService.startScan(event, options);
    return result;
  } catch (error) {
    log.error('[IPC] 历史回溯扫描失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('daily-scan:get-status', async (_event) => {
  try {
    const dailyScannerService = require('./main_process/services/dailyScannerService');
    const status = dailyScannerService.getScanStatus();
    return { success: true, status: status };
  } catch (error) {
    log.error('[IPC] 获取扫描状态失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('daily-scan:stop', async (_event) => {
  try {
    const dailyScannerService = require('./main_process/services/dailyScannerService');
    const stopped = dailyScannerService.stopCurrentScan();
    return { success: true, stopped: stopped };
  } catch (error) {
    log.error('[IPC] 停止扫描失败:', error.message);
    return { success: false, error: error.message };
  }
});

// 未来新品扫描 IPC 处理器
ipcMain.handle('upcoming-scan:start', async (event) => {
  try {
    const dailyScannerService = require('./main_process/services/dailyScannerService');
    const result = await dailyScannerService.scanUpcomingReleases(event);
    return result;
  } catch (error) {
    log.error('[IPC] 未来新品扫描失败:', error.message);
    return { success: false, error: error.message };
  }
});

// 演员档案 IPC 处理器
ipcMain.handle('actor:get-profile', async (event, actorName) => {
  try {
    const { actorProfileService } = require('./main_process/services/actorProfileService');
    const result = await actorProfileService.getActorProfile(actorName);
    return result;
  } catch (error) {
    log.error('[IPC] 获取演员档案失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('actor:get-all-profiles', async (event, limit = 50, offset = 0) => {
  try {
    const { actorProfileService } = require('./main_process/services/actorProfileService');
    const profiles = await actorProfileService.getAllProfiles(limit, offset);
    return { success: true, data: profiles };
  } catch (error) {
    log.error('[IPC] 获取演员档案列表失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('actor:delete-profile', async (event, actorName) => {
  try {
    const { actorProfileService } = require('./main_process/services/actorProfileService');
    const success = await actorProfileService.deleteProfile(actorName);
    return { success };
  } catch (error) {
    log.error('[IPC] 删除演员档案失败:', error.message);
    return { success: false, error: error.message };
  }
});

// 根据演员获取影片列表 IPC 处理器
ipcMain.handle('movies:get-by-actor', async (event, actorName) => {
  try {
    const databaseService = require('./main_process/services/databaseService');
    const movies = databaseService.getMoviesByActor(actorName);
    return { success: true, data: movies };
  } catch (error) {
    log.error('[IPC] 根据演员获取影片列表失败:', error.message);
    return { success: false, error: error.message, data: [] };
  }
});

// 获取演员完整作品列表 IPC 处理器
ipcMain.handle('actor:get-complete-filmography', async (event, actorName) => {
  try {
    const { actorProfileService } = require('./main_process/services/actorProfileService');
    const result = await actorProfileService.getActorCompleteFilmography(actorName);
    return result;
  } catch (error) {
    log.error('[IPC] 获取演员完整作品列表失败:', error.message);
    return { success: false, error: error.message, data: null };
  }
});

// NFO 铸造厂 IPC 处理器
ipcMain.handle('nfo:export', async (event, nfoId) => {
  try {
    const databaseService = require('./main_process/services/databaseService');
    const { nfoService } = require('./main_process/services/nfoService');

    // 根据nfoId获取影片信息
    const movie = databaseService.getMovieByNfoId(nfoId);
    if (!movie) {
      return { success: false, error: '未找到指定的影片' };
    }

    // 读取.meta.json文件获取display_data
    const metaJsonPath = movie.metaJsonPath;
    if (!metaJsonPath) {
      return { success: false, error: '影片缺少元数据文件路径' };
    }

    const fs = require('fs').promises;
    const metaJsonContent = await fs.readFile(metaJsonPath, 'utf8');
    const metaData = JSON.parse(metaJsonContent);

    if (!metaData.display_data) {
      return { success: false, error: '元数据文件缺少display_data' };
    }

    // 获取资产根目录
    const path = require('path');
    const assetRootPath = path.dirname(metaJsonPath);

    // 调用NFO导出服务
    const success = await nfoService.exportNfo(metaData.display_data, assetRootPath, nfoId);

    return {
      success,
      message: success ? 'NFO文件导出成功' : 'NFO文件导出失败',
      nfoPath: success ? path.join(assetRootPath, `${nfoId}.nfo`) : null
    };

  } catch (error) {
    log.error('[IPC] NFO导出失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('nfo:import', async (event, nfoFilePath) => {
  try {
    const { nfoService } = require('./main_process/services/nfoService');

    // 调用NFO导入服务
    const result = await nfoService.importNfo(nfoFilePath);

    if (result && result.success) {
      return {
        success: true,
        message: 'NFO文件解析成功',
        metaJsonPath: result.metaJsonPath,
        displayData: result.displayData
      };
    } else {
      return {
        success: false,
        error: result ? result.error : '解析失败'
      };
    }

  } catch (error) {
    log.error('[IPC] NFO导入失败:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('browse-image-for-dataurl', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [{ name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'webp', 'gif'] }]
  });
  if (result.canceled || result.filePaths.length === 0) {
    return { success: true, dataUrl: null }; // User cancelled
  }
  const filePath = result.filePaths[0];
  try {
    const dataUrl = fileUtils.imagePathToDataUrl(filePath);
    if (dataUrl) {
      return { success: true, dataUrl };
    } else {
      return { success: false, error: '无法将图片转换为DataURL。可能是文件格式不受支持或文件损坏。' };
    }
  } catch (error) {
    log.error(`[IPC] browse-image-for-dataurl 转换 ${filePath} 失败:`, error);
    return { success: false, error: error.message };
  }
});
ipcMain.handle('image-path-to-data-url', async (_event, filePath) => fileUtils.imagePathToDataUrl(filePath));

ipcMain.handle('get-actor-avatar-details', async (_event, actorName) => {
  try {
    const avatarInfo = await avatarFileTreeService.getAvatarInfo(actorName);
    if (!avatarInfo) {
        // This now means not found in DB cache.
        return { success: true, dataUrl: null, avatarUrl: null, source: 'not_found_in_cache' }; 
    }
    // Assuming getAvatarInfo now only returns type 'localPath' from DB cache
    if (avatarInfo.type === 'localPath') {
        const dataUrl = fileUtils.imagePathToDataUrl(avatarInfo.path);
        if (!dataUrl) {
            log.warn(`[IPC] 无法为演员 ${actorName} 的本地缓存路径 ${avatarInfo.path} 生成 DataURL. 可能文件已损坏或不存在.`);
            // The avatar service or scraping logic should handle cache invalidation,
            // but we can trigger a re-check or log it.
            // await databaseService.upsertActorMetadata(actorName, null, null, null); // Example of clearing if invalid
            return { success: true, dataUrl: null, avatarUrl: null, source: 'local_cache_path_invalid' };
        }
        return { success: true, dataUrl: dataUrl, source: avatarInfo.source, actorName: avatarInfo.actorName };
    }
    // If getAvatarInfo could return 'url' (e.g., if some direct fetching was re-added), handle it:
    // else if (avatarInfo.type === 'url') { ... }
    return { success: false, error: '未知的头像信息类型或头像未缓存。' };
  } catch (error) {
    log.error(`[IPC] 获取演员 ${actorName} 头像详情失败:`, error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('scrape-actor-avatars', async (event) => {
  log.info('[IPC] 收到刮削演员头像请求。');
  try {
    // Pass mainWindow.webContents for progress reporting
    const result = await avatarFileTreeService.scrapeAndCacheAllAvatars(event.sender);
    return result;
  } catch (error) {
    log.error('[IPC] 刮削演员头像时发生主进程错误:', error);
    return { success: false, error: error.message || "刮削时发生未知错误。" };
  }
});


ipcMain.on('select-folder-and-scan', async (event, { pathsToScan, libraryId }) => {
  // `pathsToScan` will be an array of paths from the library, or defaultScanPaths (old way)
  scanHandler.handleSelectFolderAndScan(pathsToScan || [], libraryId, mainWindow.webContents);
});


ipcMain.handle('get-movies', async (_event, params) => {
  try {
    const result = databaseService.getMoviesFromDb(params);
    if (result.success) {
      result.movies = result.movies.map(movie => {
        let coverDataUrlToUse = movie.sessionAssignedCover || null; // Prioritize session assigned cover
        if (!coverDataUrlToUse && movie.localCoverPath) {
            try {
                const dataUrl = fileUtils.imagePathToDataUrl(movie.localCoverPath);
                if (dataUrl) coverDataUrlToUse = dataUrl;
            } catch (e) {
                log.warn(`[主进程] 无法为 ${movie.localCoverPath} 生成 data URL: ${e.message}`);
            }
        }
        return { ...movie, coverDataUrl: coverDataUrlToUse };
      });
    }
    return result;
  } catch (e) {
    log.error('[主进程] get-movies IPC 处理失败:', e);
    return { success: false, movies:[], totalMovies:0, error: e.message };
  }
});


ipcMain.on('play-video', (_event, { filePath, title, strmUrl }) => {
  if (!filePath) {
    log.warn('[IPC] 尝试播放无效的文件路径。');
    return;
  }

  log.info(`[IPC] 请求播放: ${filePath}`);
  if (filePath.toLowerCase().endsWith('.strm')) {
    const urlToPlay = strmUrl || fs.readFileSync(filePath, 'utf-8').trim().split('\n')[0];
    if (urlToPlay) {
      log.info(`[IPC] .strm 文件，尝试播放 URL: ${urlToPlay}`);
      shell.openExternal(urlToPlay)
        .then(() => {
          log.info(`[IPC] 成功尝试用外部应用打开 .strm URL: ${urlToPlay}`);
          databaseService.markMovieAsPlayed(filePath);
        })
        .catch(err => {
          log.error(`[IPC] 尝试用外部应用打开 .strm URL ${urlToPlay} 失败, 尝试应用内播放:`, err);
          // Fallback to internal player if shell.openExternal fails or for certain schemes
          if (urlToPlay.startsWith('http:') || urlToPlay.startsWith('https:')) {
            mainWindow.webContents.send('play-strm-url', { url: urlToPlay, title: title || path.basename(filePath) });
            databaseService.markMovieAsPlayed(filePath);
          } else {
            log.error(`[IPC] .strm URL ${urlToPlay} 非HTTP/HTTPS且外部打开失败，无法播放。`);
            dialog.showErrorBox('播放错误', `无法打开 .strm 文件中的链接: ${urlToPlay}. 链接类型不受支持或外部打开失败。`);
          }
        });
    } else {
      log.error(`[IPC] .strm 文件 ${filePath} 内容为空或无效。`);
      dialog.showErrorBox('播放错误', '.strm 文件内容为空或无效。');
    }
  } else {
    shell.openPath(filePath)
      .then(() => {
        log.info(`[IPC] 成功打开视频文件: ${filePath}`);
        databaseService.markMovieAsPlayed(filePath);
      })
      .catch(err => {
        log.error(`[IPC] 无法打开视频文件 ${filePath}:`, err);
        dialog.showErrorBox('播放错误', `无法打开视频文件: ${err.message}`);
      });
  }
});


ipcMain.on('open-external-url', (_event, url) => {
  if (url && (url.startsWith('http:') || url.startsWith('https:'))) {
    log.info(`[IPC] 请求打开外部链接: ${url}`);
    shell.openExternal(url).catch(err => {
      log.error(`[IPC] 无法打开外部链接 ${url}:`, err);
    });
  } else {
    log.warn(`[IPC] 尝试打开无效的外部链接: ${url}`);
  }
});
ipcMain.handle('save-nfo-data', async (_event, { videoFilePath, movieData }) => {
  return databaseService.saveNfoDataAndUpdateDb(
    videoFilePath,
    movieData,
    pythonRunnerService,
    settingsService.getSettings().pythonExecutablePath,
    fileUtils.imagePathToDataUrl 
  );
});

// AI related IPC handlers
ipcMain.handle('generate-plot-summary-with-ai', async (_event, params) => aiService.generatePlotSummaryWithAI(settingsService.getStore(), params.title, params.year));
ipcMain.handle('translate-plot-with-ai', async (_event, params) => aiService.translatePlotWithAI(settingsService.getStore(), params));
ipcMain.handle('embellish-plot-with-ai', async (_event, params) => aiService.embellishPlotWithAI(settingsService.getStore(), params, params.options || {}));
ipcMain.handle('analyze-movie-tags-with-ai', async (_event, params) => aiService.analyzeMovieTagsWithAI(settingsService.getStore(), params));
ipcMain.handle('suggest-cover-from-snapshots-with-ai', async (_event, params) => aiService.suggestCoverFromSnapshotsWithAI(settingsService.getStore(), params.movieTitle, params.movieDbId, params.numberOfSnapshots)); 
ipcMain.handle('analyze-movie-recommendation-index', async (_event, params) => aiService.analyzeMovieRecommendationIndexWithAI(settingsService.getStore(), params));
ipcMain.handle('get-cleanup-suggestions-from-ai', async (_event) => {
    const stats = databaseService.getMovieCleanupStats();
    return aiService.getCleanupSuggestionsWithAI(settingsService.getStore(), stats);
});
ipcMain.handle('linluo-chat', async (event, userInput) => aiService.invokeLinLuoChatWithAIStream(settingsService.getStore(), userInput, event.sender));
ipcMain.handle('save-chat-history', async (_event, params) => {
  const { fileName, content } = params;
  if (!fileName || !content) {
    return { success: false, error: "文件名或内容不能为空。" };
  }
  try {
    const documentsPath = app.getPath('documents');
    const soulForgeChatDir = path.join(documentsPath, 'SoulForgeChats'); // Directory name retained as is
    if (!fs.existsSync(soulForgeChatDir)) {
      fs.mkdirSync(soulForgeChatDir, { recursive: true });
    }
    const safeFileName = fileName.replace(/[<>:"/\\|?*\x00-\x1F]/g, '_') + ".md";
    const filePath = path.join(soulForgeChatDir, safeFileName);
    
    fs.writeFileSync(filePath, content, 'utf8');
    log.info(`[IPC] 聊天记录已保存到: ${filePath}`);
    return { success: true, filePath };
  } catch (error) {
    log.error(`[IPC] 保存聊天记录失败:`, error);
    return { success: false, error: error.message };
  }
});
ipcMain.handle('download-nfo-cover', async (_event, { videoFilePath, imageUrl, desiredLocalFilename }) => { /* ... */ });
ipcMain.handle('browse-local-cover', async (_event, { videoFilePath, desiredLocalFilenameTemplate }) => { /* ... */ });

ipcMain.handle('get-movie-versions', async (_event, nfoId) => databaseService.getMovieVersionsFromDb(nfoId, fileUtils.imagePathToDataUrl));
ipcMain.handle('movie:get-unified-versions', async (_event, nfoId) => {
  return databaseService.getUnifiedVersionsByNfoId(nfoId);
});
ipcMain.handle('get-movie-cd-parts', async (_event, nfoId) => databaseService.getMovieCdPartsFromDb(nfoId, fileUtils.imagePathToDataUrl));
ipcMain.handle('update-movie-nfo-id', async (_event, { movieId, nfoId }) => databaseService.updateMovieNfoId(movieId, nfoId));

// Node.js NFO 修复功能
ipcMain.handle('batch-fix-nfo-ids', async (_event) => {
  const NodeBatchNfoFixer = require('./main_process/services/nodeBatchNfoFixer');
  return await NodeBatchNfoFixer.fixAllMissingNfoIds(databaseService, log);
});

ipcMain.handle('analyze-nfo-id-status', async (_event) => {
  const NodeBatchNfoFixer = require('./main_process/services/nodeBatchNfoFixer');
  return await NodeBatchNfoFixer.analyzeNfoIdStatus(databaseService, log);
});

ipcMain.handle('validate-nfo-ids', async (_event) => {
  const NodeBatchNfoFixer = require('./main_process/services/nodeBatchNfoFixer');
  return await NodeBatchNfoFixer.validateNfoIds(databaseService, log);
});

// 刮削器服务 IPC 处理器
ipcMain.handle('scraper-scrape-movie', async (_event, nfoId) => {
  try {
    const scraperManager = require('./main_process/services/scraperManager');
    const result = await scraperManager.scrapeMovieById(nfoId);
    return { success: true, data: result };
  } catch (error) {
    log.error(`[IPC] 刮削电影 ${nfoId} 失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 即时番号侦察 IPC 处理器
ipcMain.handle('archive:scrape-and-create-virtual-asset', async (event, nfoId) => {
  if (!nfoId || !nfoId.trim()) {
    return { success: false, error: '番号不能为空。' };
  }

  const trimmedNfoId = nfoId.trim();

  try {
    // 步骤 1: 调用刮削器管理器进行刮削
    log.info(`[即时侦察] 开始为 ${trimmedNfoId} 刮削元数据...`);
    event.sender.send('archive-scrape-progress', {
      stage: 'scraping',
      message: `正在刮削 ${trimmedNfoId} 的元数据...`,
      progress: 25
    });

    const scraperManager = require('./main_process/services/scraperManager');
    const scrapedData = await scraperManager.scrapeMovieById(trimmedNfoId);

    // 步骤 2: 调用路径解析服务生成标准化路径
    log.info(`[即时侦察] 刮削成功，正在为 ${trimmedNfoId} 解析资产路径...`);
    event.sender.send('archive-scrape-progress', {
      stage: 'resolving',
      message: `正在解析 ${trimmedNfoId} 的资产路径...`,
      progress: 60
    });

    const pathResolverService = require('./main_process/services/pathResolverService');
    const assetPaths = await pathResolverService.resolveAssetPaths(scrapedData);

    // 步骤 3: 调用数据库服务创建虚拟资产
    log.info(`[即时侦察] 路径解析完成，正在为 ${trimmedNfoId} 创建虚拟资产记录...`);
    event.sender.send('archive-scrape-progress', {
      stage: 'creating',
      message: `正在创建 ${trimmedNfoId} 的虚拟资产记录...`,
      progress: 85
    });

    const newAsset = await databaseService.createVirtualAsset(scrapedData, assetPaths.movieRootPath, assetPaths);

    // 步骤 4: 完成
    event.sender.send('archive-scrape-progress', {
      stage: 'complete',
      message: `${trimmedNfoId} 已成功作为虚拟资产入库！`,
      progress: 100
    });

    log.info(`[即时侦察] ${trimmedNfoId} 已成功作为虚拟资产入库，ID: ${newAsset.db_id}, 路径: ${assetPaths.movieRootPath}`);
    return {
      success: true,
      data: newAsset,
      assetPaths: assetPaths,
      message: `${trimmedNfoId} 已成功作为虚拟资产入库！`
    };

  } catch (error) {
    log.error(`[即时侦察] 处理 ${trimmedNfoId} 失败: ${error.message}`);
    event.sender.send('archive-scrape-progress', {
      stage: 'error',
      message: `处理 ${trimmedNfoId} 失败: ${error.message}`,
      progress: 0
    });
    return { success: false, error: error.message };
  }
});

// Collector 模块验证
ipcMain.handle('verify-collected-links-table', async (_event) => {
  return databaseService.verifyCollectedLinksTable();
});

// Collector 服务 IPC 处理器
ipcMain.handle('collector-start-task', async (event, { siteKey, targetUrl, options }) => {
  try {
    // 设置状态更新回调，向前端发送实时状态
    collectorService.setStatusUpdateCallback((statusUpdate) => {
      event.sender.send('collector-status-update', statusUpdate);
    });

    return await collectorService.startTask(siteKey, targetUrl, options);
  } catch (error) {
    log.error(`[IPC] Collector 启动任务失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-stop-task', async (_event) => {
  try {
    return await collectorService.stopTask();
  } catch (error) {
    log.error(`[IPC] Collector 停止任务失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 强制停止搜集任务
ipcMain.handle('collector-force-stop-task', async (_event) => {
  try {
    collectorService.forceStopCollectionTask();
    return { success: true };
  } catch (error) {
    log.error(`[IPC] Collector 强制停止任务失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-get-status', async (_event) => {
  try {
    return { success: true, status: collectorService.getTaskStatus() };
  } catch (error) {
    log.error(`[IPC] Collector 获取状态失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-get-forums', async (_event) => {
  try {
    const forums = collectorService.getSupportedForums();
    return { success: true, forums: forums };
  } catch (error) {
    log.error(`[IPC] Collector 获取论坛列表失败: ${error.message}`);
    return { success: false, error: error.message, forums: [] };
  }
});

ipcMain.handle('collector-configure-download', async (_event, config) => {
  try {
    return collectorService.configureDownload(config);
  } catch (error) {
    log.error(`[IPC] Collector 配置下载失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-get-data', async (_event, options) => {
  try {
    const result = databaseService.getCollectedLinks(options);
    return result;
  } catch (error) {
    log.error(`[IPC] Collector 获取数据失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-delete-link', async (_event, id) => {
  try {
    const success = databaseService.deleteCollectedLink(id);
    if (success) {
      return { success: true, message: '记录删除成功' };
    } else {
      return { success: false, error: '记录不存在或删除失败' };
    }
  } catch (error) {
    log.error(`[IPC] Collector 删除记录失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-update-md-path', async (_event, id, mdDocumentPath) => {
  try {
    const result = databaseService.updateCollectedLinkMdPath(id, mdDocumentPath);
    return result;
  } catch (error) {
    log.error(`[IPC] Collector 更新md文档路径失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('collector-analyze-with-ai', async (_event, recordId) => {
  try {
    log.info(`[IPC] 开始AI分析记录: ${recordId}`);

    // 1. 从数据库获取记录的完整信息
    const record = databaseService.getCollectedLinkById(recordId);
    if (!record) {
      return { success: false, error: '记录不存在' };
    }

    // 2. 获取用户设置的分类标签 - 现在从数据库获取
    const userCategories = aiCategoryService.getCategoryNames();

    if (userCategories.length === 0) {
      return { success: false, error: '未找到可用的分类标签，请先在设置中配置' };
    }

    // 3. 获取用户设置（包含Gemini API配置）
    const settings = settingsService.getSettings();

    // 4. 调用 LLM 智能分类引擎
    const aiTags = await aiAnalysisService.analyzePostWithLLM(record.post_body_text, userCategories, settings);

    if (aiTags && aiTags.length > 0) {
      // 4. 持久化 AI 标签到数据库
      const updateResult = databaseService.updateCollectedLinkAiTags(recordId, aiTags);

      if (updateResult.success) {
        // 5. 更新 .md 文件 (如果存在)
        if (record.md_document_path) {
          try {
            const fs = require('fs').promises;
            const mdContent = await fs.readFile(record.md_document_path, 'utf8');

            // 简单的frontmatter更新 - 在文档开头添加或更新ai_tags字段
            let updatedContent;
            if (mdContent.startsWith('---')) {
              // 已有frontmatter，更新ai_tags字段
              const frontmatterEnd = mdContent.indexOf('---', 3);
              if (frontmatterEnd !== -1) {
                const frontmatter = mdContent.substring(0, frontmatterEnd + 3);
                const content = mdContent.substring(frontmatterEnd + 3);

                // 移除现有的ai_tags行（如果存在）
                const lines = frontmatter.split('\n');
                const filteredLines = lines.filter(line => !line.trim().startsWith('ai_tags:'));

                // 在最后一个---之前添加ai_tags
                filteredLines.splice(-1, 0, `ai_tags: ${JSON.stringify(aiTags)}`);
                updatedContent = filteredLines.join('\n') + content;
              } else {
                updatedContent = mdContent;
              }
            } else {
              // 没有frontmatter，添加新的
              updatedContent = `---\nai_tags: ${JSON.stringify(aiTags)}\n---\n\n${mdContent}`;
            }

            await fs.writeFile(record.md_document_path, updatedContent, 'utf8');
            log.info(`[IPC] 已更新md文档的AI标签: ${record.md_document_path}`);
          } catch (mdError) {
            log.error(`[IPC] 更新md文档失败: ${mdError.message}`);
            // 不影响主要功能，继续返回成功
          }
        }

        return {
          success: true,
          message: 'AI分析完成',
          tags: aiTags,
          recordId: recordId
        };
      } else {
        return { success: false, error: updateResult.error };
      }
    } else {
      return { success: false, error: 'AI分析未生成有效标签' };
    }
  } catch (error) {
    log.error(`[IPC] AI分析失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// AI分类管理相关的IPC处理器
ipcMain.handle('ai-categories:get-all', async (_event) => {
  try {
    const categories = aiCategoryService.getAllCategories();
    return { success: true, data: categories };
  } catch (error) {
    log.error(`[IPC] 获取AI分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-categories:add', async (_event, name) => {
  try {
    const result = aiCategoryService.addCategory(name);
    return result;
  } catch (error) {
    log.error(`[IPC] 添加AI分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-categories:update', async (_event, { id, newName }) => {
  try {
    const result = aiCategoryService.updateCategory(id, newName);
    return result;
  } catch (error) {
    log.error(`[IPC] 更新AI分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-categories:delete', async (_event, id) => {
  try {
    const result = aiCategoryService.deleteCategory(id);
    return result;
  } catch (error) {
    log.error(`[IPC] 删除AI分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 历史档案复杂查询 IPC 处理器
ipcMain.handle('archive:complex-query', async (_event, filters) => {
  try {
    log.info(`[IPC] 开始历史档案复杂查询: ${JSON.stringify(filters)}`);
    const result = databaseService.queryArchive(filters);
    return { success: true, ...result };
  } catch (error) {
    log.error(`[IPC] 历史档案复杂查询失败: ${error.message}`);
    return { success: false, error: error.message, data: [], total: 0 };
  }
});

// 获取所有符合条件的链接 IPC 处理器
ipcMain.handle('archive:get-all-links-for-query', async (_event, filters) => {
  try {
    log.info(`[IPC] 开始获取所有链接: ${JSON.stringify(filters)}`);
    const links = databaseService.getAllLinksForQuery(filters);
    return { success: true, links };
  } catch (error) {
    log.error(`[IPC] 获取所有链接失败: ${error.message}`);
    return { success: false, error: error.message, links: [] };
  }
});

// 批量 AI 分析 IPC 处理器
ipcMain.handle('archive:batch-analyze-ai', async (event, recordIds) => {
  try {
    log.info(`[IPC] 开始批量AI分析: ${recordIds.length} 条记录`);

    // 立即返回，让 UI 解锁
    event.sender.send('batch-ai-progress', {
      status: 'started',
      total: recordIds.length,
      processed: 0,
      message: `已启动 ${recordIds.length} 条记录的批量分析任务`
    });

    // 在后台执行任务
    (async () => {
      let processedCount = 0;
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < recordIds.length; i++) {
        const recordId = recordIds[i];
        try {
          // 获取记录信息
          const record = databaseService.getCollectedLinkById(recordId);
          if (!record) {
            log.warn(`[批量AI分析] 记录不存在: ${recordId}`);
            errorCount++;
            event.sender.send('batch-ai-progress', {
              status: 'error',
              message: `记录 ${recordId} 不存在`,
              processed: processedCount + 1,
              total: recordIds.length
            });
            continue;
          }

          // 检查是否已经分析过
          if (record.ai_tags_json && record.ai_tags_json.trim() !== '') {
            log.info(`[批量AI分析] 记录 ${recordId} 已有AI标签，跳过分析`);
            processedCount++;
            event.sender.send('batch-ai-progress', {
              status: 'processing',
              processed: processedCount,
              total: recordIds.length,
              currentTitle: record.post_title,
              message: `跳过已分析的记录: ${record.post_title}`
            });
            continue;
          }

          // 发送开始处理当前记录的进度
          event.sender.send('batch-ai-progress', {
            status: 'processing',
            processed: processedCount,
            total: recordIds.length,
            currentTitle: record.post_title,
            message: `正在分析: ${record.post_title}`
          });

          // 获取用户设置的分类标签
          const userCategories = aiCategoryService.getCategoryNames();
          if (userCategories.length === 0) {
            log.warn(`[批量AI分析] 未找到可用的分类标签`);
            errorCount++;
            event.sender.send('batch-ai-progress', {
              status: 'error',
              message: `未找到可用的分类标签，请先在设置中配置`,
              processed: processedCount + 1,
              total: recordIds.length
            });
            continue;
          }

          // 调用AI分析
          const settings = settingsService.getSettings();
          const postText = record.post_body_text || record.full_post_text || record.post_title || '';

          const aiTags = await aiAnalysisService.analyzePostWithLLM(postText, userCategories, settings);

          if (aiTags && aiTags.length > 0) {
            // 保存AI标签到数据库
            const updateResult = databaseService.updateCollectedLinkAiTags(recordId, aiTags);
            if (updateResult.success) {
              successCount++;
              log.info(`[批量AI分析] 记录 ${recordId} 分析成功: ${JSON.stringify(aiTags)}`);
            } else {
              errorCount++;
              log.error(`[批量AI分析] 记录 ${recordId} 保存失败: ${updateResult.error}`);
            }
          } else {
            errorCount++;
            log.warn(`[批量AI分析] 记录 ${recordId} AI分析未返回有效标签`);
          }

          processedCount++;

          // 发送进度更新
          event.sender.send('batch-ai-progress', {
            status: 'processing',
            processed: processedCount,
            total: recordIds.length,
            currentTitle: record.post_title,
            successCount,
            errorCount
          });

        } catch (error) {
          errorCount++;
          processedCount++;
          log.error(`[批量AI分析] 处理记录 ${recordId} 时出错: ${error.message}`);
          event.sender.send('batch-ai-progress', {
            status: 'error',
            message: `处理记录 ${recordId} 时出错: ${error.message}`,
            processed: processedCount,
            total: recordIds.length,
            successCount,
            errorCount
          });
        }

        // 【安全流控】在两次调用之间加入延迟
        const delay = settingsService.getSettings().aiApiDelay || 1500; // 默认1.5秒
        if (i < recordIds.length - 1) { // 最后一个不需要延迟
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      // 发送完成信号
      event.sender.send('batch-ai-progress', {
        status: 'completed',
        total: recordIds.length,
        processed: processedCount,
        successCount,
        errorCount,
        message: `批量分析完成: 成功 ${successCount} 条，失败 ${errorCount} 条`
      });

      log.info(`[批量AI分析] 任务完成: 总计 ${recordIds.length} 条，成功 ${successCount} 条，失败 ${errorCount} 条`);
    })();

    return {
      success: true,
      message: `已启动 ${recordIds.length} 条记录的批量分析任务。`
    };

  } catch (error) {
    log.error(`[IPC] 批量AI分析启动失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 批量删除记录（用于重抓）
ipcMain.handle('collector-delete-links', async (_event, ids) => {
  try {
    if (!Array.isArray(ids) || ids.length === 0) {
      return { success: false, error: '无效的ID列表' };
    }

    let successCount = 0;
    let failCount = 0;

    for (const id of ids) {
      try {
        const success = databaseService.deleteCollectedLink(id);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        log.error(`[IPC] 删除记录 ${id} 失败: ${error.message}`);
        failCount++;
      }
    }

    if (successCount > 0) {
      return {
        success: true,
        message: `成功删除 ${successCount} 条记录${failCount > 0 ? `，${failCount} 条失败` : ''}`,
        successCount,
        failCount
      };
    } else {
      return { success: false, error: '所有记录删除失败' };
    }
  } catch (error) {
    log.error(`[IPC] Collector 批量删除记录失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 批量清除（记录+文件）
ipcMain.handle('collector-purge-links', async (_event, ids) => {
  try {
    if (!Array.isArray(ids) || ids.length === 0) {
      return { success: false, error: '无效的ID列表' };
    }

    const fs = require('fs');
    const path = require('path');
    let successCount = 0;
    let failCount = 0;

    for (const id of ids) {
      try {
        // 首先获取记录信息，以便删除相关文件
        const record = databaseService.getCollectedLinkById(id);

        // 删除数据库记录
        const deleteSuccess = databaseService.deleteCollectedLink(id);

        if (deleteSuccess) {
          // 尝试删除相关文件
          if (record && record.archive_path) {
            try {
              if (fs.existsSync(record.archive_path)) {
                fs.unlinkSync(record.archive_path);
                log.info(`[Purge] 已删除档案文件: ${record.archive_path}`);
              }
            } catch (fileError) {
              log.warn(`[Purge] 删除档案文件失败: ${record.archive_path}, 错误: ${fileError.message}`);
            }
          }

          if (record && record.download_path) {
            try {
              if (fs.existsSync(record.download_path)) {
                fs.unlinkSync(record.download_path);
                log.info(`[Purge] 已删除下载文件: ${record.download_path}`);
              }
            } catch (fileError) {
              log.warn(`[Purge] 删除下载文件失败: ${record.download_path}, 错误: ${fileError.message}`);
            }
          }

          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        log.error(`[IPC] 清除记录 ${id} 失败: ${error.message}`);
        failCount++;
      }
    }

    if (successCount > 0) {
      return {
        success: true,
        message: `成功清除 ${successCount} 条记录及相关文件${failCount > 0 ? `，${failCount} 条失败` : ''}`,
        successCount,
        failCount
      };
    } else {
      return { success: false, error: '所有记录清除失败' };
    }
  } catch (error) {
    log.error(`[IPC] Collector 批量清除失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 检查文件是否存在
ipcMain.handle('file-exists', async (_event, filePath) => {
  try {
    const fs = require('fs');
    const exists = fs.existsSync(filePath);
    log.info(`[IPC] 检查文件存在性: ${filePath} - ${exists ? '存在' : '不存在'}`);
    return exists;
  } catch (error) {
    log.error(`[IPC] 检查文件存在性失败: ${error.message}`);
    return false;
  }
});

// WhatsLink API 代理
ipcMain.handle('misc:query-whatslink', async (_event, magnetUrl) => {
  try {
    log.info(`[IPC] WhatsLink 查询请求: ${magnetUrl}`);

    if (!magnetUrl || typeof magnetUrl !== 'string') {
      throw new Error('磁力链接URL不能为空');
    }

    const encodedMagnetUrl = encodeURIComponent(magnetUrl);
    const apiUrl = `https://whatslink.info/api/v1/link?url=${encodedMagnetUrl}`;

    log.info(`[IPC] 请求 WhatsLink API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'SoulForge-Electron/1.0',
        'Accept': 'application/json'
      },
      timeout: 30000 // 30秒超时
    });

    if (!response.ok) {
      throw new Error(`WhatsLink API 请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    log.info(`[IPC] WhatsLink API 响应成功`);

    return {
      success: true,
      data: data
    };
  } catch (error) {
    log.error(`[IPC] WhatsLink 查询失败: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// 用系统默认程序打开文件
ipcMain.handle('open-file-in-default-app', async (_event, filePath) => {
  try {
    const { shell } = require('electron');
    const fs = require('fs');

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return { success: false, error: '文件不存在' };
    }

    // 使用系统默认程序打开文件
    await shell.openPath(filePath);
    log.info(`[IPC] 已用默认程序打开文件: ${filePath}`);
    return { success: true, message: '文件已打开' };
  } catch (error) {
    log.error(`[IPC] 打开文件失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 选择文件夹对话框
ipcMain.handle('select-folder', async (_event) => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ['openDirectory'],
      title: '选择工作区文件夹'
    });

    if (result.canceled) {
      return { success: false, canceled: true };
    }

    return { success: true, folderPath: result.filePaths[0] };
  } catch (error) {
    log.error(`[IPC] 选择文件夹失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// 选择文件对话框
ipcMain.handle('select-file', async (_event, options) => {
  try {
    const result = await dialog.showOpenDialog({
      title: options.title || '选择文件',
      filters: options.filters || [{ name: '所有文件', extensions: ['*'] }],
      properties: ['openFile']
    });

    return result;
  } catch (error) {
    log.error(`[IPC] 选择文件失败: ${error.message}`);
    return { canceled: true, error: error.message };
  }
});

ipcMain.handle('collector-user-confirmation', async (_event, confirmed) => {
  try {
    collectorService.handleUserConfirmation(confirmed);
    return { success: true };
  } catch (error) {
    log.error(`[IPC] Collector 用户确认失败: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('generate-thumbnails', async (_event, params) => {
    log.info(`[主进程] 收到快照生成请求，参数: ${JSON.stringify({
        videoDbId: params.videoDbId,
        videoFilePath: params.videoFilePath ? params.videoFilePath.substring(0, 50) + '...' : 'null',
        snapshotQuality: params.snapshotQuality
    })}`);

    const settings = settingsService.getSettings();
    const quality = params.snapshotQuality || settings.snapshotQuality || 'hd_640p';
    const cacheBasePath = settings.snapshotCachePath || defaultThumbnailCacheBasePathInternal;

    log.info(`[主进程] 快照生成配置: FFmpeg路径=${settings.ffmpegPath || 'ffmpeg'}, 缓存路径=${cacheBasePath}`);
    log.info(`[主进程] 注意: 已移除Python依赖，现在使用JavaScript/Node.js直接调用FFmpeg`);

    // 检查必要的参数
    if (!params.videoFilePath) {
        log.error(`[主进程] 快照生成失败: 视频文件路径为空`);
        return {success: false, error: '视频文件路径为空'};
    }

    if (!fs.existsSync(params.videoFilePath)) {
        log.error(`[主进程] 快照生成失败: 视频文件不存在: ${params.videoFilePath}`);
        return {success: false, error: `视频文件不存在: ${params.videoFilePath}`};
    }

    if (!fs.existsSync(cacheBasePath)) {
        try { fs.mkdirSync(cacheBasePath, { recursive: true }); }
        catch (e) { log.error(`创建快照缓存目录 ${cacheBasePath} 失败:`, e); return {success:false, error: `创建缓存目录失败: ${e.message}`};}
    }

    // 自动检测正确的 FFmpeg 路径
    let ffmpegPath = settings.ffmpegPath || 'ffmpeg';

    // 如果设置的路径包含错误的 ffprobe_binaries，自动修正为 ffmpeg_binaries
    if (ffmpegPath.includes('ffprobe_binaries')) {
        ffmpegPath = ffmpegPath.replace('ffprobe_binaries', 'ffmpeg_binaries');
        log.info(`[主进程] 自动修正 FFmpeg 路径: ${ffmpegPath}`);
    }

    // 如果是相对路径或默认值，尝试使用内置的 FFmpeg
    if (ffmpegPath === 'ffmpeg' || !path.isAbsolute(ffmpegPath)) {
        const builtinFFmpegPath = path.join(__dirname, 'build', 'ffmpeg_binaries', 'x64', 'ffmpeg.exe');
        if (fs.existsSync(builtinFFmpegPath)) {
            ffmpegPath = builtinFFmpegPath;
            log.info(`[主进程] 使用内置 FFmpeg: ${ffmpegPath}`);
        }
    }

    // 使用新的JavaScript快照生成服务
    return snapshotGeneratorService.generateSnapshots(
        params.videoFilePath,
        params.videoDbId,
        quality,
        ffmpegPath,
        cacheBasePath,
        fileUtils.imagePathToDataUrl
    );
});
ipcMain.handle('get-existing-snapshots', async (_event, params) => {
    const cacheBasePath = settingsService.getSettings().snapshotCachePath || defaultThumbnailCacheBasePathInternal;
    const snapshots = snapshotUtils.getExistingSnapshotsInfo(params.movieDbId, cacheBasePath, fileUtils.imagePathToDataUrl);
    return { success: true, snapshots: snapshots || [] };
});
ipcMain.handle('movie:get-all-snapshots-for-nfoId', async (_event, nfoId) => {
    try {
        const groupedSnapshots = await databaseService.getAllSnapshotsForNfoId(nfoId);
        return { success: true, data: groupedSnapshots };
    } catch (error) {
        log.error(`[主进程] 获取 nfoId ${nfoId} 的全量快照失败: ${error.message}`, error);
        return { success: false, error: error.message };
    }
});
ipcMain.handle('save-version-marks', async (_event, params) => databaseService.saveVersionMarksToDb(params, fileUtils.imagePathToDataUrl));
ipcMain.handle('rename-files-by-nfo-id', async (_event, nfoId) => {
  const settings = settingsService.getSettings();
  return databaseService.renameFilesByNfoIdInDb(nfoId, settings.filenameRenameTemplate, pythonRunnerService, settings.pythonExecutablePath);
});
ipcMain.handle('backup-database', async () => databaseService.backupDatabase(userDataPath));
ipcMain.handle('restore-database', async (_event) => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [{ name: 'SQLite Databases', extensions: ['sqlite', 'db', 'sqlite3'] }]
  });
  if (result.canceled || result.filePaths.length === 0) {
    return { success: false, error: '用户取消恢复操作。' };
  }
  const backupFilePath = result.filePaths[0];
  return databaseService.restoreDatabase(backupFilePath, dbPath);
});
ipcMain.handle('move-to-recycle-bin', async (_event, filePath) => {
  try {
    await shell.trashItem(filePath);
    log.info(`[IPC] 文件已移至回收站: ${filePath}`);
    return { success: true, message: `文件 "${path.basename(filePath)}" 已移至回收站。` };
  } catch (error) {
    log.error(`[IPC] 移动文件到回收站失败: ${filePath}`, error);
    return { success: false, error: `移动文件到回收站失败: ${error.message}` };
  }
});
ipcMain.handle('get-path-sep', () => path.sep);
ipcMain.handle('test-ai-connection', async (_event, provider, config) => aiService.testAiConnectionWithProvider(settingsService.getStore(), provider, config));

// Favorites IPC Handlers
ipcMain.handle('toggle-favorite', async (_event, { itemType, itemValue }) => databaseService.toggleFavorite(itemType, itemValue));
ipcMain.handle('is-favorite', async (_event, { itemType, itemValue }) => databaseService.isFavoriteItem(itemType, itemValue));
ipcMain.handle('batch-check-favorites', async (_event, items) => databaseService.batchCheckFavoriteItems(items));
ipcMain.handle('get-favorites', async (_event, itemType) => databaseService.getFavoriteItems(itemType));

// Privacy Mode IPC Handlers
ipcMain.handle('get-privacy-mode-state', async () => {
  const settings = settingsService.getSettings();
  return {
    isEnabled: !!settings.privacyModeEnabled,
    isLocked: !!settings.privacyModeEnabled && !!settings.privacyModePassword, 
  };
});

ipcMain.handle('attempt-unlock-privacy-mode', async (_event, password) => {
  const settings = settingsService.getSettings();
  if (!settings.privacyModeEnabled) {
    return { success: false, error: '隐私模式未启用。' };
  }
  if (settings.privacyModePassword === password) {
    log.info('[IPC] 隐私模式密码验证成功。');
    return { success: true };
  } else {
    log.warn('[IPC] 隐私模式密码验证失败。');
    return { success: false, error: '密码错误。' };
  }
});

ipcMain.handle('toggle-privacy-mode-no-password', async (_event) => {
  const currentSettings = settingsService.getSettings();
  if (currentSettings.privacyModePassword) {
    log.warn('[IPC] 尝试无密码切换已设置密码的隐私模式。不允许。');
    return { success: false, error: '隐私模式已设置密码，无法无密码切换。请在设置中清除密码。', newState: currentSettings.privacyModeEnabled };
  }
  const newIsEnabledState = !currentSettings.privacyModeEnabled;
  const saveResult = settingsService.saveSettings({ ...currentSettings, privacyModeEnabled: newIsEnabledState });
  if (saveResult.success) {
    log.info(`[IPC] 隐私模式已切换 (无密码)。新状态: ${newIsEnabledState}`);
    _event.sender.send('privacy-settings-changed'); 
    return { success: true, newState: newIsEnabledState };
  } else {
    log.error('[IPC] 保存隐私模式切换 (无密码) 失败:', saveResult.error);
    return { success: false, error: saveResult.error || '保存切换状态失败。', newState: currentSettings.privacyModeEnabled };
  }
});


// Recommendation IPC Handlers
ipcMain.handle('get-initial-recommendations', async (_event, params) => databaseService.getRecommendationsByFavorites(params));
ipcMain.handle('format-recommendations-as-ai-message', async (_event, params) => aiService.formatRecommendationsAsAiMessage(settingsService.getStore(), params));

// NFO Plot Polisher Tool IPC Handlers
ipcMain.on('nfo-polish-tool:scan-directories', (event, directories) => {
  if (!directories || directories.length === 0) {
    log.warn('[NFO剧情润色工具IPC] 未提供扫描目录.');
    event.sender.send('nfo-polish-tool:error', { filePath: null, error: '未提供扫描目录。' });
    return;
  }
  log.info(`[NFO剧情润色工具IPC] 收到扫描请求, 目录: ${directories.join(', ')}`);
  nfoPlotPolisherService.scanDirectoriesForNfoFiles(directories, event.sender)
    .catch(err => {
      log.error('[NFO剧情润色工具IPC] scanDirectoriesForNfoFiles 发生未捕获错误:', err);
      event.sender.send('nfo-polish-tool:error', { filePath: null, error: `扫描时发生严重错误: ${err.message}` });
    });
});

ipcMain.on('nfo-polish-tool:process-files', (event, nfoFilePaths) => {
  if (!nfoFilePaths || nfoFilePaths.length === 0) {
    log.warn('[NFO剧情润色工具IPC] 未提供待处理的NFO文件列表.');
    event.sender.send('nfo-polish-tool:error', { filePath: null, error: '未提供NFO文件列表进行处理。' });
    return;
  }
  log.info(`[NFO剧情润色工具IPC] 收到处理请求, 文件数: ${nfoFilePaths.length}`);
  nfoPlotPolisherService.processNfoFiles(nfoFilePaths, event.sender)
    .catch(err => {
      log.error('[NFO剧情润色工具IPC] processNfoFiles 发生未捕获错误:', err);
      event.sender.send('nfo-polish-tool:error', { filePath: null, error: `处理时发生严重错误: ${err.message}` });
    });
});

ipcMain.on('nfo-polish-tool:cancel-operation', () => {
  log.info('[NFO剧情润色工具IPC] 收到取消操作请求。');
  if (nfoPlotPolisherService.cancelCurrentOperation) {
    nfoPlotPolisherService.cancelCurrentOperation();
  } else {
    log.warn('[NFO剧情润色工具IPC] 服务中未找到 cancelCurrentOperation 方法。');
  }
});

// Movie Libraries IPC Handlers
ipcMain.handle('manage-movie-library', async (_event, params) => {
  // params: { id?, name, paths } or { id, delete: true }
  return databaseService.manageMovieLibrary(params);
});

ipcMain.handle('get-movie-libraries', async () => {
  return databaseService.getMovieLibraries();
});

// Smart Libraries IPC Handlers
ipcMain.handle('get-smart-libraries', async () => {
  try {
    return { success: true, libraries: databaseService.getSmartLibraries() };
  } catch (error) {
    log.error('[智能片库IPC] 获取智能片库失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('create-smart-library', async (_event, libraryData) => {
  try {
    const result = databaseService.createSmartLibrary(libraryData);
    return { success: true, ...result };
  } catch (error) {
    log.error('[智能片库IPC] 创建智能片库失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-smart-library', async (_event, id, updateData) => {
  try {
    const result = databaseService.updateSmartLibrary(id, updateData);
    return { success: true, ...result };
  } catch (error) {
    log.error('[智能片库IPC] 更新智能片库失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-smart-library', async (_event, id) => {
  try {
    const result = databaseService.deleteSmartLibrary(id);
    return { success: true, ...result };
  } catch (error) {
    log.error('[智能片库IPC] 删除智能片库失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-recent-movies', async (_event, { fetchType, limit }) => {
  // This will call a new method in databaseService to fetch recent movies
  return databaseService.getRecentMovies(fetchType, limit);
});

// Dashboard IPC Handlers
ipcMain.handle('dashboard:get-activity', async (_event) => {
  try {
    const dashboardService = require('./main_process/services/dashboardService');
    return await dashboardService.getRecentActivity();
  } catch (error) {
    log.error('[IPC] 获取看板活动失败:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
});

ipcMain.handle('dashboard:get-stats', async (_event) => {
  try {
    const dashboardService = require('./main_process/services/dashboardService');
    return await dashboardService.getLibraryStats();
  } catch (error) {
    log.error('[IPC] 获取看板统计失败:', error);
    return {
      success: false,
      error: error.message,
      data: {
        movieCount: 0,
        linkCount: 0,
        actorCount: 0
      }
    };
  }
});


// --- App Lifecycle ---
app.whenReady().then(async () => { // Make this async
  log.info('[主进程] Electron 应用已就绪 (ready event)。');
  try {
    databaseService.connectAndSetupDatabase();
    log.info('[主进程] 数据库连接和结构设置完成。');

    // 初始化演员档案服务
    const { actorProfileService } = require('./main_process/services/actorProfileService');
    actorProfileService.initialize(databaseService.getDatabase(), app.getPath('userData'));
    log.info('[主进程] 演员档案服务初始化完成。');

    // 初始化AI分类服务（需要在数据库初始化完成后）
    aiCategoryService.initializeAiCategoryService(log, databaseService.getDatabase());

    // Filetree loading is now handled on demand during scraping

    // 创建窗口并自动启动Vite服务器
    await createWindow();
  } catch(dbError) {
    log.fatal('[主进程] 数据库初始化失败，应用无法启动。', dbError);
    dialog.showErrorBox("数据库错误", `无法初始化数据库: ${dbError.message}\n应用将退出。`);
    app.quit();
    return;
  }

  // 自动修复 NFO ID 问题
  setTimeout(async () => {
    try {
      log.info('[主进程] 开始自动修复 NFO ID...');
      const NodeBatchNfoFixer = require('./main_process/services/nodeBatchNfoFixer');
      const fixResult = await NodeBatchNfoFixer.fixAllMissingNfoIds(databaseService, log);

      if (fixResult.success && fixResult.updated > 0) {
        log.info(`[主进程] ✅ NFO ID 自动修复完成: 成功修复 ${fixResult.updated} 部电影`);
        log.info('[主进程] 🎉 多版本合并功能现在应该正常工作了！');

        // 通知前端刷新数据
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('nfo-fix-completed', {
            success: true,
            updated: fixResult.updated,
            message: `成功修复 ${fixResult.updated} 部电影的 NFO ID，多版本合并功能已启用！`
          });
        }
      } else if (fixResult.success) {
        log.info('[主进程] NFO ID 检查完成: 无需修复');
      } else {
        log.warn(`[主进程] NFO ID 自动修复失败: ${fixResult.error}`);
      }
    } catch (error) {
      log.error('[主进程] NFO ID 自动修复异常:', error);
    }
  }, 3000); // 延迟3秒执行，确保应用完全启动

  // 移除重复的createWindow()调用，因为已经在上面的try块中调用了

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    log.info('[主进程] 所有窗口已关闭，应用退出。');
    databaseService.closeDatabase();
    app.quit();
  }
});
app.on('will-quit', () => {
  log.info('[主进程] 应用即将退出事件。');
  databaseService.closeDatabase();
});
process.on('unhandledRejection', (reason, promise) => {
  log.error('[主进程] 未处理的 Promise Rejection:', reason, 'Promise:', promise);
  log.error('[主进程] 错误堆栈:', reason?.stack || '无堆栈信息');

  // 不立即退出应用，只记录错误
  // 如果是关键错误，让具体的模块处理
});

process.on('uncaughtException', (error) => {
  log.fatal('[主进程] 捕获到未处理的异常:', error);
  log.fatal('[主进程] 错误堆栈:', error.stack);

  // 检查是否是关键错误
  const isCriticalError = error.message.includes('数据库') ||
                         error.message.includes('ENOENT') ||
                         error.message.includes('权限') ||
                         error.code === 'EACCES';

  if (isCriticalError) {
    dialog.showErrorBox("发生严重错误", `主进程遇到无法恢复的错误: ${error.message}\n应用即将退出。\n请查看日志文件获取更多信息。`);
    try { databaseService.closeDatabase(); } catch (e) { log.error("关闭数据库时也发生错误:", e); }
    app.quit();
  } else {
    // 非关键错误，只记录但不退出应用
    log.warn('[主进程] 非关键错误，应用继续运行');
  }
});