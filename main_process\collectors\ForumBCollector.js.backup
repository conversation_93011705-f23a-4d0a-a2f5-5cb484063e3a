/**
 * 论坛B (98堂) 采集器
 * 
 * 继承自ForumACollector，专门修复论坛B的特定问题：
 * 1. 重命名错误 - 异步竞态条件修复
 * 2. GIF误抓 - 精确的附件选择器
 * 3. ED2K截断 - 增强的正则表达式
 */

const ForumACollector = require('./ForumACollector');
const path = require('path');
const fs = require('fs');

class ForumBCollector extends ForumACollector {
  constructor(config) {
    super(config);
    this.forumName = '98堂';
  }

  /**
   * 🔧 修复：提取ED2K链接 - 解决截断问题
   * 使用增强的正则表达式，支持换行符匹配
   */
  async parsePostContent(page, siteProfile, postUrl) {
    try {
      // 抓取帖子标题
      let postTitle = '';
      try {
        await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
        postTitle = await page.locator(siteProfile.postTitleSelector).first().textContent();
        postTitle = postTitle ? postTitle.trim() : '';
      } catch (error) {
        postTitle = await page.title() || `帖子`;
      }

      // 🔧 新增：提取帖子正文内容进行元数据解析
      let postBodyText = '';
      let extractedMetadata = {};
      let cloudLinks = []; // 🔧 新增：网盘链接数组

      try {
        if (siteProfile.postBodyContainerSelector) {
          this.log.info(`[${this.forumName}] 🔍 开始提取帖子正文内容...`);
          await page.waitForSelector(siteProfile.postBodyContainerSelector, { timeout: 5000 });

          // 获取主内容区域的纯文本
          const bodyElements = await page.locator(siteProfile.postBodyContainerSelector).all();
          if (bodyElements.length > 0) {
            postBodyText = await bodyElements[0].innerText();
            this.log.info(`[${this.forumName}] 📄 提取到正文内容长度: ${postBodyText.length} 字符`);

            // 解析元数据
            extractedMetadata = this.extractMetadataFromText(postBodyText);
            this.log.info(`[${this.forumName}] 📊 提取的元数据:`, extractedMetadata);

            // 🔧 新增：提取网盘链接与提取码
            cloudLinks = this.extractCloudLinksWithCodes(postBodyText);
            if (cloudLinks.length > 0) {
              this.log.info(`[${this.forumName}] 🌐 提取到 ${cloudLinks.length} 个网盘链接`);
              cloudLinks.forEach((link, index) => {
                this.log.info(`[${this.forumName}] 🌐 网盘链接 ${index + 1}: ${link.url} (提取码: ${link.code})`);
              });
            }
          }
        }
      } catch (error) {
        this.log.warn(`[${this.forumName}] ⚠️ 提取正文内容失败: ${error.message}`);
      }

      // 提取NFO ID（优先使用从正文提取的品番，其次使用标题）
      const NodeNfoParser = require('../services/nodeNfoParser');
      let nfoId = extractedMetadata.nfoId || NodeNfoParser.extractJavIdFromFilename(postTitle);

      // 抓取磁力链接 - 支持从li标签内提取，修复截断问题
      let magnetLinks = [];
      try {
        const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
        this.log.info(`[${this.forumName}] 🧲 查找磁力链接，使用选择器: ${siteProfile.magnetLinkSelector}`);
        this.log.info(`[${this.forumName}] 🧲 找到 ${magnetElements.length} 个磁力链接元素`);

        for (const element of magnetElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('magnet:')) {
              // 使用更强大的正则表达式提取完整磁力链接
              const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
              if (magnetMatches) {
                magnetMatches.forEach(link => {
                  // 清理HTML标签
                  const cleanLink = link.replace(/<[^>]*>/g, '').trim();
                  magnetLinks.push(cleanLink);
                  this.log.info(`[${this.forumName}] 🧲 从li标签提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('magnet:')) {
              magnetLinks.push(href.trim());
              this.log.info(`[${this.forumName}] 🧲 从href属性提取磁力链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('magnet:')) {
                const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
                if (magnetMatches) {
                  magnetMatches.forEach(link => {
                    const cleanLink = link.replace(/<[^>]*>/g, '').trim();
                    magnetLinks.push(cleanLink);
                    this.log.info(`[${this.forumName}] 🧲 从HTML内容提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        this.log.info(`[${this.forumName}] 🧲 总共提取到 ${magnetLinks.length} 个磁力链接`);
      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法获取磁力链接: ${error.message}`);
      }

      // 🔧 关键修复：抓取ED2K链接 - 解决截断问题
      let ed2kLinks = [];
      try {
        const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
        this.log.info(`[${this.forumName}] 🔗 查找ED2K链接，使用选择器: ${siteProfile.ed2kLinkSelector}`);
        this.log.info(`[${this.forumName}] 🔗 找到 ${ed2kElements.length} 个ED2K链接元素`);

        for (const element of ed2kElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('ed2k:')) {
              // 🔧 关键修复：使用增强的正则表达式，支持换行符
              const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/gs);
              if (ed2kMatches) {
                ed2kMatches.forEach(link => {
                  // 清理HTML标签和多余空白
                  const cleanLink = link.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
                  ed2kLinks.push(cleanLink);
                  this.log.info(`[${this.forumName}] 🔗 从li标签提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('ed2k:')) {
              ed2kLinks.push(href.trim());
              this.log.info(`[${this.forumName}] 🔗 从href属性提取ED2K链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('ed2k:')) {
                // 🔧 关键修复：使用增强的正则表达式，支持换行符
                const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/gs);
                if (ed2kMatches) {
                  ed2kMatches.forEach(link => {
                    // 清理HTML标签和多余空白
                    const cleanLink = link.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
                    ed2kLinks.push(cleanLink);
                    this.log.info(`[${this.forumName}] 🔗 从HTML内容提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        this.log.info(`[${this.forumName}] 🔗 总共提取到 ${ed2kLinks.length} 个ED2K链接`);
      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法获取ED2K链接: ${error.message}`);
      }

      // 继续其他内容...
      return this.buildPostData(postUrl, postTitle, nfoId, magnetLinks, ed2kLinks, extractedMetadata, cloudLinks, postBodyText, page, siteProfile);

    } catch (error) {
      this.log.error(`[${this.forumName}] 解析帖子内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 🔧 修复：下载附件 - 解决重命名错误和GIF误抓问题
   * 关键修复：确保postData正确传递，避免异步竞态条件
   */
  async downloadAttachments(page, postData, siteProfile) {
    const { postTitle, decompressionPassword, attachmentUrl } = postData;

    if (!attachmentUrl) {
      this.log.info(`[${this.forumName}] 跳过下载: 没有附件链接`);
      return { success: false, message: '没有附件链接' };
    }

    const attachmentUrls = attachmentUrl.split('\n').filter(url => url.trim());
    this.log.info(`[${this.forumName}] 发现 ${attachmentUrls.length} 个附件链接`);

    // 🔧 修正：使用全局统一的attachments目录
    const attachmentsDir = path.join(this.workspacePath, 'attachments');

    // 确保目录存在
    if (!fs.existsSync(attachmentsDir)) {
      fs.mkdirSync(attachmentsDir, { recursive: true });
    }

    let downloadSuccess = false;

    for (let i = 0; i < attachmentUrls.length; i++) {
      const url = attachmentUrls[i].trim();

      try {
        this.updateTaskStatus('downloading', `正在下载附件 ${i + 1}/${attachmentUrls.length}: ${postTitle}`);

        // 导航到帖子页面
        await page.goto(postData.postUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        await page.waitForTimeout(2000);

        // 🔧 GIF误抓修复：使用精确的附件选择器
        // 注意：这里使用的是site-profiles.json中更新后的选择器
        const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
        this.log.info(`[${this.forumName}] 🔗 使用精确选择器找到 ${attachmentElements.length} 个附件元素`);

        if (attachmentElements.length === 0) {
          this.log.warn(`[${this.forumName}] 未找到附件元素，跳过下载`);
          continue;
        }

        // 🔧 修正：遍历所有附件元素进行下载
        for (let attachmentIndex = 0; attachmentIndex < attachmentElements.length; attachmentIndex++) {
          this.log.info(`[${this.forumName}] 准备下载附件 ${attachmentIndex + 1}/${attachmentElements.length}`);

          // 🔧 关键修复：创建下载处理函数，确保postData正确传递
          const success = await this.downloadSingleAttachment(
            page,
            attachmentElements[attachmentIndex],
            postData, // 🔧 关键：显式传递postData
            attachmentsDir,
            attachmentIndex
          );

          if (success) {
            downloadSuccess = true;
          }

          // 多附件下载间隔
          if (attachmentIndex < attachmentElements.length - 1) {
            await this.delay(2000);
          }
        }

      } catch (error) {
        this.log.error(`[${this.forumName}] 下载附件失败: ${error.message}`);

        if (error.message.includes('下载次数已达上限') ||
            error.message.includes('需要人机验证')) {
          throw error;
        }
      }

      // 下载间隔
      if (i < attachmentUrls.length - 1) {
        await this.delay(2000);
      }
    }

    return { success: downloadSuccess, message: downloadSuccess ? '下载成功' : '下载失败' };
  }

  /**
   * 🔧 关键修复：下载单个附件，确保postData正确传递
   * 这个方法解决了异步竞态条件导致的重命名错误问题
   */
  async downloadSingleAttachment(page, attachmentElement, postData, attachmentsDir, attachmentIndex) {
    try {
      this.log.info(`[${this.forumName}] 🔗 开始下载附件 ${attachmentIndex + 1}`);

      // 设置popup事件监听器
      const popupPromise = page.waitForEvent('popup');

      // 点击附件链接
      await attachmentElement.click();

      // 等待并捕获弹窗
      const popupPage = await popupPromise;
      this.log.info(`[${this.forumName}] 成功捕获弹窗页面: ${popupPage.url()}`);

      // 🔧 关键修复：创建下载处理函数，将postData作为闭包变量
      let downloadCompleted = false;
      let downloadPath = null;

      const downloadHandler = async (download) => {
        try {
          const originalFileName = download.suggestedFilename();
          this.log.info(`[${this.forumName}] 开始下载: ${originalFileName}`);

          // 🔧 关键修复：使用闭包中的postData，确保数据正确
          // 这里的postData是函数参数，不会被外层循环影响
          const newFileName = this.fileNameBuilder ?
            this.fileNameBuilder.buildStandardFileName(postData, originalFileName) :
            originalFileName;
          const fullPath = path.join(attachmentsDir, newFileName);

          this.log.info(`[${this.forumName}] 🔧 重命名修复：使用正确的postData`);
          this.log.info(`[${this.forumName}] - 帖子标题: ${postData.postTitle}`);
          this.log.info(`[${this.forumName}] - 原文件名: ${originalFileName}`);
          this.log.info(`[${this.forumName}] - 新文件名: ${newFileName}`);

          // 保存文件
          await download.saveAs(fullPath);
          await page.waitForTimeout(1000);

          // 验证文件是否存在
          if (fs.existsSync(fullPath)) {
            const stats = fs.statSync(fullPath);
            this.log.info(`[${this.forumName}] ✅ 下载成功: ${newFileName} (${stats.size} bytes)`);

            downloadCompleted = true;
            downloadPath = fullPath;

            // 更新数据库状态
            if (this.databaseService) {
              this.databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
            }
          } else {
            throw new Error('文件保存失败');
          }

        } catch (error) {
          this.log.error(`[${this.forumName}] 下载处理失败: ${error.message}`);
          if (this.databaseService) {
            this.databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, error.message);
          }
        }
      };

      // 为主页面和弹窗页面都设置下载监听器
      page.on('download', downloadHandler);
      popupPage.on('download', downloadHandler);

      // 等待下载完成或超时
      const timeout = 30000;
      const startTime = Date.now();

      while (!downloadCompleted && (Date.now() - startTime) < timeout) {
        await page.waitForTimeout(1000);
      }

      // 清理事件监听器
      page.off('download', downloadHandler);
      popupPage.off('download', downloadHandler);

      // 关闭弹窗
      try {
        await popupPage.close();
      } catch (error) {
        this.log.warn(`[${this.forumName}] 关闭弹窗失败: ${error.message}`);
      }

      if (downloadCompleted) {
        this.log.info(`[${this.forumName}] ✅ 附件 ${attachmentIndex + 1} 下载成功: ${downloadPath}`);
        return true;
      } else {
        this.log.warn(`[${this.forumName}] ⏰ 附件 ${attachmentIndex + 1} 下载超时`);
        return false;
      }

    } catch (error) {
      this.log.error(`[${this.forumName}] 下载单个附件失败: ${error.message}`);
      return false;
    }
  }
}

module.exports = ForumBCollector;
