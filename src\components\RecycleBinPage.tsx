import React, { useState, useEffect } from 'react';
import { Trash2, Refresh<PERSON><PERSON>, Download, FolderOpen, AlertTriangle, CheckSquare, Square, RotateCcw } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface RecycledItem {
  db_id: number;
  nfoId: string;
  title: string;
  fileName: string;
  filePath: string;
  recycledAt: string | null;
  type: 'movie' | 'version';
}

interface RecycledItemsData {
  success: boolean;
  totalCount: number;
  moviesCount: number;
  versionsCount: number;
  items: {
    movies: RecycledItem[];
    versions: RecycledItem[];
  };
  error?: string;
}

export const RecycleBinPage: React.FC = () => {
  const [recycledItems, setRecycledItems] = useState<RecycledItemsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isGeneratingList, setIsGeneratingList] = useState(false);

  // 加载回收站项目
  const loadRecycledItems = async () => {
    setIsLoading(true);
    try {
      const result = await window.sfeElectronAPI.getRecycledItems();
      setRecycledItems(result);
      
      if (!result.success) {
        toast.error(`加载回收站失败: ${result.error}`);
      }
    } catch (error) {
      console.error('加载回收站项目失败:', error);
      toast.error('加载回收站项目失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadRecycledItems();
  }, []);

  // 生成115删除清单
  const handleGenerateDeletionList = async () => {
    setIsGeneratingList(true);
    try {
      const result = await window.sfeElectronAPI.generateDeletionList();
      
      if (result.success) {
        if (result.itemsCount === 0) {
          toast.success('没有需要删除的项目');
        } else {
          toast.success(
            `删除清单生成成功！\n` +
            `包含 ${result.pathsCount} 个文件路径\n` +
            `文件已保存到: ${result.filePath}`,
            { duration: 8000 }
          );
          
          // 提供打开文件位置的选项
          if (result.filePath) {
            const openLocation = window.confirm(
              `删除清单已生成完成！\n\n` +
              `文件位置: ${result.filePath}\n` +
              `包含项目: ${result.itemsCount} 个\n` +
              `文件路径: ${result.pathsCount} 个\n\n` +
              `是否打开文件所在位置？`
            );
            
            if (openLocation) {
              await window.sfeElectronAPI.openFileLocation(result.filePath);
            }
          }
        }
      } else {
        toast.error(`生成删除清单失败: ${result.error}`);
      }
    } catch (error) {
      console.error('生成删除清单失败:', error);
      toast.error('生成删除清单失败');
    } finally {
      setIsGeneratingList(false);
    }
  };

  // 切换项目选择状态
  const toggleItemSelection = (itemKey: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemKey)) {
      newSelected.delete(itemKey);
    } else {
      newSelected.add(itemKey);
    }
    setSelectedItems(newSelected);
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (!recycledItems) return;
    
    const allItems = [
      ...recycledItems.items.movies.map(item => `movie-${item.db_id}`),
      ...recycledItems.items.versions.map(item => `version-${item.db_id}`)
    ];
    
    if (selectedItems.size === allItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(allItems));
    }
  };

  // 格式化回收时间
  const formatRecycledTime = (recycledAt: string | null): string => {
    if (!recycledAt) return '未知时间';
    
    try {
      const date = new Date(recycledAt);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '时间格式错误';
    }
  };

  // 渲染项目行
  const renderItemRow = (item: RecycledItem, index: number) => {
    const itemKey = `${item.type}-${item.db_id}`;
    const isSelected = selectedItems.has(itemKey);
    
    return (
      <tr key={itemKey} className={`border-b border-gray-700 hover:bg-gray-700/50 ${isSelected ? 'bg-blue-900/30' : ''}`}>
        <td className="px-4 py-3">
          <button
            onClick={() => toggleItemSelection(itemKey)}
            className="text-gray-400 hover:text-white"
          >
            {isSelected ? <CheckSquare className="h-4 w-4" /> : <Square className="h-4 w-4" />}
          </button>
        </td>
        <td className="px-4 py-3">
          <div className="flex items-center gap-2">
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              item.type === 'movie' 
                ? 'bg-red-900 text-red-200' 
                : 'bg-orange-900 text-orange-200'
            }`}>
              {item.type === 'movie' ? '整部影片' : '单一版本'}
            </span>
            <span className="text-white font-medium">{item.nfoId}</span>
          </div>
        </td>
        <td className="px-4 py-3 text-gray-300">
          <div className="max-w-xs truncate" title={item.title || item.fileName}>
            {item.title || item.fileName}
          </div>
        </td>
        <td className="px-4 py-3 text-gray-400 text-sm">
          {formatRecycledTime(item.recycledAt)}
        </td>
        <td className="px-4 py-3">
          <div className="flex items-center gap-2">
            <button
              className="text-blue-400 hover:text-blue-300 text-sm flex items-center gap-1"
              title="恢复此项目"
              disabled
            >
              <RotateCcw className="h-3 w-3" />
              恢复
            </button>
            <button
              className="text-red-400 hover:text-red-300 text-sm flex items-center gap-1"
              title="永久删除此项目"
              disabled
            >
              <Trash2 className="h-3 w-3" />
              删除
            </button>
          </div>
        </td>
      </tr>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#B8860B]"></div>
            <span className="ml-3 text-gray-400">正在加载回收站...</span>
          </div>
        </div>
      </div>
    );
  }

  const allItems = recycledItems ? [
    ...recycledItems.items.movies,
    ...recycledItems.items.versions
  ] : [];

  const allItemKeys = allItems.map(item => `${item.type}-${item.db_id}`);
  const isAllSelected = allItemKeys.length > 0 && selectedItems.size === allItemKeys.length;

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Trash2 className="h-8 w-8 text-[#B8860B]" />
            <div>
              <h1 className="text-3xl font-bold text-white">回收站</h1>
              <p className="text-gray-400 mt-1">管理已回收的影片和版本</p>
            </div>
          </div>
          
          <button
            onClick={loadRecycledItems}
            disabled={isLoading}
            className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>

        {/* 统计信息 */}
        {recycledItems && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-900 rounded">
                  <Trash2 className="h-5 w-5 text-blue-300" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">总计项目</p>
                  <p className="text-2xl font-bold text-white">{recycledItems.totalCount}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-900 rounded">
                  <Trash2 className="h-5 w-5 text-red-300" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">回收影片</p>
                  <p className="text-2xl font-bold text-white">{recycledItems.moviesCount}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-900 rounded">
                  <Trash2 className="h-5 w-5 text-orange-300" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">回收版本</p>
                  <p className="text-2xl font-bold text-white">{recycledItems.versionsCount}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <button
            onClick={handleGenerateDeletionList}
            disabled={isGeneratingList || !recycledItems || recycledItems.totalCount === 0}
            className="px-6 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isGeneratingList ? (
              <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            生成 115 删除清单
          </button>
          
          <button
            disabled
            className="px-6 py-2 bg-gray-600 text-gray-400 font-medium rounded cursor-not-allowed flex items-center gap-2"
            title="功能开发中"
          >
            <RotateCcw className="h-4 w-4" />
            恢复选中项 ({selectedItems.size})
          </button>
          
          <button
            disabled
            className="px-6 py-2 bg-gray-600 text-gray-400 font-medium rounded cursor-not-allowed flex items-center gap-2"
            title="功能开发中"
          >
            <Trash2 className="h-4 w-4" />
            清空回收站
          </button>
        </div>

        {/* 项目列表 */}
        {recycledItems && recycledItems.totalCount > 0 ? (
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left">
                      <button
                        onClick={toggleSelectAll}
                        className="text-gray-400 hover:text-white"
                      >
                        {isAllSelected ? <CheckSquare className="h-4 w-4" /> : <Square className="h-4 w-4" />}
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">项目信息</th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">标题/文件名</th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">回收时间</th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {allItems.map((item, index) => renderItemRow(item, index))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="bg-gray-800 rounded-lg p-12 text-center">
            <Trash2 className="h-16 w-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-400 mb-2">回收站为空</h3>
            <p className="text-gray-500">
              {recycledItems?.success === false 
                ? '加载回收站数据失败，请尝试刷新页面'
                : '没有已回收的影片或版本'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
