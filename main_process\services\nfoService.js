// main_process/services/nfoService.js
// NFO 铸造厂 - 双向NFO文件处理服务

const fs = require('fs').promises;
const path = require('path');
const { create, convert } = require('xmlbuilder2');
const log = require('electron-log');

/**
 * NFO 铸造厂服务
 * 负责 JSON ↔ NFO 的双向转换
 */
class NfoService {
  constructor() {
    this.name = 'NFO铸造厂';
    this.version = '1.0.0';
  }

  /**
   * 正向转换：将 displayData 导出为标准 .nfo 文件 (铸造)
   * @param {Object} displayData - A区显示数据对象
   * @param {string} assetRootPath - 影片资产根目录
   * @param {string} nfoId - NFO ID
   * @returns {Promise<boolean>} 是否成功
   */
  async exportNfo(displayData, assetRootPath, nfoId) {
    try {
      log.info(`[${this.name}] 开始铸造NFO文件: ${nfoId}`);

      // 构建XML结构
      const xmlDoc = this.buildNfoXml(displayData);
      
      // 生成格式化的XML字符串
      const xmlString = xmlDoc.end({ 
        prettyPrint: true, 
        indent: '  ',
        newline: '\n'
      });

      // 确保目录存在
      await fs.mkdir(assetRootPath, { recursive: true });

      // 写入NFO文件
      const nfoFilePath = path.join(assetRootPath, `${nfoId}.nfo`);
      await fs.writeFile(nfoFilePath, xmlString, 'utf8');

      log.info(`[${this.name}] NFO文件铸造成功: ${nfoFilePath}`);
      return true;

    } catch (error) {
      log.error(`[${this.name}] NFO文件铸造失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 构建符合Kodi/Emby规范的XML文档
   * @param {Object} displayData - 显示数据对象
   * @returns {Object} XML文档对象
   */
  buildNfoXml(displayData) {
    const root = create({ version: '1.0', encoding: 'UTF-8' })
      .ele('movie');

    // 基础信息映射
    this.mapBasicInfo(root, displayData);
    
    // 评分信息映射
    this.mapRatingInfo(root, displayData);
    
    // 演员信息映射
    this.mapActorInfo(root, displayData);
    
    // 标签和类型映射
    this.mapTagsAndGenres(root, displayData);
    
    // 其他元数据映射
    this.mapOtherMetadata(root, displayData);

    return root;
  }

  /**
   * 映射基础信息
   * @param {Object} root - XML根元素
   * @param {Object} displayData - 显示数据
   */
  mapBasicInfo(root, displayData) {
    // 标题
    if (displayData.title) {
      root.ele('title').txt(displayData.title);
      root.ele('originaltitle').txt(displayData.title);
    }

    // 简介
    if (displayData.plot) {
      root.ele('plot').txt(displayData.plot);
      root.ele('outline').txt(displayData.plot.substring(0, 200) + '...');
    }

    // 发布日期
    if (displayData.release_date) {
      const releaseDate = displayData.release_date;
      root.ele('releasedate').txt(releaseDate);
      root.ele('premiered').txt(releaseDate);
      root.ele('year').txt(new Date(releaseDate).getFullYear().toString());
    }

    // 制片厂
    if (displayData.studio) {
      root.ele('studio').txt(displayData.studio);
    }

    // 导演
    if (displayData.director) {
      const directors = Array.isArray(displayData.director) 
        ? displayData.director 
        : [displayData.director];
      
      directors.forEach(director => {
        if (director) {
          root.ele('director').txt(director);
        }
      });
    }

    // 运行时间
    if (displayData.runtime) {
      root.ele('runtime').txt(displayData.runtime.toString());
    }
  }

  /**
   * 映射评分信息
   * @param {Object} root - XML根元素
   * @param {Object} displayData - 显示数据
   */
  mapRatingInfo(root, displayData) {
    if (displayData.rating && displayData.rating.score) {
      const score = displayData.rating.score;
      root.ele('rating').txt(score.toString());
      root.ele('userrating').txt(score.toString());
      
      // 添加评分详情
      const ratingsEle = root.ele('ratings');
      const ratingEle = ratingsEle.ele('rating', { name: 'default', max: '10', default: 'true' });
      ratingEle.ele('value').txt(score.toString());
      
      if (displayData.rating.votes) {
        ratingEle.ele('votes').txt(displayData.rating.votes.toString());
      }
    }
  }

  /**
   * 映射演员信息
   * @param {Object} root - XML根元素
   * @param {Object} displayData - 显示数据
   */
  mapActorInfo(root, displayData) {
    if (displayData.actresses && Array.isArray(displayData.actresses)) {
      displayData.actresses.forEach(actress => {
        if (actress && actress.name) {
          const actorEle = root.ele('actor');
          actorEle.ele('name').txt(actress.name);
          actorEle.ele('role').txt('Actress');
          
          // 添加头像路径
          if (actress.avatar_local_path) {
            actorEle.ele('thumb').txt(actress.avatar_local_path);
          }
          
          // 添加演员顺序
          if (actress.order !== undefined) {
            actorEle.ele('order').txt(actress.order.toString());
          }
        }
      });
    }
  }

  /**
   * 映射标签和类型
   * @param {Object} root - XML根元素
   * @param {Object} displayData - 显示数据
   */
  mapTagsAndGenres(root, displayData) {
    // 标签
    if (displayData.tags && Array.isArray(displayData.tags)) {
      displayData.tags.forEach(tag => {
        if (tag) {
          root.ele('tag').txt(tag);
        }
      });
    }

    // 类型
    if (displayData.genres && Array.isArray(displayData.genres)) {
      displayData.genres.forEach(genre => {
        if (genre) {
          root.ele('genre').txt(genre);
        }
      });
    }
  }

  /**
   * 映射其他元数据
   * @param {Object} root - XML根元素
   * @param {Object} displayData - 显示数据
   */
  mapOtherMetadata(root, displayData) {
    // 唯一标识符
    if (displayData.nfoId) {
      root.ele('id').txt(displayData.nfoId);
      root.ele('uniqueid', { type: 'nfo', default: 'true' }).txt(displayData.nfoId);
    }

    // 文件信息
    if (displayData.fileinfo) {
      const fileinfoEle = root.ele('fileinfo');
      const streamdetailsEle = fileinfoEle.ele('streamdetails');
      
      // 视频流信息
      if (displayData.fileinfo.video) {
        const videoEle = streamdetailsEle.ele('video');
        const video = displayData.fileinfo.video;
        
        if (video.codec) videoEle.ele('codec').txt(video.codec);
        if (video.width) videoEle.ele('width').txt(video.width.toString());
        if (video.height) videoEle.ele('height').txt(video.height.toString());
        if (video.duration) videoEle.ele('durationinseconds').txt(video.duration.toString());
      }
      
      // 音频流信息
      if (displayData.fileinfo.audio) {
        const audioEle = streamdetailsEle.ele('audio');
        const audio = displayData.fileinfo.audio;
        
        if (audio.codec) audioEle.ele('codec').txt(audio.codec);
        if (audio.channels) audioEle.ele('channels').txt(audio.channels.toString());
        if (audio.language) audioEle.ele('language').txt(audio.language);
      }
    }

    // 观看状态
    if (displayData.playcount !== undefined) {
      root.ele('playcount').txt(displayData.playcount.toString());
    }

    if (displayData.watched !== undefined) {
      root.ele('watched').txt(displayData.watched ? 'true' : 'false');
    }

    // 最后播放时间
    if (displayData.lastplayed) {
      root.ele('lastplayed').txt(displayData.lastplayed);
    }

    // 添加时间
    if (displayData.dateadded) {
      root.ele('dateadded').txt(displayData.dateadded);
    }
  }

  /**
   * 逆向转换：读取 .nfo 文件并生成基础 .meta.json 档案 (解析)
   * @param {string} nfoFilePath - NFO文件完整路径
   * @returns {Promise<Object|null>} 解析结果
   */
  async importNfo(nfoFilePath) {
    try {
      log.info(`[${this.name}] 开始解析NFO文件: ${nfoFilePath}`);

      // 检查文件是否存在
      const exists = await fs.access(nfoFilePath).then(() => true).catch(() => false);
      if (!exists) {
        log.warn(`[${this.name}] NFO文件不存在: ${nfoFilePath}`);
        return null;
      }

      // 读取NFO文件内容
      const nfoContent = await fs.readFile(nfoFilePath, 'utf8');

      // 解析XML
      const xmlObj = convert(nfoContent, { format: 'object' });

      if (!xmlObj || !xmlObj.movie) {
        log.warn(`[${this.name}] NFO文件格式无效: ${nfoFilePath}`);
        return null;
      }

      // 逆向映射为displayData结构
      const displayData = this.parseNfoToDisplayData(xmlObj.movie);

      // 构建完整的.meta.json结构
      const metaJsonData = {
        display_data: displayData,
        source_data: {}, // B区暂时留空
        custom_data: {}  // C区暂时留空
      };

      // 写入.meta.json文件
      const metaJsonPath = nfoFilePath.replace(/\.nfo$/i, '.meta.json');
      await fs.writeFile(metaJsonPath, JSON.stringify(metaJsonData, null, 2), 'utf8');

      log.info(`[${this.name}] NFO解析成功，已生成: ${metaJsonPath}`);
      return {
        success: true,
        metaJsonPath: metaJsonPath,
        displayData: displayData
      };

    } catch (error) {
      log.error(`[${this.name}] NFO解析失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 将NFO XML解析为displayData结构
   * @param {Object} movieObj - XML movie对象
   * @returns {Object} displayData对象
   */
  parseNfoToDisplayData(movieObj) {
    const displayData = {};

    try {
      // 基础信息解析
      this.parseBasicInfoFromObj(movieObj, displayData);

      // 评分信息解析
      this.parseRatingInfoFromObj(movieObj, displayData);

      // 演员信息解析
      this.parseActorInfoFromObj(movieObj, displayData);

      // 标签和类型解析
      this.parseTagsAndGenresFromObj(movieObj, displayData);

      // 其他元数据解析
      this.parseOtherMetadataFromObj(movieObj, displayData);

    } catch (error) {
      log.error(`[${this.name}] 解析NFO内容时发生错误: ${error.message}`);
    }

    return displayData;
  }

  /**
   * 解析基础信息（从对象）
   * @param {Object} movieObj - XML movie对象
   * @param {Object} displayData - 目标displayData对象
   */
  parseBasicInfoFromObj(movieObj, displayData) {
    // 标题
    const title = this.getValueFromObj(movieObj, 'title') ||
                  this.getValueFromObj(movieObj, 'originaltitle');
    if (title) displayData.title = title;

    // 简介
    const plot = this.getValueFromObj(movieObj, 'plot');
    if (plot) displayData.plot = plot;

    // 发布日期
    const releaseDate = this.getValueFromObj(movieObj, 'releasedate') ||
                        this.getValueFromObj(movieObj, 'premiered');
    if (releaseDate) displayData.release_date = releaseDate;

    // 年份
    const year = this.getValueFromObj(movieObj, 'year');
    if (year) displayData.year = parseInt(year);

    // 制片厂
    const studio = this.getValueFromObj(movieObj, 'studio');
    if (studio) displayData.studio = studio;

    // 导演
    const directors = this.getArrayFromObj(movieObj, 'director');
    if (directors.length > 0) {
      displayData.director = directors.length === 1 ? directors[0] : directors;
    }

    // 运行时间
    const runtime = this.getValueFromObj(movieObj, 'runtime');
    if (runtime) displayData.runtime = parseInt(runtime);
  }

  /**
   * 解析评分信息（从对象）
   * @param {Object} movieObj - XML movie对象
   * @param {Object} displayData - 目标displayData对象
   */
  parseRatingInfoFromObj(movieObj, displayData) {
    const rating = this.getValueFromObj(movieObj, 'rating') ||
                   this.getValueFromObj(movieObj, 'userrating');

    if (rating) {
      displayData.rating = {
        score: parseFloat(rating),
        max_score: 10
      };

      // 尝试从ratings元素获取更详细的评分信息
      if (movieObj.ratings && movieObj.ratings.rating) {
        const ratingObj = movieObj.ratings.rating;
        const votes = this.getValueFromObj(ratingObj, 'votes');
        if (votes) {
          displayData.rating.votes = parseInt(votes);
        }
      }
    }
  }

  /**
   * 解析演员信息（从对象）
   * @param {Object} movieObj - XML movie对象
   * @param {Object} displayData - 目标displayData对象
   */
  parseActorInfoFromObj(movieObj, displayData) {
    if (!movieObj.actor) return;

    const actresses = [];
    const actorArray = Array.isArray(movieObj.actor) ? movieObj.actor : [movieObj.actor];

    actorArray.forEach((actorObj, index) => {
      const name = this.getValueFromObj(actorObj, 'name');
      if (name) {
        const actress = {
          name: name,
          order: index
        };

        const thumb = this.getValueFromObj(actorObj, 'thumb');
        if (thumb) {
          actress.avatar_local_path = thumb;
        }

        const role = this.getValueFromObj(actorObj, 'role');
        if (role) {
          actress.role = role;
        }

        actresses.push(actress);
      }
    });

    if (actresses.length > 0) {
      displayData.actresses = actresses;
    }
  }

  /**
   * 解析标签和类型（从对象）
   * @param {Object} movieObj - XML movie对象
   * @param {Object} displayData - 目标displayData对象
   */
  parseTagsAndGenresFromObj(movieObj, displayData) {
    // 标签
    const tags = this.getArrayFromObj(movieObj, 'tag');
    if (tags.length > 0) {
      displayData.tags = tags;
    }

    // 类型
    const genres = this.getArrayFromObj(movieObj, 'genre');
    if (genres.length > 0) {
      displayData.genres = genres;
    }
  }

  /**
   * 解析其他元数据（从对象）
   * @param {Object} movieObj - XML movie对象
   * @param {Object} displayData - 目标displayData对象
   */
  parseOtherMetadataFromObj(movieObj, displayData) {
    // 唯一标识符
    const id = this.getValueFromObj(movieObj, 'id');
    if (id) displayData.nfoId = id;

    // 观看次数
    const playcount = this.getValueFromObj(movieObj, 'playcount');
    if (playcount) displayData.playcount = parseInt(playcount);

    // 观看状态
    const watched = this.getValueFromObj(movieObj, 'watched');
    if (watched) displayData.watched = watched.toLowerCase() === 'true';

    // 最后播放时间
    const lastplayed = this.getValueFromObj(movieObj, 'lastplayed');
    if (lastplayed) displayData.lastplayed = lastplayed;

    // 添加时间
    const dateadded = this.getValueFromObj(movieObj, 'dateadded');
    if (dateadded) displayData.dateadded = dateadded;
  }

  /**
   * 获取XML元素的文本内容
   * @param {Object} parentElement - 父元素
   * @param {string} tagName - 标签名
   * @returns {string|null} 文本内容
   */
  getElementText(parentElement, tagName) {
    try {
      const elements = parentElement.find(tagName);
      if (elements && elements.length > 0) {
        const element = Array.isArray(elements) ? elements[0] : elements;
        return element.node?.textContent?.trim() || null;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取所有同名XML元素的文本内容
   * @param {Object} parentElement - 父元素
   * @param {string} tagName - 标签名
   * @returns {Array<string>} 文本内容数组
   */
  getAllElementTexts(parentElement, tagName) {
    try {
      const elements = parentElement.find(tagName);
      if (!elements || elements.length === 0) return [];

      const elementsArray = Array.isArray(elements) ? elements : [elements];
      return elementsArray
        .map(element => element.node?.textContent?.trim())
        .filter(text => text && text.length > 0);
    } catch (error) {
      return [];
    }
  }

  /**
   * 从对象中获取值
   * @param {Object} obj - 对象
   * @param {string} key - 键名
   * @returns {string|null} 值
   */
  getValueFromObj(obj, key) {
    if (!obj || !obj[key]) return null;
    const value = obj[key];
    if (typeof value === 'string') return value.trim();
    if (typeof value === 'object' && value['#text']) return value['#text'].trim();
    return null;
  }

  /**
   * 从对象中获取数组
   * @param {Object} obj - 对象
   * @param {string} key - 键名
   * @returns {Array<string>} 值数组
   */
  getArrayFromObj(obj, key) {
    if (!obj || !obj[key]) return [];
    const value = obj[key];

    if (Array.isArray(value)) {
      return value.map(item => {
        if (typeof item === 'string') return item.trim();
        if (typeof item === 'object' && item['#text']) return item['#text'].trim();
        return null;
      }).filter(item => item);
    } else {
      const singleValue = this.getValueFromObj(obj, key);
      return singleValue ? [singleValue] : [];
    }
  }
}

// 创建单例实例
const nfoService = new NfoService();

module.exports = {
  nfoService,
  NfoService
};
