/**
 * 日期解析工具 - 从collectorService.js提取
 * 
 * 提供统一的日期解析功能，支持多种论坛日期格式
 */

let log = null;

/**
 * 初始化日期解析器
 * @param {Object} logger - 日志对象
 */
function initializeDateParser(logger) {
  log = logger;
}

/**
 * 解析论坛日期格式
 * @param {string} dateText - 日期文本
 * @returns {Date|null} 解析后的日期对象，失败返回null
 */
function parseForumDate(dateText) {
  if (!dateText) return null;

  // 清理文本
  const cleanText = dateText.trim();

  // 匹配 YYYY-MM-DD 格式
  const match = cleanText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
  if (match) {
    return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
  }

  // 匹配其他常见格式，如 YYYY/MM/DD
  const match2 = cleanText.match(/(\d{4})\/(\d{1,2})\/(\d{1,2})/);
  if (match2) {
    return new Date(parseInt(match2[1]), parseInt(match2[2]) - 1, parseInt(match2[3]));
  }

  // 匹配 MM-DD 格式（假设是当年）
  const match3 = cleanText.match(/(\d{1,2})-(\d{1,2})/);
  if (match3) {
    const currentYear = new Date().getFullYear();
    return new Date(currentYear, parseInt(match3[1]) - 1, parseInt(match3[2]));
  }

  // 解析相对日期格式
  const now = new Date();

  // 匹配 "X小时前"
  const hoursMatch = cleanText.match(/(\d+)\s*小时前/);
  if (hoursMatch) {
    const hours = parseInt(hoursMatch[1]);
    return new Date(now.getTime() - hours * 60 * 60 * 1000);
  }

  // 匹配 "X分钟前"
  const minutesMatch = cleanText.match(/(\d+)\s*分钟前/);
  if (minutesMatch) {
    const minutes = parseInt(minutesMatch[1]);
    return new Date(now.getTime() - minutes * 60 * 1000);
  }

  // 匹配 "X天前"
  const daysMatch = cleanText.match(/(\d+)\s*天前/);
  if (daysMatch) {
    const days = parseInt(daysMatch[1]);
    return new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
  }

  // 匹配 "昨天"
  if (cleanText.includes('昨天')) {
    return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // 匹配 "今天"
  if (cleanText.includes('今天')) {
    return new Date(now.getFullYear(), now.getMonth(), now.getDate());
  }

  // 匹配 "前天"
  if (cleanText.includes('前天')) {
    return new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
  }

  // 如果无法解析，返回当前日期作为备用
  if (log) {
    log.warn(`[DateParser] 无法解析日期格式: "${dateText}"，使用当前日期作为备用`);
  }
  return new Date();
}

module.exports = {
  initializeDateParser,
  parseForumDate
};
