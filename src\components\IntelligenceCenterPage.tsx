// soul-forge-electron/src/components/IntelligenceCenterPage.tsx
import React from 'react';
import { LuRadar, LuTrendingUp, LuCalendar, <PERSON>Eye, LuBrain, LuSearch, <PERSON><PERSON><PERSON>, LuTarget } from 'react-icons/lu';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from './ui/Tabs';
import { DailyScanner } from './archive/DailyScanner';
import { OnDemandScraper } from './archive/OnDemandScraper';
import ScraperTestTool from './tools/ScraperTestTool';
import { useIntelligenceCenterStore } from '../hooks/useIntelligenceCenterStore';

interface IntelligenceCenterPageProps {
  onMovieDataChanged?: () => void;
}

export const IntelligenceCenterPage: React.FC<IntelligenceCenterPageProps> = ({
  onMovieDataChanged
}) => {
  // 使用统一状态管理
  const {
    activeTab,
    setActiveTab,
    isAnyTaskRunning,
    getRunningTasksCount,
    updateUpcomingReleasesTask,
    updateInstantReconTask,
    instantReconTarget,
    triggerInstantRecon,
    updateScanResult
  } = useIntelligenceCenterStore();

  const [upcomingScanResult, setUpcomingScanResult] = React.useState<{
    success: boolean;
    message: string;
    timestamp: number;
  } | null>(null);

  const handleUpcomingScan = async () => {
    updateUpcomingReleasesTask({ isRunning: true, progress: 0, message: '开始扫描...', startTime: Date.now() });
    setUpcomingScanResult(null);

    try {
      const result = await window.sfeElectronAPI.scanUpcomingReleases();

      setUpcomingScanResult({
        success: result.success,
        message: result.message || (result.success ? '扫描完成' : '扫描失败'),
        timestamp: Date.now()
      });

      // 更新扫描结果到store
      updateScanResult('upcomingReleases', {
        foundCount: result.totalFound || 0,
        newCount: result.newCount || 0,
        updatedCount: result.updatedCount || 0
      });

      if (result.success && onMovieDataChanged) {
        // 刷新主页影片库以显示新的未来新品
        setTimeout(() => {
          onMovieDataChanged();
        }, 1000);
      }

    } catch (error) {
      setUpcomingScanResult({
        success: false,
        message: error instanceof Error ? error.message : '发生未知错误',
        timestamp: Date.now()
      });
    } finally {
      updateUpcomingReleasesTask({ isRunning: false, progress: 100 });
    }
  };

  // 自动清除结果消息
  React.useEffect(() => {
    if (upcomingScanResult) {
      const timer = setTimeout(() => {
        setUpcomingScanResult(null);
      }, 8000); // 8秒后自动清除

      return () => clearTimeout(timer);
    }
  }, [upcomingScanResult]);
  return (
    <div className="container mx-auto py-6 px-4">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-3">
            <LuRadar className="h-8 w-8 text-[#B8860B]" />
            <h1 className="text-3xl font-bold text-white">情报中心</h1>
          </div>
          <div className="flex-1 h-px bg-gradient-to-r from-[#B8860B] to-transparent"></div>
          {/* 全局状态指示器 */}
          {isAnyTaskRunning() && (
            <div className="flex items-center gap-2 px-3 py-1 bg-amber-900/30 border border-amber-500/30 rounded-full">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
              <span className="text-amber-300 text-sm font-medium">
                {getRunningTasksCount()} 个任务运行中
              </span>
            </div>
          )}
        </div>
        <p className="text-gray-400 text-lg">
          集成化的情报收集与分析平台，实时监控新作发布、预览内容提取和趋势分析
        </p>
      </div>

      {/* 中央指挥室 - 标签页布局 */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-gray-800 border border-gray-700">
          <TabsTrigger
            value="daily-briefing"
            className="flex items-center gap-2 data-[state=active]:bg-[#B8860B] data-[state=active]:text-black"
          >
            <LuTrendingUp className="h-4 w-4" />
            <span className="hidden sm:inline">每日简报</span>
            <span className="sm:hidden">简报</span>
          </TabsTrigger>
          <TabsTrigger
            value="upcoming-releases"
            className="flex items-center gap-2 data-[state=active]:bg-[#B8860B] data-[state=active]:text-black"
          >
            <LuCalendar className="h-4 w-4" />
            <span className="hidden sm:inline">未来新品</span>
            <span className="sm:hidden">新品</span>
          </TabsTrigger>
          <TabsTrigger
            value="instant-recon"
            className="flex items-center gap-2 data-[state=active]:bg-[#B8860B] data-[state=active]:text-black"
          >
            <LuTarget className="h-4 w-4" />
            <span className="hidden sm:inline">即时侦察</span>
            <span className="sm:hidden">侦察</span>
          </TabsTrigger>
        </TabsList>

        {/* 每日简报标签页 */}
        <TabsContent value="daily-briefing" className="mt-6">
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3 mb-6">
              <LuBrain className="h-6 w-6 text-[#B8860B]" />
              <h2 className="text-xl font-bold text-white">智能情报收集</h2>
              <div className="flex items-center gap-2 text-xs text-green-300">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>系统活跃</span>
              </div>
            </div>

            <div className="mb-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <p className="text-gray-300 text-sm">
                📊 从AV Help Wiki获取每日新作发布信息，支持历史回溯扫描和VR预告片破译
              </p>
            </div>

            <DailyScanner
              onScanCompleted={() => {
                if (onMovieDataChanged) {
                  onMovieDataChanged();
                }
              }}
              className="bg-transparent border-0 p-0"
            />
          </div>
        </TabsContent>

        {/* 未来新品标签页 */}
        <TabsContent value="upcoming-releases" className="mt-6">
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3 mb-6">
              <LuClock className="h-6 w-6 text-[#B8860B]" />
              <h2 className="text-xl font-bold text-white">未来新品监视器</h2>
              <div className="flex items-center gap-2 text-xs text-green-300">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>系统活跃</span>
              </div>
            </div>

            <div className="mb-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <p className="text-gray-300 text-sm">
                🔮 监控DMM发售日历，提前发现即将发布的新作预告信息，支持前瞻-侦察联动
              </p>
            </div>

            <div className="bg-[#2c2c2c] border border-[#444] rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <LuCalendar className="h-5 w-5 text-[#B8860B]" />
                <h3 className="text-lg font-medium text-white">DMM发售日历扫描</h3>
              </div>

              <div className="flex items-center gap-3 mb-4">
                <button
                  onClick={handleUpcomingScan}
                  disabled={isAnyTaskRunning()}
                  className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[140px] justify-center"
                >
                  {isAnyTaskRunning() ? (
                    <>
                      <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                      扫描中...
                    </>
                  ) : (
                    <>
                      <LuCalendar className="h-4 w-4" />
                      扫描未来新品
                    </>
                  )}
                </button>

                <div className="text-sm text-gray-400">
                  扫描DMM发售日历，发现即将发售的新作品
                </div>
              </div>

              {/* 结果反馈 */}
              {upcomingScanResult && (
                <div className={`p-3 rounded-lg flex items-center gap-2 mb-4 ${
                  upcomingScanResult.success
                    ? 'bg-green-900/20 border border-green-500/30'
                    : 'bg-red-900/20 border border-red-500/30'
                }`}>
                  {upcomingScanResult.success ? (
                    <LuCalendar className="h-4 w-4 text-green-400 flex-shrink-0" />
                  ) : (
                    <LuClock className="h-4 w-4 text-red-400 flex-shrink-0" />
                  )}
                  <span className={`text-sm ${
                    upcomingScanResult.success ? 'text-green-300' : 'text-red-300'
                  }`}>
                    {upcomingScanResult.message}
                  </span>
                </div>
              )}

              {/* 使用说明 */}
              <div className="text-xs text-gray-400">
                <p>💡 扫描DMM发售日历，自动发现未来30天内即将发售的新作品</p>
                <p>📅 新发现的作品将标记为"未来发售"状态，可在主页影片库中查看</p>
                <p>🔄 建议每日运行一次，以获取最新的发售信息</p>
                <p>🎯 点击发现的新品可快速切换到"即时侦察"进行深度分析</p>
              </div>
            </div>

            {/* 未来新品列表示例 */}
            <div className="mt-6 bg-[#2c2c2c] border border-[#444] rounded-lg p-4">
              <div className="flex items-center gap-3 mb-4">
                <LuEye className="h-5 w-5 text-[#B8860B]" />
                <h3 className="text-lg font-medium text-white">发现的未来新品</h3>
              </div>

              <div className="space-y-3">
                {/* 示例未来新品条目 */}
                <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-[#B8860B] font-mono text-sm">ABF-249</span>
                        <span className="text-xs text-gray-400">2025-08-10</span>
                        <span className="px-2 py-1 bg-purple-900/30 border border-purple-500/30 rounded text-purple-300 text-xs">
                          未来发售
                        </span>
                      </div>
                      <p className="text-white text-sm mb-1">ヤリ過ぎ中出し温泉旅館～至高のHカップを好き放題～ 河合あすな</p>
                      <p className="text-gray-400 text-xs">演员: 河合あすな</p>
                    </div>
                    <button
                      onClick={() => triggerInstantRecon('ABF-249')}
                      className="ml-4 px-3 py-1 bg-[#B8860B] text-black text-xs font-medium rounded hover:bg-[#DAA520] flex items-center gap-1"
                    >
                      <LuTarget className="h-3 w-3" />
                      立即侦察
                    </button>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-[#B8860B] font-mono text-sm">ABF-250</span>
                        <span className="text-xs text-gray-400">2025-08-10</span>
                        <span className="px-2 py-1 bg-purple-900/30 border border-purple-500/30 rounded text-purple-300 text-xs">
                          未来发售
                        </span>
                      </div>
                      <p className="text-white text-sm mb-1">何もない田舎で幼馴染と、想いが通じ合った濃厚SEXの日々。 case.14 中森ななみ</p>
                      <p className="text-gray-400 text-xs">演员: 中森ななみ</p>
                    </div>
                    <button
                      onClick={() => triggerInstantRecon('ABF-250')}
                      className="ml-4 px-3 py-1 bg-[#B8860B] text-black text-xs font-medium rounded hover:bg-[#DAA520] flex items-center gap-1"
                    >
                      <LuTarget className="h-3 w-3" />
                      立即侦察
                    </button>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-[#B8860B] font-mono text-sm">ABF-251</span>
                        <span className="text-xs text-gray-400">2025-08-10</span>
                        <span className="px-2 py-1 bg-purple-900/30 border border-purple-500/30 rounded text-purple-300 text-xs">
                          未来发售
                        </span>
                      </div>
                      <p className="text-white text-sm mb-1">おじさんが好き とびっきり美少女とイチャベロ3本番 八掛うみ</p>
                      <p className="text-gray-400 text-xs">演员: 八掛うみ</p>
                    </div>
                    <button
                      onClick={() => triggerInstantRecon('ABF-251')}
                      className="ml-4 px-3 py-1 bg-[#B8860B] text-black text-xs font-medium rounded hover:bg-[#DAA520] flex items-center gap-1"
                    >
                      <LuTarget className="h-3 w-3" />
                      立即侦察
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-4 text-center">
                <p className="text-gray-400 text-xs">
                  💡 这些是从DMM发售日历扫描到的示例数据。点击"立即侦察"可切换到即时侦察标签页进行深度分析。
                </p>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* 即时侦察标签页 */}
        <TabsContent value="instant-recon" className="mt-6">
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3 mb-6">
              <LuTarget className="h-6 w-6 text-[#B8860B]" />
              <h2 className="text-xl font-bold text-white">即时侦察</h2>
              <div className="flex items-center gap-2 text-xs text-green-300">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>系统活跃</span>
              </div>
            </div>

            <div className="mb-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
              <p className="text-gray-300 text-sm">
                🎯 输入番号进行即时刮削测试，支持多Provider聚合和数据精炼
              </p>
              {instantReconTarget && (
                <div className="mt-2 p-2 bg-amber-900/20 border border-amber-500/30 rounded">
                  <span className="text-amber-300 text-xs">
                    🎯 联动目标: {instantReconTarget}
                  </span>
                </div>
              )}
            </div>

            {/* 集成OnDemandScraper */}
            <OnDemandScraper
              className="bg-transparent border-0 p-0"
              initialNfoId={instantReconTarget}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IntelligenceCenterPage;
