import React from 'react';
import { useArchiveStore, ArchiveRecord } from '../hooks/useArchiveStore';
import { ExternalLink, Download, FileText, Calendar, Tag, AlertCircle, CheckCircle, Clock, Brain, Square, CheckSquare } from 'lucide-react';

export function ArchiveDataGrid() {
  const {
    results,
    total,
    loading,
    error,
    filters,
    setPage,
    fetchResults,
    selectedRecordIds,
    toggleSelection,
    selectAll,
    clearSelection
  } = useArchiveStore();

  // 分页计算
  const totalPages = Math.ceil(total / filters.pageSize);
  const startIndex = (filters.page - 1) * filters.pageSize + 1;
  const endIndex = Math.min(filters.page * filters.pageSize, total);

  // 处理页码变更
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = () => {
    const currentPageIds = results.map(record => record.id);
    const allSelected = currentPageIds.every(id => selectedRecordIds.has(id));

    if (allSelected) {
      // 如果当前页全部选中，则取消选中当前页
      currentPageIds.forEach(id => {
        if (selectedRecordIds.has(id)) {
          toggleSelection(id);
        }
      });
    } else {
      // 否则选中当前页所有项
      currentPageIds.forEach(id => {
        if (!selectedRecordIds.has(id)) {
          toggleSelection(id);
        }
      });
    }
  };

  // 检查当前页是否全选
  const isCurrentPageAllSelected = results.length > 0 && results.every(record => selectedRecordIds.has(record.id));
  const isCurrentPagePartialSelected = results.some(record => selectedRecordIds.has(record.id)) && !isCurrentPageAllSelected;

  // 生成分页按钮
  const renderPaginationButtons = () => {
    const buttons = [];
    const maxButtons = 7;
    let startPage = Math.max(1, filters.page - Math.floor(maxButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxButtons - 1);

    if (endPage - startPage + 1 < maxButtons) {
      startPage = Math.max(1, endPage - maxButtons + 1);
    }

    // 首页
    if (startPage > 1) {
      buttons.push(
        <button key="first" onClick={() => handlePageChange(1)} className="pagination-button">
          1
        </button>
      );
      if (startPage > 2) {
        buttons.push(<span key="dots1" className="px-2 text-gray-400">...</span>);
      }
    }

    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`pagination-button ${
            i === filters.page ? 'bg-blue-600 text-white' : ''
          }`}
        >
          {i}
        </button>
      );
    }

    // 末页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        buttons.push(<span key="dots2" className="px-2 text-gray-400">...</span>);
      }
      buttons.push(
        <button key="last" onClick={() => handlePageChange(totalPages)} className="pagination-button">
          {totalPages}
        </button>
      );
    }

    return buttons;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // 解析AI标签
  const parseAiTags = (aiTagsJson: string | null | undefined): string[] => {
    if (!aiTagsJson) return [];
    try {
      return JSON.parse(aiTagsJson);
    } catch {
      return [];
    }
  };

  // 处理打开链接
  const handleOpenUrl = async (url: string) => {
    try {
      window.sfeElectronAPI.openExternalUrl(url);
    } catch (error) {
      console.error('打开链接失败:', error);
    }
  };

  // 判断AI处理状态
  const getAiProcessingStatus = (record: ArchiveRecord) => {
    const hasAiTags = record.ai_tags_json && record.ai_tags_json.trim() && record.ai_tags_json !== '[]';
    return hasAiTags ? 'processed' : 'pending';
  };

  // AI智能分析
  const handleAiAnalysis = async (id: number, postTitle: string) => {
    console.log('🤖 handleAiAnalysis 被调用，id:', id, 'postTitle:', postTitle);

    try {
      // 调用AI分析API
      const result = await window.sfeElectronAPI.collectorAnalyzeWithAi(id);

      if (result.success) {
        console.log('✅ AI分析成功，生成标签:', result.tags);
        // 刷新数据列表以显示新的AI标签
        await fetchResults();
      } else {
        console.error('❌ AI分析失败:', result.error);
        alert(`AI分析失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ AI分析异常:', error);
      alert(`AI分析异常: ${error}`);
    }
  };

  // 处理打开md文档
  const handleOpenMdDocument = async (mdPath: string) => {
    try {
      const fileExists = await window.sfeElectronAPI.fileExists(mdPath);
      if (fileExists) {
        await window.sfeElectronAPI.openFileInDefaultApp(mdPath);
      } else {
        alert('md文档文件不存在');
      }
    } catch (error) {
      console.error('打开md文档失败:', error);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 p-8 rounded-lg">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-300">正在查询...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800 p-8 rounded-lg">
        <div className="flex items-center justify-center text-red-400">
          <AlertCircle className="h-6 w-6 mr-2" />
          <span>查询失败: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg">
      {/* 结果统计和批量操作 */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div className="text-gray-300">
              共找到 <span className="text-white font-semibold">{total}</span> 条记录
              {total > 0 && (
                <span className="text-gray-400 ml-2">
                  显示第 {startIndex}-{endIndex} 条
                </span>
              )}
            </div>
            {selectedRecordIds.size > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-blue-400 text-sm">
                  已选择 {selectedRecordIds.size} 条记录
                </span>
                <button
                  onClick={clearSelection}
                  className="text-xs text-gray-400 hover:text-gray-300 underline"
                >
                  清除选择
                </button>
              </div>
            )}
          </div>
          <div className="flex items-center gap-4">
            {results.length > 0 && (
              <button
                onClick={handleSelectAll}
                className="flex items-center gap-2 text-sm text-gray-400 hover:text-gray-300"
                title={isCurrentPageAllSelected ? "取消选择当前页" : "选择当前页"}
              >
                {isCurrentPageAllSelected ? (
                  <CheckSquare className="h-4 w-4" />
                ) : isCurrentPagePartialSelected ? (
                  <div className="h-4 w-4 border border-gray-400 bg-blue-600/50 rounded-sm" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                {isCurrentPageAllSelected ? "取消全选" : "全选当前页"}
              </button>
            )}
            <div className="text-sm text-gray-400">
              每页 {filters.pageSize} 条 | 第 {filters.page}/{totalPages} 页
            </div>
          </div>
        </div>
      </div>

      {/* 数据列表 */}
      {results.length === 0 ? (
        <div className="p-8 text-center text-gray-400">
          <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>没有找到匹配的记录</p>
          <p className="text-sm mt-1">请尝试调整筛选条件</p>
        </div>
      ) : (
        <div className="divide-y divide-gray-700">
          {results.map((record: ArchiveRecord) => (
            <div key={record.id} className={`p-4 hover:bg-gray-700/50 transition-colors ${
              selectedRecordIds.has(record.id) ? 'bg-blue-900/20 border-l-4 border-blue-500' : ''
            }`}>
              {/* 标题行 */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start gap-3 flex-1">
                  {/* 复选框 */}
                  <button
                    onClick={() => toggleSelection(record.id)}
                    className="mt-1 text-gray-400 hover:text-gray-300"
                    title={selectedRecordIds.has(record.id) ? "取消选择" : "选择此记录"}
                  >
                    {selectedRecordIds.has(record.id) ? (
                      <CheckSquare className="h-4 w-4 text-blue-400" />
                    ) : (
                      <Square className="h-4 w-4" />
                    )}
                  </button>

                  <div className="flex-1">
                    <h3 className="text-white font-medium text-lg leading-tight">
                      {record.md_document_path ? (
                        <button
                          onClick={() => handleOpenMdDocument(record.md_document_path!)}
                          className="text-blue-400 hover:text-blue-300 text-left"
                          title="点击打开md文档"
                        >
                          {record.post_title}
                        </button>
                      ) : (
                        <span>{record.post_title}</span>
                      )}
                    </h3>
                    {!record.md_document_path && (
                      <span className="text-xs text-yellow-400">(未关联md文档)</span>
                    )}
                  </div>
                </div>
              </div>

              {/* 信息行 */}
              <div className="flex flex-wrap gap-4 text-sm text-gray-400 mb-2">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDate(record.collection_date)}
                </div>
                <div>来源: {record.source_forum}</div>
                {record.nfoId && <div>番号: {record.nfoId}</div>}
                <div className={`px-2 py-0.5 rounded text-xs ${
                  record.download_status === 'completed' ? 'bg-green-600' :
                  record.download_status === 'failed' ? 'bg-red-600' :
                  record.download_status === 'downloading' ? 'bg-blue-600' :
                  'bg-gray-600'
                }`}>
                  {record.download_status === 'completed' ? '已下载' :
                   record.download_status === 'failed' ? '下载失败' :
                   record.download_status === 'downloading' ? '下载中' :
                   record.download_status || '未下载'}
                </div>
                <div className={`flex items-center gap-1 px-2 py-0.5 rounded text-xs ${
                  getAiProcessingStatus(record) === 'processed'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-600 text-gray-300'
                }`}>
                  {getAiProcessingStatus(record) === 'processed' ? (
                    <>
                      <CheckCircle className="h-3 w-3" />
                      已处理
                    </>
                  ) : (
                    <>
                      <Clock className="h-3 w-3" />
                      待处理
                    </>
                  )}
                </div>
                <button
                  onClick={() => handleOpenUrl(record.post_url)}
                  className="flex items-center gap-1 text-blue-400 hover:text-blue-300 transition-colors text-xs"
                  title="打开原帖链接"
                >
                  <ExternalLink className="h-3 w-3" />
                  查看原帖
                </button>

                {/* AI分析按钮 - 只对未处理的记录显示 */}
                {getAiProcessingStatus(record) === 'pending' && (
                  <button
                    onClick={() => handleAiAnalysis(record.id, record.post_title)}
                    className="flex items-center gap-1 text-purple-400 hover:text-purple-300 transition-colors text-xs"
                    title="使用AI分析帖子内容并生成标签"
                  >
                    <Brain className="h-3 w-3" />
                    AI分析
                  </button>
                )}
              </div>

              {/* AI标签 */}
              {record.ai_tags_json && (
                <div className="flex flex-wrap gap-1 mb-2">
                  <Tag className="h-3 w-3 text-purple-400 mt-0.5 mr-1" />
                  {parseAiTags(record.ai_tags_json).map((tag, index) => (
                    <span
                      key={index}
                      className="bg-purple-600 text-white text-xs px-2 py-0.5 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* 下载路径 */}
              {record.download_path && (
                <div className="text-xs text-gray-500 truncate">
                  <Download className="h-3 w-3 inline mr-1" />
                  {record.download_path}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 分页控件 */}
      {totalPages > 1 && (
        <div className="p-4 border-t border-gray-700">
          <div className="flex justify-center items-center gap-2">
            <button
              onClick={() => handlePageChange(filters.page - 1)}
              disabled={filters.page <= 1}
              className="pagination-button"
            >
              上一页
            </button>
            
            {renderPaginationButtons()}
            
            <button
              onClick={() => handlePageChange(filters.page + 1)}
              disabled={filters.page >= totalPages}
              className="pagination-button"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
