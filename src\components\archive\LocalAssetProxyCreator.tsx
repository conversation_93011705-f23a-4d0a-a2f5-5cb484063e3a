import React, { useState } from 'react';
import { FileVideo, Upload, AlertCircle, CheckCircle, Loader, FolderOpen } from 'lucide-react';

interface LocalAssetProxyCreatorProps {
  onAssetCreated?: () => void;
  className?: string;
}

export const LocalAssetProxyCreator: React.FC<LocalAssetProxyCreatorProps> = ({
  onAssetCreated,
  className = ''
}) => {
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSelectFile = async () => {
    try {
      const filePath = await window.sfeElectronAPI.dialogSelectFile({
        title: '选择视频文件',
        filters: [
          {
            name: '视频文件',
            extensions: ['mp4', 'mkv', 'avi', 'wmv', 'mov', 'flv', 'webm', 'm4v', 'ts', 'm2ts', 'mts', 'vob', 'iso', 'rmvb', 'rm', 'asf']
          },
          {
            name: '所有文件',
            extensions: ['*']
          }
        ]
      });

      if (filePath) {
        setSelectedFile(filePath);
        setResult(null);
        setError(null);
      }
    } catch (error) {
      console.error('选择文件失败:', error);
      setError('选择文件失败');
    }
  };

  const handleCreateAsset = async () => {
    if (!selectedFile) {
      setError('请先选择视频文件');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      const result = await window.sfeElectronAPI.createAssetFromLocalFile(selectedFile);

      setResult(result);

      if (result.success) {
        // 延迟清理，让用户看到成功消息
        setTimeout(() => {
          setSelectedFile('');
          setResult(null);
          if (onAssetCreated) {
            onAssetCreated();
          }
        }, 3000);
      } else {
        setError(result.error || '创建资产失败');
      }
    } catch (error) {
      console.error('创建本地资产失败:', error);
      setError('创建资产失败，请检查网络连接');
    } finally {
      setIsProcessing(false);
    }
  };

  const getFileName = (filePath: string): string => {
    return filePath.split(/[/\\]/).pop() || filePath;
  };

  return (
    <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
      {/* 标题 */}
      <div className="flex items-center gap-3 mb-4">
        <FileVideo className="h-6 w-6 text-[#B8860B]" />
        <h3 className="text-lg font-bold text-white">从本地文件创建资产</h3>
      </div>

      <p className="text-gray-400 text-sm mb-6">
        选择本地视频文件，系统将自动提取番号、刮削元数据、下载媒体资产并生成 STRM 文件。
      </p>

      {/* 文件选择 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            选择视频文件
          </label>
          
          <div className="flex items-center gap-3">
            <input
              type="text"
              value={selectedFile ? getFileName(selectedFile) : ''}
              readOnly
              placeholder="未选择文件"
              title={selectedFile}
              className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B] truncate"
            />
            
            <button
              onClick={handleSelectFile}
              disabled={isProcessing}
              className="px-4 py-2 bg-gray-600 text-white font-medium rounded hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <FolderOpen className="h-4 w-4" />
              浏览...
            </button>
          </div>
          
          {selectedFile && (
            <p className="text-xs text-gray-500 mt-1 truncate" title={selectedFile}>
              完整路径: {selectedFile}
            </p>
          )}
        </div>

        {/* 处理说明 */}
        <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
          <h4 className="text-blue-300 font-medium mb-2 flex items-center gap-2">
            <Upload className="h-4 w-4" />
            处理流程说明
          </h4>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>• 从文件名自动提取番号</li>
            <li>• 刮削完整的元数据信息</li>
            <li>• 创建标准化目录结构</li>
            <li>• 下载封面、背景图和预览图</li>
            <li>• 生成指向本地文件的 STRM</li>
            <li>• 将资产信息入库到数据库</li>
          </ul>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 flex items-center gap-3">
            <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
            <div>
              <p className="text-red-300 font-medium">处理失败</p>
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* 成功提示 */}
        {result && result.success && (
          <div className="bg-green-900/50 border border-green-700 rounded-lg p-4 flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
            <div>
              <p className="text-green-300 font-medium">资产创建成功</p>
              <p className="text-green-400 text-sm">
                {result.nfoId}: {result.message}
              </p>
              {result.strmPath && (
                <p className="text-green-500 text-xs mt-1">
                  STRM 文件: {result.strmPath}
                </p>
              )}
            </div>
          </div>
        )}

        {/* 处理结果详情 */}
        {result && result.success && result.details && (
          <div className="bg-gray-700 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">处理详情</h4>
            <div className="space-y-1 text-sm">
              {result.details.scraped && (
                <p className="text-green-400">✓ 元数据刮削完成</p>
              )}
              {result.details.mediaDownloaded && (
                <p className="text-green-400">✓ 媒体资产下载完成</p>
              )}
              {result.details.strmCreated && (
                <p className="text-green-400">✓ STRM 文件生成完成</p>
              )}
              {result.details.databaseUpdated && (
                <p className="text-green-400">✓ 数据库记录已更新</p>
              )}
            </div>
          </div>
        )}

        {/* 创建按钮 */}
        <button
          onClick={handleCreateAsset}
          disabled={!selectedFile || isProcessing}
          className="w-full px-6 py-3 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {isProcessing ? (
            <>
              <Loader className="h-5 w-5 animate-spin" />
              处理中...
            </>
          ) : (
            <>
              <Upload className="h-5 w-5" />
              创建本地资产代理
            </>
          )}
        </button>
      </div>
    </div>
  );
};
