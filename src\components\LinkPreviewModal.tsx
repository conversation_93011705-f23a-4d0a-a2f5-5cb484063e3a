import React, { useState, useEffect } from 'react';
import { useArchiveStore } from '../hooks/useArchiveStore';
import { X, Copy, Download, FileText } from 'lucide-react';

export function LinkPreviewModal() {
  const { isLinkModalOpen, linksForPreview, closeLinkModal } = useArchiveStore();
  const [editableText, setEditableText] = useState('');
  const [isCopying, setIsCopying] = useState(false);

  // 当弹窗打开时，初始化可编辑文本
  useEffect(() => {
    if (isLinkModalOpen && linksForPreview.length > 0) {
      setEditableText(linksForPreview.join('\n'));
    }
  }, [isLinkModalOpen, linksForPreview]);

  // 处理复制到剪贴板
  const handleCopy = async () => {
    setIsCopying(true);
    try {
      await navigator.clipboard.writeText(editableText);
      alert(`复制成功！\n\n已将 ${editableText.split('\n').filter(line => line.trim()).length} 行内容复制到剪贴板。`);
    } catch (error) {
      console.error('复制失败:', error);
      alert('复制失败，请手动选择文本复制。');
    } finally {
      setIsCopying(false);
    }
  };

  // 处理关闭弹窗
  const handleClose = () => {
    closeLinkModal();
    setEditableText('');
  };

  // 如果弹窗未打开，不渲染
  if (!isLinkModalOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg shadow-xl w-11/12 max-w-4xl h-5/6 flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-400" />
            <h2 className="text-lg font-semibold text-white">
              链接预览与编辑
            </h2>
            <span className="text-sm text-gray-400">
              ({linksForPreview.length} 条链接)
            </span>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white transition-colors"
            title="关闭"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-4 flex flex-col">
          <div className="mb-3 text-sm text-gray-300">
            <p>您可以在下方文本框中编辑链接内容，然后点击"复制到剪贴板"按钮。</p>
            <p className="text-gray-400 mt-1">
              提示：每行一个链接，空行将被忽略。您可以删除不需要的链接或添加注释。
            </p>
          </div>

          {/* 可编辑文本区域 */}
          <textarea
            value={editableText}
            onChange={(e) => setEditableText(e.target.value)}
            className="flex-1 w-full p-3 bg-gray-900 border border-gray-600 rounded-md text-white font-mono text-sm resize-none focus:outline-none focus:border-green-500"
            placeholder="链接将显示在这里..."
            spellCheck={false}
          />
        </div>

        {/* 底部按钮栏 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-700">
          <div className="text-sm text-gray-400">
            当前行数: {editableText.split('\n').filter(line => line.trim()).length}
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleCopy}
              disabled={isCopying || !editableText.trim()}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 text-white rounded-md transition-colors flex items-center gap-2"
            >
              <Copy className={`h-4 w-4 ${isCopying ? 'animate-pulse' : ''}`} />
              {isCopying ? '复制中...' : '复制到剪贴板'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
