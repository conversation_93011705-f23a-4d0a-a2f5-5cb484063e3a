import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const libraries = await prisma.movieLibrary.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        movieLinks: {
          include: {
            movie: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: libraries,
    });
  } catch (error) {
    console.error('Error fetching libraries:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch libraries',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, paths } = body;

    if (!name || !paths) {
      return NextResponse.json(
        {
          success: false,
          error: 'Name and paths are required',
        },
        { status: 400 }
      );
    }

    const library = await prisma.movieLibrary.create({
      data: {
        name,
        paths: typeof paths === 'string' ? paths : JSON.stringify(paths),
      },
    });

    return NextResponse.json({
      success: true,
      data: library,
    });
  } catch (error) {
    console.error('Error creating library:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create library',
      },
      { status: 500 }
    );
  }
}
