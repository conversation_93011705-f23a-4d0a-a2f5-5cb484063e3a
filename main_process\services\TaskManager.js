/**
 * 任务管理器 - 专门处理任务管理相关功能
 * 
 * 从 collectorService.js 中提取的任务管理相关逻辑，包括：
 * - 任务状态管理
 * - 进度跟踪
 * - 错误处理
 * - 任务历史记录
 * - 状态更新回调
 */

const { EventEmitter } = require('events');

class TaskManager extends EventEmitter {
  constructor(config) {
    super();
    
    this.log = config.log;
    this.statusUpdateCallback = config.statusUpdateCallback;
    
    // 任务状态
    this.isRunning = false;
    this.isStopping = false;
    this.forceStop = false;
    this.currentTask = null;
    this.currentStatus = 'idle';
    this.taskHistory = [];
    
    // 任务统计
    this.stats = {
      totalTasks: 0,
      successfulTasks: 0,
      failedTasks: 0,
      cancelledTasks: 0
    };
  }

  /**
   * 设置状态更新回调
   */
  setStatusUpdateCallback(callback) {
    this.statusUpdateCallback = callback;
  }

  /**
   * 更新任务状态
   * @param {string} status - 状态
   * @param {string} message - 消息
   * @param {string} filePath - 文件路径（可选）
   */
  updateTaskStatus(status, message, filePath = null) {
    // 更新当前状态
    this.currentStatus = status;

    if (this.statusUpdateCallback) {
      const statusUpdate = {
        status: status,
        message: message,
        timestamp: new Date().toISOString(),
        isRunning: this.isRunning,
        currentTask: this.currentTask ? {
          siteKey: this.currentTask.siteKey,
          targetUrl: this.currentTask.targetUrl,
          startTime: this.currentTask.startTime,
          status: this.currentTask.status
        } : null,
        filePath: filePath
      };

      try {
        this.statusUpdateCallback(statusUpdate);
      } catch (error) {
        this.log.error(`[TaskManager] 状态更新回调失败: ${error.message}`);
      }
    }

    // 发出事件
    this.emit('statusUpdate', {
      status,
      message,
      filePath,
      timestamp: new Date().toISOString()
    });

    this.log.info(`[TaskManager] 状态更新: ${status} - ${message}`);
  }

  /**
   * 开始新任务
   * @param {string} siteKey - 站点键
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 选项
   * @returns {Object} 任务启动结果
   */
  startTask(siteKey, targetUrl, options = {}) {
    if (this.isRunning) {
      return { success: false, error: '已有任务正在运行' };
    }

    try {
      // 重置状态
      this.isRunning = true;
      this.isStopping = false;
      this.forceStop = false;

      // 创建新任务
      this.currentTask = {
        id: this.generateTaskId(),
        siteKey,
        targetUrl,
        options,
        startTime: new Date(),
        status: 'running',
        progress: {
          currentPage: 0,
          totalPages: 0,
          processedPosts: 0,
          totalPosts: 0,
          downloadedFiles: 0,
          failedDownloads: 0
        }
      };

      this.stats.totalTasks++;
      
      this.log.info(`[TaskManager] 启动新任务: ${siteKey} -> ${targetUrl}`);
      this.updateTaskStatus('initializing', '正在启动搜集任务...');

      this.emit('taskStarted', this.currentTask);

      return { 
        success: true, 
        taskId: this.currentTask.id,
        message: '任务已启动' 
      };

    } catch (error) {
      this.log.error(`[TaskManager] 启动任务失败: ${error.message}`);
      this.isRunning = false;
      this.currentTask = null;
      
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  /**
   * 停止当前任务
   * @param {boolean} force - 是否强制停止
   * @returns {Object} 停止结果
   */
  stopTask(force = false) {
    if (!this.isRunning) {
      return { success: false, error: '当前没有运行中的任务' };
    }

    try {
      if (force) {
        this.forceStop = true;
        this.updateTaskStatus('force-stopping', '正在强制停止任务...');
        this.log.info('[TaskManager] 强制停止任务');
      } else {
        this.isStopping = true;
        this.updateTaskStatus('stopping', '正在停止任务...');
        this.log.info('[TaskManager] 请求停止任务');
      }

      this.emit('taskStopRequested', { force, taskId: this.currentTask?.id });

      return { 
        success: true, 
        message: force ? '强制停止请求已发送' : '停止请求已发送' 
      };

    } catch (error) {
      this.log.error(`[TaskManager] 停止任务失败: ${error.message}`);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  /**
   * 完成当前任务
   * @param {Object} result - 任务结果
   */
  completeTask(result) {
    if (!this.currentTask) {
      this.log.warn('[TaskManager] 尝试完成任务，但没有当前任务');
      return;
    }

    try {
      // 更新任务信息
      this.currentTask.endTime = new Date();
      this.currentTask.result = result;
      
      if (this.forceStop) {
        this.currentTask.status = 'force-stopped';
        this.stats.cancelledTasks++;
        this.updateTaskStatus('force-stopped', `任务已强制停止`);
      } else if (this.isStopping) {
        this.currentTask.status = 'stopped';
        this.stats.cancelledTasks++;
        this.updateTaskStatus('stopped', `任务已停止`);
      } else if (result.success) {
        this.currentTask.status = 'completed';
        this.stats.successfulTasks++;
        this.updateTaskStatus('completed', `任务完成`);
      } else {
        this.currentTask.status = 'failed';
        this.stats.failedTasks++;
        this.updateTaskStatus('failed', `任务失败: ${result.error || '未知错误'}`);
      }

      // 添加到历史记录
      this.taskHistory.push({ ...this.currentTask });
      
      // 限制历史记录数量
      if (this.taskHistory.length > 100) {
        this.taskHistory = this.taskHistory.slice(-50);
      }

      this.emit('taskCompleted', this.currentTask);

      this.log.info(`[TaskManager] 任务完成: ${this.currentTask.status}`);

    } catch (error) {
      this.log.error(`[TaskManager] 完成任务时发生错误: ${error.message}`);
    } finally {
      // 重置状态
      this.isRunning = false;
      this.isStopping = false;
      this.forceStop = false;
      this.currentTask = null;
      this.currentStatus = 'idle';
    }
  }

  /**
   * 更新任务进度
   * @param {Object} progress - 进度信息
   */
  updateProgress(progress) {
    if (!this.currentTask) {
      return;
    }

    try {
      // 更新进度信息
      Object.assign(this.currentTask.progress, progress);

      // 计算百分比
      let percentage = 0;
      if (this.currentTask.progress.totalPosts > 0) {
        percentage = Math.round((this.currentTask.progress.processedPosts / this.currentTask.progress.totalPosts) * 100);
      }

      this.emit('progressUpdate', {
        taskId: this.currentTask.id,
        progress: this.currentTask.progress,
        percentage
      });

    } catch (error) {
      this.log.error(`[TaskManager] 更新进度失败: ${error.message}`);
    }
  }

  /**
   * 处理页面状态错误
   * @param {string} status - 页面状态
   * @param {Object} page - 页面对象
   * @param {string} context - 上下文信息
   * @returns {string} 处理结果: 'continue', 'stop', 'retry', 'wait_user'
   */
  async handlePageStatusError(status, page, context = '') {
    switch (status) {
      case 'download_limit_exceeded':
        this.updateTaskStatus('stopped', `检测到下载次数已达上限，任务自动停止。${context}`);
        this.log.info('[TaskManager] 下载超限，任务停止');
        return 'stop';

      case 'human_verification_required':
        this.updateTaskStatus('paused', `检测到人机验证页面，请手动完成验证后继续。${context}`);
        this.log.info('[TaskManager] 需要人工干预，任务暂停');
        return 'wait_user';

      case 'server_error':
        this.updateTaskStatus('retrying', `服务器错误，等待重试。${context}`);
        this.log.info('[TaskManager] 服务器错误，准备重试');
        return 'retry';

      case 'network_error':
        this.updateTaskStatus('retrying', `网络错误，等待重试。${context}`);
        this.log.info('[TaskManager] 网络错误，准备重试');
        return 'retry';

      case 'access_denied':
        this.updateTaskStatus('stopped', `访问被拒绝，任务停止。${context}`);
        this.log.info('[TaskManager] 访问被拒绝，任务停止');
        return 'stop';

      default:
        this.log.info(`[TaskManager] 未知页面状态: ${status}，继续执行`);
        return 'continue';
    }
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态信息
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isStopping: this.isStopping,
      forceStop: this.forceStop,
      currentStatus: this.currentStatus,
      currentTask: this.currentTask ? {
        id: this.currentTask.id,
        siteKey: this.currentTask.siteKey,
        targetUrl: this.currentTask.targetUrl,
        startTime: this.currentTask.startTime,
        status: this.currentTask.status,
        progress: this.currentTask.progress
      } : null,
      stats: { ...this.stats },
      recentTasks: this.taskHistory.slice(-5) // 最近5个任务
    };
  }

  /**
   * 获取任务历史
   * @param {number} limit - 限制数量
   * @returns {Array} 任务历史
   */
  getTaskHistory(limit = 20) {
    return this.taskHistory.slice(-limit);
  }

  /**
   * 清理任务历史
   * @param {number} keepCount - 保留数量
   */
  cleanupHistory(keepCount = 50) {
    if (this.taskHistory.length > keepCount) {
      const removed = this.taskHistory.length - keepCount;
      this.taskHistory = this.taskHistory.slice(-keepCount);
      this.log.info(`[TaskManager] 清理任务历史，删除 ${removed} 条记录`);
    }
  }

  /**
   * 生成任务ID
   * @returns {string} 任务ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查是否应该停止任务
   * @returns {boolean} 是否应该停止
   */
  shouldStop() {
    return this.isStopping || this.forceStop;
  }

  /**
   * 检查是否强制停止
   * @returns {boolean} 是否强制停止
   */
  isForceStop() {
    return this.forceStop;
  }

  /**
   * 重置任务管理器
   */
  reset() {
    this.isRunning = false;
    this.isStopping = false;
    this.forceStop = false;
    this.currentTask = null;
    this.currentStatus = 'idle';
    
    this.log.info('[TaskManager] 任务管理器已重置');
    this.updateTaskStatus('idle', '任务管理器已重置');
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalCompleted = this.stats.successfulTasks + this.stats.failedTasks + this.stats.cancelledTasks;
    const successRate = totalCompleted > 0 ? Math.round((this.stats.successfulTasks / totalCompleted) * 100) : 0;

    return {
      ...this.stats,
      totalCompleted,
      successRate,
      currentlyRunning: this.isRunning ? 1 : 0
    };
  }
}

module.exports = TaskManager;
