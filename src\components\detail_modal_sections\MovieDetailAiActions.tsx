// soul-forge-electron/src/components/detail_modal_sections/MovieDetailAiActions.tsx
import React from 'react';
import { Movie } from '../../types';

interface MovieDetailAiActionsProps {
  movie: Movie; // For context if needed, though AI actions might just use movie from hook
  generatePlot: () => Promise<void>;
  embellishPlot: () => Promise<void>;
  analyzeTags: () => Promise<void>;
  isGeneratingPlot: boolean;
  isEmbellishingPlot: boolean;
  isAnalyzingTags: boolean;
}

const MovieDetailAiActions: React.FC<MovieDetailAiActionsProps> = ({
  movie,
  generatePlot,
  embellishPlot,
  analyzeTags,
  isGeneratingPlot,
  isEmbellishingPlot,
  isAnalyzingTags,
}) => {
  
  return (
    <div className="mt-3 p-3 border border-purple-500/30 rounded-md bg-[#2d2d2d]/50 space-y-2">
        <h4 className="text-sm font-semibold text-purple-300 mb-1.5">林珞姐姐的魔法辅助 ✨</h4>
        <div className="grid grid-cols-2 sm:grid-cols-2 gap-2">
            <button onClick={generatePlot} disabled={isGeneratingPlot || !movie} className="button-secondary-app text-xs py-1.5">
                {isGeneratingPlot ? 'AI生成剧情中...' : 'AI生成剧情'}
            </button>
            <button onClick={embellishPlot} disabled={isEmbellishingPlot || !movie?.db_id} className="button-secondary-app text-xs py-1.5">
                {isEmbellishingPlot ? '林珞润色中...' : '林珞润色剧情'}
            </button>
            <button onClick={analyzeTags} disabled={isAnalyzingTags || !movie?.db_id} className="button-secondary-app text-xs py-1.5">
                {isAnalyzingTags ? 'AI分析标签中...' : 'AI智能标签分析'}
            </button>
        </div>
    </div>
  );
};

export default MovieDetailAiActions;
