
// soul-forge-electron/src/components/detail_modal_sections/MovieDetailInfoSection.tsx
import React from 'react'; 
import { Movie, DetailFilterType, FavoriteItemType } from '../../types';
import { LuStar, LuUser } from 'react-icons/lu';
import ImageWithFallback from '../ImageWithFallback';
import { EditableMovieState, movieToEditableState } from './utils';
import { useAppSettings } from '../../hooks/useAppSettings';
import { ActorList } from '../common/ActorLink';


const formatRuntime = (runtimeMinutes?: number | null): string => {
    if (runtimeMinutes === null || runtimeMinutes === undefined || isNaN(runtimeMinutes) || runtimeMinutes <= 0) return '暂无';
    if (runtimeMinutes < 60) return `${runtimeMinutes} 分钟`;
    const hours = Math.floor(runtimeMinutes / 60);
    const minutes = runtimeMinutes % 60;
    return `${hours} 小时 ${minutes > 0 ? ` ${minutes} 分钟` : ''}`.trim();
};

const formatFileSize = (bytes?: number | null): string => {
  if (bytes === null || bytes === undefined || isNaN(bytes) || bytes < 0) return '未知大小';
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

interface MovieDetailInfoSectionProps {
  movie: Movie;
  editableMovie: EditableMovieState;
  isEditing: boolean;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onTagClick?: (value: string, type: DetailFilterType) => void;
  favoriteStatus: Record<FavoriteItemType, Record<string, boolean>>; 
  onToggleFavorite: (type: FavoriteItemType, value: string) => void; 
  onUpdateMovie: (updatedMovie: Movie) => void; 
  setEditableMovie: React.Dispatch<React.SetStateAction<EditableMovieState | null>>; 
  appDefaultActorAvatar?: string | null;
  actorAvatars: Record<string, string | null>; 
  onOpenAvatarPreview: (actorName: string) => void; 
  aiGeneratedPlotDisplay: string | null; 
  aiEmbellishedPlotDisplay: string | null; 
}

const MovieDetailInfoSection: React.FC<MovieDetailInfoSectionProps> = ({
  movie,
  editableMovie,
  isEditing,
  onInputChange,
  onTagClick,
  favoriteStatus, 
  onToggleFavorite, 
  onUpdateMovie,
  setEditableMovie, 
  appDefaultActorAvatar,
  actorAvatars, 
  onOpenAvatarPreview, 
  aiGeneratedPlotDisplay,
  aiEmbellishedPlotDisplay,
}) => {
  const { appSettings } = useAppSettings(); 

  const renderInfoListDisplay = (
    items?: string[] | null, 
    label?: string, 
    currentOnTagClick?: (value: string, type: DetailFilterType) => void,
    detailFilterType?: DetailFilterType,
    itemClassName?: string, 
    isAiTag: boolean = false,
    isDirector: boolean = false
  ) => {
      if (!items || items.length === 0) {
        if (label && !isAiTag && !isDirector) return <p className="text-sm text-neutral-400">{label}: 暂无</p>; 
        if (label && isDirector) return <p className="text-sm text-neutral-400">{label}: 暂无</p>;
        return null;
      }
      return (
        <div className="mb-3">
          {label && <h4 className="text-xs font-semibold text-neutral-400 mb-1.5 uppercase tracking-wider">{label}</h4>}
          <div className="flex flex-wrap gap-x-3 gap-y-2.5">
            {items.map((item, index) => (
              <div 
                key={index} 
                className={`flex flex-col items-center rounded-lg shadow-md overflow-hidden
                            ${isAiTag ? 'bg-purple-700/70' : (isDirector ? 'bg-sky-700/70' : 'bg-[#3a3a3a]')}
                            ${detailFilterType === 'actor' ? 'w-20' : ''} `}
              >
                {(detailFilterType === 'actor' || isDirector) && (
                  <div 
                    className={`w-full ${detailFilterType === 'actor' ? 'h-20' : 'h-16'} flex-shrink-0 bg-[#2c2c2c] ${detailFilterType === 'actor' && currentOnTagClick ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
                    onClick={() => detailFilterType === 'actor' && currentOnTagClick ? currentOnTagClick(item, 'actor') : (detailFilterType === 'actor' ? onOpenAvatarPreview(item) : undefined)}
                    title={detailFilterType === 'actor' && currentOnTagClick ? `筛选演员: ${item}` : (detailFilterType === 'actor' ? `预览 ${item} 头像` : undefined)}
                  >
                    {isDirector ? (
                        <LuUser className="w-full h-full text-sky-300 p-3 opacity-70"/>
                    ) : (
                        <ImageWithFallback
                            primarySrc={actorAvatars[item]}
                            appDefaultCoverDataUrl={appDefaultActorAvatar}
                            alt={`${item} avatar`}
                            className="w-full h-full object-cover"
                            placeholder={<div className="w-full h-full bg-neutral-700 text-[10px] text-neutral-400 flex items-center justify-center text-center p-1">无头像</div>}
                            forceShowPlaceholder={!appSettings.imagesGloballyVisible}
                        />
                    )}
                  </div>
                )}
                <div className={`flex items-center justify-center px-1.5 py-1 text-center w-full ${detailFilterType === 'actor' || isDirector ? 'mt-0.5' : ''}`}>
                    <button
                      onClick={() => detailFilterType && currentOnTagClick?.(item, detailFilterType)}
                      className={`text-xs 
                                  ${detailFilterType && currentOnTagClick ? 'hover:text-[#B8860B] transition-colors cursor-pointer focus:outline-none' : 'cursor-default'}
                                  ${isAiTag ? 'text-purple-100' : (isDirector ? 'text-sky-100 font-medium' : 'text-neutral-100')}
                                  ${itemClassName || ''} break-words line-clamp-2 leading-tight`}
                      disabled={!detailFilterType || !currentOnTagClick}
                      aria-label={detailFilterType && currentOnTagClick ? `筛选 ${label || detailFilterType}: ${item}` : item}
                      title={item}
                    >
                      {item}
                      {isAiTag && <span className="ml-1 text-[9px] opacity-80">(AI)</span>}
                    </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
  };

  const displayObject = isEditing ? editableMovie : movie;
  
  const getRuntimeForDisplay = (runtime: string | number | null | undefined): number | null => {
    if (typeof runtime === 'string') {
        const parsed = parseInt(runtime, 10);
        return isNaN(parsed) ? null : parsed;
    }
    return runtime ?? null; 
  };

  const getArrayForDisplay = (value: string | string[] | null | undefined): string[] | null => {
    if (typeof value === 'string' && value.trim() !== '') { 
        return value.split(',').map(s => s.trim()).filter(Boolean);
    } else if (Array.isArray(value)) {
        return value.filter(Boolean);
    }
    return null; 
  };

  const handleRatingChangeDisplay = async (newRating: number) => {
    if (!movie || !movie.filePath) return;
    const ratingToSet = movie.personalRating === newRating ? null : newRating;
    const updatedMovie = { ...movie, personalRating: ratingToSet };
    onUpdateMovie(updatedMovie); 
    window.sfeElectronAPI.saveNfoData(movie.filePath, updatedMovie).catch(err => {
      console.error("保存个人评分更改失败", err);
    });
  };
  
  const handleToggleWatchedDisplay = async () => {
    if (!movie || !movie.filePath) return; 
    const updatedMovie = { ...movie, watched: !movie.watched };
    onUpdateMovie(updatedMovie); 
    window.sfeElectronAPI.saveNfoData(movie.filePath, updatedMovie).catch(err => {
      console.error("保存观看状态切换失败", err);
    });
  };

  const handleRatingChangeInEditMode = (newRating: number) => {
    if (!editableMovie) return;
    const ratingToSet = editableMovie.personalRating === newRating ? null : newRating;
     onInputChange({ target: { name: 'personalRating', value: String(ratingToSet), type: 'number' } } as any);

  };

  const renderStars = (currentRating: number | null | undefined, onStarClick: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            onClick={() => onStarClick(star)}
            className={`transition-colors duration-150 focus:outline-none 
                        ${star <= (currentRating || 0) ? 'text-amber-400 hover:text-amber-300' : 'text-neutral-500 hover:text-amber-500'}`}
            aria-label={`评价 ${star} 星`}
            title={`评价 ${star} 星`}
          >
            <LuStar size={20} className={`${star <= (currentRating || 0) ? 'fill-current' : ''}`} />
          </button>
        ))}
      </div>
    );
  };

  const handleUseAiPlot = (aiPlot: string | null) => {
    if (aiPlot && editableMovie) {
        if (window.confirm("要使用这个AI生成的剧情替换当前编辑框中的剧情吗？切换到编辑模式可保存。")) {
            setEditableMovie(prev => prev ? { ...prev, plot: aiPlot } : null);
        }
    } else if (aiPlot && !isEditing) {
         if (window.confirm("要使用这个AI生成的剧情吗？内容将填充到编辑框，您需要进入编辑模式并保存。")) {
            setEditableMovie(prev => {
                if (!prev && movie) { 
                    const baseEditable = movieToEditableState(movie);
                    return { ...baseEditable, plot: aiPlot };
                }
                return prev ? { ...prev, plot: aiPlot } : null;
            });
        }
    }
  };


  return (
    <>
      {isEditing ? (
        <> 
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div><label htmlFor="title" className="settings-label">标题</label><input type="text" name="title" id="title" value={editableMovie.title} onChange={onInputChange} className="form-input-app" /></div>
            <div><label htmlFor="originalTitle" className="settings-label">原标题</label><input type="text" name="originalTitle" id="originalTitle" value={editableMovie.originalTitle} onChange={onInputChange} className="form-input-app" /></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            <div><label htmlFor="nfoId" className="settings-label">番号/ID</label><input type="text" name="nfoId" id="nfoId" value={editableMovie.nfoId} onChange={onInputChange} className="form-input-app" /></div>
            <div><label htmlFor="year" className="settings-label">年份</label><input type="number" name="year" id="year" value={editableMovie.year} onChange={onInputChange} className="form-input-app" /></div>
            <div><label htmlFor="releaseDate" className="settings-label">发行日期</label><input type="date" name="releaseDate" id="releaseDate" value={editableMovie.releaseDate} onChange={onInputChange} className="form-input-app" /></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <label htmlFor="runtime" className="settings-label">时长 (分钟)</label>
                <input type="number" name="runtime" id="runtime" value={editableMovie.runtime} onChange={onInputChange} className="form-input-app" />
              </div>
          </div>
          <div>
            <label htmlFor="plot" className="settings-label flex items-center">剧情简介</label>
            <textarea name="plot" id="plot" value={editableMovie.plot} onChange={onInputChange} rows={4} className="form-textarea-app"></textarea>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <div><label htmlFor="studio" className="settings-label">制作商</label><input type="text" name="studio" id="studio" value={editableMovie.studio} onChange={onInputChange} className="form-input-app" /></div>
            <div><label htmlFor="series" className="settings-label">系列</label><input type="text" name="series" id="series" value={editableMovie.series} onChange={onInputChange} className="form-input-app" /></div>
          </div>
            <div><label htmlFor="director" className="settings-label">导演</label><input type="text" name="director" id="director" value={editableMovie.director} onChange={onInputChange} className="form-input-app" /></div>
            <div><label htmlFor="actors" className="settings-label">演员 (逗号分隔)</label><textarea name="actors" id="actors" value={editableMovie.actors} onChange={onInputChange} rows={2} className="form-textarea-app"></textarea></div>
            <div>
              <label htmlFor="genres" className="settings-label">类型 (逗号分隔)</label>
              <textarea name="genres" id="genres" value={editableMovie.genres} onChange={onInputChange} rows={2} className="form-textarea-app"></textarea>
            </div>
            <div>
              <label htmlFor="tags" className="settings-label flex items-center">标签 (逗号分隔)</label>
              <textarea name="tags" id="tags" value={editableMovie.tags} onChange={onInputChange} rows={2} className="form-textarea-app"></textarea>
            </div>
            {editableMovie.aiAnalyzedTags && editableMovie.aiAnalyzedTags.length > 0 && (
              <div><label className="settings-label">AI分析标签 (只读, 编辑普通标签来修改)</label><p className="text-xs text-purple-300 bg-[#2d2d2d] p-2 rounded-md border border-[#4f4f4f]">{editableMovie.aiAnalyzedTags}</p></div>
            )}
            
          <div className="flex items-center space-x-4">
              <div>
                  <label className="settings-label">个人评分</label>
                  {renderStars(editableMovie.personalRating, handleRatingChangeInEditMode)}
              </div>
              <div className="flex items-center pt-5">
                  <input type="checkbox" name="watched" id="watched" checked={editableMovie.watched} onChange={onInputChange} className="form-checkbox-app" />
                  <label htmlFor="watched" className="ml-2 text-sm text-neutral-100">已观看</label>
              </div>
          </div>
        </>
      ) : (
        <> 
          <div className="flex justify-between items-center mb-2">
            {renderStars(displayObject.personalRating, handleRatingChangeDisplay)}
            <button onClick={handleToggleWatchedDisplay} 
                    className={`px-3 py-1 rounded-md text-sm font-medium border transition-colors
                                ${displayObject.watched ? 'bg-green-600 hover:bg-green-500 text-white border-green-500' : 'bg-neutral-600 hover:bg-neutral-500 text-neutral-200 border-neutral-500'}`}>
              {displayObject.watched ? '已观看 ✓' : '标记为已观看'}
            </button>
          </div>
          <p className="text-sm text-neutral-300"><strong>原标题:</strong> {displayObject.originalTitle || 'N/A'}</p>
          <p className="text-sm text-neutral-300"><strong>番号/ID:</strong> 
            {displayObject.nfoId ? 
              <button onClick={() => onTagClick?.(displayObject.nfoId!, 'nfoId')} className="ml-1 text-sky-400 hover:text-sky-300 underline hover:no-underline">{displayObject.nfoId}</button> 
              : 'N/A'}
          </p>
          <p className="text-sm text-neutral-300"><strong>年份:</strong> {displayObject.year || 'N/A'} <span className="text-neutral-500 mx-1">|</span> <strong>发行日期:</strong> {displayObject.releaseDate || 'N/A'}</p>
          <p className="text-sm text-neutral-300"><strong>时长:</strong> {formatRuntime(getRuntimeForDisplay(displayObject.runtime))}</p>
          <p className="text-sm text-neutral-300"><strong>文件路径:</strong> <span className="text-neutral-400 text-xs break-all">{displayObject.filePath}</span></p>
          <p className="text-sm text-neutral-300"><strong>文件大小:</strong> {formatFileSize(displayObject.fileSize)} <span className="text-neutral-500 mx-1">|</span> <strong>分辨率:</strong> {displayObject.resolution || 'N/A'}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6">
             {/* 演员列表 - 使用可点击的ActorList组件 */}
            <div className="mb-3">
              <h4 className="text-sm font-semibold text-neutral-300 mb-1">演员</h4>
              <div className="text-sm text-neutral-200">
                <ActorList
                  actors={getArrayForDisplay(displayObject.actors) || []}
                  variant="default"
                  className="flex flex-wrap gap-1"
                />
              </div>
            </div>
            {renderInfoListDisplay(getArrayForDisplay(displayObject.director), '导演', onTagClick, 'director', undefined, false, true)}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6">
            {renderInfoListDisplay(getArrayForDisplay(displayObject.studio), '制作商', onTagClick, 'studio')}
             {/* Ensure displayObject.series is used for '系列' */}
            {renderInfoListDisplay(getArrayForDisplay(displayObject.series), '系列', onTagClick, 'series')}
          </div>
          
          {renderInfoListDisplay(getArrayForDisplay(displayObject.genres), '类型', onTagClick, 'genre')}
          {renderInfoListDisplay(getArrayForDisplay(displayObject.tags), '标签', onTagClick, 'tag')}
          {renderInfoListDisplay(getArrayForDisplay(displayObject.aiAnalyzedTags), 'AI分析标签', onTagClick, 'tag', 'text-purple-200', true)}
          
          <div>
            <h4 className="text-sm font-semibold text-neutral-300 mb-1">剧情简介</h4>
            <p className="text-sm text-neutral-200 bg-[#2d2d2d] p-3 rounded-md border border-[#4f4f4f] max-h-32 overflow-y-auto settings-scroll-container leading-relaxed whitespace-pre-wrap">
              {displayObject.plot || '暂无剧情简介。'}
            </p>
          </div>

          {aiGeneratedPlotDisplay && (
            <div className="mt-3">
              <h4 className="text-sm font-semibold text-sky-300 mb-1">AI 生成剧情</h4>
              <p className="text-sm text-neutral-200 bg-[#2a3b4d] p-3 rounded-md border border-sky-500/50 max-h-32 overflow-y-auto settings-scroll-container leading-relaxed whitespace-pre-wrap">
                {aiGeneratedPlotDisplay}
              </p>
              <button 
                onClick={() => handleUseAiPlot(aiGeneratedPlotDisplay)} 
                className="button-secondary-app text-xs mt-1.5 !py-1 !px-2"
              >
                使用此剧情
              </button>
            </div>
          )}

          {aiEmbellishedPlotDisplay && (
            <div className="mt-3">
              <h4 className="text-sm font-semibold text-pink-300 mb-1">林珞姐姐 润色剧情</h4>
              <p className="text-sm text-neutral-200 bg-[#4d2a3b] p-3 rounded-md border border-pink-500/50 max-h-32 overflow-y-auto settings-scroll-container leading-relaxed whitespace-pre-wrap">
                {aiEmbellishedPlotDisplay}
              </p>
              <button 
                onClick={() => handleUseAiPlot(aiEmbellishedPlotDisplay)} 
                className="button-secondary-app text-xs mt-1.5 !py-1 !px-2"
              >
                使用此剧情
              </button>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default MovieDetailInfoSection;
      