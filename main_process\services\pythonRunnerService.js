// soul-forge-electron/main_process/services/pythonRunnerService.js
// 🚫 【废弃】这个文件已被废弃！软件已移除所有Python依赖，改用JavaScript实现
// 快照生成功能已迁移到: main_process/services/snapshotGeneratorService.js
// 其他Python功能需要重构为JavaScript实现
// 请不要再使用这个服务！

const { spawn } = require('node:child_process');
const path = require('node:path');
const fs = require('node:fs');

let log;
let isDevInternal;
let mainDirInternal;
let resourcesPathInternal;

function initializePythonRunner(logger, isDev, mainDir, resourcesPath) {
  log = logger;
  isDevInternal = isDev;
  mainDirInternal = mainDir;
  resourcesPathInternal = resourcesPath;
  log.info('[Python运行服务] 初始化。');
}

function getPythonScriptPath(scriptName) {
  let scriptPath;
  if (isDevInternal) {
    scriptPath = path.join(mainDirInternal, 'python_scripts', scriptName);
  } else {
    scriptPath = path.join(resourcesPathInternal, 'python_scripts', scriptName);
  }
  
  if (!fs.existsSync(scriptPath)) {
    log.error(`[Python运行服务] Python 脚本未找到: ${scriptPath}`);
    throw new Error(`Python script not found: ${scriptPath}`);
  }
  return scriptPath;
}

function runPythonScript(scriptName, args = [], preferredPythonPath = null) {
  return new Promise((resolve, reject) => {
    let scriptPath;
    try {
      scriptPath = getPythonScriptPath(scriptName);
    } catch (error) {
      // Reject directly if script path is not found, to be caught by caller
      return reject(error); 
    }

    const pythonExecutable = preferredPythonPath || (process.platform === 'win32' ? 'python' : 'python3');
    
    // Ensure all arguments are strings before spawning
    const stringArgs = args.map(arg => String(arg));

    log.info(`[Python运行服务] 执行脚本: "${pythonExecutable}" "${scriptPath}" 参数: [${stringArgs.map(a => typeof a === 'string' && a.length > 70 ? a.substring(0,70) + '...' : a).join(', ')}]`);

    const pythonProcess = spawn(pythonExecutable, [scriptPath, ...stringArgs]);

    let stdoutData = '';
    let stderrData = '';

    pythonProcess.stdout.on('data', (data) => {
      stdoutData += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderrData += data.toString();
    });

    pythonProcess.on('close', (code) => {
      log.info(`[Python运行服务] 脚本 ${scriptName} 已关闭，退出码 ${code}。`);
      if (stderrData) {
        log.warn(`[Python运行服务] 脚本 ${scriptName} STDERR (前300字符): ${stderrData.substring(0,300)}`);
      }
      if (code === 0) {
        // Resolve with success true for 0 exit code
        resolve({ success: true, data: stdoutData.trim(), error: stderrData.trim() || null });
      } else {
        // Resolve with success false for non-0 exit code, including stderr in error field
        resolve({ success: false, error: stderrData.trim() || `Python script exited with code ${code}`, data: stdoutData.trim() });
      }
    });

    pythonProcess.on('error', (error) => {
      log.error(`[Python运行服务] 启动 Python 脚本 ${scriptName} 失败:`, error);
      // Reject on spawn error
      reject(new Error(`Failed to start Python script ${scriptName}: ${error.message}`));
    });
  });
}

async function generateThumbnailsWithPython(videoFilePath, videoDbId, quality, ffmpegPath, cachePath, pythonPath, imagePathToDataUrlFunc) {
  log.info(`[Python运行服务] 开始为 "${videoFilePath}" (DB ID: ${videoDbId}) 生成缩略图。质量: ${quality}, FFmpeg: ${ffmpegPath || '系统默认'}, 缓存: ${cachePath}`);
  if (!videoFilePath || typeof videoDbId !== 'number') {
    return { success: false, error: '无效的视频文件路径或数据库ID。' };
  }

  const args = [
    videoFilePath, 
    String(videoDbId), // Ensure dbId is string for python script
    quality, 
    ffmpegPath || "ffmpeg", // Pass "ffmpeg" if null, script might find it in PATH
    cachePath
  ];
  
  try {
    const result = await runPythonScript('generate_thumbnails.py', args, pythonPath);

    if (result.success && result.data) {
      try {
        const thumbnailPaths = JSON.parse(result.data); 
        const thumbnailDataUrls = thumbnailPaths.map(p => p ? imagePathToDataUrlFunc(p) : null);
        log.info(`[Python运行服务] 成功为 ${videoFilePath} 生成 ${thumbnailDataUrls.filter(Boolean).length} 个缩略图。`);
        return { success: true, thumbnailDataUrls };
      } catch (e) {
        log.error(`[Python运行服务] 解析缩略图脚本输出失败: ${e.message}, 输出 (前100): ${String(result.data).substring(0,100)}`);
        return { success: false, error: `解析脚本输出失败: ${e.message}` };
      }
    } else {
      // runPythonScript now resolves with success: false and an error field for non-zero exits
      log.error(`[Python运行服务] 生成缩略图的 Python 脚本执行失败: ${result.error}`);
      return { success: false, error: result.error || 'Python 脚本执行失败，未提供具体错误。' };
    }
  } catch (scriptError) {
    // Catch errors from runPythonScript itself (e.g., script not found, spawn error)
    log.error(`[Python运行服务] 执行 generate_thumbnails.py 脚本时发生严重错误: ${scriptError.message}`);
    return { success: false, error: scriptError.message };
  }
}


module.exports = {
  initializePythonRunner,
  runPythonScript,
  generateThumbnailsWithPython,
};