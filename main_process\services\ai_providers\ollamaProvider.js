// main_process/services/ai_providers/ollamaProvider.js
// Ollama 本地AI提供商 - 数据100%不出本地的AI处理

const axios = require('axios');
const log = require('electron-log');

/**
 * Ollama 本地AI提供商
 * 与本地运行的Ollama服务进行通信，实现完全本地化的AI处理
 */
class OllamaProvider {
  constructor() {
    this.name = 'Ollama本地AI';
    this.version = '1.0.0';
    this.description = '本地运行的AI服务，数据100%不出本地';
  }

  /**
   * 获取AI响应 - 核心接口函数
   * @param {string} prompt - 用户提示词
   * @param {Object} settings - 用户设置对象
   * @returns {Promise<string>} AI生成的响应内容
   */
  async getAiResponse(prompt, settings) {
    try {
      log.info(`[${this.name}] 开始处理AI请求`);
      
      // 验证必要参数
      if (!prompt || typeof prompt !== 'string') {
        throw new Error('提示词不能为空');
      }

      if (!settings) {
        throw new Error('设置对象不能为空');
      }

      // 从设置中获取Ollama配置
      const apiEndpoint = settings.ollamaApiEndpoint || 'http://localhost:11434';
      const modelName = settings.ollamaModelName || 'llama3';

      log.info(`[${this.name}] 使用配置 - 端点: ${apiEndpoint}, 模型: ${modelName}`);

      // 构造Ollama API请求
      const requestUrl = `${apiEndpoint}/api/generate`;
      const requestBody = {
        model: modelName,
        prompt: prompt,
        stream: false, // 明确设置为非流式模式
        options: {
          temperature: 0.7, // 适中的创造性
          top_p: 0.9,
          top_k: 40
        }
      };

      log.info(`[${this.name}] 发送请求到: ${requestUrl}`);

      // 发起HTTP请求
      const startTime = Date.now();
      const response = await axios.post(requestUrl, requestBody, {
        timeout: 60000, // 60秒超时
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'SoulForge-Ollama-Client/1.0'
        }
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      log.info(`[${this.name}] 请求完成，耗时: ${duration}ms`);

      // 验证响应格式
      if (!response.data) {
        throw new Error('Ollama服务返回空响应');
      }

      if (typeof response.data !== 'object') {
        throw new Error('Ollama服务返回格式无效');
      }

      // 提取AI生成的内容
      const aiResponse = response.data.response;
      
      if (!aiResponse || typeof aiResponse !== 'string') {
        throw new Error('Ollama服务未返回有效的响应内容');
      }

      log.info(`[${this.name}] AI响应长度: ${aiResponse.length} 字符`);

      // 记录使用统计
      this.logUsageStats(modelName, prompt.length, aiResponse.length, duration);

      return aiResponse.trim();

    } catch (error) {
      return this.handleError(error, settings);
    }
  }

  /**
   * 错误处理 - 提供友好的错误信息和降级方案
   * @param {Error} error - 原始错误对象
   * @param {Object} settings - 用户设置
   * @returns {string} 错误信息或降级响应
   */
  handleError(error, settings) {
    const apiEndpoint = settings?.ollamaApiEndpoint || 'http://localhost:11434';
    const modelName = settings?.ollamaModelName || 'llama3';

    // 网络连接错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      const errorMsg = `❌ 无法连接到Ollama服务 (${apiEndpoint})
      
🔧 解决方案：
1. 确保已安装Ollama: https://ollama.ai/
2. 启动Ollama服务: ollama serve
3. 验证服务运行: curl ${apiEndpoint}/api/tags
4. 检查防火墙设置

💡 提示：Ollama默认运行在 http://localhost:11434`;

      log.error(`[${this.name}] 连接失败: ${error.message}`);
      return errorMsg;
    }

    // 超时错误
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      const errorMsg = `⏰ Ollama服务响应超时
      
🔧 可能原因：
1. 模型 "${modelName}" 正在首次下载
2. 系统资源不足，处理速度较慢
3. 网络连接不稳定

💡 建议：
- 等待模型下载完成
- 尝试使用更小的模型
- 检查系统内存和CPU使用情况`;

      log.error(`[${this.name}] 请求超时: ${error.message}`);
      return errorMsg;
    }

    // HTTP状态错误
    if (error.response) {
      const status = error.response.status;
      const statusText = error.response.statusText;
      
      if (status === 404) {
        const errorMsg = `🤖 模型 "${modelName}" 未找到
        
🔧 解决方案：
1. 下载模型: ollama pull ${modelName}
2. 查看可用模型: ollama list
3. 或在设置中选择其他模型

📋 推荐模型：
- llama3 (通用对话)
- qwen:7b (中文优化)
- codellama (代码生成)`;

        log.error(`[${this.name}] 模型未找到: ${modelName}`);
        return errorMsg;
      }

      const errorMsg = `❌ Ollama服务错误 (${status} ${statusText})
      
🔧 请检查：
1. Ollama服务是否正常运行
2. 模型 "${modelName}" 是否可用
3. API端点 "${apiEndpoint}" 是否正确

💡 尝试重启Ollama服务: ollama serve`;

      log.error(`[${this.name}] HTTP错误: ${status} ${statusText}`);
      return errorMsg;
    }

    // 其他未知错误
    const errorMsg = `❌ Ollama处理失败: ${error.message}
    
🔧 建议：
1. 检查Ollama服务状态
2. 验证网络连接
3. 查看Ollama日志
4. 尝试重启服务

💡 获取帮助: https://github.com/ollama/ollama/issues`;

    log.error(`[${this.name}] 未知错误: ${error.message}`);
    return errorMsg;
  }

  /**
   * 记录使用统计
   * @param {string} modelName - 模型名称
   * @param {number} promptLength - 提示词长度
   * @param {number} responseLength - 响应长度
   * @param {number} duration - 处理时长(ms)
   */
  logUsageStats(modelName, promptLength, responseLength, duration) {
    const stats = {
      provider: 'ollama',
      model: modelName,
      promptTokens: Math.ceil(promptLength / 4), // 粗略估算token数
      responseTokens: Math.ceil(responseLength / 4),
      duration: duration,
      timestamp: new Date().toISOString()
    };

    log.info(`[${this.name}] 使用统计:`, stats);
  }

  /**
   * 健康检查 - 验证Ollama服务是否可用
   * @param {Object} settings - 用户设置
   * @returns {Promise<Object>} 健康检查结果
   */
  async healthCheck(settings) {
    try {
      const apiEndpoint = settings?.ollamaApiEndpoint || 'http://localhost:11434';
      const response = await axios.get(`${apiEndpoint}/api/tags`, {
        timeout: 5000
      });

      const models = response.data?.models || [];
      
      return {
        status: 'healthy',
        endpoint: apiEndpoint,
        modelsCount: models.length,
        availableModels: models.map(m => m.name),
        message: `Ollama服务正常，发现 ${models.length} 个可用模型`
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        endpoint: settings?.ollamaApiEndpoint || 'http://localhost:11434',
        error: error.message,
        message: 'Ollama服务不可用'
      };
    }
  }

  /**
   * 获取可用模型列表
   * @param {Object} settings - 用户设置
   * @returns {Promise<Array>} 模型列表
   */
  async getAvailableModels(settings) {
    try {
      const apiEndpoint = settings?.ollamaApiEndpoint || 'http://localhost:11434';
      const response = await axios.get(`${apiEndpoint}/api/tags`, {
        timeout: 10000
      });

      const models = response.data?.models || [];
      
      return models.map(model => ({
        name: model.name,
        size: model.size,
        modified: model.modified_at,
        family: model.details?.family || 'unknown'
      }));

    } catch (error) {
      log.error(`[${this.name}] 获取模型列表失败: ${error.message}`);
      return [];
    }
  }
}

// 创建单例实例
const ollamaProvider = new OllamaProvider();

module.exports = {
  ollamaProvider,
  OllamaProvider,
  // 导出核心接口函数
  getAiResponse: (prompt, settings) => ollamaProvider.getAiResponse(prompt, settings),
  healthCheck: (settings) => ollamaProvider.healthCheck(settings),
  getAvailableModels: (settings) => ollamaProvider.getAvailableModels(settings)
};
