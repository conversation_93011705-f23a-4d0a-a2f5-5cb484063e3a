// soul-forge-electron/main_process/services/ai_providers/grokProvider.js
const httpClient = require('../../utils/httpClient');

let log;
const DEFAULT_GROK_ENDPOINT = "https://api.x.ai/v1/chat/completions";
const DEFAULT_GROK_MODEL = "grok-3-mini-fast"; // Updated default model


function initializeGrokProvider(logger) {
  log = logger;
  log.info('[Grok提供商] 初始化。');
}

function getClientConfig(config) {
  if (!config.apiKey) {
    log.error('[Grok提供商] API Key 未配置。');
    throw new Error('Grok API Key 未配置。');
  }
  return { 
    endpoint: config.endpoint || DEFAULT_GROK_ENDPOINT, 
    apiKey: config.apiKey, 
    model: config.model || DEFAULT_GROK_MODEL 
  };
}

async function generateContent(providerConfig, prompt, systemInstruction = null, options = {}, httpClientTimeout = 30000, abortSignal = null) {
  const { endpoint, apiKey, model } = providerConfig;
  log.info(`[Grok提供商] 生成内容。Endpoint: ${endpoint}, Model: ${model}, Prompt (前30): ${prompt.substring(0,30)}... Timeout: ${httpClientTimeout}ms`);
  
  const messages = [];
  if (systemInstruction) {
    messages.push({ role: "system", content: systemInstruction });
  }
  messages.push({ role: "user", content: prompt });

  const requestBodyJson = { 
    model, 
    messages, 
    max_tokens: options.max_tokens || 1024, 
    temperature: options.temperature || 0.7, 
    stream: false 
  };
   if (options.response_format) {
    requestBodyJson.response_format = options.response_format;
  }
  const requestBody = JSON.stringify(requestBodyJson);

  const headers = { 
    'Content-Type': 'application/json', 
    'Authorization': `Bearer ${apiKey}`,
    'Content-Length': Buffer.byteLength(requestBody)
  };
  
  try {
    const jsonData = await httpClient.makeAiRequest(endpoint, 'POST', headers, requestBody, httpClientTimeout, abortSignal);
    const content = (jsonData.choices?.[0]?.message?.content || jsonData.text || '').trim();
    log.info(`[Grok提供商] 内容生成成功。`);
    return content;
  } catch (error) {
    log.error(`[Grok提供商] 调用 API 失败: ${error.message}`);
    throw error;
  }
}

async function testConnection(providerConfig, httpClientTimeout = 20000) {
  const { endpoint, apiKey, model } = providerConfig; // Model is used from providerConfig
  log.info(`[Grok提供商] 测试连接。Endpoint: ${endpoint}, Model: ${model}, Timeout: ${httpClientTimeout}ms`);
  try {
    // Test connection does not need abort signal typically
    await generateContent({ endpoint, apiKey, model }, '你好', 'You are a test assistant.', {max_tokens: 5}, httpClientTimeout);
    log.info('[Grok提供商] 连接测试成功。');
    return { success: true, message: 'Grok 连接成功！' };
  } catch (error) {
    log.error(`[Grok提供商] 连接测试失败: ${error.message}`, error);
    return { success: false, message: `Grok 连接失败: ${error.message}` };
  }
}

async function generateJsonContent(providerConfig, prompt, systemInstruction = null, options = {}, httpClientTimeout = 30000, abortSignal = null) {
  const { endpoint, apiKey, model } = providerConfig;
  log.info(`[Grok提供商] 生成 JSON 内容。Endpoint: ${endpoint}, Model: ${model}, Prompt (前30): ${prompt.substring(0,30)}... Timeout: ${httpClientTimeout}ms`);
  
  const messages = [];
  if (systemInstruction) {
    messages.push({ role: "system", content: systemInstruction });
  }
  messages.push({ role: "user", content: prompt });

  const requestBodyJson = { 
    model, 
    messages, 
    max_tokens: options.max_tokens || 1024, 
    temperature: options.temperature || 0.2, 
    stream: false,
  };
  const requestBody = JSON.stringify(requestBodyJson);

  const headers = { 
    'Content-Type': 'application/json', 
    'Authorization': `Bearer ${apiKey}`,
    'Content-Length': Buffer.byteLength(requestBody)
  };
  
  try {
    const jsonData = await httpClient.makeAiRequest(endpoint, 'POST', headers, requestBody, httpClientTimeout, abortSignal);
    const contentStr = (jsonData.choices?.[0]?.message?.content || jsonData.text || '{}').trim();
    log.info(`[Grok提供商] 原始JSON响应 (前100): ${contentStr.substring(0,100)}`);
    
    let parsedData;
    try {
        parsedData = JSON.parse(contentStr);
    } catch (parseError) {
        log.warn(`[Grok提供商] 响应内容不是直接的JSON对象, 尝试移除Markdown栅栏: ${contentStr.substring(0,100)}`);
        let cleanedContent = contentStr.trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = cleanedContent.match(fenceRegex);
        if (match && match[2]) cleanedContent = match[2].trim();
        try {
            parsedData = JSON.parse(cleanedContent);
        } catch (finalParseError) {
            log.error(`[Grok提供商] 最终解析JSON失败: ${finalParseError.message}. 内容: ${cleanedContent}`);
            throw new Error(`解析AI响应JSON失败: ${finalParseError.message}.`);
        }
    }
    return parsedData;

  } catch (error) {
    log.error(`[Grok提供商] 调用 API 生成 JSON 失败: ${error.message}`);
    throw error;
  }
}


module.exports = {
  initializeGrokProvider,
  getClientConfig,
  generateContent,
  testConnection,
  generateJsonContent,
  DEFAULT_GROK_MODEL,
  DEFAULT_GROK_ENDPOINT,
};