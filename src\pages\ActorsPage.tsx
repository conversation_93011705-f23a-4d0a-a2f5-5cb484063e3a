import React, { useEffect, useState } from 'react';
import { Lu<PERSON>sers, LuSearch, LuStar, LuCalendar, LuTrendingUp } from 'react-icons/lu';

interface Actor {
  id: string;
  name: string;
  avatar?: string;
  movieCount: number;
  lastMovieDate: string;
  rating?: number;
  isPopular?: boolean;
}

const ActorsPage: React.FC = () => {
  const [actors, setActors] = useState<Actor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'movieCount' | 'lastMovieDate'>('movieCount');

  useEffect(() => {
    const fetchActors = async () => {
      try {
        setIsLoading(true);
        // TODO: 实现演员数据获取
        // const result = await window.sfeElectronAPI.getActors();
        // if (result.success) {
        //   setActors(result.data);
        // }
        
        // 模拟数据
        setTimeout(() => {
          setActors([
            {
              id: '1',
              name: '橋本有菜',
              movieCount: 25,
              lastMovieDate: '2024-01-15',
              rating: 4.8,
              isPopular: true
            },
            {
              id: '2',
              name: '三上悠亜',
              movieCount: 18,
              lastMovieDate: '2024-01-10',
              rating: 4.7,
              isPopular: true
            },
            {
              id: '3',
              name: '深田えいみ',
              movieCount: 12,
              lastMovieDate: '2024-01-08',
              rating: 4.6,
              isPopular: false
            },
            {
              id: '4',
              name: '明日花キララ',
              movieCount: 8,
              lastMovieDate: '2024-01-05',
              rating: 4.5,
              isPopular: false
            }
          ]);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('获取演员数据失败:', error);
        setIsLoading(false);
      }
    };

    fetchActors();
  }, []);

  const filteredActors = actors
    .filter(actor => 
      actor.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'movieCount':
          return b.movieCount - a.movieCount;
        case 'lastMovieDate':
          return new Date(b.lastMovieDate).getTime() - new Date(a.lastMovieDate).getTime();
        default:
          return 0;
      }
    });

  const handleActorClick = (actor: Actor) => {
    // TODO: 打开演员详情模态框或导航到演员详情页
    console.log('查看演员:', actor);
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
            <LuUsers className="h-8 w-8 text-purple-500" />
            演员档案
          </h1>
          <p className="text-gray-400">浏览和管理演员信息</p>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索演员姓名..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <label className="text-gray-400 text-sm">排序方式:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#B8860B]"
              >
                <option value="movieCount">影片数量</option>
                <option value="name">姓名</option>
                <option value="lastMovieDate">最新影片</option>
              </select>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuUsers className="h-8 w-8 text-purple-400" />
              <div>
                <p className="text-gray-400 text-sm">总演员数</p>
                <p className="text-white text-2xl font-bold">{actors.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuTrendingUp className="h-8 w-8 text-green-400" />
              <div>
                <p className="text-gray-400 text-sm">热门演员</p>
                <p className="text-white text-2xl font-bold">
                  {actors.filter(actor => actor.isPopular).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuStar className="h-8 w-8 text-yellow-400" />
              <div>
                <p className="text-gray-400 text-sm">平均评分</p>
                <p className="text-white text-2xl font-bold">
                  {actors.length > 0 
                    ? (actors.reduce((sum, actor) => sum + (actor.rating || 0), 0) / actors.length).toFixed(1)
                    : '0.0'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 演员列表 */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
                <div className="w-16 h-16 bg-gray-700 rounded-full mx-auto mb-4"></div>
                <div className="h-6 bg-gray-700 rounded w-3/4 mx-auto mb-2"></div>
                <div className="h-4 bg-gray-700 rounded w-1/2 mx-auto mb-1"></div>
                <div className="h-4 bg-gray-700 rounded w-1/3 mx-auto"></div>
              </div>
            ))}
          </div>
        ) : filteredActors.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredActors.map((actor) => (
              <div
                key={actor.id}
                onClick={() => handleActorClick(actor)}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-[#B8860B] transition-all duration-200 cursor-pointer hover:scale-105 hover:shadow-xl group"
              >
                <div className="text-center">
                  {/* 头像 */}
                  <div className="w-16 h-16 mx-auto mb-4 relative">
                    {actor.avatar ? (
                      <img
                        src={actor.avatar}
                        alt={actor.name}
                        className="w-full h-full object-cover rounded-full"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-700 rounded-full flex items-center justify-center">
                        <LuUsers className="h-8 w-8 text-gray-500" />
                      </div>
                    )}
                    
                    {actor.isPopular && (
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <LuTrendingUp className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                  
                  {/* 姓名 */}
                  <h3 className="text-white font-semibold text-lg mb-2 group-hover:text-[#B8860B] transition-colors">
                    {actor.name}
                  </h3>
                  
                  {/* 统计信息 */}
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">影片数量</span>
                      <span className="text-white font-medium">{actor.movieCount}</span>
                    </div>
                    
                    {actor.rating && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">评分</span>
                        <div className="flex items-center gap-1">
                          <LuStar className="h-3 w-3 text-yellow-400" />
                          <span className="text-white font-medium">{actor.rating}</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">最新影片</span>
                      <span className="text-gray-300 text-xs">{actor.lastMovieDate}</span>
                    </div>
                  </div>
                  
                  {/* 查看按钮 */}
                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <button className="w-full text-[#B8860B] text-sm font-medium hover:text-amber-400 transition-colors">
                      查看档案 →
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <LuUsers className="h-16 w-16 mx-auto mb-4 text-gray-600" />
            <h3 className="text-xl font-semibold text-white mb-2">未找到演员</h3>
            <p className="text-gray-400">尝试调整搜索条件</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActorsPage;
