
// soul-forge-electron/main_process/ipcHandlers/scanHandler.js
const path = require('node:path');
const fs = require('node:fs'); // For checking if path is file or directory
const NodeVideoScanner = require('../services/nodeVideoScanner');

let mainLog;
let mainPythonRunnerService;
let mainDbAccess;
let mainSettingsService;
let mainFileUtils;

function initializeScanHandler(log, pythonRunnerService, dbService, settingsService, fileUtils) {
  mainLog = log;
  mainPythonRunnerService = pythonRunnerService;
  mainDbAccess = dbService.getDb(); 
  if (!mainDbAccess) {
      mainLog.error("[扫描处理器] 初始化失败：无法获取数据库实例。扫描功能将不可用。");
  }
  mainSettingsService = settingsService;
  mainFileUtils = fileUtils;
  mainLog.info('[扫描处理器] 初始化完成。');
}


async function handleSelectFolderAndScan(foldersToScan, libraryId, webContentsSender) {
  if (!mainDbAccess) {
      mainLog.error("[扫描处理器] 无法执行扫描：数据库服务不可用。");
      webContentsSender.send('scan-error', `扫描失败：数据库服务不可用。`);
      return;
  }
  const settings = mainSettingsService.getSettings();
  const libraryLogName = libraryId ? `片库ID: ${libraryId}` : '默认扫描路径';
  mainLog.info(`[扫描处理器] 开始扫描。${libraryLogName}, 文件夹: ${foldersToScan.join(', ')}, FFprobe: ${settings.ffprobePath || '系统默认'}, Python: ${settings.pythonExecutablePath || '系统默认'}`);
  webContentsSender.send('scan-status-update', '扫描初始化...');
  webContentsSender.send('scan-progress-update', { overallPercentage: 0, currentPathMessage: "准备扫描...", filesInPathProcessed: null, totalFilesInPath: null, currentFileMessage: null, error: null });

  let totalFilesFound = 0;
  let totalFilesProcessedInPython = 0;
  const totalFolders = foldersToScan.length;
  let foldersProcessed = 0;
  let cumulativeNewCount = 0;
  let cumulativeUpdatedCount = 0;

  for (const folderPath of foldersToScan) {
    foldersProcessed++;
    const overallProgressForFolderStart = ((foldersProcessed -1) / totalFolders) * 100;
    const overallProgressForFolderEnd = (foldersProcessed / totalFolders) * 100;
    
    webContentsSender.send('scan-status-update', `正在分析文件夹: ${path.basename(folderPath)} (${foldersProcessed}/${totalFolders})`);
    webContentsSender.send('scan-progress-update', { 
        overallPercentage: overallProgressForFolderStart, 
        currentPathMessage: `准备扫描: ${path.basename(folderPath)}`,
        filesInPathProcessed: 0, 
        totalFilesInPath: null, 
        currentFileMessage: null, 
        error: null 
    });

    const scanOptions = {
      ffprobePath: settings.ffprobePath || "ffprobe",
      suffixRules: settings.filenameSuffixRules || [],
      includeSubtitles: true,
      includeTechnicalInfo: true
    };

    try {
      mainLog.info(`[扫描处理器] 使用 Node.js 扫描器扫描: ${folderPath}`);
      const result = await NodeVideoScanner.scanPath(folderPath, scanOptions);

      mainLog.info(`[扫描处理器] Node.js 扫描器 (${folderPath}) 完成，结果: ${result.success ? '成功' : '失败'}`);
      if (result.error) {
        mainLog.warn(`[扫描处理器] Node.js 扫描器 (${folderPath}) 错误: ${result.error}`);
      }

      if (result.success && result.movies) {
        const moviesData = result.movies;
        mainLog.info(`[扫描处理器] 文件夹 "${folderPath}" 的 Node.js 扫描器成功完成。发现 ${moviesData.length} 个媒体文件。`);
        totalFilesFound += moviesData.length;
        
        if (moviesData.length > 0) {
          webContentsSender.send('scan-status-update', `正在处理 ${path.basename(folderPath)} 中的 ${moviesData.length} 个媒体文件数据入库...`);
          
          const transaction = mainDbAccess.transaction((movies) => {
            const insertStmt = mainDbAccess.prepare(`
              INSERT INTO movies (
                filePath, fileName, title, originalTitle, nfoId, year, releaseDate, runtime, plot, plotJa, plotZh, studio, series, director,
                trailerUrl, posterUrl, coverUrl, localCoverPath, watched, personalRating, 
                actors, genres, tags, lastScanned, nfoLastModified, resolution, fileSize, videoCodec, audioCodec, 
                preferredStatus, customFileTags, versionCategories, autoDetectedFileNameTags,
                fps, videoCodecFull, videoBitrate, audioCodecFull, audioChannelsDesc, audioSampleRate, audioBitrate, videoHeight,
                aiAnalyzedTags, aiRecommendationType, aiRecommendationScore, aiRecommendationJustification,
                hasExternalSubtitles, cdPartInfo
              ) VALUES (
                @filePath, @fileName, @title, @originalTitle, @nfoId, @year, @releaseDate, @runtime, @plot, @plotJa, @plotZh, @studio, @series, @director,
                @trailerUrl, @posterUrl, @coverUrl, @localCoverPath, @watched, @personalRating,
                @actors, @genres, @tags, datetime('now', 'localtime'), @nfoLastModified, @resolution, @fileSize, @videoCodec, @audioCodec,
                @preferredStatus, @customFileTags, @versionCategories, @autoDetectedFileNameTags,
                @fps, @videoCodecFull, @videoBitrate, @audioCodecFull, @audioChannelsDesc, @audioSampleRate, @audioBitrate, @videoHeight,
                @aiAnalyzedTags, @aiRecommendationType, @aiRecommendationScore, @aiRecommendationJustification,
                @hasExternalSubtitles, @cdPartInfo
              ) ON CONFLICT(filePath) DO UPDATE SET
                fileName = excluded.fileName, title = excluded.title, originalTitle = excluded.originalTitle, nfoId = excluded.nfoId, year = excluded.year, releaseDate = excluded.releaseDate, runtime = excluded.runtime, 
                plot = excluded.plot, plotJa = excluded.plotJa, plotZh = excluded.plotZh, 
                studio = excluded.studio, series = excluded.series, director = excluded.director,
                trailerUrl = excluded.trailerUrl, posterUrl = excluded.posterUrl, coverUrl = excluded.coverUrl, 
                localCoverPath = CASE WHEN excluded.localCoverPath IS NOT NULL AND excluded.localCoverPath != '' THEN excluded.localCoverPath ELSE movies.localCoverPath END,
                watched = excluded.watched, personalRating = excluded.personalRating, 
                actors = excluded.actors, genres = excluded.genres, tags = excluded.tags, 
                lastScanned = datetime('now', 'localtime'), nfoLastModified = excluded.nfoLastModified,
                resolution = excluded.resolution, fileSize = excluded.fileSize, videoCodec = excluded.videoCodec, audioCodec = excluded.audioCodec,
                preferredStatus = COALESCE(excluded.preferredStatus, movies.preferredStatus), 
                customFileTags = COALESCE(excluded.customFileTags, movies.customFileTags), 
                versionCategories = COALESCE(excluded.versionCategories, movies.versionCategories),
                autoDetectedFileNameTags = excluded.autoDetectedFileNameTags,
                fps = excluded.fps, videoCodecFull = excluded.videoCodecFull, videoBitrate = excluded.videoBitrate,
                audioCodecFull = excluded.audioCodecFull, audioChannelsDesc = excluded.audioChannelsDesc,
                audioSampleRate = excluded.audioSampleRate, audioBitrate = excluded.audioBitrate, videoHeight = excluded.videoHeight,
                aiAnalyzedTags = CASE WHEN excluded.aiAnalyzedTags IS NOT NULL THEN excluded.aiAnalyzedTags ELSE movies.aiAnalyzedTags END,
                aiRecommendationType = CASE WHEN excluded.aiRecommendationType IS NOT NULL THEN excluded.aiRecommendationType ELSE movies.aiRecommendationType END,
                aiRecommendationScore = CASE WHEN excluded.aiRecommendationScore IS NOT NULL THEN excluded.aiRecommendationScore ELSE movies.aiRecommendationScore END,
                aiRecommendationJustification = CASE WHEN excluded.aiRecommendationJustification IS NOT NULL THEN excluded.aiRecommendationJustification ELSE movies.aiRecommendationJustification END,
                hasExternalSubtitles = excluded.hasExternalSubtitles, cdPartInfo = excluded.cdPartInfo
              WHERE movies.nfoLastModified IS NULL OR excluded.nfoLastModified IS NULL OR movies.nfoLastModified < excluded.nfoLastModified OR (movies.plot IS NULL OR movies.plot = '') AND (excluded.plot IS NOT NULL AND excluded.plot != '')
              RETURNING db_id; 
            `);

            const linkStmt = mainDbAccess.prepare(`
                INSERT OR IGNORE INTO movie_library_links (movie_db_id, library_id)
                VALUES (?, ?)
            `);

            let currentFolderNewCount = 0;
            let currentFolderUpdatedCount = 0;

            for (let i = 0; i < movies.length; i++) {
              const movie = movies[i];
              const params = {
                filePath: movie.filePath || null,
                fileName: movie.fileName || null,
                title: movie.title || null,
                originalTitle: movie.originalTitle || null,
                nfoId: movie.nfoId || null,
                year: movie.year || null,
                releaseDate: movie.releaseDate || null,
                runtime: movie.runtime || null,
                plot: movie.plot || null,
                plotJa: movie.plotJa || null,
                plotZh: movie.plotZh || null,
                studio: movie.studio || null,
                series: movie.series || null,
                director: movie.director || null,
                trailerUrl: movie.trailerUrl || null,
                posterUrl: movie.posterUrl || null,
                coverUrl: movie.coverUrl || null,
                localCoverPath: movie.localCoverPath || null, // Ensure this has a default
                watched: movie.watched ? 1 : 0,
                personalRating: movie.personalRating || null,
                actors: JSON.stringify(movie.actors || []),
                genres: JSON.stringify(movie.genres || []),
                tags: JSON.stringify(movie.tags || []),
                nfoLastModified: movie.nfoLastModified || null,
                resolution: movie.resolution || null,
                fileSize: movie.fileSize || null,
                videoCodec: movie.videoCodec || null,
                audioCodec: movie.audioCodec || null,
                preferredStatus: movie.preferredStatus || null,
                customFileTags: JSON.stringify(movie.customFileTags || []),
                versionCategories: JSON.stringify(movie.versionCategories || []),
                autoDetectedFileNameTags: JSON.stringify(movie.autoDetectedFileNameTags || []),
                fps: movie.fps || null,
                videoCodecFull: movie.videoCodecFull || null,
                videoBitrate: movie.videoBitrate || null,
                audioCodecFull: movie.audioCodecFull || null,
                audioChannelsDesc: movie.audioChannelsDesc || null,
                audioSampleRate: movie.audioSampleRate || null,
                audioBitrate: movie.audioBitrate || null,
                videoHeight: movie.videoHeight !== undefined ? String(movie.videoHeight) : null,
                aiAnalyzedTags: JSON.stringify(movie.aiAnalyzedTags || []),
                aiRecommendationType: movie.aiRecommendation?.type || movie.aiRecommendationType || null,
                aiRecommendationScore: movie.aiRecommendation?.score || movie.aiRecommendationScore || null,
                aiRecommendationJustification: movie.aiRecommendation?.justification || movie.aiRecommendationJustification || null,
                hasExternalSubtitles: movie.hasExternalSubtitles ? 1: 0,
                cdPartInfo: movie.cdPartInfo || null,
              };
              
              const existing = mainDbAccess.prepare("SELECT db_id FROM movies WHERE filePath = ?").get(movie.filePath);
              const info = insertStmt.get(params); 

              if (info && info.db_id) { 
                if (!existing) currentFolderNewCount++; else currentFolderUpdatedCount++;
                
                if (libraryId) {
                    linkStmt.run(info.db_id, libraryId);
                }
                 webContentsSender.send('scan-progress-update', {
                    overallPercentage: overallProgressForFolderStart + ((i + 1) / movies.length) * (overallProgressForFolderEnd - overallProgressForFolderStart),
                    currentPathMessage: `处理中: ${path.basename(folderPath)} (${i + 1}/${movies.length})`,
                    filesInPathProcessed: i + 1,
                    totalFilesInPath: movies.length,
                    currentFileMessage: `${existing ? '更新' : '新增'}: ${movie.fileName}`,
                    error: null
                });
              } else if (info && info.changes === 0) { 
                 if (libraryId && existing && existing.db_id) {
                    linkStmt.run(existing.db_id, libraryId);
                 }
              } else if (!info && existing) { 
                 if (libraryId && existing.db_id) {
                    linkStmt.run(existing.db_id, libraryId);
                 }
              }
              totalFilesProcessedInPython++; 
            }
            return { newCount: currentFolderNewCount, updatedCount: currentFolderUpdatedCount };
          });
          
          const { newCount, updatedCount } = transaction(moviesData);
          cumulativeNewCount += newCount;
          cumulativeUpdatedCount += updatedCount;
          mainLog.info(`[扫描处理器] 文件夹 "${folderPath}" (${libraryLogName}) 数据库操作完成。新增: ${newCount}, 更新: ${updatedCount}`);
        }
      } else { 
        const pythonError = result.error || 'Python 脚本执行失败，未提供具体错误。';
        mainLog.error(`[扫描处理器] 文件夹 "${folderPath}" (${libraryLogName}) 的 Python 脚本执行失败: ${pythonError}`);
        webContentsSender.send('scan-error', `扫描文件夹 ${path.basename(folderPath)} 失败: ${pythonError}`);
         webContentsSender.send('scan-progress-update', { 
            overallPercentage: overallProgressForFolderEnd, 
            currentPathMessage: `错误扫描: ${path.basename(folderPath)}`,
            filesInPathProcessed: null, 
            totalFilesInPath: null, 
            currentFileMessage: null, 
            error: `Python脚本错误: ${pythonError}` 
        });
      }
    } catch (scriptError) { 
        mainLog.error(`[扫描处理器] 执行 Python 脚本时发生严重错误 for ${folderPath} (${libraryLogName}):`, scriptError);
        webContentsSender.send('scan-error', `扫描文件夹 ${path.basename(folderPath)} 时发生严重错误: ${scriptError.message}`);
        webContentsSender.send('scan-progress-update', { 
            overallPercentage: overallProgressForFolderEnd, 
            currentPathMessage: `错误扫描: ${path.basename(folderPath)}`,
            filesInPathProcessed: null, 
            totalFilesInPath: null, 
            currentFileMessage: null, 
            error: `脚本执行错误: ${scriptError.message}`
        });
    }
  }

  mainLog.info(`[扫描处理器] 所有文件夹扫描完成 (${libraryLogName})。总共发现 (Python报告): ${totalFilesFound}。总共处理 (DB尝试): ${totalFilesProcessedInPython}。累计新增: ${cumulativeNewCount}, 累计更新: ${cumulativeUpdatedCount}`);
  webContentsSender.send('scan-complete', { 
    newCount: cumulativeNewCount, 
    updatedCount: cumulativeUpdatedCount, 
    totalScanned: totalFilesFound, 
    libraryId: libraryId // Include libraryId in the completion data
  });
  webContentsSender.send('scan-status-update', `所有扫描任务完成。共识别 ${totalFilesFound} 个媒体文件。新增 ${cumulativeNewCount}, 更新 ${cumulativeUpdatedCount}。`);
  webContentsSender.send('scan-progress-update', { overallPercentage: 100, currentPathMessage: "扫描全部完成!", filesInPathProcessed: null, totalFilesInPath: null, currentFileMessage: null, error: null });
}

module.exports = {
  initializeScanHandler,
  handleSelectFolderAndScan,
};
