// soul-forge-electron/src/components/FavoritesView.tsx
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { FavoriteItem, FavoriteItemType, ToggleFavoriteResult, ActorAvatarInfoResult } from '../types';
import { LuTag, LuUsers, LuBuilding, LuFilm, LuClapperboard, LuTrash2, LuHeartCrack, LuShapes } from 'react-icons/lu'; 
import ImageWithFallback from './ImageWithFallback'; // Import ImageWithFallback

interface FavoritesViewProps {
  onTagClick: (itemType: FavoriteItemType, itemValue: string) => void;
}

const favoriteTypeLabels: Record<FavoriteItemType, string> = {
  actress: '收藏的演员',
  studio: '收藏的制作商',
  series: '收藏的影片系列', // Updated label
  director: '收藏的导演',
  movie_nfo_id: '收藏的影片 (NFO ID)',
  tag: '收藏的标签',
  genre: '收藏的类型',
};

const favoriteTypeIcons: Record<FavoriteItemType, React.ElementType> = {
  actress: LuU<PERSON><PERSON>,
  studio: LuBuilding,
  series: LuFilm, // Using LuFilm for series as well
  director: LuClapperboard,
  movie_nfo_id: LuFilm,
  tag: LuTag,
  genre: LuShapes,
};

const FavoriteItemCard: React.FC<{
  item: FavoriteItem;
  onTagClick: (itemType: FavoriteItemType, itemValue: string) => void;
  onRemoveFavorite: (itemType: FavoriteItemType, itemValue: string) => void;
  IconComponent: React.ElementType;
  appDefaultActorAvatar?: string | null;
}> = ({ item, onTagClick, onRemoveFavorite, IconComponent, appDefaultActorAvatar }) => {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [isLoadingAvatar, setIsLoadingAvatar] = useState(false);

  useEffect(() => {
    if (item.item_type === 'actress') {
      setIsLoadingAvatar(true);
      window.sfeElectronAPI.getActorAvatarDetails(item.item_value)
        .then((result: ActorAvatarInfoResult) => {
          if (result.success) {
            setAvatarUrl(result.dataUrl || result.avatarUrl || null);
          }
        })
        .catch(err => console.error(`Error fetching avatar for ${item.item_value}:`, err))
        .finally(() => setIsLoadingAvatar(false));
    }
  }, [item.item_type, item.item_value]);

  const avatarPlaceholder = (
    <div className="w-full h-full flex items-center justify-center bg-neutral-700">
      <LuUsers className="w-4 h-4 text-neutral-400" />
    </div>
  );

  return (
    <div 
      className="bg-[#2c2c2c] p-2.5 rounded-md border border-[#444444] flex items-center group hover:bg-[#383838] transition-colors"
    >
      {item.item_type === 'actress' && (
        <div className="w-10 h-10 rounded-full overflow-hidden mr-2.5 flex-shrink-0 border-2 border-pink-500/50">
          {isLoadingAvatar ? (
            avatarPlaceholder
          ) : (
            <ImageWithFallback
              primarySrc={avatarUrl}
              appDefaultCoverDataUrl={appDefaultActorAvatar}
              alt={`${item.item_value} avatar`}
              className="w-full h-full object-cover"
              placeholder={avatarPlaceholder}
            />
          )}
        </div>
      )}
      {item.item_type === 'studio' && (
         <LuBuilding size={20} className="mr-2.5 text-sky-400 flex-shrink-0" />
      )}
      {item.item_type !== 'actress' && item.item_type !== 'studio' && (
         <IconComponent size={18} className="mr-2.5 text-neutral-400 flex-shrink-0" />
      )}
      <span 
        onClick={() => onTagClick(item.item_type, item.item_value)}
        className="text-sm text-neutral-100 truncate cursor-pointer hover:text-amber-300 flex-grow"
        title={`筛选: ${item.item_value}`}
      >
        {item.item_value}
      </span>
      <button
        onClick={() => onRemoveFavorite(item.item_type, item.item_value)}
        className="text-red-500 hover:text-red-300 opacity-50 group-hover:opacity-100 transition-opacity ml-2 p-1 rounded-full hover:bg-red-500/20"
        title="取消收藏"
      >
        <LuTrash2 size={16} />
      </button>
    </div>
  );
};


const FavoritesView: React.FC<FavoritesViewProps> = ({ onTagClick }) => {
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [appDefaultActorAvatar, setAppDefaultActorAvatar] = useState<string | null>(null);


  const fetchFavoritesAndSettings = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [favResult, settingsResult] = await Promise.all([
        window.sfeElectronAPI.getFavorites(),
        window.sfeElectronAPI.getSettings()
      ]);
      
      if (favResult.success) {
        setFavorites(favResult.favorites);
      } else {
        setError(favResult.error || '获取收藏列表失败。');
        setFavorites([]);
      }
      setAppDefaultActorAvatar(settingsResult.defaultActorAvatarDataUrl || null);

    } catch (e: any) {
      setError(`获取收藏列表或设置时发生前端错误: ${e.message}`);
      setFavorites([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFavoritesAndSettings();
  }, [fetchFavoritesAndSettings]);

  const handleRemoveFavorite = async (itemType: FavoriteItemType, itemValue: string) => {
    if (!window.confirm(`确定要取消收藏 "${itemValue}" 吗？`)) return;
    try {
      const result: ToggleFavoriteResult = await window.sfeElectronAPI.toggleFavorite(itemType, itemValue);
      if (result.success && !result.isFavorite) {
        setFavorites(prev => prev.filter(fav => !(fav.item_type === itemType && fav.item_value === itemValue)));
      } else {
        alert(`取消收藏操作失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) {
      alert(`取消收藏时发生前端错误: ${e.message}`);
    }
  };

  const groupedFavorites = useMemo(() => {
    return favorites.reduce((acc, fav) => {
      if (!acc[fav.item_type]) {
        acc[fav.item_type] = [];
      }
      acc[fav.item_type].push(fav);
      return acc;
    }, {} as Record<FavoriteItemType, FavoriteItem[]>);
  }, [favorites]);

  if (isLoading) {
    return <div className="text-center text-neutral-400 mt-10 text-lg">正在加载收藏夹...</div>;
  }

  if (error) {
    return <div className="text-center text-red-400 mt-10 text-lg bg-red-800/30 p-4 rounded-md">{error}</div>;
  }

  if (favorites.length === 0) {
    return (
        <div className="text-center text-neutral-400 mt-10 text-lg flex flex-col items-center">
            <LuHeartCrack size={64} className="mb-4 text-neutral-500" />
            <span>您的收藏夹还是空的哦。</span>
            <p className="text-sm mt-2">在筛选结果页面，可以收藏您喜欢的标签、演员等。</p>
        </div>
    );
  }
  
  const displayOrder: FavoriteItemType[] = ['actress', 'studio', 'series', 'director', 'genre', 'tag', 'movie_nfo_id'];


  return (
    <div className="p-4 space-y-6">
      <h1 className="text-3xl font-bold text-amber-400 mb-6 text-center">我的收藏夹</h1>
      {displayOrder.map(type => {
        const items = groupedFavorites[type];
        if (!items || items.length === 0) return null;

        const IconComponent = favoriteTypeIcons[type] || LuTag;
        return (
          <div key={type} className="bg-[#232323] p-4 rounded-lg shadow-lg border border-[#3a3a3a]">
            <h2 className="text-xl font-semibold text-sky-300 mb-3 flex items-center">
              <IconComponent size={22} className="mr-2 opacity-80" />
              {favoriteTypeLabels[type] || '其他收藏'} ({items.length})
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {items.map(item => (
                <FavoriteItemCard
                  key={`${item.item_type}-${item.item_value}`}
                  item={item}
                  onTagClick={onTagClick}
                  onRemoveFavorite={handleRemoveFavorite}
                  IconComponent={IconComponent}
                  appDefaultActorAvatar={appDefaultActorAvatar}
                />
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default FavoritesView;