import { prisma } from '@/lib/db';
import { Movie, FilterOptions, Sort<PERSON>ield, SortOrder, PaginationInfo } from '@/lib/types';
import { Prisma } from '@prisma/client';

export class MovieService {
  static async getMovies(
    filters: FilterOptions = {},
    sortField: SortField = 'lastScanned',
    sortOrder: SortOrder = 'desc',
    page: number = 1,
    limit: number = 20
  ) {
    const where: Prisma.MovieWhereInput = {};

    // Apply filters
    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { originalTitle: { contains: filters.search, mode: 'insensitive' } },
        { director: { contains: filters.search, mode: 'insensitive' } },
        { studio: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    if (filters.genres && filters.genres.length > 0) {
      where.genres = {
        contains: filters.genres[0], // Simplified for now
      };
    }

    if (filters.actors && filters.actors.length > 0) {
      where.actors = {
        contains: filters.actors[0], // Simplified for now
      };
    }

    if (filters.studios && filters.studios.length > 0) {
      where.studio = {
        in: filters.studios,
      };
    }

    if (filters.years && filters.years.length > 0) {
      where.year = {
        in: filters.years,
      };
    }

    if (filters.watched !== undefined) {
      where.watched = filters.watched;
    }

    if (filters.libraryId) {
      where.libraryLinks = {
        some: {
          libraryId: filters.libraryId,
        },
      };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await prisma.movie.count({ where });

    // Get movies with version counts
    const movies = await prisma.movie.findMany({
      where,
      orderBy: {
        [sortField]: sortOrder,
      },
      skip,
      take: limit,
      include: {
        libraryLinks: {
          include: {
            library: true,
          },
        },
        snapshots: true,
      },
    });

    // First get ALL movies to properly group them
    const allMovies = await prisma.movie.findMany({
      where,
      include: {
        libraryLinks: {
          include: {
            library: true,
          },
        },
      },
    });

    console.log(`Found ${allMovies.length} total movies`);

    // Group movies by nfoId and apply merging logic
    const movieGroups = new Map<string, any[]>();

    for (const movie of allMovies) {
      const groupKey = movie.nfoId && movie.nfoId.trim() !== ''
        ? movie.nfoId.toLowerCase().trim()
        : movie.filePath;

      if (!movieGroups.has(groupKey)) {
        movieGroups.set(groupKey, []);
      }
      movieGroups.get(groupKey)!.push(movie);
    }

    console.log(`Grouped into ${movieGroups.size} groups`);

    // Log groups with multiple movies
    let mergedCount = 0;
    for (const [key, movies] of movieGroups) {
      if (movies.length > 1) {
        console.log(`Group "${key}" has ${movies.length} movies:`, movies.map(m => m.fileName));
        mergedCount++;
      }
    }
    console.log(`Found ${mergedCount} groups with multiple versions`);

    // Select the preferred movie from each group and calculate version counts
    const moviesWithVersions = [];

    for (const [groupKey, groupMovies] of movieGroups) {
      // Sort movies in group by preference
      groupMovies.sort((a, b) => {
        // Preferred status first
        if (a.preferredStatus === 'preferred' && b.preferredStatus !== 'preferred') return -1;
        if (b.preferredStatus === 'preferred' && a.preferredStatus !== 'preferred') return 1;

        // Then by year (newer first)
        if (a.year !== b.year) return (b.year || 0) - (a.year || 0);

        // Then by file size (larger first)
        if (a.fileSize !== b.fileSize) return (b.fileSize || 0) - (a.fileSize || 0);

        // Finally by ID (newer first)
        return b.id - a.id;
      });

      const representativeMovie = groupMovies[0];

      // Calculate version counts
      let versionCount = 1;
      let multiCdCountForNfoId = 0;

      if (representativeMovie.nfoId && representativeMovie.nfoId.trim() !== '') {
        // Count all movies with same nfoId
        versionCount = await prisma.movie.count({
          where: {
            nfoId: representativeMovie.nfoId,
          },
        });

        // Count CD parts
        const cdParts = await prisma.movie.findMany({
          where: {
            nfoId: representativeMovie.nfoId,
            cdPartInfo: {
              not: null,
            },
          },
          select: {
            cdPartInfo: true,
          },
          distinct: ['cdPartInfo'],
        });

        multiCdCountForNfoId = cdParts.length;
      }

      moviesWithVersions.push({
        ...representativeMovie,
        versionCount: versionCount > 1 ? versionCount : undefined,
        multiCdCountForNfoId: multiCdCountForNfoId > 1 ? multiCdCountForNfoId : undefined,
      });
    }

    // Sort the merged results
    moviesWithVersions.sort((a, b) => {
      const aValue = a[sortField as keyof typeof a];
      const bValue = b[sortField as keyof typeof b];

      if (aValue === bValue) return 0;

      const comparison = aValue > bValue ? 1 : -1;
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    // Apply pagination after merging
    const paginatedMovies = moviesWithVersions.slice(skip, skip + limit);

    console.log(`After merging and pagination: ${paginatedMovies.length} movies`);

    const pagination: PaginationInfo = {
      page,
      limit,
      total: moviesWithVersions.length, // Use merged count for total
      totalPages: Math.ceil(moviesWithVersions.length / limit),
    };

    return {
      movies: paginatedMovies.map(this.transformMovie),
      pagination,
    };
  }

  static async getMovieById(id: string) {
    const movie = await prisma.movie.findUnique({
      where: { id },
      include: {
        libraryLinks: {
          include: {
            library: true,
          },
        },
        snapshots: true,
      },
    });

    return movie ? this.transformMovie(movie) : null;
  }

  static async createMovie(data: Partial<Movie>) {
    const movie = await prisma.movie.create({
      data: {
        filePath: data.filePath!,
        fileName: data.fileName!,
        title: data.title,
        originalTitle: data.originalTitle,
        nfoId: data.nfoId,
        year: data.year,
        releaseDate: data.releaseDate,
        runtime: data.runtime,
        plot: data.plot,
        plotJa: data.plotJa,
        plotZh: data.plotZh,
        studio: data.studio,
        series: data.series,
        director: data.director,
        trailerUrl: data.trailerUrl,
        posterUrl: data.posterUrl,
        coverUrl: data.coverUrl,
        localCoverPath: data.localCoverPath,
        watched: data.watched || false,
        personalRating: data.personalRating,
        actors: data.actors ? JSON.stringify(data.actors) : null,
        genres: data.genres ? JSON.stringify(data.genres) : null,
        tags: data.tags ? JSON.stringify(data.tags) : null,
        resolution: data.resolution,
        fileSize: data.fileSize,
        videoCodec: data.videoCodec,
        audioCodec: data.audioCodec,
        preferredStatus: data.preferredStatus,
        customFileTags: data.customFileTags ? JSON.stringify(data.customFileTags) : null,
        versionCategories: data.versionCategories ? JSON.stringify(data.versionCategories) : null,
        autoDetectedFileNameTags: data.autoDetectedFileNameTags ? JSON.stringify(data.autoDetectedFileNameTags) : null,
        fps: data.fps,
        videoCodecFull: data.videoCodecFull,
        videoBitrate: data.videoBitrate,
        audioCodecFull: data.audioCodecFull,
        audioChannelsDesc: data.audioChannelsDesc,
        audioSampleRate: data.audioSampleRate,
        audioBitrate: data.audioBitrate,
        videoHeight: data.videoHeight,
        aiAnalyzedTags: data.aiAnalyzedTags ? JSON.stringify(data.aiAnalyzedTags) : null,
        aiRecommendationType: data.aiRecommendationType,
        aiRecommendationScore: data.aiRecommendationScore,
        aiRecommendationJustification: data.aiRecommendationJustification,
        hasExternalSubtitles: data.hasExternalSubtitles || false,
        cdPartInfo: data.cdPartInfo,
      },
    });

    return this.transformMovie(movie);
  }

  static async updateMovie(id: string, data: Partial<Movie>) {
    const movie = await prisma.movie.update({
      where: { id },
      data: {
        title: data.title,
        originalTitle: data.originalTitle,
        year: data.year,
        plot: data.plot,
        watched: data.watched,
        personalRating: data.personalRating,
        // Add other fields as needed
      },
    });

    return this.transformMovie(movie);
  }

  static async deleteMovie(id: string) {
    await prisma.movie.delete({
      where: { id },
    });
  }

  static async toggleWatched(id: string) {
    const movie = await prisma.movie.findUnique({
      where: { id },
    });

    if (!movie) {
      throw new Error('Movie not found');
    }

    const updated = await prisma.movie.update({
      where: { id },
      data: {
        watched: !movie.watched,
      },
    });

    return this.transformMovie(updated);
  }

  private static transformMovie(movie: any): Movie {
    return {
      id: movie.id,
      filePath: movie.filePath,
      fileName: movie.fileName,
      title: movie.title,
      originalTitle: movie.originalTitle,
      nfoId: movie.nfoId,
      year: movie.year,
      releaseDate: movie.releaseDate,
      runtime: movie.runtime,
      plot: movie.plot,
      plotJa: movie.plotJa,
      plotZh: movie.plotZh,
      studio: movie.studio,
      series: movie.series,
      director: movie.director,
      trailerUrl: movie.trailerUrl,
      posterUrl: movie.posterUrl,
      coverUrl: movie.coverUrl,
      localCoverPath: movie.localCoverPath,
      watched: movie.watched,
      personalRating: movie.personalRating,
      actors: movie.actors ? JSON.parse(movie.actors) : [],
      genres: movie.genres ? JSON.parse(movie.genres) : [],
      tags: movie.tags ? JSON.parse(movie.tags) : [],
      lastScanned: movie.lastScanned,
      nfoLastModified: movie.nfoLastModified,
      resolution: movie.resolution,
      fileSize: movie.fileSize,
      videoCodec: movie.videoCodec,
      audioCodec: movie.audioCodec,
      preferredStatus: movie.preferredStatus,
      customFileTags: movie.customFileTags ? JSON.parse(movie.customFileTags) : [],
      versionCategories: movie.versionCategories ? JSON.parse(movie.versionCategories) : [],
      autoDetectedFileNameTags: movie.autoDetectedFileNameTags ? JSON.parse(movie.autoDetectedFileNameTags) : [],
      fps: movie.fps,
      videoCodecFull: movie.videoCodecFull,
      videoBitrate: movie.videoBitrate,
      audioCodecFull: movie.audioCodecFull,
      audioChannelsDesc: movie.audioChannelsDesc,
      audioSampleRate: movie.audioSampleRate,
      audioBitrate: movie.audioBitrate,
      videoHeight: movie.videoHeight,
      aiAnalyzedTags: movie.aiAnalyzedTags ? JSON.parse(movie.aiAnalyzedTags) : [],
      aiRecommendationType: movie.aiRecommendationType,
      aiRecommendationScore: movie.aiRecommendationScore,
      aiRecommendationJustification: movie.aiRecommendationJustification,
      hasExternalSubtitles: movie.hasExternalSubtitles,
      cdPartInfo: movie.cdPartInfo,
      createdAt: movie.createdAt,
      updatedAt: movie.updatedAt,
      versionCount: movie.versionCount || 1,
      multiCdCountForNfoId: movie.multiCdCountForNfoId || 0,
    };
  }
}
