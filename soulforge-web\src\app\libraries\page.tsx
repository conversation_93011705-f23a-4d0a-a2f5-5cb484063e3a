'use client';

import React from 'react';
import { useLibraryStore } from '@/lib/stores/library-store';
import { useUIStore } from '@/lib/stores/ui-store';
import { AppLayout } from '@/components/layout/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { MovieLibrary } from '@/lib/types';
import { Plus, RefreshCw, Folder, Scan, Trash2, Edit } from 'lucide-react';

export default function LibrariesPage() {
  const {
    libraries,
    loading,
    error,
    fetchLibraries,
    saveLibrary,
    deleteLibraryAsync,
    scanLibrary,
  } = useLibraryStore();

  const { addToast } = useUIStore();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = React.useState(false);
  const [editingLibrary, setEditingLibrary] = React.useState<MovieLibrary | null>(null);
  const [formData, setFormData] = React.useState({
    name: '',
    paths: [''],
  });

  React.useEffect(() => {
    fetchLibraries();
  }, [fetchLibraries]);

  const handleCreateLibrary = async () => {
    if (!formData.name.trim() || !formData.paths.some(path => path.trim())) {
      addToast({
        type: 'error',
        title: '验证失败',
        description: '请填写库名称和至少一个路径',
      });
      return;
    }

    try {
      const library = await saveLibrary({
        name: formData.name,
        paths: JSON.stringify(formData.paths.filter(path => path.trim())),
      });

      if (library) {
        addToast({
          type: 'success',
          title: '创建成功',
          description: `媒体库 "${library.name}" 已创建`,
        });
        setIsCreateDialogOpen(false);
        setFormData({ name: '', paths: [''] });
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: '创建失败',
        description: '无法创建媒体库',
      });
    }
  };

  const handleEditLibrary = (library: MovieLibrary) => {
    setEditingLibrary(library);
    setFormData({
      name: library.name,
      paths: typeof library.paths === 'string' ? JSON.parse(library.paths) : library.paths,
    });
    setIsCreateDialogOpen(true);
  };

  const handleUpdateLibrary = async () => {
    if (!editingLibrary) return;

    try {
      const updated = await saveLibrary({
        id: editingLibrary.id,
        name: formData.name,
        paths: JSON.stringify(formData.paths.filter(path => path.trim())),
      });

      if (updated) {
        addToast({
          type: 'success',
          title: '更新成功',
          description: `媒体库 "${updated.name}" 已更新`,
        });
        setIsCreateDialogOpen(false);
        setEditingLibrary(null);
        setFormData({ name: '', paths: [''] });
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: '更新失败',
        description: '无法更新媒体库',
      });
    }
  };

  const handleDeleteLibrary = async (library: MovieLibrary) => {
    if (!confirm(`确定要删除媒体库 "${library.name}" 吗？`)) {
      return;
    }

    try {
      await deleteLibraryAsync(library.id);
      addToast({
        type: 'success',
        title: '删除成功',
        description: `媒体库 "${library.name}" 已删除`,
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: '删除失败',
        description: '无法删除媒体库',
      });
    }
  };

  const handleScanLibrary = async (library: MovieLibrary) => {
    try {
      await scanLibrary(library.id);
      addToast({
        type: 'success',
        title: '扫描完成',
        description: `媒体库 "${library.name}" 扫描完成`,
      });
    } catch (error) {
      addToast({
        type: 'error',
        title: '扫描失败',
        description: '无法扫描媒体库',
      });
    }
  };

  const addPath = () => {
    setFormData(prev => ({
      ...prev,
      paths: [...prev.paths, ''],
    }));
  };

  const updatePath = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      paths: prev.paths.map((path, i) => i === index ? value : path),
    }));
  };

  const removePath = (index: number) => {
    setFormData(prev => ({
      ...prev,
      paths: prev.paths.filter((_, i) => i !== index),
    }));
  };

  if (error) {
    return (
      <AppLayout>
        <div className="container mx-auto p-6">
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-lg font-semibold text-destructive mb-2">
                加载错误
              </h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => fetchLibraries()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                重试
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">媒体库管理</h1>
            <p className="text-muted-foreground">
              管理您的电影媒体库和扫描路径
            </p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={() => fetchLibraries()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  添加媒体库
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>
                    {editingLibrary ? '编辑媒体库' : '创建媒体库'}
                  </DialogTitle>
                  <DialogDescription>
                    配置媒体库名称和扫描路径
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">库名称</label>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="输入媒体库名称"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">扫描路径</label>
                    <div className="space-y-2">
                      {formData.paths.map((path, index) => (
                        <div key={index} className="flex space-x-2">
                          <Input
                            value={path}
                            onChange={(e) => updatePath(index, e.target.value)}
                            placeholder="输入文件夹路径"
                          />
                          {formData.paths.length > 1 && (
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => removePath(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                      <Button variant="outline" onClick={addPath} className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        添加路径
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsCreateDialogOpen(false);
                        setEditingLibrary(null);
                        setFormData({ name: '', paths: [''] });
                      }}
                    >
                      取消
                    </Button>
                    <Button onClick={editingLibrary ? handleUpdateLibrary : handleCreateLibrary}>
                      {editingLibrary ? '更新' : '创建'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Libraries Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                    <div className="h-8 bg-muted rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : libraries.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">暂无媒体库</h3>
              <p className="text-muted-foreground mb-4">
                创建您的第一个媒体库来开始管理电影
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                创建媒体库
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {libraries.map((library) => {
              const paths = typeof library.paths === 'string' 
                ? JSON.parse(library.paths) 
                : library.paths;
              
              return (
                <Card key={library.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{library.name}</span>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditLibrary(library)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteLibrary(library)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardTitle>
                    <CardDescription>
                      {paths.length} 个扫描路径
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 mb-4">
                      {paths.slice(0, 2).map((path: string, index: number) => (
                        <div key={index} className="text-sm text-muted-foreground truncate">
                          {path}
                        </div>
                      ))}
                      {paths.length > 2 && (
                        <div className="text-sm text-muted-foreground">
                          +{paths.length - 2} 个路径
                        </div>
                      )}
                    </div>
                    <Button
                      onClick={() => handleScanLibrary(library)}
                      className="w-full"
                      disabled={loading}
                    >
                      <Scan className="h-4 w-4 mr-2" />
                      扫描媒体库
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </AppLayout>
  );
}
