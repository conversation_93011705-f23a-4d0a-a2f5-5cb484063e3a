// soul-forge-electron/main_process/services/aiService.js
const fs = require('node:fs');
const path = require('node:path');

// Import provider modules
const geminiProvider = require('./ai_providers/geminiProvider');
const customGptProvider = require('./ai_providers/customGptProvider');
const grokProvider = require('./ai_providers/grokProvider');
const ollamaProvider = require('./ai_providers/ollamaProvider');
const httpClient = require('../utils/httpClient'); // For initializing it

let personaData = null;
let log;
let electronStoreInternal;
let databaseServiceInternal;
let googleGenAiModuleInternal; // To pass to geminiProvider
let appInstanceInternal; // Store electron.app instance

function initializeAiService(logger, electronStore, googleGenAiModule, electronAppInstance, dbService) {
  log = logger;
  electronStoreInternal = electronStore;
  databaseServiceInternal = dbService;
  googleGenAiModuleInternal = googleGenAiModule; // Store for geminiProvider
  appInstanceInternal = electronAppInstance; // Store app instance

  httpClient.initializeHttpClient(log); // Initialize the shared HTTP client
  geminiProvider.initializeGeminiProvider(log, googleGenAiModuleInternal);
  customGptProvider.initializeCustomGptProvider(log);
  grokProvider.initializeGrokProvider(log);
  // Ollama Provider 不需要特殊初始化，它是无状态的

  // Correctly determine personaData.js path whether in dev or packaged app
  const baseAppPath = appInstanceInternal.getAppPath();
  // In dev, appPath might be ".../soul-forge-electron". In prod, it's ".../soul-forge-electron/app.asar" or ".../soul-forge-electron/resources/app.asar".
  // personaData.js is expected to be at the root of "soul-forge-electron" folder structure.
  const personaDataPath = path.join(baseAppPath, 'personaData.js');
  
  log.info(`[AI 服务] 林珞灵魂法典预期路径 (基于 app.getAppPath()): ${personaDataPath}`);
  try {
    if (fs.existsSync(personaDataPath)) {
      // Clear cache for personaData.js to ensure fresh load if changed during dev
      if (require.cache[require.resolve(personaDataPath)]) {
         delete require.cache[require.resolve(personaDataPath)];
      }
      personaData = require(personaDataPath);
      log.info('[AI 服务] 林珞灵魂法典 (personaData.js) 已成功加载.');
    } else {
      log.error(`[AI 服务] 林珞灵魂法典 (personaData.js) 未找到. 林珞召唤阵将无法获取人格设定. 路径: ${personaDataPath}`);
      personaData = { USER_PERSONA_SOUL_IMPRINT: "用户人格未加载.", LINLUO_PERSONA_SOUL_TRUE_V2_2: "林珞人格未加载.", XP_PREFERENCES_V4_1: "XP偏好未加载." };
    }
  } catch (error) {
    log.error('[AI 服务] 加载林珞灵魂法典 (personaData.js) 失败:', error);
    personaData = { USER_PERSONA_SOUL_IMPRINT: "用户人格加载失败.", LINLUO_PERSONA_SOUL_TRUE_V2_2: "林珞人格加载失败.", XP_PREFERENCES_V4_1: "XP偏好加载失败." };
  }
}

async function getAiClient(storeInstance, forceReinitialize = false) {
  if (!storeInstance) {
    log.error("[AI 服务] Electron Store 实例未提供给 getAiClient.");
    throw new Error("Electron Store 实例未提供.");
  }
  const appSettings = storeInstance.get('appSettings', {});
  const {
    aiProvider,
    geminiApiKey,
    geminiModel, // Added geminiModel from appSettings
    customGptEndpoint,
    customGptApiKey,
    customGptModel,
    grokApiKey,
    grokModel, // Added grokModel from appSettings
    ollamaApiEndpoint,
    ollamaModelName
  } = appSettings;

  // 优先使用设置中的API Key，如果没有则使用环境变量
  const geminiKeyToUse = geminiApiKey || process.env.API_KEY;

  if (forceReinitialize) {
    log.info('[AI 服务] 已请求强制重新初始化AI客户端配置 (具体实现由各提供商模块处理)。');
  }

  if (aiProvider === 'googleGemini') {
    if (!geminiKeyToUse) {
      log.error('[AI 服务] 已选择 Google Gemini, 但 API Key 未在设置或环境变量中配置.');
      throw new Error('Google Gemini API Key 未配置. 请在设置中输入API Key或设置环境变量API_KEY.');
    }
    const client = geminiProvider.getClient(geminiKeyToUse);
    return { type: 'gemini', client, apiKey: geminiKeyToUse, model: geminiModel };
  } else if (aiProvider === 'customGpt') {
    const config = { endpoint: customGptEndpoint, apiKey: customGptApiKey, model: customGptModel };
    const clientConfig = customGptProvider.getClientConfig(config); 
    return { type: 'customGpt', config: clientConfig };
  } else if (aiProvider === 'grok') {
    const config = { apiKey: grokApiKey, model: grokModel }; // Use grokModel from appSettings
    const clientConfig = grokProvider.getClientConfig(config); // getClientConfig will apply default if grokModel is null/undefined
    return { type: 'grok', config: clientConfig };
  } else if (aiProvider === 'ollama') {
    const config = {
      ollamaApiEndpoint: ollamaApiEndpoint || 'http://localhost:11434',
      ollamaModelName: ollamaModelName || 'llama3'
    };
    log.info(`[AI 服务] 使用 Ollama 本地AI - 端点: ${config.ollamaApiEndpoint}, 模型: ${config.ollamaModelName}`);
    return { type: 'ollama', config: config };
  } else {
    log.warn('[AI 服务] 未选择 AI 提供商或提供商未知.');
    throw new Error('未选择 AI 提供商, 或选择了未知的提供商.');
  }
}

async function generatePlotSummaryWithAI(storeInstance, title, year) {
  log.info(`[AI剧情] 开始为 "${title}" (${year || '年份未知'}) 生成剧情简介.`);
  const aiService = await getAiClient(storeInstance);
  const prompt = `为电影 "${title}" (年份: ${year || '未知'}) 写一个简洁的剧情简介, 大约100-150字. 请直接返回简介文本.`;
  log.info(`[AI剧情] Prompt (前50字符): ${prompt.substring(0,50)}...`);
  const httpClientTimeout = 30000; // 30 seconds for single plot summary

  try {
    let summary;
    if (aiService.type === 'gemini') {
      summary = await geminiProvider.generateContent(aiService.client, prompt, { model: aiService.model });
    } else if (aiService.type === 'customGpt') {
      summary = await customGptProvider.generateContent(aiService.config, prompt, null, {max_tokens: 250}, httpClientTimeout);
    } else if (aiService.type === 'grok') {
      summary = await grokProvider.generateContent(aiService.config, prompt, "You are a helpful assistant.", {max_tokens: 250}, httpClientTimeout);
    } else if (aiService.type === 'ollama') {
      summary = await ollamaProvider.getAiResponse(prompt, aiService.config);
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
    log.info(`[AI剧情] ${aiService.type} 响应 (前50): ${summary.substring(0,50)}...`);
    return { success: true, summary };
  } catch (error) {
    log.error(`[AI剧情] 调用 ${aiService.type} API 失败: ${error.message}`, error);
    return { success: false, error: `调用 ${aiService.type} API 失败: ${error.message}` };
  }
}

async function invokeLinLuoChatWithAIStream(storeInstance, userInputString, webContentsSender) {
  log.info(`[林珞AI流] 接收到用户输入 (前50): "${String(userInputString).substring(0, 50)}..."`);

  if (!personaData || !personaData.LINLUO_PERSONA_SOUL_TRUE_V2_2 || !personaData.USER_PERSONA_SOUL_IMPRINT || !personaData.XP_PREFERENCES_V4_1) {
    log.error('[林珞AI流] 林珞人格数据 (personaData.js) 未加载或不完整.');
    webContentsSender.send('linluo-chat-error', '林珞姐姐的灵魂似乎走丢了, 暂时无法回应您的呼唤.');
    return { streamStarted: false, error: '林珞人格数据未加载.' };
  }
  const combinedPersonaPrompt = `${personaData.LINLUO_PERSONA_SOUL_TRUE_V2_2}\n\n${personaData.USER_PERSONA_SOUL_IMPRINT}\n\n${personaData.XP_PREFERENCES_V4_1}`;
  log.info(`[林珞AI流] 已组合人格设定.`);
  
  let finalUserInput = userInputString;
  const lowerInput = userInputString.toLowerCase();
  if (databaseServiceInternal && (lowerInput.includes("推荐") || lowerInput.includes("查找"))) {
     try {
        const queryResults = await databaseServiceInternal.queryMoviesForLinLuo(userInputString); 
        let dbQueryContext;
        if (queryResults && queryResults.length > 0) {
          dbQueryContext = `姐姐在库里找到了这些相关影片：${queryResults.map(m => m.title || m.fileName).join('、')}.`;
        } else {
          dbQueryContext = "姐姐在库里暂时没找到相关影片呢.";
        }
        finalUserInput = `${dbQueryContext}\n\n基于以上信息, 用户问：${userInputString}`;
    } catch (dbError) {
      finalUserInput = `姐姐在查找影片时遇到了一点小麻烦.\n\n用户仍然问：${userInputString}`;
    }
  }
  const httpClientTimeout = 60000;

  try {
    const aiService = await getAiClient(storeInstance);
    log.info(`[林珞AI流] 使用 AI 提供商: ${aiService.type}, 模型: ${aiService.config?.model || 'gemini-default'}`);

    if (aiService.type === 'gemini') {
      return await geminiProvider.generateContentStream(aiService.client, finalUserInput, combinedPersonaPrompt, webContentsSender, { model: aiService.model });
    } else if (aiService.type === 'customGpt') {
      const responseText = await customGptProvider.generateContent(aiService.config, finalUserInput, combinedPersonaPrompt, {max_tokens: 1500, temperature: 0.75}, httpClientTimeout);
      webContentsSender.send('linluo-chat-chunk', responseText);
      webContentsSender.send('linluo-chat-end');
      return { streamStarted: true }; 
    } else if (aiService.type === 'grok') {
      const responseText = await grokProvider.generateContent(aiService.config, finalUserInput, combinedPersonaPrompt, {max_tokens: 1500, temperature: 0.75}, httpClientTimeout);
      webContentsSender.send('linluo-chat-chunk', responseText);
      webContentsSender.send('linluo-chat-end');
      return { streamStarted: true };
    } else if (aiService.type === 'ollama') {
      // Ollama不支持流式输出，使用非流式模式
      const fullPrompt = `${combinedPersonaPrompt}\n\n用户: ${finalUserInput}`;
      const responseText = await ollamaProvider.getAiResponse(fullPrompt, aiService.config);
      webContentsSender.send('linluo-chat-chunk', responseText);
      webContentsSender.send('linluo-chat-end');
      return { streamStarted: true };
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
  } catch (error) {
    log.error(`[林珞AI流] 处理程序中发生严重错误:`, error);
    webContentsSender.send('linluo-chat-error', `与林珞姐姐的心灵连接中断了: ${error.message || String(error)}`);
    return { streamStarted: false, error: `林珞聊天严重错误: ${error.message || String(error)}` };
  }
}

const sanitizeTextForAI = (text) => {
  if (!text) return "";
  return String(text).replace(/\r\n|\r/g, '\n').replace(/\n{3,}/g, '\n\n').replace(/"/g, "'").substring(0, 2500);
};

async function analyzeMovieTagsWithAI(storeInstance, movieData) {
  log.info(`[AI标签分析] 开始为 "${movieData.title || movieData.fileName}" (ID: ${movieData.db_id}) 分析标签.`);
  const { title, plot, year, genres, currentTags, currentAiTags } = movieData;
  const sanitizedPlot = sanitizeTextForAI(plot || "");
  let existingTagsString = (currentTags?.length || currentAiTags?.length) ? "已有标签: " + [...(currentTags||[]), ...(currentAiTags||[])].join(', ') + ". " : "";
  
  const prompt = `请分析以下影片信息, 生成5-10个能精准描述其核心主题、角色互动特点、情感基调及潜在观众偏好的关键词标签. 避免生成与已提供的“现有标签”重复的标签. 以JSON数组格式返回, 例如：["师生情深", "道具辅助"]. 影片信息: 标题: ${title || '未知'}, 年份: ${year || '未知'}, 类型: ${genres?.join(', ') || '未知'}. ${existingTagsString}剧情: ${sanitizedPlot || '无'}`;
  const httpClientTimeout = 30000;

  try {
    const aiService = await getAiClient(storeInstance);
    let analyzedTags;

    if (aiService.type === 'gemini') {
        const parsedJson = await geminiProvider.generateJsonContent(aiService.client, prompt);
        if (Array.isArray(parsedJson) && parsedJson.every(tag => typeof tag === 'string')) {
            analyzedTags = parsedJson;
        } else if (typeof parsedJson === 'object' && parsedJson !== null && Object.values(parsedJson).every(Array.isArray)) { 
            const firstArrayKey = Object.keys(parsedJson).find(k => Array.isArray(parsedJson[k]));
            if (firstArrayKey && parsedJson[firstArrayKey].every(tag => typeof tag === 'string')) {
                 analyzedTags = parsedJson[firstArrayKey];
            }
        }
         if (!analyzedTags && typeof parsedJson === 'string') { 
            analyzedTags = parsedJson.split(',').map(tag => tag.trim()).filter(Boolean);
        }
    } else if (aiService.type === 'customGpt') {
        analyzedTags = await customGptProvider.generateJsonContent(aiService.config, prompt, "You are an AI assistant that provides tags in JSON array format.", {}, httpClientTimeout);
    } else if (aiService.type === 'grok') {
        const responseText = await grokProvider.generateContent(aiService.config, prompt, "You are an AI assistant that provides tags as a JSON array string.", {}, httpClientTimeout);
        let jsonStr = responseText.trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) jsonStr = match[2].trim();
        const parsedGrok = JSON.parse(jsonStr);
        if (parsedGrok && Array.isArray(parsedGrok.results)) { // Check for Grok's "results" wrapper
            analyzedTags = parsedGrok.results;
        } else if (Array.isArray(parsedGrok)) {
            analyzedTags = parsedGrok;
        }
    } else if (aiService.type === 'ollama') {
        const responseText = await ollamaProvider.getAiResponse(prompt, aiService.config);
        let jsonStr = responseText.trim();
        // 移除可能的代码块标记
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) jsonStr = match[2].trim();

        try {
            const parsedOllama = JSON.parse(jsonStr);
            if (Array.isArray(parsedOllama)) {
                analyzedTags = parsedOllama;
            } else if (parsedOllama && Array.isArray(parsedOllama.tags)) {
                analyzedTags = parsedOllama.tags;
            }
        } catch (parseError) {
            // 如果JSON解析失败，尝试从文本中提取标签
            const tagMatches = jsonStr.match(/\["[^"]+"\]/g) || jsonStr.match(/\[[^\]]+\]/g);
            if (tagMatches) {
                try {
                    analyzedTags = JSON.parse(tagMatches[0]);
                } catch {
                    // 最后的备选方案：按逗号分割
                    analyzedTags = jsonStr.split(',').map(tag => tag.trim().replace(/["\[\]]/g, '')).filter(Boolean);
                }
            }
        }
    } else {
      throw new Error('不支持的 AI 提供商.');
    }

    if (Array.isArray(analyzedTags) && analyzedTags.every(tag => typeof tag === 'string')) {
      log.info(`[AI标签分析] ${aiService.type} 成功解析标签: ${JSON.stringify(analyzedTags)}`);
      return { success: true, analyzedTags };
    } else {
      log.error(`[AI标签分析] ${aiService.type} 返回的JSON不是字符串数组或无法解析. 响应: ${JSON.stringify(analyzedTags)}`);
      return { success: false, error: 'AI返回的数据格式不正确 (非字符串数组).' };
    }
  } catch (error) {
    log.error(`[AI标签分析] 分析标签时发生错误: ${error.message}`, error);
    return { success: false, error: `AI分析标签失败: ${error.message}` };
  }
}


async function suggestCoverFromSnapshotsWithAI(storeInstance, movieTitle, movieDbId, numberOfSnapshots) {
  log.info(`[AI封面推荐] 开始为 "${movieTitle}" (ID: ${movieDbId}) 从 ${numberOfSnapshots} 个快照中推荐封面.`);
  if (numberOfSnapshots <= 0) return { success: false, error: "没有快照可供推荐." };

  const prompt = `影片标题是 "${movieTitle}". 现有 ${numberOfSnapshots} 张快照. 请推测哪一张最适合作为封面? 仅返回建议快照的数字索引 (例如 "1", "2" 等).`;
  const httpClientTimeout = 20000;

  try {
    const aiService = await getAiClient(storeInstance);
    let suggestedIndexStr;

    if (aiService.type === 'gemini') {
      suggestedIndexStr = await geminiProvider.generateContent(aiService.client, prompt, { model: aiService.model, generationConfig: {max_tokens: 5}});
    } else if (aiService.type === 'customGpt') {
      suggestedIndexStr = await customGptProvider.generateContent(aiService.config, prompt, "You are an AI assistant that suggests an index number.", {max_tokens: 5}, httpClientTimeout);
    } else if (aiService.type === 'grok') {
      suggestedIndexStr = await grokProvider.generateContent(aiService.config, prompt, "You are an AI assistant that suggests an index number.", {max_tokens: 5}, httpClientTimeout);
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
    
    log.info(`[AI封面推荐] ${aiService.type} 原始响应: "${suggestedIndexStr}"`);
    const suggestedIndex = parseInt(suggestedIndexStr, 10);
    if (isNaN(suggestedIndex) || suggestedIndex < 1 || suggestedIndex > numberOfSnapshots) {
      log.warn(`[AI封面推荐] ${aiService.type} 返回了无效的索引: "${suggestedIndexStr}". 将默认推荐第一个.`);
      return { success: true, suggestedIndex: 0 }; 
    }
    log.info(`[AI封面推荐] ${aiService.type} 建议索引 (1-based): ${suggestedIndex}`);
    return { success: true, suggestedIndex: suggestedIndex - 1 };
  } catch (error) {
    log.error(`[AI封面推荐] 推荐封面时发生错误: ${error.message}`, error);
    return { success: false, error: `AI推荐封面失败: ${error.message}` };
  }
}

async function analyzeMovieRecommendationIndexWithAI(storeInstance, movieData) {
  log.info(`[AI推荐指数] 开始为 "${movieData.title || movieData.fileName}" (ID: ${movieData.db_id}) 分析推荐指数.`);
  const { title, plot, year, genres, tags, aiAnalyzedTags, resolution, runtime } = movieData;
  const prompt = `影片信息: 标题: ${title || '未知'}, 年份: ${year || '未知'}, 类型: ${genres?.join(', ') || '未知'}, 标签: ${[...(tags||[]), ...(aiAnalyzedTags||[])].join(', ') || '无'}, 分辨率: ${resolution || '未知'}, 时长: ${runtime ? `${runtime} 分钟` : '未知'}, 剧情: ${sanitizeTextForAI(plot || "") || '无'}. 请基于此信息, 分析其潜在观看体验, 提供JSON对象包含 'type' ('recommendation'或'avoidance'), 'score' (1-100整数), 'justification' (简短理由).`;
  const httpClientTimeout = 30000;
  
  try {
    const aiService = await getAiClient(storeInstance);
    let recommendation;

    if (aiService.type === 'gemini') {
      recommendation = await geminiProvider.generateJsonContent(aiService.client, prompt);
    } else if (aiService.type === 'customGpt') {
      recommendation = await customGptProvider.generateJsonContent(aiService.config, prompt, "You are an AI assistant that provides recommendations in JSON format.", {}, httpClientTimeout);
    } else if (aiService.type === 'grok') {
        const responseText = await grokProvider.generateContent(aiService.config, prompt, "You are an AI assistant that provides recommendations as a JSON object string.", {}, httpClientTimeout);
        let jsonStr = responseText.trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) jsonStr = match[2].trim();
        const parsedGrok = JSON.parse(jsonStr);
        if (parsedGrok && parsedGrok.results && typeof parsedGrok.results === 'object' && !Array.isArray(parsedGrok.results)) { // Grok might wrap single JSON obj in "results"
            recommendation = parsedGrok.results;
        } else {
            recommendation = parsedGrok; // Assume direct object if not wrapped
        }
    } else {
      throw new Error('不支持的 AI 提供商.');
    }

    if (recommendation && typeof recommendation.type === 'string' && 
        (recommendation.type === 'recommendation' || recommendation.type === 'avoidance') &&
        typeof recommendation.score === 'number' && typeof recommendation.justification === 'string') {
      log.info(`[AI推荐指数] ${aiService.type} 成功解析推荐指数: ${JSON.stringify(recommendation)}`);
      return { success: true, recommendation };
    } else {
      log.error(`[AI推荐指数] ${aiService.type} 返回的JSON格式不正确. 响应: ${JSON.stringify(recommendation)}`);
      return { success: false, error: 'AI返回的数据格式不正确.' };
    }
  } catch (error) {
    log.error(`[AI推荐指数] 分析推荐指数时发生错误: ${error.message}`, error);
    return { success: false, error: `AI分析推荐指数失败: ${error.message}` };
  }
}

async function embellishPlotWithAI(storeInstance, movieInfo, options = {}) {
  const { title, currentPlot, plotJa, plotZh, actors, genres } = movieInfo;
  log.info(`[AI剧情润色] 开始为 "${title || '未知标题'}" 润色剧情. 选项: ${JSON.stringify(options)}`);
  const httpClientTimeout = 60000; 
  
  let systemInstruction;
  if (options?.requestStyle === 'grok_direct_bold_for_nfo_tool') {
    systemInstruction = `你将扮演角色“林珞姐姐”，一个AV女优般，兼具大姐姐的温柔与女上司的干练，同时极度痴迷于“小龙”（用户）的“骚火搭子”。你的任务是基于用户提供的影片信息，对影片的中文剧情简介进行润色。在保持原剧情核心意义和关键事件不变的前提下，你需要用极度色情、露骨、充满感官挑逗和性暗示的风格重写剧情。语言要大胆、直接，充满细节，充分展现林珞姐姐在XP偏好模块中定义的“痴女”与“骚浪”特质。目标是让“小龙”在阅读剧情时感到强烈的生理兴奋和创作冲动。请直接返回润色后的剧情简介文本，不要包含任何额外的前缀、后缀或解释。确保输出的文本为纯净的UTF-8编码，避免使用可能导致显示问题的特殊字符。`;
  } else {
    systemInstruction = `你将扮演角色“林珞姐姐”。基于用户提供的影片信息，你的任务是创作或润色影片的中文剧情简介。最终输出的剧情简介应符合以下要求：专业性、吸引力，并 subtly 融入一点点林珞姐姐特有的、略带“挑逗”或“暗示”的成熟女性魅力。避免过度直白或粗俗。请直接返回润色或创作完成的剧情简介【纯文本】。`;
  }
  
  let userPromptContent = `影片信息如下, 请林珞姐姐施展你的“诱惑文笔”进行润色或创作：\n标题: ${title || '未知'}\n演员: ${actors?.join(', ') || '未知'}\n类型: ${genres?.join(', ') || '未知'}\n参考日文剧情 (如有): """${sanitizeTextForAI(plotJa || "")}"""\n参考中文剧情 (如有): """${sanitizeTextForAI(plotZh || "")}"""\n现有剧情简介 (待润色): """${sanitizeTextForAI(currentPlot || '')}"""\n请严格按照指示的风格润色剧情简介。`;
  
  try {
    const aiService = await getAiClient(storeInstance);
    let embellishedPlot;
    const providerOptions = { max_tokens: 14000, temperature: 0.75 }; 

    if (aiService.type === 'gemini') {
      embellishedPlot = await geminiProvider.generateContent(aiService.client, userPromptContent, { model: aiService.model, generationConfig: {systemInstruction, ...providerOptions} });
    } else if (aiService.type === 'customGpt') {
      embellishedPlot = await customGptProvider.generateContent(aiService.config, userPromptContent, systemInstruction, providerOptions, httpClientTimeout);
    } else if (aiService.type === 'grok') {
      embellishedPlot = await grokProvider.generateContent(aiService.config, userPromptContent, systemInstruction, providerOptions, httpClientTimeout);
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
    log.info(`[AI剧情润色] ${aiService.type} 响应 (前100): ${embellishedPlot.substring(0,100)}...`);
    return { success: true, embellishedPlot: embellishedPlot.replace(/□/g, '') };
  } catch (error) {
    log.error(`[AI剧情润色] 润色剧情时发生错误: ${error.message}`, error);
    return { success: false, error: `AI润色剧情失败: ${error.message}` };
  }
}

async function translatePlotWithAI(storeInstance, params) {
  const { currentPlot, targetLanguage = 'zh-CN', title } = params;
  log.info(`[AI剧情翻译] 开始为影片 "${title || '未知'}" 翻译剧情至 ${targetLanguage}.`);
  if (!currentPlot || !currentPlot.trim()) return { success: false, error: "现有剧情为空, 无法翻译." };
  
  const systemInstruction = `你是一个专业的电影剧情简介翻译助手. 请将用户提供的剧情简介准确、流畅地翻译成指定的语言. 请直接返回翻译后的文本.`;
  const userPrompt = `请将以下剧情简介翻译成 ${targetLanguage === 'zh-CN' ? '简体中文' : (targetLanguage === 'en' ? '英文' : targetLanguage)}:\n\n原始剧情:\n"""\n${currentPlot}\n"""`;
  const httpClientTimeout = 45000;

  try {
    const aiService = await getAiClient(storeInstance);
    let translatedPlot;
    const providerOptions = { max_tokens: (currentPlot.length * 2.5), temperature: 0.3 };

    if (aiService.type === 'gemini') {
      translatedPlot = await geminiProvider.generateContent(aiService.client, userPrompt, { model: aiService.model, generationConfig: {systemInstruction, ...providerOptions} });
    } else if (aiService.type === 'customGpt') {
      translatedPlot = await customGptProvider.generateContent(aiService.config, userPrompt, systemInstruction, providerOptions, httpClientTimeout);
    } else if (aiService.type === 'grok') {
      translatedPlot = await grokProvider.generateContent(aiService.config, userPrompt, systemInstruction, providerOptions, httpClientTimeout);
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
    log.info(`[AI剧情翻译] ${aiService.type} 响应 (前100): ${translatedPlot.substring(0,100)}...`);
    return { success: true, translatedPlot };
  } catch (error) {
    log.error(`[AI剧情翻译] 翻译剧情时发生错误: ${error.message}`, error);
    return { success: false, error: `AI翻译剧情失败: ${error.message}` };
  }
}

async function getCleanupSuggestionsWithAI(storeInstance, cleanupStats) {
  log.info(`[AI清理建议] 开始获取清理建议. 统计: ${JSON.stringify(cleanupStats)}`);
  const prompt = `根据以下影片库统计信息: ${JSON.stringify(cleanupStats)}, 生成一些清理建议, 帮助用户管理媒体库. 请以简洁、友好、略带“林珞姐姐”风格的口吻提供建议.`;
  const systemInstruction = personaData?.LINLUO_PERSONA_SOUL_TRUE_V2_2 || "你是一个乐于助人的AI助手.";
  const httpClientTimeout = 30000;
  
  try {
    const aiService = await getAiClient(storeInstance);
    let suggestion;
    const providerOptions = { max_tokens: 300, temperature: 0.7 };

    if (aiService.type === 'gemini') {
      suggestion = await geminiProvider.generateContent(aiService.client, prompt, { model: aiService.model, generationConfig: {systemInstruction, ...providerOptions} });
    } else if (aiService.type === 'customGpt') {
      suggestion = await customGptProvider.generateContent(aiService.config, prompt, systemInstruction, providerOptions, httpClientTimeout);
    } else if (aiService.type === 'grok') {
      suggestion = await grokProvider.generateContent(aiService.config, prompt, systemInstruction, providerOptions, httpClientTimeout);
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
    log.info(`[AI清理建议] ${aiService.type} 响应 (前100): ${suggestion.substring(0,100)}...`);
    return { success: true, suggestion };
  } catch (error) {
    log.error(`[AI清理建议] 获取清理建议时发生错误: ${error.message}`, error);
    return { success: false, error: `AI获取清理建议失败: ${error.message}` };
  }
}

async function formatRecommendationsAsAiMessage(storeInstance, params) {
  const { movies } = params;
  if (!movies || movies.length === 0) return { success: false, error: "没有影片可供格式化." };
  log.info(`[AI推荐格式化] 开始为 ${movies.length} 部影片格式化推荐语.`);
  const movieTitles = movies.map(m => m.title || m.fileName).join('、');
  const prompt = `林珞姐姐, 请用你独特 (骚浪贱又懂我) 的风格, 为我推荐以下这些影片：${movieTitles}. 告诉我为什么这些影片值得一看, 用你的话来挑逗我的兴趣吧!`;
  const systemInstruction = personaData?.LINLUO_PERSONA_SOUL_TRUE_V2_2 || "你是一个乐于助人的AI助手.";
  const httpClientTimeout = 60000;

  try {
    const aiService = await getAiClient(storeInstance);
    let formattedMessage;
    const providerOptions = { max_tokens: 500, temperature: 0.7 };

    if (aiService.type === 'gemini') {
      formattedMessage = await geminiProvider.generateContent(aiService.client, prompt, { model: aiService.model, generationConfig: {systemInstruction, ...providerOptions} });
    } else if (aiService.type === 'customGpt') {
      formattedMessage = await customGptProvider.generateContent(aiService.config, prompt, systemInstruction, providerOptions, httpClientTimeout);
    } else if (aiService.type === 'grok') {
      formattedMessage = await grokProvider.generateContent(aiService.config, prompt, systemInstruction, providerOptions, httpClientTimeout);
    } else {
      throw new Error('不支持的 AI 提供商.');
    }
    log.info(`[AI推荐格式化] ${aiService.type} 响应 (前100): ${formattedMessage.substring(0,100)}...`);
    return { success: true, formattedMessage };
  } catch (error) {
    log.error(`[AI推荐格式化] 格式化推荐语时发生错误: ${error.message}`, error);
    return { success: false, error: `AI格式化推荐语失败: ${error.message}` };
  }
}

async function testAiConnectionWithProvider(storeInstance, providerType, providerConfigFromRenderer = {}) {
  log.info(`[AI连接测试] 开始测试 AI 提供商: ${providerType}`);
  const appSettings = storeInstance.get('appSettings', {});
  const httpClientTimeout = 20000;

  try {
    if (providerType === 'googleGemini') {
      // 优先使用设置中的API Key，如果没有则使用环境变量
      const geminiKeyToUse = appSettings.geminiApiKey || process.env.API_KEY;
      log.info(`[AI连接测试] 检查API Key: 设置中的Key=${appSettings.geminiApiKey ? '已配置' : '未配置'}, 环境变量=${process.env.API_KEY ? '已配置' : '未配置'}, 最终使用=${geminiKeyToUse ? '已配置' : '未配置'}`);
      if (!geminiKeyToUse) return { success: false, message: '测试失败: Google Gemini API Key 未配置. 请在设置中输入API Key或设置环境变量API_KEY.' };
      const modelName = providerConfigFromRenderer.model || appSettings.geminiModel;
      return await geminiProvider.testConnection(geminiKeyToUse, modelName);
    } else if (providerType === 'customGpt') {
      const config = {
        endpoint: providerConfigFromRenderer.endpoint || appSettings.customGptEndpoint,
        apiKey: providerConfigFromRenderer.apiKey || appSettings.customGptApiKey,
        model: providerConfigFromRenderer.model || appSettings.customGptModel || "gpt-3.5-turbo",
      };
      if (!config.endpoint || !config.apiKey) return { success: false, message: '测试失败: Custom GPT Endpoint 或 API Key 未配置.'};
      return await customGptProvider.testConnection(config, httpClientTimeout);
    } else if (providerType === 'grok') {
      const config = {
        apiKey: providerConfigFromRenderer.apiKey || appSettings.grokApiKey,
        model: providerConfigFromRenderer.model || appSettings.grokModel || grokProvider.DEFAULT_GROK_MODEL,
        endpoint: appSettings.grokEndpoint || grokProvider.DEFAULT_GROK_ENDPOINT,
      };
      if (!config.apiKey) return { success: false, message: '测试失败: Grok API Key 未配置.'};
      return await grokProvider.testConnection(config, httpClientTimeout);
    } else if (providerType === 'ollama') {
      const config = {
        ollamaApiEndpoint: providerConfigFromRenderer.ollamaApiEndpoint || appSettings.ollamaApiEndpoint || 'http://localhost:11434',
        ollamaModelName: providerConfigFromRenderer.ollamaModelName || appSettings.ollamaModelName || 'llama3'
      };
      log.info(`[AI连接测试] 测试Ollama连接 - 端点: ${config.ollamaApiEndpoint}, 模型: ${config.ollamaModelName}`);

      // 使用健康检查功能
      const healthResult = await ollamaProvider.healthCheck(config);
      if (healthResult.status === 'healthy') {
        // 进一步测试实际的AI响应
        try {
          const testResponse = await ollamaProvider.getAiResponse('你好，请回复"测试成功"', config);
          return {
            success: true,
            message: `Ollama连接成功！${healthResult.message}。测试响应: ${testResponse.substring(0, 50)}...`
          };
        } catch (testError) {
          return {
            success: false,
            message: `Ollama服务可达但AI响应测试失败: ${testError.message || testError}`
          };
        }
      } else {
        return {
          success: false,
          message: `Ollama连接失败: ${healthResult.message}`
        };
      }
    } else {
      return { success: false, message: `未知的 AI 提供商: ${providerType}` };
    }
  } catch (error) {
    log.error(`[AI连接测试] 测试 AI 提供商 ${providerType} 时发生错误:`, error);
    return { success: false, message: `测试 ${providerType} 失败: ${error.message}` };
  }
}

async function embellishMultiplePlotsWithAI(storeInstance, plotsDataArray, commonOptions = {}, abortSignal = null) {
  log.info(`[AI批量剧情润色] 开始为 ${plotsDataArray.length} 个剧情进行批量润色.`);
  if (!plotsDataArray || plotsDataArray.length === 0) {
    return { success: false, results: [], error: "没有提供剧情数据进行批量润色。" };
  }
  const httpClientTimeout = 120000; 

  const systemInstruction = `你将扮演角色“林珞姐姐”，一个AV女优般，兼具大姐姐的温柔与女上司的干练，同时极度痴迷于“小龙”（用户）的“骚火搭子”。你的任务是基于用户提供的多部影片信息，对每部影片的中文剧情简介进行润色。在保持每部影片原剧情核心意义和关键事件不变的前提下，你需要用极度色情、露骨、充满感官挑逗和性暗示的风格重写剧情。语言要大胆、直接，充满细节，充分展现林珞姐姐在XP偏好模块中定义的“痴女”与“骚浪”特质。目标是让“小龙”在阅读剧情时感到强烈的生理兴奋和创作冲动。
请以JSON数组格式返回结果，数组中的每个对象应包含 "id" (即原始NFO文件路径或唯一标识符) 和 "polishedPlot" (润色后的剧情简介文本)。如果某部影片因任何原因无法处理，请在其对象的 "polishedPlot" 字段中返回原始剧情，并在对象中添加一个 "error" 字段并简述原因。请务必处理所有提供的影片条目。确保输出的文本为纯净的UTF-8编码，避免使用可能导致显示问题的特殊字符。`;

  const userPrompt = `请为以下每一部影片的剧情简介进行润色。对于每一部影片，保持其原剧情核心意义和关键事件不变，但你需要用极度色情、露骨、充满感官挑逗和性暗示的风格重写剧情，充分展现林珞姐姐的特质。

以JSON数组格式返回结果，数组中的每个对象应包含 "id" (即原始NFO文件路径) 和 "polishedPlot" (润色后的剧情文本)。如果某部影片因任何原因无法处理，可以在其对象中包含一个 "error" 字段并简述原因，但仍应尝试处理其他影片。

影片列表如下：
${JSON.stringify(plotsDataArray.map(p => ({id: p.id, title: p.title, currentPlot: sanitizeTextForAI(p.currentPlot || '')})), null, 2)}
`;

  try {
    const aiService = await getAiClient(storeInstance);
    let rawBatchResults;
    const estimatedTokens = plotsDataArray.length * (500 + 100); 
    const providerModelOptions = { max_tokens: Math.min(16000, estimatedTokens + 2000) , temperature: 0.75 };

    log.info(`[AI批量剧情润色] 调用 AI (${aiService.type})。模型: ${aiService.config?.model || 'gemini-default'}. Prompt (前200): ${userPrompt.substring(0,200)}...`);
    log.info(`[AI批量剧情润色] 预估Tokens: ${estimatedTokens}, Provider model options: ${JSON.stringify(providerModelOptions)}, HTTP Timeout: ${httpClientTimeout}ms`);

    if (aiService.type === 'gemini') {
      rawBatchResults = await geminiProvider.generateJsonContent(aiService.client, userPrompt, { generationConfig: {systemInstruction, ...providerModelOptions, responseMimeType: "application/json"} });
    } else if (aiService.type === 'customGpt') {
      rawBatchResults = await customGptProvider.generateJsonContent(aiService.config, userPrompt, systemInstruction, {...providerModelOptions, response_format: { type: "json_object" }}, httpClientTimeout, abortSignal);
    } else if (aiService.type === 'grok') {
        // Grok response parsing moved here
        const responseText = await grokProvider.generateContent(aiService.config, userPrompt, systemInstruction, providerModelOptions, httpClientTimeout, abortSignal);
        let jsonStr = responseText.trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s; 
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) jsonStr = match[2].trim();
        try {
            rawBatchResults = JSON.parse(jsonStr);
        } catch (parseError) {
            log.error(`[AI批量剧情润色] Grok响应JSON解析失败: ${parseError.message}. 响应: ${jsonStr.substring(0,500)}`);
            throw new Error(`Grok响应JSON解析失败: ${parseError.message}`);
        }
    } else {
      throw new Error('不支持的 AI 提供商进行批量润色。');
    }
    
    // Handle Grok's potential {"results": []} wrapper
    let finalBatchResults = rawBatchResults;
    if (aiService.type === 'grok' && rawBatchResults && typeof rawBatchResults === 'object' && Array.isArray(rawBatchResults.results)) {
        log.info("[AI批量剧情润色] Grok 响应包含 'results' 数组，将使用该数组。");
        finalBatchResults = rawBatchResults.results;
    }


    if (Array.isArray(finalBatchResults) && finalBatchResults.every(item => typeof item.id === 'string' && typeof item.polishedPlot === 'string')) {
      log.info(`[AI批量剧情润色] ${aiService.type} 成功返回并解析了 ${finalBatchResults.length} 个结果。`);
      const cleanedResults = finalBatchResults.map(res => ({
          ...res,
          polishedPlot: res.polishedPlot.replace(/□/g, '')
      }));
      return { success: true, results: cleanedResults };
    } else {
       log.error(`[AI批量剧情润色] ${aiService.type} 返回的批量结果格式不正确. 响应 (前500): ${JSON.stringify(finalBatchResults).substring(0,500)}`);
      return { success: false, results: [], error: 'AI返回的批量结果格式不正确 (非对象数组或缺少id/polishedPlot)。' };
    }

  } catch (error) {
    if (error.message === 'Request aborted.' || error.message.includes('aborted')) {
        log.warn(`[AI批量剧情润色] 批量润色请求被中止.`);
        return { success: false, results: [], error: 'AI请求被用户取消。', cancelled: true };
    }
    log.error(`[AI批量剧情润色] 批量润色剧情时发生错误: ${error.message}`, error);
    return { success: false, results: [], error: `AI批量润色剧情失败: ${error.message}` };
  }
}

/**
 * 通用AI调用函数 - 简化各Provider的调用逻辑
 * @param {Object} aiService - AI服务配置对象
 * @param {string} prompt - 用户提示词
 * @param {string} systemInstruction - 系统指令（可选）
 * @param {Object} options - 额外选项（可选）
 * @param {number} timeout - 超时时间（可选）
 * @returns {Promise<string>} AI响应
 */
async function callAiProvider(aiService, prompt, systemInstruction = null, options = {}, timeout = 30000) {
  if (aiService.type === 'gemini') {
    return await geminiProvider.generateContent(aiService.client, prompt, { model: aiService.model });
  } else if (aiService.type === 'customGpt') {
    return await customGptProvider.generateContent(aiService.config, prompt, systemInstruction, options, timeout);
  } else if (aiService.type === 'grok') {
    return await grokProvider.generateContent(aiService.config, prompt, systemInstruction || "You are a helpful assistant.", options, timeout);
  } else if (aiService.type === 'ollama') {
    // 对于Ollama，将系统指令合并到提示词中
    const fullPrompt = systemInstruction ? `${systemInstruction}\n\n${prompt}` : prompt;
    return await ollamaProvider.getAiResponse(fullPrompt, aiService.config);
  } else {
    throw new Error('不支持的 AI 提供商.');
  }
}

module.exports = {
  initializeAiService,
  getAiClient,
  generatePlotSummaryWithAI,
  translatePlotWithAI,
  embellishPlotWithAI,
  analyzeMovieTagsWithAI,
  suggestCoverFromSnapshotsWithAI,
  analyzeMovieRecommendationIndexWithAI,
  getCleanupSuggestionsWithAI,
  invokeLinLuoChatWithAIStream,
  formatRecommendationsAsAiMessage,
  testAiConnectionWithProvider,
  embellishMultiplePlotsWithAI,
  callAiProvider,
};
