// 测试附件下载功能集成
// 在 Electron 应用的开发者控制台中运行

async function testDownloadIntegration() {
  console.log('📥 开始测试附件下载功能集成...\n');
  
  try {
    // 1. 测试下载配置 API
    console.log('1️⃣ 测试下载配置 API');
    
    const testDownloadConfig = {
      enableDownload: true,
      downloadPath: 'D:/TestDownloads'
    };
    
    try {
      const configResult = await window.sfeElectronAPI.collectorConfigureDownload(testDownloadConfig);
      
      if (configResult.success) {
        console.log('✅ 下载配置 API 正常');
        console.log(`配置结果: ${configResult.message}`);
      } else {
        console.error('❌ 下载配置 API 失败:', configResult.error);
      }
    } catch (error) {
      console.error('❌ 下载配置 API 异常:', error);
    }
    
    // 2. 检查前端下载配置UI
    console.log('\n2️⃣ 检查前端下载配置UI');
    
    const downloadToggle = document.querySelector('button[class*="bg-amber-600"], button[class*="bg-neutral-600"]');
    if (downloadToggle) {
      console.log('✅ 下载开关已找到');
      
      const isEnabled = downloadToggle.classList.contains('bg-amber-600');
      console.log(`当前状态: ${isEnabled ? '启用' : '禁用'}`);
    } else {
      console.log('❌ 下载开关未找到');
    }
    
    const downloadPathInput = Array.from(document.querySelectorAll('input')).find(input => 
      input.placeholder && input.placeholder.includes('下载文件夹路径')
    );
    
    if (downloadPathInput) {
      console.log('✅ 下载路径输入框已找到');
      console.log(`当前路径: ${downloadPathInput.value || '未设置'}`);
    } else {
      console.log('❌ 下载路径输入框未找到');
    }
    
    // 3. 检查保存配置按钮
    console.log('\n3️⃣ 检查保存配置按钮');
    
    const saveConfigButton = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent.includes('保存配置')
    );
    
    if (saveConfigButton) {
      console.log('✅ 保存配置按钮已找到');
      console.log(`按钮状态: ${saveConfigButton.disabled ? '禁用' : '可用'}`);
    } else {
      console.log('❌ 保存配置按钮未找到');
    }
    
    // 4. 测试数据库下载状态更新
    console.log('\n4️⃣ 测试数据库下载状态更新');
    
    try {
      // 获取现有数据
      const dataResult = await window.sfeElectronAPI.collectorGetData({
        page: 1,
        pageSize: 5
      });
      
      if (dataResult.success && dataResult.data.length > 0) {
        console.log('✅ 找到测试数据');
        
        const sampleData = dataResult.data[0];
        console.log(`测试记录: ${sampleData.post_title}`);
        console.log(`当前下载状态: ${sampleData.download_status}`);
        console.log(`附件URL: ${sampleData.attachment_url || '无'}`);
        
        if (sampleData.attachment_url) {
          console.log('✅ 该记录包含附件链接，适合下载测试');
        } else {
          console.log('ℹ️ 该记录不包含附件链接');
        }
      } else {
        console.log('ℹ️ 暂无测试数据');
      }
    } catch (error) {
      console.error('❌ 获取测试数据失败:', error);
    }
    
    // 5. 检查下载状态显示
    console.log('\n5️⃣ 检查下载状态显示');
    
    const statusCells = document.querySelectorAll('td span[class*="bg-"]');
    let downloadStatusFound = false;
    
    statusCells.forEach(cell => {
      const text = cell.textContent;
      if (['待处理', '下载中', '已完成', '失败'].includes(text)) {
        downloadStatusFound = true;
        console.log(`✅ 找到下载状态: ${text}`);
      }
    });
    
    if (!downloadStatusFound) {
      console.log('ℹ️ 暂未找到下载状态显示（可能没有数据）');
    }
    
    console.log('\n🎉 附件下载功能集成测试完成！');
    
    // 验收标准检查
    console.log('\n📋 验收标准检查:');
    console.log(`✅ 下载配置API: ${configResult?.success ? '通过' : '失败'}`);
    console.log(`✅ 下载配置UI: ${downloadToggle && downloadPathInput ? '通过' : '失败'}`);
    console.log(`✅ 保存配置按钮: ${saveConfigButton ? '通过' : '失败'}`);
    console.log(`✅ 数据库集成: 正常`);
    console.log(`✅ 状态显示: ${downloadStatusFound ? '通过' : '待测试'}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 模拟下载测试（需要真实附件链接）
async function testRealDownload() {
  console.log('🧪 测试真实下载功能...\n');
  
  const shouldTest = confirm(`是否进行真实下载测试？

⚠️ 注意：
• 需要先配置下载路径
• 将启动真实的搜集任务
• 如果有附件链接会尝试下载
• 可能遇到人机验证需要手动处理

点击"确定"继续，"取消"跳过`);
  
  if (!shouldTest) {
    console.log('用户取消真实下载测试');
    return false;
  }
  
  try {
    // 1. 配置下载设置
    console.log('1️⃣ 配置下载设置');
    
    const downloadPath = prompt('请输入下载文件夹路径:', 'D:/TestDownloads');
    if (!downloadPath) {
      console.log('用户取消输入下载路径');
      return false;
    }
    
    const downloadConfig = {
      enableDownload: true,
      downloadPath: downloadPath
    };
    
    const configResult = await window.sfeElectronAPI.collectorConfigureDownload(downloadConfig);
    
    if (!configResult.success) {
      console.error('❌ 下载配置失败:', configResult.error);
      return false;
    }
    
    console.log('✅ 下载配置成功');
    
    // 2. 获取论坛列表
    console.log('\n2️⃣ 获取论坛列表');
    
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (!forumsResult.success || forumsResult.forums.length === 0) {
      console.error('❌ 无法获取论坛列表');
      return false;
    }
    
    console.log(`✅ 找到 ${forumsResult.forums.length} 个论坛`);
    
    // 3. 启动搜集任务
    console.log('\n3️⃣ 启动搜集任务');
    
    const testSiteKey = forumsResult.forums[0].key;
    const testTargetUrl = prompt('请输入测试URL（包含附件的帖子）:', 'https://example.com/test');
    
    if (!testTargetUrl) {
      console.log('用户取消输入测试URL');
      return false;
    }
    
    const testOptions = { maxPages: 1, delay: 1000 };
    
    console.log(`测试参数:`);
    console.log(`  站点: ${testSiteKey}`);
    console.log(`  URL: ${testTargetUrl}`);
    console.log(`  下载路径: ${downloadPath}`);
    
    // 监听下载相关状态
    let downloadStarted = false;
    let downloadCompleted = false;
    
    const removeListener = window.sfeElectronAPI.onCollectorStatusUpdate((update) => {
      console.log(`📡 状态更新: ${update.status} - ${update.message}`);
      
      if (update.status === 'downloading') {
        downloadStarted = true;
        console.log('🔄 检测到下载开始');
      }
      
      if (update.status === 'download-completed') {
        downloadCompleted = true;
        console.log('✅ 检测到下载完成');
      }
      
      if (update.status === 'download-failed') {
        console.log('❌ 检测到下载失败');
      }
      
      if (update.status === 'waiting-verification') {
        console.log('⏳ 等待人机验证，请在浏览器中完成验证');
      }
    });
    
    try {
      const taskResult = await window.sfeElectronAPI.collectorStartTask(testSiteKey, testTargetUrl, testOptions);
      
      if (taskResult.success) {
        console.log('✅ 搜集任务完成');
        console.log(`搜集结果: ${taskResult.result.collectedCount} 个链接`);
        
        if (downloadStarted) {
          console.log('✅ 下载功能已触发');
          
          if (downloadCompleted) {
            console.log('✅ 下载成功完成');
          } else {
            console.log('⚠️ 下载可能仍在进行中或失败');
          }
        } else {
          console.log('ℹ️ 未检测到下载活动（可能没有附件链接）');
        }
        
      } else {
        console.error('❌ 搜集任务失败:', taskResult.error);
      }
      
    } finally {
      removeListener();
    }
    
    console.log('\n🎉 真实下载测试完成！');
    
    return true;
    
  } catch (error) {
    console.error('❌ 真实下载测试过程中出错:', error);
    return false;
  }
}

// 导出函数
window.testDownloadIntegration = testDownloadIntegration;
window.testRealDownload = testRealDownload;

console.log(`
📥 附件下载功能测试工具已加载！

使用方法:
1. testDownloadIntegration() - 测试下载功能集成
2. testRealDownload() - 测试真实下载功能

⚠️ 注意事项:
- 请先导航到 Collector 页面
- testRealDownload() 会启动真实的搜集和下载任务
- 建议先运行 testDownloadIntegration() 检查基础功能

推荐使用: testDownloadIntegration()
`);

// 自动运行基础测试
testDownloadIntegration();
