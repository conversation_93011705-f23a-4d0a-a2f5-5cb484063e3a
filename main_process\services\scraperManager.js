// main_process/services/scraperManager.js
const log = require('electron-log');
const path = require('path');
const fs = require('fs');
const javbusProvider = require('./scrapers/javbusProvider');
const dmmProvider = require('./scrapers/dmmProvider');
const javdbProvider = require('./scrapers/javdbProvider');
const avsoxProvider = require('./scrapers/avsoxProvider');
// 以后在这里引入其他 Provider

// 引入路径解析和媒体下载服务
const pathResolverService = require('./pathResolverService');
const mediaDownloadService = require('./mediaDownloadService');

// 引入设置服务
const settingsService = require('./settingsService');

// 【重要】从用户设置中读取的刮削优先级列表
// 这里暂时硬编码，未来将从设置服务中动态读取。
const PROVIDER_PRIORITY = [
    'javbus', 'freejavbt', 'jav321', 'dmm', 'javlibrary', 
    '7mmtv', 'javdb', 'avsex', 'airav', 'avsox'
];

// 将所有已实现的 Provider 注册到一个 map 中，方便调用
const providers = {
    'javbus': javbusProvider,
    'dmm': dmmProvider,
    'javdb': javdbProvider,
    'avsox': avsoxProvider,
    // 'freejavbt': freejavbtProvider,
};

// 记录 Provider 版本信息
log.info(`[ScraperManager] 已注册的 Provider:`);
Object.entries(providers).forEach(([name, provider]) => {
    const version = provider.version || '未知版本';
    log.info(`[ScraperManager] - ${name}: ${version}`);
});

/**
 * 根据 nfoId 刮削电影元数据，升级为"聚合式"引擎。
 * 从所有可用 Provider 收集数据，然后通过精炼规则生成最终报告。
 * @param {string} nfoId - 要刮削的番号。
 * @returns {Promise<Object>} 精炼后的最终数据
 */
async function scrapeMovieById(nfoId) {
    log.info(`[ScraperManager] 开始为 ${nfoId} 进行全面刮削（聚合模式）...`);

    // 【重构】初始化数据收集容器（B区）
    const sourceData = {};
    let successCount = 0;

    // 【重构】全面收集模式 - 不再提前返回
    for (const providerName of PROVIDER_PRIORITY) {
        const provider = providers[providerName];
        if (!provider) {
            log.debug(`[ScraperManager] Provider [${providerName}] 未实现，跳过。`);
            continue; // 如果 provider 还未实现，则跳过
        }

        try {
            log.info(`[ScraperManager] 尝试使用 [${providerName}] 刮削 ${nfoId}...`);
            const scrapedData = await provider.scrape(nfoId);
            log.info(`[ScraperManager] [${providerName}] 成功刮削到 ${nfoId} 的数据。`);

            // 【重构】存储到 sourceData，不退出循环
            sourceData[providerName] = scrapedData;
            successCount++;

        } catch (error) {
            log.warn(`[ScraperManager] Provider [${providerName}] 刮削 ${nfoId} 失败: ${error.message}`);
            // 继续循环，尝试下一个 provider
        }
    }

    // 检查是否有任何成功的数据
    if (successCount === 0) {
        log.error(`[ScraperManager] 所有 Provider 都未能成功刮削 ${nfoId}。`);
        throw new Error(`所有 Provider 都未能成功刮削 ${nfoId}。`);
    }

    log.info(`[ScraperManager] ${nfoId} 数据收集完成，成功 ${successCount} 个 Provider: ${Object.keys(sourceData).join(', ')}`);

    // 【重构】调用数据精炼厂
    return await refineAndSaveData(nfoId, sourceData);

}

/**
 * 数据精炼厂核心函数 - 将多个 Provider 的原始数据精炼为最终的 A区数据
 * @param {string} nfoId - 番号
 * @param {Object} sourceData - 来自各个 Provider 的原始数据 (B区)
 * @returns {Promise<Object>} 精炼后的最终数据
 */
async function refineAndSaveData(nfoId, sourceData) {
    log.info(`[数据精炼厂] 开始为 ${nfoId} 精炼数据...`);

    try {
        // 第一步：择优选择和合并
        const displayData = await refineDisplayData(nfoId, sourceData);

        // 第二步：解析资产路径
        const assetPaths = await pathResolverService.resolveAssetPaths({ nfoId });

        // 第三步：下载媒体资产
        const downloadResult = await downloadMediaAssets(nfoId, sourceData, assetPaths);

        // 第四步：【数据天桥】从 Collector 系统获取补充数据
        let collectorData = null;
        try {
            const collectorBridgeService = require('./collectorBridgeService');

            // 初始化桥接服务（如果尚未初始化）
            if (!collectorBridgeService.isAvailable()) {
                const databaseService = require('./databaseService');
                const db = databaseService.getDatabase();
                collectorBridgeService.initialize(db);
            }

            // 安全地获取补充数据
            collectorData = await collectorBridgeService.getSupplementaryData(nfoId);

            if (collectorData) {
                log.info(`[数据精炼厂] 成功从 Collector 系统获取 ${nfoId} 的补充数据`);
            } else {
                log.debug(`[数据精炼厂] 未找到 ${nfoId} 的 Collector 补充数据`);
            }

        } catch (error) {
            log.warn(`[数据精炼厂] Collector 桥接失败，继续使用基础数据: ${error.message}`);
            collectorData = null;
        }

        // 第五步：组装最终档案
        const metaData = {
            nfoId: nfoId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            source_providers: Object.keys(sourceData),

            // A区：精炼后的展示数据
            display_data: displayData,

            // B区：原始刮削数据（保留用于调试和重新精炼）
            source_data: sourceData,

            // 【数据天桥】D区：Collector 系统补充数据
            collector_data: collectorData,

            // C区：预留给AI和用户数据
            custom_data: {
                ai_tags: [],
                ai_plot_translation: '',
                ai_review_by_linluo: null,
                user_tags: [],
                user_notes: '',

                // 【数据天桥】将 Collector 数据映射到用户友好的字段
                forum_links: collectorData?.magnetLinks || [],
                cloud_storage: collectorData?.cloudLinks || [],
                community_discussions: collectorData?.forumDiscussions || []
            },

            // 下载统计
            download_stats: downloadResult
        };

        // 第六步：保存 .meta.json 文件
        const metaJsonPath = path.join(assetPaths.movieRootPath, '.meta.json');
        await saveMetaJsonFile(metaJsonPath, metaData);

        log.info(`[数据精炼厂] ${nfoId} 数据精炼完成，已保存到 ${metaJsonPath}`);

        // 返回兼容旧接口的数据结构
        return {
            ...displayData,
            resolvedPaths: assetPaths,
            downloadStats: downloadResult,
            metaDataPath: metaJsonPath
        };

    } catch (error) {
        log.error(`[数据精炼厂] ${nfoId} 数据精炼失败: ${error.message}`);
        throw error;
    }
}

/**
 * 择优选择和合并逻辑 - 根据精炼规则生成 DisplayData
 * @param {string} nfoId - 番号
 * @param {Object} sourceData - 来自各个 Provider 的原始数据
 * @returns {Promise<Object>} DisplayData 对象
 */
async function refineDisplayData(nfoId, sourceData) {
    log.info(`[数据精炼厂] 开始为 ${nfoId} 择优选择数据...`);

    // 【重构】从设置中动态读取精炼规则
    const settings = settingsService.getSettings();
    const priorityRules = settings.scraperPriorityRules || {};

    log.debug(`[数据精炼厂] 使用动态精炼规则，包含 ${Object.keys(priorityRules).length} 个字段配置`);

    const displayData = {};

    // 遍历所有精炼规则
    for (const [field, rule] of Object.entries(priorityRules)) {
        try {
            if (rule === 'calculated') {
                // 计算字段的特殊处理
                displayData[field] = await calculateField(field, nfoId, sourceData);
            } else if (Array.isArray(rule)) {
                // 按优先级择优选择
                displayData[field] = selectBestValue(field, rule, sourceData);
            }
        } catch (error) {
            log.warn(`[数据精炼厂] 处理字段 ${field} 时出错: ${error.message}`);
            displayData[field] = getDefaultValue(field);
        }
    }

    // 特殊处理：合并标签
    displayData.tags = mergeAndDeduplicateTags(sourceData);

    // 特殊处理：影片类型推断
    displayData.type = inferMovieType(nfoId, sourceData);

    log.info(`[数据精炼厂] ${nfoId} 择优选择完成，生成 ${Object.keys(displayData).length} 个字段`);
    return displayData;
}

/**
 * 按优先级择优选择字段值
 * @param {string} field - 字段名
 * @param {Array} priorityList - 优先级列表
 * @param {Object} sourceData - 源数据
 * @returns {*} 选择的最佳值
 */
function selectBestValue(field, priorityList, sourceData) {
    for (const providerName of priorityList) {
        const providerData = sourceData[providerName];
        if (providerData && providerData[field] !== undefined && providerData[field] !== null && providerData[field] !== '') {
            log.debug(`[数据精炼厂] 字段 ${field} 选择来源: ${providerName}`);
            return providerData[field];
        }
    }

    log.debug(`[数据精炼厂] 字段 ${field} 无有效值，使用默认值`);
    return getDefaultValue(field);
}

/**
 * 计算字段的特殊处理
 * @param {string} field - 字段名
 * @param {string} nfoId - 番号
 * @param {Object} sourceData - 源数据
 * @returns {Promise<*>} 计算结果
 */
async function calculateField(field, nfoId, sourceData) {
    switch (field) {
        case 'version_count':
            // TODO: 从数据库查询版本数量
            return 1; // 临时返回1

        case 'is_watched':
            // TODO: 从数据库查询观看状态
            return false; // 临时返回false

        default:
            return getDefaultValue(field);
    }
}

/**
 * 获取字段的默认值
 * @param {string} field - 字段名
 * @returns {*} 默认值
 */
function getDefaultValue(field) {
    const defaults = {
        title: '',
        display_id: '',
        type: 'other',
        is_watched: false,
        version_count: 1,
        has_4k: false,
        has_bluray: false,
        has_subtitles: false,
        is_uncensored_cracked: false,
        is_leaked: false,
        cover_path: '',
        cover_orientation: 'portrait',
        nfo_prefix: '',
        year: '',
        release_date: '',
        runtime: 0,
        studio: '',
        publisher: '',
        series: '',
        rating: null,
        tags: [],
        actresses: [],
        actors_male: [],
        director: null,
        plot: '',
        preview_image_paths: [],
        user_reviews: [],
        similar_movies: []
    };

    return defaults[field] !== undefined ? defaults[field] : null;
}

/**
 * 合并并去重标签
 * @param {Object} sourceData - 源数据
 * @returns {Array} 合并后的标签数组
 */
function mergeAndDeduplicateTags(sourceData) {
    const allTags = new Set();

    // 从所有 Provider 收集标签
    Object.values(sourceData).forEach(providerData => {
        if (providerData.tags && Array.isArray(providerData.tags)) {
            providerData.tags.forEach(tag => {
                if (tag && typeof tag === 'string') {
                    allTags.add(tag.trim());
                }
            });
        }

        // 也收集 genres 字段（某些 Provider 可能使用这个字段）
        if (providerData.genres && Array.isArray(providerData.genres)) {
            providerData.genres.forEach(genre => {
                if (genre && typeof genre === 'string') {
                    allTags.add(genre.trim());
                }
            });
        }
    });

    return Array.from(allTags).sort();
}

/**
 * 推断影片类型
 * @param {string} nfoId - 番号
 * @param {Object} sourceData - 源数据
 * @returns {string} 影片类型
 */
function inferMovieType(nfoId, sourceData) {
    // 优先从 DMM 获取类型信息
    if (sourceData.dmm && sourceData.dmm.type) {
        return sourceData.dmm.type;
    }

    // 从番号推断
    const upperNfoId = nfoId.toUpperCase();

    // 无码类型判断
    if (upperNfoId.includes('UNCENSORED') ||
        upperNfoId.match(/^(CARIB|1PONDO|HEYZO|FC2|PACOPACOMAMA|MURAMURA)/)) {
        return 'uncensored';
    }

    // VR类型判断
    if (upperNfoId.includes('VR') || upperNfoId.includes('3D')) {
        return 'vr';
    }

    // 国产类型判断
    if (upperNfoId.match(/^(MD|PMC|PMS|TM|XK|91)/)) {
        return 'chinese';
    }

    // 欧美类型判断
    if (upperNfoId.match(/^(BLACKED|TUSHY|VIXEN|DEEPER|SLAYED)/)) {
        return 'western';
    }

    // 默认为有码
    return 'censored';
}

/**
 * 下载媒体资产
 * @param {string} nfoId - 番号
 * @param {Object} sourceData - 源数据
 * @param {Object} assetPaths - 资产路径
 * @returns {Promise<Object>} 下载结果统计
 */
async function downloadMediaAssets(nfoId, sourceData, assetPaths) {
    log.info(`[数据精炼厂] 开始为 ${nfoId} 下载媒体资产...`);

    const downloadTasks = [];

    // 收集所有可用的媒体URL
    const mediaUrls = {
        covers: new Set(),
        posters: new Set(),
        previews: new Set(),
        trailers: new Set()
    };

    // 从所有 Provider 收集媒体URL
    Object.values(sourceData).forEach(providerData => {
        // 封面图
        if (providerData.coverUrl && mediaDownloadService.isValidUrl(providerData.coverUrl)) {
            mediaUrls.covers.add(providerData.coverUrl);
        }

        // 海报图
        if (providerData.posterUrl && mediaDownloadService.isValidUrl(providerData.posterUrl)) {
            mediaUrls.posters.add(providerData.posterUrl);
        }

        // 预览图
        if (providerData.previewImages && Array.isArray(providerData.previewImages)) {
            providerData.previewImages.forEach(url => {
                if (mediaDownloadService.isValidUrl(url)) {
                    mediaUrls.previews.add(url);
                }
            });
        }

        // 预告片
        if (providerData.trailerUrl && mediaDownloadService.isValidUrl(providerData.trailerUrl)) {
            mediaUrls.trailers.add(providerData.trailerUrl);
        }
    });

    // 选择最佳封面（优先 DMM，然后 JavBus）
    const bestCoverUrl = selectBestMediaUrl(mediaUrls.covers, sourceData, 'coverUrl', ['dmm', 'javbus', 'javdb']);
    if (bestCoverUrl) {
        downloadTasks.push({
            url: bestCoverUrl,
            savePath: assetPaths.posterPath,
            type: 'cover'
        });
    }

    // 选择最佳海报（如果与封面不同）
    const bestPosterUrl = selectBestMediaUrl(mediaUrls.posters, sourceData, 'posterUrl', ['dmm', 'javbus', 'javdb']);
    if (bestPosterUrl && bestPosterUrl !== bestCoverUrl) {
        downloadTasks.push({
            url: bestPosterUrl,
            savePath: assetPaths.fanartPath,
            type: 'fanart'
        });
    }

    // 下载预览图（最多10张）
    const previewUrls = Array.from(mediaUrls.previews).slice(0, 10);
    previewUrls.forEach((url, index) => {
        const previewPath = pathResolverService.generatePreviewPath(assetPaths.previewsDir, index);
        downloadTasks.push({
            url: url,
            savePath: previewPath,
            type: 'preview',
            index: index
        });
    });

    // 下载预告片
    const bestTrailerUrl = selectBestMediaUrl(mediaUrls.trailers, sourceData, 'trailerUrl', ['dmm', 'javbus', 'javdb']);
    if (bestTrailerUrl) {
        downloadTasks.push({
            url: bestTrailerUrl,
            savePath: assetPaths.trailerPath,
            type: 'trailer'
        });
    }

    // 执行下载
    let downloadResult = { success: 0, failed: 0, total: downloadTasks.length };

    if (downloadTasks.length > 0) {
        log.info(`[数据精炼厂] 开始下载 ${downloadTasks.length} 个媒体文件...`);
        const result = await mediaDownloadService.downloadMultipleFiles(
            downloadTasks.map(task => ({ url: task.url, savePath: task.savePath })),
            3 // 并发数限制
        );

        downloadResult = {
            success: result.success,
            failed: result.failed,
            total: downloadTasks.length
        };

        log.info(`[数据精炼厂] ${nfoId} 媒体下载完成: 成功 ${result.success}, 失败 ${result.failed}`);
    } else {
        log.warn(`[数据精炼厂] ${nfoId} 没有有效的媒体 URL 可下载`);
    }

    return downloadResult;
}

/**
 * 选择最佳媒体URL
 * @param {Set} urls - URL集合
 * @param {Object} sourceData - 源数据
 * @param {string} field - 字段名
 * @param {Array} priority - 优先级列表
 * @returns {string|null} 最佳URL
 */
function selectBestMediaUrl(urls, sourceData, field, priority) {
    if (urls.size === 0) return null;

    // 按优先级查找
    for (const providerName of priority) {
        const providerData = sourceData[providerName];
        if (providerData && providerData[field] && urls.has(providerData[field])) {
            return providerData[field];
        }
    }

    // 如果没有找到，返回第一个
    return Array.from(urls)[0];
}

/**
 * 保存 .meta.json 文件
 * @param {string} filePath - 文件路径
 * @param {Object} metaData - 元数据
 * @returns {Promise<void>}
 */
async function saveMetaJsonFile(filePath, metaData) {
    try {
        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 写入文件
        const jsonContent = JSON.stringify(metaData, null, 2);
        fs.writeFileSync(filePath, jsonContent, 'utf8');

        log.info(`[数据精炼厂] 成功保存 .meta.json 文件: ${filePath}`);
    } catch (error) {
        log.error(`[数据精炼厂] 保存 .meta.json 文件失败: ${error.message}`);
        throw error;
    }
}

module.exports = { scrapeMovieById };
