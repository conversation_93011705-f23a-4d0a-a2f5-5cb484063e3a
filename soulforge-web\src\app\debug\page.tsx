'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/layout/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Movie {
  id: string;
  fileName: string;
  nfoId: string | null;
  versionCount?: number;
  multiCdCountForNfoId?: number;
}

export default function DebugPage() {
  const [movies, setMovies] = useState<Movie[]>([]);
  const [loading, setLoading] = useState(false);
  const [rawMovies, setRawMovies] = useState<Movie[]>([]);

  const loadMovies = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/movies?limit=50');
      const data = await response.json();
      
      if (data.success) {
        setMovies(data.movies);
        console.log('Loaded movies:', data.movies);
      }
    } catch (error) {
      console.error('Failed to load movies:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRawMovies = async () => {
    setLoading(true);
    try {
      // This would be a direct database query without merging
      const response = await fetch('/api/debug/raw-movies');
      const data = await response.json();
      
      if (data.success) {
        setRawMovies(data.movies);
        console.log('Loaded raw movies:', data.movies);
      }
    } catch (error) {
      console.error('Failed to load raw movies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMovies();
  }, []);

  const groupMoviesByNfoId = (movieList: Movie[]) => {
    const groups = new Map<string, Movie[]>();
    
    for (const movie of movieList) {
      const key = movie.nfoId && movie.nfoId.trim() !== '' 
        ? movie.nfoId.toLowerCase().trim() 
        : movie.fileName;
      
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(movie);
    }
    
    return Array.from(groups.entries()).filter(([_, movies]) => movies.length > 1);
  };

  const duplicateGroups = groupMoviesByNfoId(rawMovies);

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">多版本合并调试</h1>
          <div className="space-x-2">
            <Button onClick={loadMovies} disabled={loading}>
              加载合并后的电影
            </Button>
            <Button onClick={loadRawMovies} disabled={loading} variant="outline">
              加载原始电影数据
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>合并后电影数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{movies.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>原始电影数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{rawMovies.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>重复组数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{duplicateGroups.length}</div>
            </CardContent>
          </Card>
        </div>

        {/* Merged Movies */}
        <Card>
          <CardHeader>
            <CardTitle>合并后的电影 (前20部)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {movies.slice(0, 20).map((movie) => (
                <div key={movie.id} className="flex items-center justify-between p-2 bg-muted rounded">
                  <div className="flex-1 min-w-0">
                    <div className="font-mono text-sm truncate">{movie.fileName}</div>
                    <div className="text-xs text-muted-foreground">
                      NFO ID: {movie.nfoId || 'null'}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {movie.versionCount && movie.versionCount > 1 && (
                      <Badge variant="default">
                        {movie.versionCount} 版本
                      </Badge>
                    )}
                    {movie.multiCdCountForNfoId && movie.multiCdCountForNfoId > 1 && (
                      <Badge variant="secondary">
                        {movie.multiCdCountForNfoId} CD
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Duplicate Groups */}
        {duplicateGroups.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>发现的重复组 (应该被合并)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {duplicateGroups.slice(0, 10).map(([key, groupMovies], index) => (
                  <div key={index} className="border rounded p-3">
                    <div className="font-medium mb-2">
                      组 {index + 1}: "{key}" ({groupMovies.length} 个文件)
                    </div>
                    <div className="space-y-1">
                      {groupMovies.map((movie) => (
                        <div key={movie.id} className="text-sm font-mono bg-muted p-1 rounded">
                          {movie.fileName}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Debug Info */}
        <Card>
          <CardHeader>
            <CardTitle>调试信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>合并后电影数量: {movies.length}</div>
              <div>原始电影数量: {rawMovies.length}</div>
              <div>重复组数量: {duplicateGroups.length}</div>
              <div>
                合并效果: {rawMovies.length > 0 
                  ? `减少了 ${rawMovies.length - movies.length} 个重复项` 
                  : '等待加载原始数据'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
