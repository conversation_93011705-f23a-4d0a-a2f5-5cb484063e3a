// soul-forge-electron/src/components/tools/NfoPlotPolisherTool.tsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
    NfoPolishScanProgressData, NfoPolishScanCompleteData, 
    NfoPolishProcessProgressData, NfoPolishProcessCompleteData, NfoPolishErrorData 
} from '../../types';
import { LuFolderPlus, LuPlay, LuTrash2, LuFileText, LuSparkles, LuChevronsUpDown, LuTriangle, LuCheck, LuScanSearch, LuBrainCircuit, LuX } from 'react-icons/lu';

interface NfoPlotPolisherToolProps {
  isOpen: boolean;
  onClose: () => void;
}

type ProcessingStage = 'idle' | 'scanning' | 'scan_complete' | 'processing' | 'process_complete' | 'cancelled';

// Utility to get basename for display in renderer
const getBasename = (filePath: string): string => {
  if (!filePath) return '';
  // Replace both / and \ with a common separator for splitting
  const normalizedPath = filePath.replace(/\\/g, '/');
  const parts = normalizedPath.split('/');
  return parts[parts.length - 1];
};


const NfoPlotPolisherTool: React.FC<NfoPlotPolisherToolProps> = ({ isOpen, onClose }) => {
  const [directories, setDirectories] = useState<string[]>([]);
  const [newDirectory, setNewDirectory] = useState<string>(''); // Explicitly string
  const [currentStage, setCurrentStage] = useState<ProcessingStage>('idle');
  
  const [scanProgress, setScanProgress] = useState<NfoPolishScanProgressData | null>(null);
  const [scanResult, setScanResult] = useState<NfoPolishScanCompleteData & { cancelled?: boolean } | null>(null);
  
  const [processProgress, setProcessProgress] = useState<NfoPolishProcessProgressData | null>(null);
  const [processResult, setProcessResult] = useState<NfoPolishProcessCompleteData & { cancelled?: boolean } | null>(null);

  const [logs, setLogs] = useState<string[]>([]);
  const [errors, setErrors] = useState<NfoPolishErrorData[]>([]);
  const logAreaRef = useRef<HTMLDivElement>(null);

  const isBusy = currentStage === 'scanning' || currentStage === 'processing';

  const appendLog = useCallback((message: string) => {
    setLogs(prev => {
        const newLogs = [...prev, `${new Date().toLocaleTimeString()}: ${message}`];
        if (newLogs.length > 300) return newLogs.slice(-300); // Keep last 300 logs
        return newLogs;
    });
  }, []);

  useEffect(() => {
    if (logAreaRef.current) {
      logAreaRef.current.scrollTop = logAreaRef.current.scrollHeight;
    }
  }, [logs]);

  useEffect(() => {
    if (!isOpen) {
      // Reset state when modal is closed
      if (isBusy) { // If busy when closing, attempt to cancel first
        window.sfeElectronAPI.nfoPolishToolCancel();
      }
      setDirectories([]);
      setNewDirectory('');
      setCurrentStage('idle');
      setScanProgress(null);
      setScanResult(null);
      setProcessProgress(null);
      setProcessResult(null);
      setLogs([]);
      setErrors([]);
      return;
    }

    const unsubScanProgress = window.sfeElectronAPI.onNfoPolishToolScanProgress(data => setScanProgress(data));
    const unsubScanComplete = window.sfeElectronAPI.onNfoPolishToolScanComplete(data => {
      setScanResult(data);
      if (data.cancelled) {
        setCurrentStage('cancelled');
        appendLog('扫描操作已被取消。');
      } else {
        setCurrentStage('scan_complete');
        appendLog(`扫描完成！共找到 ${data.totalNfoFilesFound} 个NFO文件。`);
      }
    });
    const unsubProcessProgress = window.sfeElectronAPI.onNfoPolishToolProcessProgress(data => setProcessProgress(data));
    const unsubProcessComplete = window.sfeElectronAPI.onNfoPolishToolProcessComplete(summary => {
      setProcessResult(summary);
      if (summary.cancelled) {
        setCurrentStage('cancelled');
        appendLog('润色处理操作已被取消。');
      } else {
        setCurrentStage('process_complete');
        appendLog(`润色处理完成！成功更新: ${summary.totalSuccessfullyUpdated}, 跳过: ${summary.totalSkipped}, 错误: ${summary.totalErrors}`);
      }
    });
    const unsubLog = window.sfeElectronAPI.onNfoPolishToolLog(message => appendLog(message));
    const unsubError = window.sfeElectronAPI.onNfoPolishToolError(errorData => {
        setErrors(prev => [...prev, errorData]);
        appendLog(`错误: ${errorData.error} (文件: ${errorData.filePath || 'N/A'})`);
    });

    return () => {
      unsubScanProgress();
      unsubScanComplete();
      unsubProcessProgress();
      unsubProcessComplete();
      unsubLog();
      unsubError();
    };
  }, [isOpen, appendLog, isBusy]);

  const handleAddDirectory = async () => {
    const currentNewDirectoryInputVal = newDirectory.trim();
    if (currentNewDirectoryInputVal && !directories.includes(currentNewDirectoryInputVal)) {
      setDirectories((prevDirs: string[]) => [...prevDirs, currentNewDirectoryInputVal]);
      setNewDirectory('');
    } else if (!currentNewDirectoryInputVal) { // Input is empty, try browsing
        try {
            const selectedPathsArray: string[] | null = await window.sfeElectronAPI.selectDirectory();
            if (selectedPathsArray && selectedPathsArray.length > 0) {
                const pathsToAddFiltered: string[] = selectedPathsArray.filter((p: string) => !directories.includes(p));
                if (pathsToAddFiltered.length > 0) {
                    setDirectories((prevDirs: string[]) => [...prevDirs, ...pathsToAddFiltered]);
                }
            }
        } catch (error) {
            console.error("浏览目录失败:", error);
            appendLog("选择目录时出错。");
        }
    } else if (directories.includes(currentNewDirectoryInputVal)) {
      alert('该目录已添加。');
    }
  };

  const handleRemoveDirectory = (dirToRemove: string) => {
    setDirectories(prev => prev.filter(dir => dir !== dirToRemove));
  };

  const handleStartScanning = () => {
    if (directories.length === 0) {
      alert('请至少添加一个要扫描的目录。');
      return;
    }
    setCurrentStage('scanning');
    setScanProgress(null);
    setScanResult(null);
    setProcessProgress(null);
    setProcessResult(null);
    setLogs(['开始扫描NFO文件...']);
    setErrors([]);
    window.sfeElectronAPI.nfoPolishToolScanDirectories(directories);
  };

  const handleStartProcessing = () => {
    if (!scanResult || scanResult.nfoFilePaths.length === 0) {
      alert('没有找到NFO文件或扫描未完成/被取消，无法开始处理。');
      return;
    }
    setCurrentStage('processing');
    setProcessProgress(null);
    setProcessResult(null);
    appendLog(`开始润色 ${scanResult.totalNfoFilesFound} 个NFO文件...`);
    window.sfeElectronAPI.nfoPolishToolProcessFiles(scanResult.nfoFilePaths);
  };
  
  const handleCancelOperation = () => {
    if (isBusy) {
      window.sfeElectronAPI.nfoPolishToolCancel();
      appendLog('正在发送取消请求...');
      // Stage will be updated by 'scan_complete' or 'process_complete' with 'cancelled: true'
    }
  };
  
  const handleResetTool = () => {
    if (isBusy) {
        handleCancelOperation(); // Attempt to cancel if busy
    }
    setDirectories([]);
    setNewDirectory('');
    setCurrentStage('idle');
    setScanProgress(null);
    setScanResult(null);
    setProcessProgress(null);
    setProcessResult(null);
    setLogs([]);
    setErrors([]);
  };

  const handleCloseModal = () => {
    if (isBusy) {
        if (window.confirm("操作正在进行中，确定要关闭并尝试取消吗？")) {
            handleCancelOperation();
            onClose();
        }
    } else {
        onClose();
    }
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[85] flex items-center justify-center p-4 bg-black/80 backdrop-blur-md">
      <div 
        className="bg-[#232323] text-neutral-200 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col border border-[#4f4f4f]"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-5 border-b border-[#3a3a3a] bg-[#2a2a2a]">
          <h2 className="text-xl font-bold text-sky-400 flex items-center">
            <LuSparkles size={24} className="mr-2" /> NFO 剧情简介 AI 润色工具
          </h2>
          <button onClick={handleCloseModal} className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-[#3a3a3a]" aria-label="关闭工具">
             <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>

        <div className="flex-grow overflow-y-auto p-5 space-y-4 settings-scroll-container">
          {currentStage !== 'processing' && currentStage !== 'process_complete' && currentStage !== 'cancelled' && (
            <div>
              <label className="settings-label">要扫描的目录列表</label>
              {directories.length === 0 && <p className="text-xs text-neutral-500 italic mb-1">暂无目录。请添加目录以开始。</p>}
              <div className="space-y-1 max-h-32 overflow-y-auto pr-1 settings-scroll-container">
                {directories.map(dir => (
                  <div key={dir} className="flex items-center justify-between bg-[#2d2d2d] p-1.5 rounded-md border border-[#4f4f4f] text-sm">
                    <span className="text-neutral-100 truncate" title={dir}>{dir}</span>
                    <button onClick={() => handleRemoveDirectory(dir)} disabled={isBusy} className="text-red-400 hover:text-red-300 font-semibold ml-2 p-0.5 text-xs disabled:opacity-50"><LuTrash2 size={14}/></button>
                  </div>
                ))}
              </div>
              <div className="flex items-center gap-2 mt-2">
                <input type="text" value={newDirectory} onChange={(e) => setNewDirectory(e.target.value)} placeholder="输入新目录路径或点击浏览..." className="form-input-app flex-grow text-sm"/>
                <button onClick={handleAddDirectory} disabled={isBusy} className="button-secondary-app px-3 py-1.5 text-sm flex items-center"><LuFolderPlus size={16} className="mr-1"/>添加/浏览目录</button>
              </div>
            </div>
          )}

          {currentStage === 'scanning' && scanProgress && (
            <div className="mt-3 p-3 bg-black/20 rounded-md border border-sky-500/30">
              <h4 className="text-sm font-semibold text-sky-300 mb-1.5">扫描进度</h4>
              <p className="text-xs text-neutral-300">目录: <span className="font-medium text-sky-400">{scanProgress.currentDirectory ? getBasename(scanProgress.currentDirectory) : 'N/A'}</span> ({scanProgress.currentDirIndex}/{scanProgress.totalDirectories})</p>
              {scanProgress.currentFileScanning && <p className="text-xs text-neutral-300">正检查: <span className="font-medium text-neutral-100 truncate" title={scanProgress.currentFileScanning}>{getBasename(scanProgress.currentFileScanning)}</span></p>}
              <p className="text-xs text-neutral-300">已找到NFO文件总数: <span className="font-medium">{scanProgress.nfoFilesFoundTotal}</span></p>
            </div>
          )}

          {currentStage === 'scan_complete' && scanResult && !scanResult.cancelled && (
            <div className="mt-3 p-3 bg-green-800/30 border border-green-600/50 rounded-md text-sm">
              <h4 className="text-md font-semibold text-green-300 mb-1 flex items-center"><LuCheck size={18} className="mr-1.5"/>扫描完成！</h4>
              <p>在 {directories.length} 个目录中总共找到 <strong className="text-white">{scanResult.totalNfoFilesFound}</strong> 个NFO文件。</p>
              {scanResult.totalNfoFilesFound === 0 && <p className="mt-1">没有找到NFO文件，无法进行下一步。</p>}
            </div>
          )}
          
          {currentStage === 'processing' && processProgress && (
             <div className="mt-3 p-3 bg-black/20 rounded-md border border-purple-500/30">
              <h4 className="text-sm font-semibold text-purple-300 mb-1.5">润色处理进度</h4>
              <p className="text-xs text-neutral-300">进度: {processProgress.filesProcessedSoFar} / {processProgress.totalFilesToProcess}</p>
              {processProgress.currentNfoFileProcessing && <p className="text-xs text-neutral-300">当前文件: <span className="font-medium text-neutral-100">{processProgress.currentNfoFileProcessing}</span></p>}
              <p className="text-xs text-neutral-300">状态: <span className="font-medium">{processProgress.statusMessage}</span></p>
            </div>
          )}

          {(currentStage === 'process_complete' || (currentStage === 'cancelled' && processResult)) && processResult && (
            <div className={`mt-3 p-3 rounded-md text-sm ${processResult.cancelled ? 'bg-orange-800/30 border-orange-600/50' : 'bg-green-800/30 border-green-600/50'}`}>
              <h4 className={`text-md font-semibold mb-1 flex items-center ${processResult.cancelled ? 'text-orange-300' : 'text-green-300'}`}>
                {processResult.cancelled ? <LuX size={18} className="mr-1.5"/> : <LuCheck size={18} className="mr-1.5"/>}
                润色处理{processResult.cancelled ? '已取消' : '完成'}！
              </h4>
              <p>计划处理NFO文件: {processResult.totalFilesToProcess}</p>
              <p>成功更新: <strong className="text-white">{processResult.totalSuccessfullyUpdated}</strong></p>
              <p>跳过 (已处理过或空剧情): {processResult.totalSkipped}</p>
              <p>处理失败: <strong className="text-red-400">{processResult.totalErrors}</strong></p>
            </div>
          )}
           {currentStage === 'cancelled' && scanResult && !processResult && ( // Cancelled during scan
             <div className="mt-3 p-3 bg-orange-800/30 border border-orange-600/50 rounded-md text-sm">
               <h4 className="text-md font-semibold text-orange-300 mb-1 flex items-center"><LuX size={18} className="mr-1.5"/>扫描已取消！</h4>
               <p>部分目录可能已扫描，找到 <strong className="text-white">{scanResult.totalNfoFilesFound}</strong> 个NFO文件（可能不完整）。</p>
             </div>
           )}
          
          {errors.length > 0 && (
            <details className="mt-3" open={errors.some(e => e.filePath !== null) || currentStage === 'process_complete' || currentStage === 'cancelled'}>
              <summary className="text-sm font-semibold text-red-400 cursor-pointer hover:text-red-300 flex items-center"><LuTriangle size={16} className="mr-1.5"/>处理错误 ({errors.length})</summary>
              <div className="mt-1 text-xs bg-red-900/30 border border-red-700/50 text-red-300 p-2 rounded-md max-h-28 overflow-y-auto settings-scroll-container space-y-0.5">
                {errors.map((err, idx) => <p key={idx} className="break-all"><strong>{err.filePath ? getBasename(err.filePath) : '系统错误'}:</strong> {err.error}</p>)}
              </div>
            </details>
          )}

          <div className="mt-3">
            <h4 className="text-sm font-semibold text-neutral-300 mb-1 flex items-center"><LuChevronsUpDown size={16} className="mr-1.5"/>处理日志</h4>
            <div ref={logAreaRef} className="text-xs bg-black/30 border border-neutral-600 p-2 rounded-md h-40 overflow-y-auto settings-scroll-container space-y-0.5">
              {logs.map((logMsg, idx) => <p key={idx} className="whitespace-pre-wrap break-all">{logMsg}</p>)}
            </div>
          </div>

        </div>

        <div className="p-4 border-t border-[#3a3a3a] bg-[#2a2a2a] flex justify-between items-center space-x-3">
          <button onClick={handleResetTool} disabled={isBusy} className="button-warning-app px-4 py-2 text-sm disabled:opacity-50">重置工具</button>
          <div className="flex space-x-3">
            {isBusy && (<button onClick={handleCancelOperation} className="button-danger-app px-4 py-2 text-sm flex items-center"><LuX size={16} className="mr-1.5"/>取消操作</button>)}
            <button onClick={handleCloseModal} className="button-neutral-app px-4 py-2 text-sm">关闭</button>
            {currentStage === 'idle' || currentStage === 'process_complete' || currentStage === 'cancelled' ? (
              <button 
                onClick={handleStartScanning} 
                disabled={isBusy || directories.length === 0} 
                className="button-primary-app px-6 py-2 text-sm flex items-center disabled:opacity-50"
              >
                <LuScanSearch size={16} className="mr-1.5"/>扫描NFO文件
              </button>
            ) : currentStage === 'scan_complete' && !scanResult?.cancelled ? (
              <button 
                onClick={handleStartProcessing} 
                disabled={isBusy || !scanResult || scanResult.totalNfoFilesFound === 0}
                className="button-success-app px-6 py-2 text-sm flex items-center disabled:opacity-50"
              >
                <LuBrainCircuit size={16} className="mr-1.5"/>开始润色处理 ({scanResult?.totalNfoFilesFound || 0})
              </button>
            ) : (
              <button 
                className="button-primary-app px-6 py-2 text-sm flex items-center disabled:opacity-50"
                disabled={true}
              >
                {currentStage === 'scanning' ? '扫描中...' : (currentStage === 'processing' ?  '处理中...' : '等待操作...')}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NfoPlotPolisherTool;