/**
 * 文件重命名管理器 - 专门处理文件重命名相关功能
 * 
 * 从 collectorService.js 中提取的文件重命名相关逻辑，包括：
 * - 标准化文件名生成
 * - 文件重命名验证
 * - 重复文件处理
 * - 文件名清理
 */

const path = require('path');
const fs = require('fs');
const fileNameBuilder = require('../utils/fileNameBuilder');

class FileRenameManager {
  constructor(config) {
    this.log = config.log;
    this.maxFileNameLength = 255;
  }

  /**
   * 生成标准化的文件名 - 带校验
   * @param {Object} postData - 帖子数据
   * @param {string} fileExtension - 文件扩展名
   * @param {string} basePath - 基础路径（用于路径长度计算）
   * @returns {Object} 包含文件名和校验信息的对象
   */
  generateStandardFileName(postData, fileExtension, basePath = null) {
    // 数据校验
    if (!postData) {
      this.log.error('[FileRenameManager] ❌ 文件重命名失败：postData为空');
      return {
        success: false,
        error: 'postData为空',
        fileName: null
      };
    }

    if (!fileExtension) {
      this.log.warn('[FileRenameManager] ⚠️ 文件扩展名为空，使用默认扩展名 rar');
      fileExtension = 'rar';
    }

    try {
      // 使用标准化文件名构建器
      this.log.info('[FileRenameManager] 🔍 使用标准化文件名构建器生成文件名');
      const standardFileName = fileNameBuilder.buildStandardFileName(postData);

      // 添加扩展名
      const fileName = `${standardFileName}.${fileExtension}`;

      // 最终校验 - 🔧 修复：检查完整路径长度，不仅仅是文件名长度
      let finalFileName = fileName;

      // 检查文件名长度
      if (fileName.length > this.maxFileNameLength) {
        this.log.warn('[FileRenameManager] ⚠️ 文件名过长，进行截断处理');
        const maxBaseLength = this.maxFileNameLength - fileExtension.length - 1;
        const baseName = standardFileName.substring(0, maxBaseLength);
        finalFileName = `${baseName}.${fileExtension}`;
      }

      // 🔧 修复：检查完整路径长度（Windows 260字符限制）
      // 使用更保守的路径长度限制，特别是对于包含中文字符的文件名
      const maxPathLength = 200; // 限制200字符，为Windows路径限制留出安全边距
      const estimatedBasePath = basePath || 'D:\\Users\\magic\\Desktop\\测试\\c1\\attachments\\'; // 使用实际基础路径
      const estimatedFullPathLength = estimatedBasePath.length + finalFileName.length;

      this.log.info(`[FileRenameManager] 🔍 路径长度检查: 基础路径=${estimatedBasePath.length}, 文件名=${finalFileName.length}, 总长度=${estimatedFullPathLength}`);

      if (estimatedFullPathLength > maxPathLength) {
        this.log.warn(`[FileRenameManager] ⚠️ 完整路径过长 (${estimatedFullPathLength} > ${maxPathLength})，进行激进截断`);
        // 计算可用的文件名长度
        const availableLength = maxPathLength - estimatedBasePath.length - fileExtension.length - 1;
        this.log.warn(`[FileRenameManager] ⚠️ 可用文件名长度: ${availableLength}`);

        if (availableLength > 20) { // 降低最小文件名长度要求
          const truncatedBase = standardFileName.substring(0, availableLength);
          finalFileName = `${truncatedBase}.${fileExtension}`;
          this.log.warn(`[FileRenameManager] ⚠️ 激进截断后的文件名: ${finalFileName}`);
        } else {
          // 如果连20个字符都放不下，使用极简文件名
          const nfoId = postData.nfoId || 'UNKNOWN';
          const timestamp = Date.now().toString().slice(-6); // 使用时间戳后6位
          finalFileName = `[${nfoId}]_${timestamp}.${fileExtension}`;
          this.log.warn(`[FileRenameManager] ⚠️ 使用极简文件名: ${finalFileName}`);
        }
      }

      this.log.info('[FileRenameManager] ✅ 生成的最终文件名:', finalFileName);

      return {
        success: true,
        fileName: finalFileName,
        originalTitle: postData.postTitle,
        cleanTitle: postData.postTitle, // 保持兼容性
        nfoId: postData.nfoId || null,
        password: postData.decompressionPassword || null
      };

    } catch (error) {
      this.log.error('[FileRenameManager] ❌ 标准化文件名生成失败:', error.message);
      this.log.error('[FileRenameManager] ❌ 错误堆栈:', error.stack);
      this.log.error('[FileRenameManager] ❌ postData内容:', JSON.stringify(postData, null, 2));

      // 回退逻辑使用原始标题
      this.log.warn('[FileRenameManager] ⚠️ 使用回退逻辑：直接使用原始标题');
      const fallbackFileName = this.sanitizeFileName(postData.postTitle || `fallback_${Date.now()}`);
      const fileName = `${fallbackFileName}.${fileExtension}`;

      return {
        success: false, // 表示使用了回退逻辑
        fileName: fileName,
        originalTitle: postData.postTitle,
        cleanTitle: postData.postTitle,
        nfoId: postData.nfoId || null,
        password: postData.decompressionPassword || null,
        usedFallback: true,
        error: error.message
      };
    }
  }

  /**
   * 清理文件名中的非法字符
   * @param {string} fileName - 原始文件名
   * @returns {string} 清理后的文件名
   */
  sanitizeFileName(fileName) {
    if (!fileName) {
      return `unnamed_${Date.now()}`;
    }

    // 移除或替换非法字符
    let sanitized = fileName
      .replace(/[<>:"/\\|?*]/g, '_') // 替换Windows非法字符
      .replace(/[\x00-\x1f\x80-\x9f]/g, '') // 移除控制字符
      .replace(/^\.+/, '') // 移除开头的点
      .replace(/\.+$/, '') // 移除结尾的点
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();

    // 确保文件名不为空
    if (!sanitized) {
      sanitized = `unnamed_${Date.now()}`;
    }

    // 限制长度
    if (sanitized.length > 200) {
      sanitized = sanitized.substring(0, 200);
    }

    return sanitized;
  }

  /**
   * 验证文件重命名操作的安全性
   * @param {string} originalPath - 原始文件路径
   * @param {string} newPath - 新文件路径
   * @param {Object} postData - 帖子数据
   * @returns {Object} 验证结果
   */
  validateFileRename(originalPath, newPath, postData) {
    // 检查原始文件是否存在
    if (!fs.existsSync(originalPath)) {
      return {
        shouldProceed: false,
        error: '原始文件不存在',
        originalPath,
        newPath
      };
    }

    // 检查目标路径是否已存在
    if (fs.existsSync(newPath)) {
      this.log.warn(`[FileRenameManager] ⚠️ 目标文件已存在: ${newPath}`);
      return {
        shouldProceed: false,
        error: '目标文件已存在',
        originalPath,
        newPath
      };
    }

    // 检查目标目录是否存在
    const targetDir = path.dirname(newPath);
    if (!fs.existsSync(targetDir)) {
      try {
        fs.mkdirSync(targetDir, { recursive: true });
        this.log.info(`[FileRenameManager] 创建目标目录: ${targetDir}`);
      } catch (error) {
        return {
          shouldProceed: false,
          error: `无法创建目标目录: ${error.message}`,
          originalPath,
          newPath
        };
      }
    }

    // 检查文件大小（基本的完整性检查）
    try {
      const stats = fs.statSync(originalPath);
      if (stats.size === 0) {
        this.log.warn(`[FileRenameManager] ⚠️ 原始文件大小为0: ${originalPath}`);
        return {
          shouldProceed: false,
          error: '原始文件大小为0，可能下载不完整',
          originalPath,
          newPath
        };
      }

      // 检查文件名长度
      const newFileName = path.basename(newPath);
      if (newFileName.length > this.maxFileNameLength) {
        return {
          shouldProceed: false,
          error: `文件名过长 (${newFileName.length} > ${this.maxFileNameLength})`,
          originalPath,
          newPath
        };
      }

      this.log.info(`[FileRenameManager] ✅ 重命名验证通过: ${path.basename(originalPath)} -> ${newFileName}`);
      return {
        shouldProceed: true,
        originalPath,
        newPath,
        fileSize: stats.size
      };

    } catch (error) {
      return {
        shouldProceed: false,
        error: `文件状态检查失败: ${error.message}`,
        originalPath,
        newPath
      };
    }
  }

  /**
   * 安全地重命名文件
   * @param {string} originalPath - 原始文件路径
   * @param {string} newPath - 新文件路径
   * @param {Object} postData - 帖子数据（用于验证）
   * @returns {Object} 重命名结果
   */
  safeRenameFile(originalPath, newPath, postData = null) {
    try {
      // 验证重命名操作
      const validation = this.validateFileRename(originalPath, newPath, postData);
      if (!validation.shouldProceed) {
        this.log.error(`[FileRenameManager] ❌ 重命名验证失败: ${validation.error}`);
        return {
          success: false,
          error: validation.error,
          originalPath,
          newPath
        };
      }

      // 执行重命名
      fs.renameSync(originalPath, newPath);
      
      // 验证重命名是否成功
      if (fs.existsSync(newPath) && !fs.existsSync(originalPath)) {
        this.log.info(`[FileRenameManager] ✅ 文件重命名成功: ${path.basename(originalPath)} -> ${path.basename(newPath)}`);
        return {
          success: true,
          originalPath,
          newPath,
          fileName: path.basename(newPath)
        };
      } else {
        throw new Error('重命名后文件状态异常');
      }

    } catch (error) {
      this.log.error(`[FileRenameManager] ❌ 文件重命名失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        originalPath,
        newPath
      };
    }
  }

  /**
   * 处理重复文件名
   * @param {string} basePath - 基础路径
   * @param {string} fileName - 文件名
   * @returns {string} 处理后的唯一文件名
   */
  handleDuplicateFileName(basePath, fileName) {
    const fullPath = path.join(basePath, fileName);
    
    if (!fs.existsSync(fullPath)) {
      return fileName;
    }

    const ext = path.extname(fileName);
    const nameWithoutExt = path.basename(fileName, ext);
    
    let counter = 1;
    let newFileName;
    let newFullPath;

    do {
      newFileName = `${nameWithoutExt}_${counter}${ext}`;
      newFullPath = path.join(basePath, newFileName);
      counter++;
    } while (fs.existsSync(newFullPath) && counter < 1000); // 防止无限循环

    if (counter >= 1000) {
      // 如果尝试了1000次还是重复，使用时间戳
      const timestamp = Date.now();
      newFileName = `${nameWithoutExt}_${timestamp}${ext}`;
    }

    this.log.info(`[FileRenameManager] 处理重复文件名: ${fileName} -> ${newFileName}`);
    return newFileName;
  }

  /**
   * 批量重命名文件
   * @param {Array} renameList - 重命名列表 [{originalPath, newPath, postData}]
   * @returns {Object} 批量重命名结果
   */
  batchRenameFiles(renameList) {
    const results = {
      success: [],
      failed: [],
      total: renameList.length
    };

    for (const item of renameList) {
      const result = this.safeRenameFile(item.originalPath, item.newPath, item.postData);
      
      if (result.success) {
        results.success.push({
          ...result,
          postData: item.postData
        });
      } else {
        results.failed.push({
          ...result,
          postData: item.postData
        });
      }
    }

    this.log.info(`[FileRenameManager] 批量重命名完成: 成功 ${results.success.length}/${results.total}, 失败 ${results.failed.length}/${results.total}`);
    
    return results;
  }

  /**
   * 获取文件扩展名
   * @param {string} fileName - 文件名
   * @returns {string} 文件扩展名（不包含点）
   */
  getFileExtension(fileName) {
    if (!fileName) return 'rar'; // 默认扩展名
    
    const ext = path.extname(fileName).toLowerCase();
    return ext.startsWith('.') ? ext.substring(1) : ext || 'rar';
  }

  /**
   * 验证文件名是否合法
   * @param {string} fileName - 文件名
   * @returns {Object} 验证结果
   */
  validateFileName(fileName) {
    if (!fileName) {
      return { valid: false, error: '文件名为空' };
    }

    if (fileName.length > this.maxFileNameLength) {
      return { valid: false, error: `文件名过长 (${fileName.length} > ${this.maxFileNameLength})` };
    }

    // 检查非法字符
    const illegalChars = /[<>:"/\\|?*\x00-\x1f\x80-\x9f]/;
    if (illegalChars.test(fileName)) {
      return { valid: false, error: '文件名包含非法字符' };
    }

    // 检查保留名称（Windows）
    const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
    if (reservedNames.test(fileName)) {
      return { valid: false, error: '文件名是系统保留名称' };
    }

    return { valid: true };
  }
}

module.exports = FileRenameManager;
