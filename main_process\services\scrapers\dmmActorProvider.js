// main_process/services/scrapers/dmmActorProvider.js
// DMM演员页面刮削器 - 获取演员完整作品列表

const log = require('electron-log');
const { chromium } = require('playwright');
const cheerio = require('cheerio');

/**
 * 生成日文演员名变体
 * @param {string} actorName - 演员姓名
 * @returns {Array<string>} 变体列表
 */
function generateJapaneseVariants(actorName) {
  const variants = [];

  // 常见的中日文对应关系
  const chineseToJapanese = {
    '河北': 'かわきた',
    '彩花': 'あやか',
    '彩伽': 'あやか',
    '花': 'はな',
    '美': 'み',
    '子': 'こ',
    '香': 'か',
    '里': 'り',
    '奈': 'な',
    '愛': 'あい',
    '優': 'ゆう',
    '真': 'まこと',
    '由': 'ゆ',
    '紀': 'き',
    '恵': 'めぐみ'
  };

  // 移除括号内容
  const cleanName = actorName.replace(/[（(].*?[）)]/g, '').trim();

  // 如果是中文名，尝试生成可能的日文读音
  if (/[\u4e00-\u9fff]/.test(cleanName)) {
    // 简单的字符替换
    let jpVariant = cleanName;
    for (const [chinese, japanese] of Object.entries(chineseToJapanese)) {
      jpVariant = jpVariant.replace(new RegExp(chinese, 'g'), japanese);
    }
    if (jpVariant !== cleanName) {
      variants.push(jpVariant);
    }
  }

  return variants;
}

const PROVIDER_NAME = 'DMM演员刮削器';
const PROVIDER_VERSION = '1.0.0';

/**
 * 刮削演员在DMM上的完整作品列表
 * @param {string} actorName - 演员姓名
 * @returns {Promise<Object>} 刮削结果
 */
async function scrapeActorFilmography(actorName) {
  log.info(`[${PROVIDER_NAME}] 开始刮削演员作品列表: ${actorName}`);
  
  let browser = null;
  let context = null;
  
  try {
    // 启动浏览器
    browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    });
    
    const page = await context.newPage();
    
    // 智能处理演员姓名：生成多个搜索候选
    const searchCandidates = [];

    // 1. 原始名称
    searchCandidates.push(actorName);

    // 2. 括号内的别名
    const aliasMatch = actorName.match(/[（(](.*?)[）)]/);
    if (aliasMatch) {
      const alias = aliasMatch[1].trim();
      searchCandidates.push(alias);
      log.info(`[${PROVIDER_NAME}] 检测到别名: "${alias}"`);
    }

    // 3. 移除括号的名称
    const cleanName = actorName.replace(/[（(].*?[）)]/g, '').trim();
    if (cleanName !== actorName) {
      searchCandidates.push(cleanName);
    }

    // 4. 尝试常见的日文转换
    const jpVariants = generateJapaneseVariants(actorName);
    searchCandidates.push(...jpVariants);

    log.info(`[${PROVIDER_NAME}] 搜索候选名称: ${searchCandidates.join(', ')}`);

    // 尝试每个候选名称
    let movies = [];
    let foundMovies = false;

    for (const searchName of searchCandidates) {
      if (foundMovies) break; // 如果已经找到作品，停止搜索

      log.info(`[${PROVIDER_NAME}] 尝试搜索: "${searchName}"`);

      const searchUrl = `https://www.dmm.co.jp/digital/videoa/-/list/=/article=actress/id=${encodeURIComponent(searchName)}/`;
      log.info(`[${PROVIDER_NAME}] 访问演员页面: ${searchUrl}`);

      await page.goto(searchUrl, { timeout: 30000, waitUntil: 'domcontentloaded' });

      // 等待页面加载
      await page.waitForTimeout(2000);

      const content = await page.content();

      // 检查地域限制
      if (content.includes('foreignError')) {
        log.warn(`[${PROVIDER_NAME}] 地域限制，请使用日本节点访问！`);
        return {
          success: false,
          error: '地域限制，请使用日本节点访问',
          data: []
        };
      }

      const $ = cheerio.load(content);

      // 提取作品列表
      const currentMovies = [];
    
      // DMM作品列表选择器
      const movieSelectors = [
        '.d-item',
        '.productBox',
        '.tmb',
        'li[data-product-id]'
      ];

      let currentFoundMovies = false;

      for (const selector of movieSelectors) {
        const movieElements = $(selector);
        if (movieElements.length > 0) {
          log.info(`[${PROVIDER_NAME}] 使用选择器 ${selector} 找到 ${movieElements.length} 个作品`);

          movieElements.each((index, element) => {
            const movie = extractMovieInfo($, element);
            if (movie) {
              currentMovies.push(movie);
            }
          });
        
          currentFoundMovies = true;
          break;
        }
      }

      if (currentFoundMovies && currentMovies.length > 0) {
        log.info(`[${PROVIDER_NAME}] 使用搜索名称 "${searchName}" 找到 ${currentMovies.length} 个作品`);
        movies = currentMovies;
        foundMovies = true;
        break; // 找到作品后停止尝试其他候选名称
      } else {
        log.warn(`[${PROVIDER_NAME}] 搜索名称 "${searchName}" 未找到作品`);
      }
    }

    if (!foundMovies) {
      log.warn(`[${PROVIDER_NAME}] 所有搜索候选都未找到演员作品列表`);
      return {
        success: false,
        error: '未找到演员作品列表',
        data: []
      };
    }

    // 去重
    const uniqueMovies = removeDuplicates(movies);

    log.info(`[${PROVIDER_NAME}] 成功刮削演员 ${actorName} 的作品: ${uniqueMovies.length} 部`);
    
    return {
      success: true,
      data: uniqueMovies,
      actorName: actorName,
      source: 'dmm'
    };
    
  } catch (error) {
    log.error(`[${PROVIDER_NAME}] 刮削失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      data: []
    };
  } finally {
    if (context) await context.close();
    if (browser) await browser.close();
  }
}

/**
 * 从单个作品元素中提取信息
 * @param {Object} $ - Cheerio对象
 * @param {Object} element - 作品元素
 * @returns {Object|null} 作品信息
 */
function extractMovieInfo($, element) {
  try {
    const $el = $(element);
    
    // 提取标题
    const titleSelectors = [
      '.tmb a[title]',
      '.productTitle a',
      'a[title]',
      '.title a'
    ];
    
    let title = '';
    let detailUrl = '';
    
    for (const selector of titleSelectors) {
      const titleEl = $el.find(selector).first();
      if (titleEl.length > 0) {
        title = titleEl.attr('title') || titleEl.text().trim();
        detailUrl = titleEl.attr('href') || '';
        break;
      }
    }
    
    if (!title) return null;
    
    // 提取番号
    const nfoId = extractNfoId(title, detailUrl);
    
    // 提取封面
    const coverSelectors = [
      'img[data-src]',
      'img[src]',
      '.tmb img'
    ];
    
    let coverUrl = '';
    for (const selector of coverSelectors) {
      const imgEl = $el.find(selector).first();
      if (imgEl.length > 0) {
        coverUrl = imgEl.attr('data-src') || imgEl.attr('src') || '';
        if (coverUrl && !coverUrl.startsWith('http')) {
          coverUrl = 'https:' + coverUrl;
        }
        break;
      }
    }
    
    // 提取发售日期
    const dateSelectors = [
      '.date',
      '.release-date',
      '.tmb-date'
    ];
    
    let releaseDate = '';
    for (const selector of dateSelectors) {
      const dateEl = $el.find(selector).first();
      if (dateEl.length > 0) {
        releaseDate = dateEl.text().trim();
        break;
      }
    }
    
    return {
      nfoId: nfoId,
      title: title.trim(),
      coverUrl: coverUrl,
      releaseDate: releaseDate,
      detailUrl: detailUrl.startsWith('http') ? detailUrl : `https://www.dmm.co.jp${detailUrl}`,
      source: 'dmm'
    };
    
  } catch (error) {
    log.error(`[${PROVIDER_NAME}] 提取作品信息失败: ${error.message}`);
    return null;
  }
}

/**
 * 从标题或URL中提取番号
 * @param {string} title - 标题
 * @param {string} url - URL
 * @returns {string} 番号
 */
function extractNfoId(title, url) {
  // 从URL中提取
  const urlMatch = url.match(/cid=([^\/&]+)/);
  if (urlMatch) {
    return urlMatch[1].toUpperCase();
  }
  
  // 从标题中提取
  const titleMatch = title.match(/([A-Z]+-\d+)/i);
  if (titleMatch) {
    return titleMatch[1].toUpperCase();
  }
  
  return '';
}

/**
 * 去除重复作品
 * @param {Array} movies - 作品数组
 * @returns {Array} 去重后的作品数组
 */
function removeDuplicates(movies) {
  const seen = new Set();
  return movies.filter(movie => {
    const key = movie.nfoId || movie.title;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

module.exports = {
  name: PROVIDER_NAME,
  version: PROVIDER_VERSION,
  scrapeActorFilmography
};
