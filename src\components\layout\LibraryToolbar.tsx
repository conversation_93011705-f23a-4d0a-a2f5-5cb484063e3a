import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rid3X<PERSON>, <PERSON><PERSON><PERSON>, LuArrowUpDown } from 'react-icons/lu';

interface LibraryToolbarProps {
  // 搜索相关
  searchQuery: string;
  onSearchChange: (query: string) => void;
  
  // 视图模式
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  
  // 筛选和排序
  onFilterClick: () => void;
  onSortClick: () => void;
  
  // 其他控件
  hasActiveFilters?: boolean;
}

const LibraryToolbar: React.FC<LibraryToolbarProps> = ({
  searchQuery,
  onSearchChange,
  viewMode,
  onViewModeChange,
  onFilterClick,
  onSortClick,
  hasActiveFilters = false
}) => {
  return (
    <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between gap-4">
          
          {/* 左侧：搜索框 */}
          <div className="flex-1 max-w-md">
            <div className="relative">
              <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索影片、演员、番号..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
              />
            </div>
          </div>

          {/* 右侧：控制按钮 */}
          <div className="flex items-center gap-2">
            
            {/* 筛选按钮 */}
            <button
              onClick={onFilterClick}
              className={`px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                hasActiveFilters 
                  ? 'bg-[#B8860B] text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              title="高级筛选"
            >
              <LuFilter className="h-4 w-4" />
              <span className="hidden sm:inline">筛选</span>
              {hasActiveFilters && (
                <span className="bg-white text-[#B8860B] text-xs px-1.5 py-0.5 rounded-full font-medium">
                  !
                </span>
              )}
            </button>

            {/* 排序按钮 */}
            <button
              onClick={onSortClick}
              className="px-3 py-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors flex items-center gap-2"
              title="排序选项"
            >
              <LuArrowUpDown className="h-4 w-4" />
              <span className="hidden sm:inline">排序</span>
            </button>

            {/* 视图模式切换 */}
            <div className="flex bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => onViewModeChange('grid')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'grid' 
                    ? 'bg-[#B8860B] text-white' 
                    : 'text-gray-400 hover:text-white'
                }`}
                title="网格视图"
              >
                <LuGrid3X3 className="h-4 w-4" />
              </button>
              <button
                onClick={() => onViewModeChange('list')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'list' 
                    ? 'bg-[#B8860B] text-white' 
                    : 'text-gray-400 hover:text-white'
                }`}
                title="列表视图"
              >
                <LuList className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LibraryToolbar;
