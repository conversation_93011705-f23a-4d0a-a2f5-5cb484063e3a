
// soul-forge-electron/src/hooks/useModalManager.ts
import { useState, useCallback } from 'react';
import { Movie } from '../types';

export function useModalManager() {
  const [selectedMovie, setSelectedMovie] = useState<Movie | null>(null);
  const [currentNfoIdForGrouping, setCurrentNfoIdForGrouping] = useState<string | null>(null);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isCDModalOpen, setIsCDModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isLinLuoModalOpen, setIsLinLuoModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isImagePreviewModalOpen, setIsImagePreviewModalOpen] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [isRecommendationsModalOpen, setIsRecommendationsModalOpen] = useState(false);
  const [isNfoPlotPolisherModalOpen, setIsNfoPlotPolisherModalOpen] = useState(false);
  const [isScraperTestModalOpen, setIsScraperTestModalOpen] = useState(false);

  const openDetailModal = useCallback((movie: Movie) => {
    setSelectedMovie(movie);
    setIsDetailModalOpen(true);
  }, []);



  const openCDModal = useCallback((movie: Movie) => {
    setSelectedMovie(movie);
    setCurrentNfoIdForGrouping(movie.nfoId || null);
    setIsCDModalOpen(true);
  }, []);
  
  const openImagePreviewModal = useCallback((url: string) => {
    setImagePreviewUrl(url);
    setIsImagePreviewModalOpen(true);
  }, []);

  const closeModal = useCallback((modalType: 'detail' | 'cd' | 'settings' | 'linluo' | 'filter' | 'imagePreview' | 'recommendations' | 'nfoPlotPolisher' | 'scraperTest') => {
    switch (modalType) {
      case 'detail': setIsDetailModalOpen(false); setSelectedMovie(null); break;
      case 'cd': setIsCDModalOpen(false); setSelectedMovie(null); setCurrentNfoIdForGrouping(null); break;
      case 'settings': setIsSettingsModalOpen(false); break;
      case 'linluo': setIsLinLuoModalOpen(false); break;
      case 'filter': setIsFilterModalOpen(false); break;
      case 'imagePreview': setIsImagePreviewModalOpen(false); setImagePreviewUrl(null); break;
      case 'recommendations': setIsRecommendationsModalOpen(false); break;
      case 'nfoPlotPolisher': setIsNfoPlotPolisherModalOpen(false); break;
      case 'scraperTest': setIsScraperTestModalOpen(false); break;
    }
  }, []);

  return {
    selectedMovie,
    currentNfoIdForGrouping,
    isDetailModalOpen,
    isCDModalOpen,
    isSettingsModalOpen, setIsSettingsModalOpen,
    isLinLuoModalOpen, setIsLinLuoModalOpen,
    isFilterModalOpen, setIsFilterModalOpen,
    isImagePreviewModalOpen,
    imagePreviewUrl,
    isRecommendationsModalOpen, setIsRecommendationsModalOpen,
    isNfoPlotPolisherModalOpen, setIsNfoPlotPolisherModalOpen,
    isScraperTestModalOpen, setIsScraperTestModalOpen,
    openDetailModal,
    openCDModal,
    openImagePreviewModal,
    closeModal,
    setSelectedMovie, // Expose if needed for direct manipulation
    setCurrentNfoIdForGrouping, // Expose if needed
  };
}
