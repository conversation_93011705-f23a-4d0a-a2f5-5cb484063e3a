// soul-forge-electron/src/components/LibraryCard.tsx
import React from 'react';
import { MovieLibrary } from '../types';
import { LuPlay, LuScanLine, LuFolder, LuFilm, LuTrash2 } from 'react-icons/lu'; 

interface LibraryCardProps {
  library: MovieLibrary;
  onClick: () => void;
  onScan: () => void;
  onDelete?: (libraryId: string) => void; 
  isScanningActiveLibrary?: boolean;
}

const LibraryCard: React.FC<LibraryCardProps> = ({ library, onClick, onScan, onDelete, isScanningActiveLibrary }) => {
  const handleScanClick = (e: React.MouseEvent) => {
    e.stopPropagation(); 
    onScan();
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      if (window.confirm(`确定要删除片库 "${library.name}" 吗？此操作也会删除片库与影片的关联，但不会删除影片文件本身。`)) {
        onDelete(library.id);
      }
    }
  };

  const placeholderCover = (
    <div className="absolute inset-0 bg-gradient-to-br from-neutral-700 to-neutral-800 flex items-center justify-center opacity-50 group-hover:opacity-30 transition-opacity duration-300">
      <LuFolder size={48} className="text-neutral-500" />
    </div>
  );

  return (
    <div
      className="bg-[#2c2c2c] rounded-lg border border-[#444444] shadow-lg hover:shadow-xl hover:shadow-amber-500/20 overflow-hidden transform hover:scale-[1.03] transition-all duration-300 ease-in-out group cursor-pointer relative aspect-[16/10] flex flex-col justify-between"
      onClick={onClick}
      aria-label={`片库: ${library.name}`}
    >
      <div className="absolute inset-0 w-full h-full">
        {library.coverMovies && library.coverMovies.length > 0 && library.coverMovies[0].coverDataUrl ? (
          <img
            src={library.coverMovies[0].coverDataUrl}
            alt={`${library.name} 片库封面`}
            className="w-full h-full object-cover object-center opacity-30 group-hover:opacity-50 transition-opacity duration-300 blur-sm scale-110"
          />
        ) : (
          placeholderCover
        )}
      </div>
      
      <div className="relative z-10 p-4 flex flex-col justify-between h-full bg-black/40 group-hover:bg-black/60 transition-colors duration-300">
        <div>
          <h3 className="text-lg sm:text-xl font-semibold text-white group-hover:text-amber-300 transition-colors duration-200 truncate" title={library.name}>
            {library.name}
          </h3>
          <p className="text-xs text-neutral-300 mt-1 flex items-center">
            <LuFilm size={12} className="mr-1 opacity-70" />
            {library.movieCount !== undefined ? `${library.movieCount} 部影片` : '统计中...'}
          </p>
        </div>

        <div className="mt-auto flex items-center justify-end space-x-2">
           {onDelete && (
            <button
                onClick={handleDeleteClick}
                className="bg-red-700/70 hover:bg-red-600 text-white text-xs font-medium py-1 px-2 rounded-md shadow-md transition-all hover:scale-105 active:scale-95 flex items-center"
                title={`删除片库 ${library.name}`}
            >
                <LuTrash2 size={14} />
            </button>
           )}
           <button
            onClick={handleScanClick}
            disabled={isScanningActiveLibrary}
            className="bg-sky-600/70 hover:bg-sky-500 text-white text-xs font-medium py-1 px-2.5 rounded-md shadow-md transition-all hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            title={isScanningActiveLibrary ? "扫描中..." : "扫描此片库"}
          >
            <LuScanLine size={14} className={`mr-1 ${isScanningActiveLibrary ? 'animate-ping' : ''}`} />
            {isScanningActiveLibrary ? "扫描中" : "扫描"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LibraryCard;