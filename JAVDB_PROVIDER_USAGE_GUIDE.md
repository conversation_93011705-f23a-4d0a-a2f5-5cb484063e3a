# JavDB Provider 使用指南

## 概述

JavDB Provider 已升级到 v1.1.0，采用更安全的 CDP (Chrome DevTools Protocol) 连接方式，参考了论坛B的最佳实践，能够有效应对 JavDB 网站的严格风控机制。

## 🔐 为什么需要手动登录？

JavDB 网站具有严格的反爬虫机制：
- **Cloudflare 防护**: 会检测自动化访问
- **IP 限制**: 频繁请求会被暂时封禁
- **登录要求**: 部分内容需要登录才能访问
- **VIP 限制**: 某些内容需要 VIP 权限

## 🚀 使用步骤

### 第一步：启动 Chrome 调试模式

在命令行中执行以下命令启动 Chrome：

```bash
chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-debug"
```

或者创建 Chrome 快捷方式，在目标路径后添加参数：
```
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-debug"
```

### 第二步：手动登录 JavDB

1. 在启动的 Chrome 中访问 https://javdb.com
2. 完成登录（如果有账号的话）
3. 确保能正常浏览内容
4. 可以尝试搜索一个番号，确认网站功能正常

### 第三步：使用 SoulForge 刮削功能

现在可以在 SoulForge 中正常使用刮削功能：
- JavDB Provider 会自动连接到您的 Chrome 浏览器
- 利用您已经建立的登录状态进行刮削
- 避免触发反爬虫机制

## 🔧 技术特性

### 安全连接方式
- 使用 CDP 协议连接到用户的 Chrome 实例
- 不启动新的浏览器，避免被检测为自动化
- 保持用户的登录状态和 Cookie

### 智能错误处理
- 自动检测各种访问限制
- 提供详细的错误信息和解决建议
- 优雅降级，不影响其他 Provider

### 磁力链接获取
- 使用安全的点击方式触发磁力链接显示
- 多种选择器策略确保兼容性
- 提取完整的链接信息（大小、字幕、文件名）

## 📋 功能特点

### 基础元数据
- ✅ 标题（中文/原文）
- ✅ 演员列表
- ✅ 发行日期
- ✅ 制作商
- ✅ 系列
- ✅ 导演
- ✅ 时长
- ✅ 标签
- ✅ 封面图片
- ✅ 预览图片

### JavDB 特色功能
- ✅ 用户评分
- ✅ 磁力链接（含文件大小、字幕信息）
- 🔄 用户评论（预留接口）
- 🔄 投票数（预留接口）

## ⚠️ 注意事项

### 网络环境
- 确保能够正常访问 javdb.com
- 如果在日本地区，可能需要使用代理
- 建议在非高峰时段使用

### 浏览器要求
- 必须使用 Chrome 浏览器
- 需要启用调试端口（9222）
- 建议使用独立的用户数据目录

### 使用频率
- 避免过于频繁的刮削请求
- 如果遇到限制，建议稍后重试
- 尊重网站的使用条款

## 🛠️ 故障排除

### 连接失败
**错误**: "请先启动 Chrome 浏览器并使用 --remote-debugging-port=9222 参数"

**解决方案**:
1. 确认 Chrome 已使用正确参数启动
2. 检查端口 9222 是否被占用
3. 尝试重启 Chrome

### 访问被拦截
**错误**: "被 Cloudflare 5 秒盾拦截"

**解决方案**:
1. 在浏览器中手动完成验证
2. 等待几分钟后重试
3. 考虑更换网络环境

### 需要登录
**错误**: "此內容需要登入才能查看"

**解决方案**:
1. 在浏览器中手动登录 JavDB
2. 确认登录状态有效
3. 重新尝试刮削

### 需要 VIP
**错误**: "需要 VIP 权限才能访问此内容"

**解决方案**:
1. 升级 JavDB VIP 账号
2. 尝试其他番号
3. 使用其他 Provider

## 🔄 版本历史

### v1.1.0 (当前版本)
- 采用 CDP 连接方式
- 增加用户登录检查
- 改进磁力链接获取
- 优化错误处理

### v1.0.0
- 初始版本
- 基础刮削功能
- 磁力链接支持

## 📞 技术支持

如果遇到问题：
1. 查看控制台日志获取详细错误信息
2. 确认网络连接和浏览器设置
3. 尝试手动在浏览器中访问相同内容
4. 考虑使用其他 Provider 作为备选方案

---

**提示**: JavDB Provider 是 SoulForge 刮削器系统的一部分，当其他 Provider 失败时会自动尝试使用。您也可以通过调整 Provider 优先级来优先使用 JavDB。
