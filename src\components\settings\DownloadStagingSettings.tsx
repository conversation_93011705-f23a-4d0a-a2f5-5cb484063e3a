import React, { useState, useEffect } from 'react';
import { Folder, Upload, Cloud, Save, RotateCcw, FolderDown } from 'lucide-react';

interface SaveStatus {
  type: 'success' | 'error' | null;
  message: string;
  timestamp: number;
}

interface DownloadStagingSettingsProps {
  className?: string;
}

export const DownloadStagingSettings: React.FC<DownloadStagingSettingsProps> = ({ 
  className = '' 
}) => {
  const [downloadStagingPath, setDownloadStagingPath] = useState<string>('');
  const [uploadQueuePath, setUploadQueuePath] = useState<string>('');
  const [cloud115RootPath, setCloud115RootPath] = useState<string>('/');
  const [importPath, setImportPath] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [saveStatus, setSaveStatus] = useState<SaveStatus | null>(null);

  // 加载当前设置
  useEffect(() => {
    loadCurrentSettings();
  }, []);

  const loadCurrentSettings = async () => {
    try {
      const settings = await window.sfeElectronAPI.getSettings();
      setDownloadStagingPath(settings.downloadStagingPath || '');
      setUploadQueuePath(settings.uploadQueuePath || '');
      setCloud115RootPath(settings.cloud115RootPath || '/');
      setImportPath(settings.importPath || '');
    } catch (error) {
      console.error('加载下载中转站设置失败:', error);
      setSaveStatus({
        type: 'error',
        message: '加载设置失败',
        timestamp: Date.now()
      });
    }
  };

  const handleSelectDownloadStagingPath = async () => {
    setIsLoading(true);
    try {
      const selectedPath = await window.sfeElectronAPI.dialogSelectDirectory();
      
      if (selectedPath) {
        setDownloadStagingPath(selectedPath);
        await saveSettings({
          downloadStagingPath: selectedPath
        });
      }
    } catch (error) {
      console.error('选择下载中转目录失败:', error);
      setSaveStatus({
        type: 'error',
        message: '选择文件夹失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectUploadQueuePath = async () => {
    setIsLoading(true);
    try {
      const selectedPath = await window.sfeElectronAPI.dialogSelectDirectory();
      
      if (selectedPath) {
        setUploadQueuePath(selectedPath);
        await saveSettings({
          uploadQueuePath: selectedPath
        });
      }
    } catch (error) {
      console.error('选择待上传队列目录失败:', error);
      setSaveStatus({
        type: 'error',
        message: '选择文件夹失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectImportPath = async () => {
    setIsLoading(true);
    try {
      const selectedPath = await window.sfeElectronAPI.dialogSelectDirectory();

      if (selectedPath) {
        setImportPath(selectedPath);
        await saveSettings({
          importPath: selectedPath
        });
      }
    } catch (error) {
      console.error('选择待导入目录失败:', error);
      setSaveStatus({
        type: 'error',
        message: '选择文件夹失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveCloud115RootPath = async () => {
    setIsLoading(true);
    try {
      await saveSettings({
        cloud115RootPath: cloud115RootPath
      });
    } catch (error) {
      console.error('保存115网盘根路径失败:', error);
      setSaveStatus({
        type: 'error',
        message: '保存失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: any) => {
    try {
      const currentSettings = await window.sfeElectronAPI.getSettings();
      const updatedSettings = {
        ...currentSettings,
        ...newSettings
      };
      
      const result = await window.sfeElectronAPI.saveSettings(updatedSettings);
      
      if (result.success) {
        setSaveStatus({
          type: 'success',
          message: '设置已保存',
          timestamp: Date.now()
        });
      } else {
        setSaveStatus({
          type: 'error',
          message: '保存失败',
          timestamp: Date.now()
        });
      }
    } catch (error) {
      setSaveStatus({
        type: 'error',
        message: '保存失败',
        timestamp: Date.now()
      });
    }
  };

  // 自动清除状态消息
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 标题和说明 */}
      <div>
        <h3 className="text-lg font-medium text-white mb-2">下载中转站设置</h3>
        <p className="text-sm text-gray-400 mb-4">
          配置下载完成文件的处理流程。系统将监视下载中转目录，自动识别新文件并提供一键处理功能。
        </p>
      </div>

      {/* 下载中转目录 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          <Folder className="inline h-4 w-4 mr-2" />
          下载中转目录
        </label>
        <p className="text-xs text-gray-500 mb-2">
          下载完成的媒体文件存放的临时目录，系统将扫描此目录中的新文件。
        </p>
        
        <div className="flex items-center gap-3">
          <input
            type="text"
            value={downloadStagingPath}
            readOnly
            placeholder="未设置下载中转目录"
            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B]"
          />
          
          <button
            onClick={handleSelectDownloadStagingPath}
            disabled={isLoading}
            className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
            ) : (
              <Folder className="h-4 w-4" />
            )}
            浏览...
          </button>
        </div>
      </div>

      {/* 待上传队列目录 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          <Upload className="inline h-4 w-4 mr-2" />
          待上传队列目录
        </label>
        <p className="text-xs text-gray-500 mb-2">
          处理后的媒体文件将移动到此目录，等待上传到云盘。
        </p>
        
        <div className="flex items-center gap-3">
          <input
            type="text"
            value={uploadQueuePath}
            readOnly
            placeholder="未设置待上传队列目录"
            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B]"
          />
          
          <button
            onClick={handleSelectUploadQueuePath}
            disabled={isLoading}
            className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
            ) : (
              <Upload className="h-4 w-4" />
            )}
            浏览...
          </button>
        </div>
      </div>

      {/* 待导入目录 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          <FolderDown className="inline h-4 w-4 mr-2" />
          待导入目录 (云端资产导入)
        </label>
        <p className="text-xs text-gray-500 mb-2">
          包含完整影片文件夹（含.strm和.nfo文件）的目录，系统将自动导入并迁移到标准位置。
        </p>

        <div className="flex items-center gap-3">
          <input
            type="text"
            value={importPath}
            readOnly
            placeholder="未设置待导入目录"
            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B]"
          />

          <button
            onClick={handleSelectImportPath}
            disabled={isLoading}
            className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
            ) : (
              <FolderDown className="h-4 w-4" />
            )}
            浏览...
          </button>
        </div>
      </div>

      {/* 115网盘根路径 */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-300">
          <Cloud className="inline h-4 w-4 mr-2" />
          115网盘根路径前缀
        </label>
        <p className="text-xs text-gray-500 mb-2">
          生成STRM文件时使用的115网盘路径前缀，例如 "/" 或 "/Movies/"。
        </p>

        <div className="flex items-center gap-3">
          <input
            type="text"
            value={cloud115RootPath}
            onChange={(e) => setCloud115RootPath(e.target.value)}
            placeholder="/"
            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B]"
          />

          <button
            onClick={handleSaveCloud115RootPath}
            disabled={isLoading}
            className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            保存
          </button>
        </div>
      </div>

      {/* 状态消息 */}
      {saveStatus && (
        <div className={`p-3 rounded-md ${
          saveStatus.type === 'success' 
            ? 'bg-green-900/50 border border-green-700 text-green-300' 
            : 'bg-red-900/50 border border-red-700 text-red-300'
        }`}>
          <div className="flex items-center gap-2">
            {saveStatus.type === 'success' ? (
              <div className="w-2 h-2 bg-green-400 rounded-full" />
            ) : (
              <div className="w-2 h-2 bg-red-400 rounded-full" />
            )}
            <span className="text-sm">{saveStatus.message}</span>
          </div>
        </div>
      )}
    </div>
  );
};
