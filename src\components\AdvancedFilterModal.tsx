// soul-forge-electron/src/components/AdvancedFilterModal.tsx
import React, { useState, useEffect } from 'react';
import { AdvancedFilterOptions } from '../types';
import MultiSelectDropdown from './MultiSelectDropdown'; 

interface AdvancedFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentFilters: AdvancedFilterOptions | null;
  onApplyFilters: (filters: AdvancedFilterOptions) => void;
  availableGenres: string[];
  availableTags: string[];
}

const resolutionPresetOptions = [
  "4K (2160p)", 
  "QHD (1440p)", 
  "FHD (1080p)", 
  "HD (720p)",
  "SD (<=480p)" 
];


const AdvancedFilterModal: React.FC<AdvancedFilterModalProps> = ({
  isOpen,
  onClose,
  currentFilters,
  onApplyFilters,
  availableGenres,
  availableTags,
}) => {
  const [localFilters, setLocalFilters] = useState<AdvancedFilterOptions>({});

  useEffect(() => {
    if (isOpen) {
      const initialRes = currentFilters?.resolution;
      // Ensure resolution is an array for MultiSelectDropdown
      const resolutionArray = Array.isArray(initialRes) ? initialRes : 
                              (initialRes ? [String(initialRes)] : []); // Convert single string to array if present
      setLocalFilters({ ...currentFilters, resolution: resolutionArray.length > 0 ? resolutionArray : undefined });
    }
  }, [isOpen, currentFilters]);

  if (!isOpen) return null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === "year" || name === "personalRating") {
        setLocalFilters(prev => ({ ...prev, [name]: value ? parseInt(value, 10) : undefined }));
    } else if (name === "filterWatchedStatus" && value === "all") {
        setLocalFilters(prev => {
            const { filterWatchedStatus, ...rest } = prev;
            return rest;
        });
    } else if (name === "filterRating" && value === "any") {
        setLocalFilters(prev => {
            const { filterRating, ...rest } = prev;
            return rest;
        });
    }
    else {
        setLocalFilters(prev => ({ ...prev, [name]: value || undefined }));
    }
  };

  const handleMultiSelectChange = (name: keyof Pick<AdvancedFilterOptions, 'selectedGenres' | 'selectedTags' | 'selectedActors' | 'resolution'>, selected: string[]) => {
    setLocalFilters(prev => ({ ...prev, [name]: selected.length > 0 ? selected : undefined }));
  };

  const handleApply = () => {
    const filtersToApply = { ...localFilters };
    // Ensure resolution is string[] or undefined. MultiSelectDropdown already provides string[]
    if (filtersToApply.resolution && filtersToApply.resolution.length === 0) {
        filtersToApply.resolution = undefined;
    }
    onApplyFilters(filtersToApply);
  };

  const handleClearModalFilters = () => {
    setLocalFilters({});
  };

  const watchedOptions = [
    { value: 'all', label: '全部状态' },
    { value: 'watched', label: '已观看' },
    { value: 'unwatched', label: '未观看' },
  ];
  
  const personalRatingOptions = [
    { value: 'any', label: '任何评分' },
    { value: 'unrated', label: '未评分' },
    { value: '1', label: '1星及以上' },
    { value: '2', label: '2星及以上' },
    { value: '3', label: '3星及以上' },
    { value: '4', label: '4星及以上' },
    { value: '5', label: '5星' },
  ];


  return (
    <div className="fixed inset-0 z-[75] flex items-center justify-center p-4 bg-black/80 backdrop-blur-md" onClick={onClose}>
      <div 
        className="bg-[#232323] text-neutral-200 rounded-xl shadow-2xl w-full max-w-xl max-h-[90vh] flex flex-col border border-[#4f4f4f]"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-5 border-b border-[#3a3a3a] bg-[#2a2a2a]">
          <h2 className="text-xl font-bold text-[#B8860B]">高级筛选</h2>
          <button onClick={onClose} className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-[#3a3a3a]" aria-label="关闭高级筛选">
             <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>

        <div className="flex-grow overflow-y-auto p-5 space-y-4 settings-scroll-container">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div><label htmlFor="year" className="settings-label">年份</label><input type="number" name="year" id="year" value={localFilters.year || ''} onChange={handleInputChange} placeholder="例如: 2023" className="form-input-app" /></div>
            <div><label htmlFor="filterWatchedStatus" className="settings-label">观看状态</label><select name="filterWatchedStatus" id="filterWatchedStatus" value={localFilters.filterWatchedStatus || 'all'} onChange={handleInputChange} className="form-select-app">{watchedOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}</select></div>
            <div><label htmlFor="filterRating" className="settings-label">个人评分</label><select name="filterRating" id="filterRating" value={localFilters.filterRating || 'any'} onChange={handleInputChange} className="form-select-app">{personalRatingOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}</select></div>
            <div>
              <label htmlFor="selectedStudio" className="settings-label">制作商</label>
              <input type="text" name="selectedStudio" id="selectedStudio" value={localFilters.selectedStudio || ''} onChange={handleInputChange} placeholder="输入制作商名称" className="form-input-app" />
            </div>
            <div>
              <label htmlFor="selectedSeries" className="settings-label">系列</label>
              <input type="text" name="selectedSeries" id="selectedSeries" value={localFilters.selectedSeries || ''} onChange={handleInputChange} placeholder="输入系列名称" className="form-input-app" />
            </div>
            <div>
              <label htmlFor="director" className="settings-label">导演</label>
              <input type="text" name="director" id="director" value={localFilters.director || ''} onChange={handleInputChange} placeholder="输入导演名称" className="form-input-app" />
            </div>
          </div>
          
          <MultiSelectDropdown
            label="分辨率"
            options={resolutionPresetOptions}
            selectedOptions={localFilters.resolution || []}
            onChange={(selected) => handleMultiSelectChange('resolution', selected)}
            className="w-full"
          />

          <MultiSelectDropdown
            label="类型 (Genres)"
            options={availableGenres}
            selectedOptions={localFilters.selectedGenres || []}
            onChange={(selected) => handleMultiSelectChange('selectedGenres', selected)}
            className="w-full"
          />
          <MultiSelectDropdown
            label="标签 (Tags)"
            options={availableTags}
            selectedOptions={localFilters.selectedTags || []}
            onChange={(selected) => handleMultiSelectChange('selectedTags', selected)}
            className="w-full"
          />
          <div>
            <label htmlFor="selectedActors" className="settings-label">演员 (逗号分隔)</label>
            <input
                type="text"
                name="selectedActors"
                id="selectedActors"
                value={(localFilters.selectedActors || []).join(', ')}
                onChange={(e) => handleMultiSelectChange('selectedActors', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                placeholder="输入演员名称, 用逗号分隔"
                className="form-input-app"
            />
          </div>
        </div>

        <div className="p-4 border-t border-[#3a3a3a] bg-[#2a2a2a] flex justify-end space-x-3">
          <button onClick={handleClearModalFilters} className="button-neutral-app px-4 py-2 text-sm">清空当前筛选</button>
          <button onClick={onClose} className="button-secondary-app px-4 py-2 text-sm">取消</button>
          <button onClick={handleApply} className="button-primary-app px-6 py-2 text-sm">应用筛选</button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedFilterModal;
