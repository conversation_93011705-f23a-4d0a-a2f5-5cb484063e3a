// 诊断 "File is not defined" 错误的脚本

const log = require('electron-log');

console.log('🔍 开始诊断 "File is not defined" 错误...\n');

// 1. 检查全局环境
console.log('1️⃣ 检查全局环境:');
console.log(`Node.js 版本: ${process.version}`);
console.log(`平台: ${process.platform}`);
console.log(`架构: ${process.arch}`);

// 检查是否有 File 对象
console.log(`全局 File 对象: ${typeof File !== 'undefined' ? '存在' : '不存在'}`);
console.log(`全局 Blob 对象: ${typeof Blob !== 'undefined' ? '存在' : '不存在'}`);
console.log(`全局 FormData 对象: ${typeof FormData !== 'undefined' ? '存在' : '不存在'}`);

// 2. 逐步加载模块
console.log('\n2️⃣ 逐步加载模块:');

try {
  console.log('加载 cheerio...');
  const cheerio = require('cheerio');
  console.log('✅ cheerio 加载成功');
} catch (error) {
  console.error('❌ cheerio 加载失败:', error.message);
}

try {
  console.log('加载 playwright...');
  const { chromium } = require('playwright');
  console.log('✅ playwright 加载成功');
} catch (error) {
  console.error('❌ playwright 加载失败:', error.message);
}

try {
  console.log('加载 settingsService...');
  const settingsService = require('./main_process/services/settingsService');
  console.log('✅ settingsService 加载成功');
} catch (error) {
  console.error('❌ settingsService 加载失败:', error.message);
  console.error('错误堆栈:', error.stack);
}

try {
  console.log('加载 browserManager...');
  const browserManager = require('./main_process/services/browserManager');
  console.log('✅ browserManager 加载成功');
} catch (error) {
  console.error('❌ browserManager 加载失败:', error.message);
  console.error('错误堆栈:', error.stack);
}

try {
  console.log('加载 javbusProvider...');
  const javbusProvider = require('./main_process/services/scrapers/javbusProvider');
  console.log('✅ javbusProvider 加载成功');
} catch (error) {
  console.error('❌ javbusProvider 加载失败:', error.message);
  console.error('错误堆栈:', error.stack);
}

try {
  console.log('加载 scraperManager...');
  const scraperManager = require('./main_process/services/scraperManager');
  console.log('✅ scraperManager 加载成功');
} catch (error) {
  console.error('❌ scraperManager 加载失败:', error.message);
  console.error('错误堆栈:', error.stack);
}

// 3. 检查模块内部
console.log('\n3️⃣ 检查模块内部:');

try {
  // 初始化 settingsService
  const path = require('path');
  const os = require('os');
  const settingsService = require('./main_process/services/settingsService');
  const userDataPath = path.join(os.tmpdir(), 'soulforge-debug');
  settingsService.initializeSettings(log, userDataPath);
  console.log('✅ settingsService 初始化成功');
  
  // 测试获取设置
  const settings = settingsService.getSettings();
  console.log('✅ 获取设置成功');
  console.log(`JavBus Base URL: ${settings.javbusBaseUrl}`);
  console.log(`JavBus Cookie: ${settings.javbusCookie || '(空)'}`);
  
} catch (error) {
  console.error('❌ settingsService 测试失败:', error.message);
  console.error('错误堆栈:', error.stack);
}

// 4. 检查 TypeScript 编译
console.log('\n4️⃣ 检查 TypeScript 相关:');

try {
  console.log('检查 src/types/scraper.ts...');
  const fs = require('fs');
  const scraperTypesPath = './src/types/scraper.ts';
  if (fs.existsSync(scraperTypesPath)) {
    const content = fs.readFileSync(scraperTypesPath, 'utf8');
    console.log('✅ scraper.ts 文件存在');
    console.log(`文件大小: ${content.length} 字符`);
  } else {
    console.log('❌ scraper.ts 文件不存在');
  }
} catch (error) {
  console.error('❌ TypeScript 检查失败:', error.message);
}

// 5. 模拟 IPC 调用
console.log('\n5️⃣ 模拟 IPC 调用:');

async function simulateIpcCall() {
  try {
    console.log('模拟 scraper-scrape-movie IPC 调用...');
    
    // 模拟 main.js 中的 IPC 处理器逻辑
    const nfoId = 'TEST-123';
    
    try {
      const scraperManager = require('./main_process/services/scraperManager');
      console.log('✅ scraperManager require 成功');
      
      // 这里不实际调用 scrape，只是检查模块加载
      console.log('✅ 模拟 IPC 调用成功');
      
    } catch (error) {
      console.error('❌ 模拟 IPC 调用失败:', error.message);
      console.error('错误堆栈:', error.stack);
      
      // 检查是否是 "File is not defined" 错误
      if (error.message.includes('File is not defined')) {
        console.error('\n🚨 发现 "File is not defined" 错误！');
        console.error('这个错误通常发生在以下情况:');
        console.error('1. 某个模块在加载时引用了浏览器环境的 File API');
        console.error('2. TypeScript 编译时引入了 DOM 类型定义');
        console.error('3. 某个依赖包在 Node.js 环境中使用了浏览器 API');
        
        // 尝试找出具体的模块
        console.error('\n🔍 尝试定位问题模块...');
        
        // 检查错误堆栈中的模块信息
        const stack = error.stack;
        if (stack) {
          const lines = stack.split('\n');
          console.error('错误堆栈分析:');
          lines.forEach((line, index) => {
            if (line.includes('at ') && (line.includes('.js') || line.includes('.ts'))) {
              console.error(`  ${index}: ${line.trim()}`);
            }
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 模拟测试失败:', error.message);
  }
}

// 运行模拟测试
simulateIpcCall().then(() => {
  console.log('\n🎉 诊断完成！');
}).catch((error) => {
  console.error('\n❌ 诊断过程出错:', error.message);
});
