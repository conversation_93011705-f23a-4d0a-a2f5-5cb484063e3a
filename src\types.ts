// soul-forge-electron/src/types.ts

/**
 * A区 (DisplayData): 经过"数据精炼厂"聚合处理后，供整个软件UI唯一使用的数据模型。
 * 这是所有前端组件的"单一事实来源 (Single Source of Truth)"。
 */
export interface DisplayData {
  // --- 核心信息 (用于影片墙和详情页顶部) ---
  title: string;          // 影片标题 (规则: DMM优先)
  display_id: string;     // 用于展示的番号 (规则: JavBus优先)
  type: 'censored' | 'uncensored' | 'chinese' | 'western' | 'vr' | 'other'; // 影片类型 (规则: DMM优先)
  is_watched: boolean;    // 是否已观看

  // --- 关键版本与标签 (用于影片墙) ---
  version_count: number;              // 总版本数 (本地+云端)
  has_4k: boolean;                    // 是否有4K版
  has_bluray: boolean;                // 是否有蓝光原盘版
  has_subtitles: 'embedded' | 'external' | false; // 是否有字幕 (内嵌/外挂)
  is_uncensored_cracked: boolean;     // 是否是(无码)破解版
  is_leaked: boolean;                 // 是否有流出版

  // --- 详情页：信息区 A ---
  cover_path: string;                 // 封面图本地路径 (规则: DMM优先)
  cover_orientation: 'portrait' | 'landscape'; // 封面图方向, 用于智能裁剪
  nfo_prefix: string;                 // 番号前缀, e.g., 'JUR' (规则: JavBus优先)

  // --- 详情页：信息区 B ---
  year: string;                       // 年份 (规则: JavBus优先)
  release_date: string;               // 发行日期 (规则: DMM优先)
  runtime: number;                    // 时长, 单位:分钟 (规则: DMM优先)
  studio: string;                     // 片商 (规则: JavBus优先)
  publisher: string;                  // 发行 (规则: JavBus优先)
  series: string;                     // 系列 (规则: JavBus优先)
  rating: {                           // 评分 (规则: JavBus优先)
    score: number;
    votes: number;
  } | null;
  tags: string[];                     // 合并后的最终标签 (DMM + B区 + C区AI标签)

  // --- 详情页：信息区 C ---
  actresses: Array<{ name: string; avatar_path: string | null; }>; // 女优列表 (规则: av-wiki.net优先)
  actors_male: Array<{ name:string; avatar_path: string | null; }>; // 男优列表 (规则: avdanyuwiki.com优先)
  director: { name: string; avatar_path: string | null; } | null;   // 导演 (规则: DMM优先)

  // --- 详情页：信息区 D ---
  plot: string;                       // 剧情简介 (DMM + B区 + C区AI生成)
  preview_image_paths: string[];      // 预览图本地路径列表 (规则: DMM优先)
  user_reviews: string[];             // 用户评价 (JavBus优先 + C区AI评论)
  similar_movies: Array<{ nfoId: string; cover_path: string; }>; // 类似影片推荐 (规则: DMM优先)
}

/**
 * C区 (CustomData): 由AI或用户生成的增值数据。
 */
export interface CustomData {
  ai_tags: string[];          // AI生成的标签
  ai_plot_translation: string; // AI翻译或润色的简介
  ai_review_by_linluo: {      // 林珞的评价
    rating: number;           // 1-5星
    comment: string;          // 简评
  };
  user_tags: string[];
  user_notes: string;
}

/**
 * 刮削源优先级规则接口 - 用于配置数据精炼厂的择优规则
 */
export interface ScraperPriorityRules {
  [key: string]: string[]; // e.g., 'title': ['dmm', 'javbus'], 'plot': ['javbus', 'dmm']
}

export type SnapshotQuality = 'sd_320p' | 'hd_640p' | 'fhd_1280p_720h';
export type AvatarDataSourceType = 'none' | 'localSimple' | 'localFileTree' | 'remoteGfriends';

export interface FilenameSuffixRule {
  id: string;
  suffix: string;
  tags: string[];
}

export type ViewMode = 'card' | 'compactList' | 'detailedList' | 'waterfall';

export interface AiRecommendation {
  type: 'recommendation' | 'avoidance';
  score: number; // e.g., 1-100
  justification: string;
}

export interface Movie {
  db_id?: number;
  filePath: string;
  fileName: string;
  title: string | null;
  originalTitle?: string | null;
  nfoId?: string | null;
  year?: number | null;
  releaseDate?: string | null;
  runtime?: number | null; // in minutes
  plot?: string | null;
  plotJa?: string | null;
  plotZh?: string | null;
  studio?: string | null;
  series?: string | null;
  director?: string | null;
  trailerUrl?: string | null;
  posterUrl?: string | null; // Network URL
  coverUrl?: string | null;   // Network URL (often same as poster)
  localCoverPath?: string | null; // Local file path for cover
  watched: boolean;
  personalRating?: number | null; // 1-5, null if not rated
  actors?: string[] | null;
  genres?: string[] | null;
  tags?: string[] | null;
  asset_status?: 'VIRTUAL' | 'AVAILABLE' | 'MISSING'; // 资产状态
  aiAnalyzedTags?: string[] | null;
  aiRecommendation?: AiRecommendation | null;
  lastScanned?: string | null; // ISO date string
  nfoLastModified?: number | null; // Timestamp
  resolution?: string | null; // e.g., "1920x1080"
  videoHeight?: string | null; // e.g., "1080" (often used for "1080p")
  fileSize?: number | null; // in bytes
  videoCodec?: string | null;
  audioCodec?: string | null;
  preferredStatus?: string | null; // e.g., "preferred", "avoid", "backup"
  customFileTags?: string[] | null;
  versionCategories?: string[] | null;
  autoDetectedFileNameTags?: string[] | null;
  fps?: number | null;
  videoCodecFull?: string | null;
  videoBitrate?: string | null; // e.g., "5000 kb/s"
  audioCodecFull?: string | null;
  audioChannelsDesc?: string | null; // e.g., "5.1", "Stereo"
  audioSampleRate?: number | null; // in Hz
  audioBitrate?: string | null; // e.g., "384 kb/s"
  strmUrl?: string | null; // Content of .strm file if it's a .strm
  
  // UI specific / derived
  coverDataUrl?: string | null; // Base64 data URL for display
  sessionAssignedCover?: string | null; // Temporary cover URL assigned in session, not saved to DB
  versionCount?: number; // Number of versions for this nfoId
  specialTags?: string[]; // Generated tags like "4K", "VR"
  hasExternalSubtitles?: boolean;
  cdPartInfo?: string | null; // e.g., "CD1", "CD2"
  multiCdCountForNfoId?: number; // Number of CD parts for this NFO ID

  // --- 新增：A区和C区数据 ---
  displayData?: DisplayData | null; // A区：经过精炼厂处理的展示数据
  customData?: CustomData | null;   // C区：AI或用户生成的增值数据
}

export type MovieVersionInfo = Movie; // A version is essentially a full movie record
export type CdPartInfo = Movie; // A CD part is also a movie record with cdPartInfo

// 统一版本类型，包含本地版本和虚拟版本
export type UnifiedVersion = (Movie & { type: 'local' }) | VirtualVersion;

export interface VirtualVersion {
  type: 'virtual';
  db_id: number;
  title: string;
  nfoId: string;
  fileName: string;
  fileSize?: number | null;
  resolution?: string | null;
  ai_tags_json?: string;
  source_forum?: string;
  post_url?: string;
  magnet_link?: string;
  ed2k_link?: string;
  attachment_url?: string;
  decompression_password?: string;
  collection_date?: string;
  download_status?: string;
  preview_image_url?: string;
  raw_data?: any; // 保留原始数据
}

export interface MovieLibrary {
  id: string; // UUID
  name: string;
  paths: string[];
  movieCount?: number; // Optional: for display on library card
  coverMovies?: Movie[]; // Optional: a few movies for cover display
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}


export interface AppSettings {
  defaultScanPaths?: string[]; // Will be deprecated in favor of MovieLibraries
  pythonExecutablePath?: string | null;
  defaultSortField?: SortableMovieField;
  defaultSortOrder?: 'asc' | 'desc';
  defaultPageSize?: number;
  defaultViewMode?: ViewMode;
  customDefaultCoverDataUrl?: string | null;
  defaultActorAvatarDataUrl?: string | null;
  
  avatarDataSourceType?: AvatarDataSourceType;
  actorAvatarLibraryPath?: string | null;
  localFileTreePath?: string | null;
  avatarPreferAiFixed?: boolean;
  remoteGfriendsFiletreeUrl?: string;
  remoteGfriendsImageBaseUrl?: string;

  ffmpegPath?: string | null;
  ffprobePath?: string | null;
  snapshotCachePath?: string | null;
  filenameSuffixRules?: FilenameSuffixRule[];
  presetVersionCategories?: string[];
  filenameRenameTemplate?: string;
  snapshotQuality?: SnapshotQuality;
  autoUpdateNfoWatchedRating?: boolean;
  autoCreateNfoOnSave?: boolean;
  aiProvider?: 'googleGemini' | 'customGpt' | 'grok' | null;
  // Gemini API settings - now configurable via UI
  geminiApiKey?: string | null;
  geminiModel?: string | null;
  customGptEndpoint?: string | null;
  customGptApiKey?: string | null;
  customGptModel?: string | null;
  grokApiKey?: string | null;
  grokModel?: string | null; // Added for Grok model selection
  ollamaApiEndpoint?: string | null; // Ollama API endpoint
  ollamaModelName?: string | null; // Ollama model name
  privacyModeEnabled?: boolean;
  privacyModePassword?: string | null;
  privacyHideTags?: string[];
  imagesGloballyVisible?: boolean;
  customSfwPlaceholderDataUrl?: string | null;
  activeLibraryId?: string | null; // ID of the currently active library

  // 首页显示设置
  homePageSettings?: {
    showRecentAdded?: boolean; // 是否显示最新入库模块
    showRecentPlayed?: boolean; // 是否显示最近播放模块
    recentAddedCount?: number; // 最新入库显示数量
    recentPlayedCount?: number; // 最近播放显示数量
  };

  // 三位一体分离式存储设置
  assetsPath?: string | null;   // 元数据仓库路径
  trailersPath?: string | null; // 预告片仓库路径
  mediaPath?: string | null;    // 正片仓库路径
}

export type SortableMovieField = 
  | 'db_id' | 'filePath' | 'fileName' | 'title' | 'originalTitle' | 'nfoId' 
  | 'year' | 'releaseDate' | 'runtime' | 'plot' | 'studio' | 'series' 
  | 'director' | 'watched' | 'personalRating' | 'lastScanned' 
  | 'nfoLastModified' | 'resolution' | 'videoHeight' | 'fileSize' 
  | 'videoCodec' | 'audioCodec' | 'preferredStatus' | 'fps' | 'videoBitrate'
  | 'aiRecommendationScore' | 'versionCount' | 'multiCdCountForNfoId';

export interface MovieFetchParams {
  sortField?: SortableMovieField;
  sortOrder?: 'asc' | 'desc';
  filterText?: string;
  advancedFilters?: AdvancedFilterOptions | null;
  pageNumber?: number;
  pageSize?: number;
  privacySettings?: { hideTags: string[] } | null;
  libraryId?: string | null; // Added to filter by library
  fetchType?: 'library_movies' | 'recent_played' | 'recent_added'; // For differentiating fetch contexts
}

export interface MovieFetchResult {
  success: boolean;
  movies: Movie[];
  totalMovies: number;
  error?: string;
}

export interface ScanProgressData {
  overallPercentage: number;
  currentPathMessage: string | null;
  filesInPathProcessed: number | null;
  totalFilesInPath: number | null;
  currentFileMessage: string | null;
  error: string | null;
}

export interface ScanCompleteData {
  newCount: number;
  updatedCount: number;
  totalScanned: number;
  libraryId?: string; // Optional: associate scan with a library
}

export interface AdvancedFilterOptions {
  selectedActors?: string[];
  selectedGenres?: string[];
  selectedTags?: string[];
  resolution?: string[]; // Can be specific strings like "1080p", "4K", or direct resolution strings
  hasSpecialTag?: string; // e.g., "VR", "4K" - this might need refinement
  selectedStudio?: string;
  selectedSeries?: string;
  year?: number;
  filterWatchedStatus?: 'all' | 'watched' | 'unwatched';
  filterRating?: 'any' | 'unrated' | '1' | '2' | '3' | '4' | '5'; // 1 means 1 and above, etc.
  director?: string;
}

export type DetailFilterType = 'actor' | 'genre' | 'tag' | 'studio' | 'series' | 'director' | 'year' | 'nfoId' | 'resolution' | 'videoHeight' | 'library';


export interface PrivacyModeState {
  isEnabled: boolean;
  isLocked: boolean;
}

export interface FavoriteBasedRecommendationParams {
  limitPerCategory?: number;
  maxTotal?: number;
}

export interface FavoriteBasedRecommendationResult {
    success: boolean;
    recommendedMovies?: Movie[];
    error?: string;
}

export interface SnapshotInfo {
  filePath: string;
  dataUrl: string | null;
}

export interface ExistingSnapshotsResult {
  success: boolean;
  snapshots?: SnapshotInfo[];
  error?: string;
}

export interface GenerateThumbnailsParams {
  videoDbId: number;
  videoFilePath: string;
  snapshotQuality: SnapshotQuality | undefined;
  // ffmpegPath and pythonExecutablePath are passed from settings in main.js
}

export interface GenerateThumbnailsResult {
  success: boolean;
  thumbnailDataUrls?: (string | null)[];
  error?: string;
}

export interface VersionMarksPayload {
  preferredStatus: string | null;
  customFileTags: string[];
  versionCategories: string[];
}

export interface SaveVersionMarksParams {
  fileDbId: number;
  marks: VersionMarksPayload;
}

export interface SaveVersionMarksResult {
  success: boolean;
  updatedMovie?: Movie; // The updated movie record for the specific version
  error?: string;
}

export interface MovieVersionsResult {
    success: boolean;
    versions: MovieVersionInfo[];
    error?: string;
}

export interface CdPartsFetchResult {
    success: boolean;
    cdParts: CdPartInfo[];
    error?: string;
}

export interface RenameFilesByNfoIdResult {
    success: boolean;
    renamedFiles?: { oldPath: string; newPath: string }[];
    errors?: { filePath: string; error: string }[];
    message?: string; // Overall status message
}

export interface MoveToRecycleBinResult {
    success: boolean;
    message?: string;
    error?: string;
}

export interface SettingsResult {
  success: boolean;
  newSettings?: AppSettings;
  message?: string;
  error?: string;
}

export type FavoriteItemType = 'actress' | 'studio' | 'series' | 'director' | 'movie_nfo_id' | 'tag' | 'genre';

export interface FavoriteItem {
  id: number;
  item_type: FavoriteItemType;
  item_value: string;
  favorited_at: string; // ISO date string
}

export interface ToggleFavoriteResult {
  success: boolean;
  isFavorite: boolean;
  changed?: boolean;
  error?: string;
}

export interface FavoritesResult {
  success: boolean;
  favorites: FavoriteItem[];
  error?: string;
}

export interface AiGeneratePlotParams {
  title: string | null;
  year?: number | null;
}
export interface AiGeneratePlotResult {
  success: boolean;
  summary?: string;
  error?: string;
}

export interface AiTranslatePlotParams {
  db_id?: number; // For context, might not be directly used by AI
  currentPlot: string | null;
  targetLanguage?: 'zh-CN' | 'en' | string; // Add more as needed
  title?: string | null; // For context
  year?: number | null;  // For context
}
export interface AiTranslatePlotResult {
  success: boolean;
  translatedPlot?: string;
  error?: string;
}

export interface AiEmbellishPlotParams {
  db_id?: number;
  title?: string | null;
  originalTitle?: string | null;
  currentPlot?: string | null;
  plotJa?: string | null;
  plotZh?: string | null;
  year?: number | null;
  genres?: string[] | null;
  actors?: string[] | null;
  tags?: string[] | null;
  aiAnalyzedTags?: string[] | null;
  options?: any; // For potential future options like style
}
export interface AiEmbellishPlotResult {
  success: boolean;
  embellishedPlot?: string;
  error?: string;
}

export interface AiAnalyzeTagsParams {
  db_id: number;
  title?: string | null;
  plot?: string | null;
  year?: number | null;
  genres?: string[] | null;
  currentTags?: string[] | null;
  currentAiTags?: string[] | null;
}
export interface AiAnalyzeTagsResult {
  success: boolean;
  analyzedTags?: string[];
  error?: string;
}

export interface AiSuggestCoverParams {
  movieTitle?: string | null;
  movieDbId: number;
  numberOfSnapshots: number;
}
export interface AiSuggestCoverResult {
  success: boolean;
  suggestedIndex?: number; // 0-based index
  error?: string;
}

export interface AiAnalyzeRecommendationParams {
  db_id: number;
  title?: string | null;
  plot?: string | null;
  year?: number | null;
  genres?: string[] | null;
  tags?: string[] | null;
  aiAnalyzedTags?: string[] | null;
  resolution?: string | null;
  runtime?: number | null;
}
export interface AiAnalyzeRecommendationResult {
  success: boolean;
  recommendation?: AiRecommendation;
  error?: string;
}

export interface AiCleanupStats {
  unwatchedOldCount: number;
  lowRatedCount: number;
  highAvoidanceCount: number;
}
export interface AiCleanupSuggestionResult {
  success: boolean;
  suggestion?: string;
  error?: string;
}

export interface FormatRecommendationsAsAiMessageParams {
  movies: Movie[];
}
export interface FormatRecommendationsAsAiMessageResult {
  success: boolean;
  formattedMessage?: string;
  error?: string;
}

export interface AttemptUnlockResult {
  success: boolean;
  error?: string;
}
export interface TogglePrivacyResult {
  success: boolean;
  newState?: boolean; // The new isEnabled state
  error?: string;
}

export interface SaveChatHistoryParams {
  fileName: string;
  content: string;
}
export interface SaveChatHistoryResult {
  success: boolean;
  filePath?: string;
  error?: string;
}

export interface ActorAvatarInfoResult {
    success: boolean;
    dataUrl?: string | null; // Base64 for local files
    avatarUrl?: string | null; // URL for remote files
    actorName?: string; // For caching purposes if URL
    filetreeSourcePath?: string; // For DB update
    source?: 'db_cache' | 'local_simple' | 'local_filetree_cached' | 'local_filetree_direct_nocache' | 'remote_gfriends' | 'not_found' | 'local_path_invalid';
    error?: string;
}

export interface BackupDatabaseResult {
    success: boolean;
    filePath?: string;
    error?: string;
}
export interface RestoreDatabaseResult {
    success: boolean;
    message?: string;
    error?: string;
}

export interface NfoPolishScanProgressData {
  currentDirectory: string | null;
  totalDirectories: number;
  currentDirIndex: number;
  nfoFilesFoundInCurrentDir: number;
  nfoFilesFoundTotal: number;
  status: 'scanning_dir' | 'scanning_file' | 'finding_nfo' | string; // More specific statuses
  currentFileScanning?: string | null;
}
export interface NfoPolishScanCompleteData {
  totalNfoFilesFound: number;
  nfoFilePaths: string[];
  cancelled?: boolean; // Added optional cancelled flag
}
export interface NfoPolishProcessProgressData {
  totalFilesToProcess: number;
  filesProcessedSoFar: number;
  currentNfoFileProcessing: string | null;
  statusMessage: string;
}
export interface NfoPolishProcessCompleteData {
  totalFilesToProcess: number;
  totalFilesProcessed: number;
  totalSuccessfullyUpdated: number;
  totalSkipped: number; // Skipped because already processed or other reasons
  totalErrors: number;
  cancelled?: boolean; // Added optional cancelled flag
}
export interface NfoPolishErrorData {
  filePath: string | null; // null for general errors
  error: string;
}


// For MovieDetailModal
export interface MovieDetailModalProps {
  movie: Movie;
  onClose: () => void;
  onUpdateMovie: (updatedMovie: Movie) => void;
  onTagClick?: (value: string, type: DetailFilterType) => void;
  appDefaultCover?: string | null;
  appDefaultActorAvatar?: string | null;
  currentSettings: AppSettings;
}

// For VersionSelectionView & CDSelectionView
export interface EditableVersionState extends MovieVersionInfo {
  localPreferredStatus: string | null;
  localCustomFileTagsStr: string;
  localVersionCategoriesStr: string;
  isSavingMarks: boolean;
  marksSaveError: string | null;
  isTrashing: boolean;
  trashError: string | null;
  snapshots: SnapshotInfo[];
  isLoadingSnapshots: boolean;
  snapshotsError: string | null;
  showSnapshots: boolean;
}

// For MovieCardPlaceholder
export interface MovieCardPlaceholderProps {
  className?: string;
}

// For EditableMovieState in MovieDetailModal (from utils.ts)
export interface EditableMovieState {
  // All fields from Movie, but some are stringified for form inputs
  title: string;
  originalTitle: string;
  nfoId: string;
  year: string; // string for form input
  releaseDate: string; // string for form input type="date"
  runtime: string; // string for form input, represents minutes
  plot: string;
  studio: string;
  series: string;
  director: string;
  trailerUrl: string;
  posterUrl: string;
  coverUrl: string;
  localCoverPath: string | null;
  watched: boolean;
  personalRating: number | null;
  actors: string; // comma-separated string
  genres: string; // comma-separated string
  tags: string; // comma-separated string
  aiAnalyzedTags: string; // comma-separated string

  // Include other Movie fields that don't need string conversion directly
  // These are inherited via spread, but listed for clarity if needed.
  db_id?: number;
  filePath: string;
  fileName: string;
  plotJa?: string | null;
  plotZh?: string | null;
  aiRecommendation?: AiRecommendation | null;
  lastScanned?: string | null;
  nfoLastModified?: number | null;
  resolution?: string | null;
  videoHeight?: string | null;
  fileSize?: number | null;
  videoCodec?: string | null;
  audioCodec?: string | null;
  preferredStatus?: string | null; // This might be handled by EditableVersionState for versions
  customFileTags?: string[] | null; // Array form for actual Movie data
  versionCategories?: string[] | null; // Array form for actual Movie data
  autoDetectedFileNameTags?: string[] | null;
  fps?: number | null;
  videoCodecFull?: string | null;
  videoBitrate?: string | null;
  audioCodecFull?: string | null;
  audioChannelsDesc?: string | null;
  audioSampleRate?: number | null;
  audioBitrate?: string | null;
  coverDataUrl?: string | null;
  sessionAssignedCover?: string | null; // UI temporary cover
  versionCount?: number;
  specialTags?: string[];
  hasExternalSubtitles?: boolean;
  cdPartInfo?: string | null;
  multiCdCountForNfoId?: number;
  strmUrl?: string | null;
}

// Avatar Scraping IPC Types
export interface ScrapeActorAvatarsResult {
  success: boolean;
  totalActors: number;
  processedCount: number;
  failedCount: number;
  message?: string;
  error?: string;
}

export interface ScrapeActorAvatarsProgress {
  currentActorName: string;
  currentIndex: number;
  totalActors: number;
  status: 'processing' | 'downloaded' | 'copied_to_cache' | 'cached' | 'skipped' | 'error';
  error?: string;
}

// Movie Library specific IPC types
export interface ManageMovieLibraryParams {
  id?: string; // for update/delete
  name: string;
  paths: string[];
}

export interface ManageMovieLibraryResult {
  success: boolean;
  library?: MovieLibrary;
  libraries?: MovieLibrary[]; // for delete or get all
  error?: string;
}

export interface MovieLibrariesResult {
  success: boolean;
  libraries: MovieLibrary[];
  error?: string;
}

export interface ScanLibraryResult extends ScanCompleteData {
  libraryId: string; // Ensure libraryId is always present
}

export interface PlayStrmUrlPayload { // For STRM playback
  url: string;
  title?: string;
}

export interface PlayVideoParams {
  filePath: string;
  title?: string; // For STRM files primarily
  strmUrl?: string; // Optional: if main process already read it
}

// 智能片库相关类型
export interface SmartLibrary {
  id: string;
  name: string;
  rules: SmartLibraryRules;
  icon?: string;
  sort_order: number;
}

export interface SmartLibraryRules {
  // 筛选条件
  favorited?: boolean;
  asset_status?: 'VIRTUAL' | 'AVAILABLE' | 'MISSING';
  selectedActors?: string[];
  selectedGenres?: string[];
  selectedTags?: string[];
  selectedStudio?: string;
  selectedSeries?: string;
  director?: string;
  year?: number;
  resolution?: string[];

  // 排序条件
  sortField?: string;
  sortOrder?: 'asc' | 'desc';

  // 其他条件
  [key: string]: any;
}

export interface SmartLibraryResult {
  success: boolean;
  libraries?: SmartLibrary[];
  error?: string;
}

// 即时番号侦察进度数据
export interface ArchiveScrapeProgressData {
  stage: 'scraping' | 'resolving' | 'creating' | 'complete' | 'error';
  message: string;
  progress: number; // 0-100
}
