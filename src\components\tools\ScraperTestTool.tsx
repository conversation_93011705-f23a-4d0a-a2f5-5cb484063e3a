import React, { useState } from 'react';

interface ScraperTestToolProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ScrapedMovieData {
  nfoId: string;
  title: string;
  plot: string;
  releaseDate: string;
  year: string;
  runtime: string;
  director: string;
  studio: string;
  publisher: string;
  series: string;
  actors: string[];
  tags: string[];
  coverUrl: string;
  posterUrl: string;
  previewImages: string[];
  trailerUrl: string;
  sourceUrl: string;
  mosaic: '有码' | '无码' | '其他';
}

const ScraperTestTool: React.FC<ScraperTestToolProps> = ({ isOpen, onClose }) => {
  const [nfoId, setNfoId] = useState('JUFE-585');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ScrapedMovieData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleScrape = async () => {
    if (!nfoId.trim()) {
      setError('请输入番号');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await window.sfeElectronAPI.scrapeMovie(nfoId.trim());
      
      if (response.success) {
        setResult(response.data);
      } else {
        setError(response.error || '刮削失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '发生未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-[#1a1a1a] text-white w-full max-w-6xl h-[90vh] overflow-y-auto rounded-lg">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-[#B8860B]">🎯 刮削器测试工具</h1>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        
        {/* 输入区域 */}
        <div className="bg-[#2c2c2c] p-4 rounded-lg mb-6">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-2">番号 (NFO ID)</label>
              <input
                type="text"
                value={nfoId}
                onChange={(e) => setNfoId(e.target.value)}
                placeholder="例如: JUFE-585"
                className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#444] rounded text-white focus:border-[#B8860B] focus:outline-none"
                disabled={isLoading}
              />
            </div>
            <button
              onClick={handleScrape}
              disabled={isLoading}
              className="px-6 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '刮削中...' : '开始刮削'}
            </button>
          </div>
        </div>

        {/* 加载状态 */}
        {isLoading && (
          <div className="bg-[#2c2c2c] p-4 rounded-lg mb-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#B8860B]"></div>
              <span>正在刮削数据，请稍候...</span>
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-900/20 border border-red-500 p-4 rounded-lg mb-6">
            <h3 className="text-red-400 font-medium mb-2">❌ 刮削失败</h3>
            <p className="text-red-300">{error}</p>
          </div>
        )}

        {/* 成功结果 */}
        {result && (
          <div className="bg-[#2c2c2c] p-6 rounded-lg">
            <h3 className="text-[#B8860B] font-bold text-lg mb-4">✅ 刮削成功</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 基本信息 */}
              <div>
                <h4 className="text-white font-medium mb-3">📋 基本信息</h4>
                <div className="space-y-2 text-sm">
                  <div><span className="text-gray-400">番号:</span> <span className="text-white">{result.nfoId}</span></div>
                  <div><span className="text-gray-400">标题:</span> <span className="text-white">{result.title}</span></div>
                  <div><span className="text-gray-400">发行日期:</span> <span className="text-white">{result.releaseDate}</span></div>
                  <div><span className="text-gray-400">片长:</span> <span className="text-white">{result.runtime} 分钟</span></div>
                  <div><span className="text-gray-400">制作商:</span> <span className="text-white">{result.studio}</span></div>
                  <div><span className="text-gray-400">发行商:</span> <span className="text-white">{result.publisher}</span></div>
                  <div><span className="text-gray-400">系列:</span> <span className="text-white">{result.series}</span></div>
                  <div><span className="text-gray-400">导演:</span> <span className="text-white">{result.director}</span></div>
                  <div><span className="text-gray-400">马赛克:</span> <span className="text-white">{result.mosaic}</span></div>
                </div>
              </div>

              {/* 演员和标签 */}
              <div>
                <h4 className="text-white font-medium mb-3">👥 演员和标签</h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-400">演员 ({result.actors.length}):</span>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {result.actors.map((actor, index) => (
                        <span key={index} className="bg-blue-600/20 text-blue-300 px-2 py-1 rounded text-xs">
                          {actor}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-400">标签 ({result.tags.length}):</span>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {result.tags.map((tag, index) => (
                        <span key={index} className="bg-green-600/20 text-green-300 px-2 py-1 rounded text-xs">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 图片信息 */}
            <div className="mt-6">
              <h4 className="text-white font-medium mb-3">🖼️ 图片信息</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">封面图:</span>
                  <div className="mt-1">
                    {result.coverUrl ? (
                      <a href={result.coverUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 break-all">
                        查看封面
                      </a>
                    ) : (
                      <span className="text-gray-500">无</span>
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">海报图:</span>
                  <div className="mt-1">
                    {result.posterUrl ? (
                      <a href={result.posterUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 break-all">
                        查看海报
                      </a>
                    ) : (
                      <span className="text-gray-500">无</span>
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-gray-400">预览图:</span>
                  <div className="mt-1">
                    <span className="text-white">{result.previewImages.length} 张</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 来源信息 */}
            <div className="mt-6">
              <h4 className="text-white font-medium mb-3">🔗 来源信息</h4>
              <div className="text-sm">
                <span className="text-gray-400">来源URL:</span>
                <a href={result.sourceUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 ml-2 break-all">
                  {result.sourceUrl}
                </a>
              </div>
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-8 bg-[#2c2c2c] p-4 rounded-lg">
          <h3 className="text-[#B8860B] font-medium mb-2">💡 使用说明</h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• 输入要测试的番号，例如：JUFE-585</li>
            <li>• 点击"开始刮削"按钮启动测试</li>
            <li>• 系统将自动启动浏览器访问 JavBus 网站</li>
            <li>• 刮削完成后会显示详细的数据结果</li>
            <li>• 如果失败，会显示具体的错误信息</li>
          </ul>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ScraperTestTool;
