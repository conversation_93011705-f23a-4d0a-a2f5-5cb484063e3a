
// soul-forge-electron/src/hooks/useRepresentativeMovie.ts
import { Movie } from '../types';

export const useRepresentativeMovieForGrouping = (
  currentNfoIdForGrouping: string | null,
  movies: Movie[],
  selectedMovie: Movie | null // This selectedMovie is the one for the detail modal, not necessarily related to the group
): Movie | null => {
  if (!currentNfoIdForGrouping) {
    return null;
  }

  // If the globally selected movie (for detail view) happens to be part of the group, use it.
  // This might be relevant if the version/CD modal was opened FROM the detail modal of one of its members.
  if (selectedMovie && selectedMovie.nfoId === currentNfoIdForGrouping) {
    return selectedMovie;
  }

  // Otherwise, find any movie from the current movie list that matches the NFO ID for grouping.
  // This is more robust if the version/CD modal is opened directly from a card click that indicates grouping.
  const foundMovie = movies.find(movie => movie.nfoId === currentNfoIdForGrouping);
  
  return foundMovie || null;
};
