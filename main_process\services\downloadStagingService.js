// main_process/services/downloadStagingService.js
const fs = require('fs').promises;
const path = require('path');
const log = require('electron-log');
const NodeNfoParser = require('./nodeNfoParser');
const settingsService = require('./settingsService');
const pathResolverService = require('./pathResolverService');
const databaseService = require('./databaseService');

/**
 * 下载中转站服务 - 负责处理下载完成文件的自动化入库流程
 */

// 支持的媒体文件扩展名
const MEDIA_EXTENSIONS = [
  '.mp4', '.mkv', '.avi', '.wmv', '.mov', '.flv', '.webm', '.m4v',
  '.ts', '.m2ts', '.mts', '.vob', '.iso', '.rmvb', '.rm', '.asf'
];

/**
 * 扫描下载中转目录，识别新的媒体文件
 * @returns {Promise<Array>} 文件信息数组
 */
async function scanStagingDirectory() {
  try {
    const settings = settingsService.getSettings();
    const stagingPath = settings.downloadStagingPath;
    
    if (!stagingPath) {
      log.warn('[下载中转站] 未配置下载中转目录');
      return [];
    }

    // 检查目录是否存在
    try {
      const stats = await fs.stat(stagingPath);
      if (!stats.isDirectory()) {
        log.error(`[下载中转站] 路径不是目录: ${stagingPath}`);
        return [];
      }
    } catch (error) {
      log.error(`[下载中转站] 无法访问下载中转目录: ${stagingPath}`, error.message);
      return [];
    }

    log.info(`[下载中转站] 开始扫描目录: ${stagingPath}`);
    
    const files = [];
    const entries = await fs.readdir(stagingPath, { withFileTypes: true });
    
    for (const entry of entries) {
      if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase();
        
        if (MEDIA_EXTENSIONS.includes(ext)) {
          const filePath = path.join(stagingPath, entry.name);
          const stats = await fs.stat(filePath);
          
          // 尝试从文件名提取番号
          const nfoId = NodeNfoParser.extractJavIdFromFilename(entry.name);
          
          const fileInfo = {
            filePath: filePath,
            fileName: entry.name,
            fileSize: stats.size,
            lastModified: stats.mtime,
            nfoId: nfoId,
            extension: ext
          };
          
          files.push(fileInfo);
          log.debug(`[下载中转站] 发现媒体文件: ${entry.name}, 识别番号: ${nfoId || '未识别'}`);
        }
      }
    }
    
    // 按修改时间排序（最新的在前）
    files.sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime());
    
    log.info(`[下载中转站] 扫描完成，找到 ${files.length} 个媒体文件`);
    return files;
    
  } catch (error) {
    log.error('[下载中转站] 扫描目录失败:', error.message);
    throw error;
  }
}

/**
 * 处理中转站中的单个文件
 * @param {string} originalFilePath - 原始文件路径
 * @param {string} nfoId - 番号
 * @param {string} targetSeriesFolder - 目标系列文件夹
 * @returns {Promise<Object>} 处理结果
 */
async function processStagedFile(originalFilePath, nfoId, targetSeriesFolder) {
  log.info(`[下载中转站] 开始处理文件: ${originalFilePath}, 番号: ${nfoId}, 目标系列: ${targetSeriesFolder}`);
  
  try {
    const settings = settingsService.getSettings();
    
    // 验证必要的设置
    if (!settings.uploadQueuePath) {
      throw new Error('未配置待上传队列目录');
    }
    
    if (!settings.mediaAssetsRootPath) {
      throw new Error('未配置媒体资产根目录');
    }
    
    // 检查原始文件是否存在
    try {
      await fs.access(originalFilePath);
    } catch (error) {
      throw new Error(`原始文件不存在: ${originalFilePath}`);
    }
    
    // 1. 获取标准路径
    const movieData = {
      nfoId: nfoId,
      title: nfoId, // 临时使用番号作为标题
      // 其他字段可以为空，pathResolverService 会处理
    };
    
    const paths = await pathResolverService.resolveAssetPaths(movieData);
    log.debug(`[下载中转站] 解析的标准路径:`, paths);
    
    // 2. 创建 STRM 文件
    const strmResult = await createStrmFile(originalFilePath, nfoId, paths, settings);
    
    // 3. 转移物理文件到待上传队列
    const moveResult = await moveFileToUploadQueue(originalFilePath, targetSeriesFolder, settings);
    
    // 4. 更新数据库
    const dbResult = await updateDatabaseRecord(nfoId, paths, moveResult.newFilePath, strmResult.strmPath);

    const result = {
      success: true,
      nfoId: nfoId,
      strmPath: strmResult.strmPath,
      newFilePath: moveResult.newFilePath,
      databaseUpdated: dbResult.updated,
      message: '文件处理完成'
    };
    
    log.info(`[下载中转站] 文件处理成功: ${nfoId}`);
    return result;
    
  } catch (error) {
    log.error(`[下载中转站] 处理文件失败: ${originalFilePath}`, error.message);
    return {
      success: false,
      error: error.message,
      nfoId: nfoId
    };
  }
}

/**
 * 创建 STRM 文件
 * @param {string} originalFilePath - 原始文件路径
 * @param {string} nfoId - 番号
 * @param {Object} paths - 标准路径对象
 * @param {Object} settings - 设置对象
 * @returns {Promise<Object>} 创建结果
 */
async function createStrmFile(originalFilePath, nfoId, paths, settings) {
  try {
    // 确保目标目录存在
    await fs.mkdir(paths.movieRootPath, { recursive: true });
    
    // 计算 STRM 文件路径
    const originalExt = path.extname(originalFilePath);
    const strmFileName = `${nfoId}.strm`;
    const strmPath = path.join(paths.movieRootPath, strmFileName);
    
    // 计算 115 网盘路径
    const cloud115RootPath = settings.cloud115RootPath || '/';
    const originalFileName = path.basename(originalFilePath);
    
    // 构建115网盘完整路径
    let cloud115FilePath;
    if (cloud115RootPath.endsWith('/')) {
      cloud115FilePath = `${cloud115RootPath}${nfoId}/${originalFileName}`;
    } else {
      cloud115FilePath = `${cloud115RootPath}/${nfoId}/${originalFileName}`;
    }
    
    // 写入 STRM 文件
    await fs.writeFile(strmPath, cloud115FilePath, 'utf8');
    
    log.info(`[下载中转站] STRM 文件已创建: ${strmPath}`);
    log.debug(`[下载中转站] STRM 内容: ${cloud115FilePath}`);
    
    return {
      success: true,
      strmPath: strmPath,
      cloud115Path: cloud115FilePath
    };
    
  } catch (error) {
    log.error(`[下载中转站] 创建 STRM 文件失败: ${nfoId}`, error.message);
    throw new Error(`创建 STRM 文件失败: ${error.message}`);
  }
}

/**
 * 将文件移动到待上传队列
 * @param {string} originalFilePath - 原始文件路径
 * @param {string} targetSeriesFolder - 目标系列文件夹
 * @param {Object} settings - 设置对象
 * @returns {Promise<Object>} 移动结果
 */
async function moveFileToUploadQueue(originalFilePath, targetSeriesFolder, settings) {
  try {
    const fileName = path.basename(originalFilePath);
    const targetDir = path.join(settings.uploadQueuePath, targetSeriesFolder);
    const newFilePath = path.join(targetDir, fileName);
    
    // 确保目标目录存在
    await fs.mkdir(targetDir, { recursive: true });
    
    // 检查目标文件是否已存在
    try {
      await fs.access(newFilePath);
      throw new Error(`目标文件已存在: ${newFilePath}`);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        throw error;
      }
      // 文件不存在，可以继续
    }
    
    // 移动文件
    await fs.rename(originalFilePath, newFilePath);
    
    log.info(`[下载中转站] 文件已移动到待上传队列: ${newFilePath}`);
    
    return {
      success: true,
      newFilePath: newFilePath,
      targetDir: targetDir
    };
    
  } catch (error) {
    log.error(`[下载中转站] 移动文件失败: ${originalFilePath}`, error.message);
    throw new Error(`移动文件失败: ${error.message}`);
  }
}

/**
 * 更新数据库记录
 * @param {string} nfoId - 番号
 * @param {Object} paths - 标准路径对象
 * @param {string} newFilePath - 新文件路径
 * @param {string} strmPath - STRM文件路径
 * @returns {Promise<Object>} 更新结果
 */
async function updateDatabaseRecord(nfoId, paths, newFilePath, strmPath) {
  try {
    // 检查数据库中是否已存在该记录
    const existingMovie = await databaseService.getMovieByNfoId(nfoId);
    
    if (existingMovie) {
      // 更新现有记录
      const updateData = {
        filePath: strmPath || newFilePath, // 优先使用 STRM 路径
        fileName: path.basename(strmPath || newFilePath),
        asset_status: 'AVAILABLE',
        lastScanned: new Date().toISOString()
      };
      
      const updated = await databaseService.updateMovie(existingMovie.db_id, updateData);
      
      log.info(`[下载中转站] 数据库记录已更新: ${nfoId}`);
      return { updated: true, action: 'updated', movieId: existingMovie.db_id };
      
    } else {
      // 创建新记录
      const movieData = {
        nfoId: nfoId,
        title: nfoId, // 使用番号作为默认标题
        filePath: strmPath || newFilePath,
        fileName: path.basename(strmPath || newFilePath),
        asset_status: 'AVAILABLE',
        lastScanned: new Date().toISOString(),
        watched: false,
        personalRating: null
      };
      
      const newMovie = await databaseService.addMovie(movieData);
      
      log.info(`[下载中转站] 数据库记录已创建: ${nfoId}`);
      return { updated: true, action: 'created', movieId: newMovie.db_id };
    }
    
  } catch (error) {
    log.error(`[下载中转站] 更新数据库失败: ${nfoId}`, error.message);
    // 不抛出错误，因为文件处理已经完成
    return { updated: false, error: error.message };
  }
}

/**
 * 获取可用的系列文件夹列表
 * @returns {Promise<Array>} 系列文件夹列表
 */
async function getAvailableSeriesFolders() {
  try {
    const settings = settingsService.getSettings();
    
    if (!settings.uploadQueuePath) {
      return [];
    }
    
    // 检查待上传队列目录是否存在
    try {
      const stats = await fs.stat(settings.uploadQueuePath);
      if (!stats.isDirectory()) {
        return [];
      }
    } catch (error) {
      return [];
    }
    
    const entries = await fs.readdir(settings.uploadQueuePath, { withFileTypes: true });
    const folders = entries
      .filter(entry => entry.isDirectory())
      .map(entry => entry.name)
      .sort();
    
    // 添加一些常见的系列文件夹
    const commonSeries = ['JUR', 'JUFE', 'SSIS', 'PRED', 'STARS', 'MIDE', 'MIAA', 'CAWD'];
    const allFolders = [...new Set([...commonSeries, ...folders])].sort();
    
    return allFolders;
    
  } catch (error) {
    log.error('[下载中转站] 获取系列文件夹列表失败:', error.message);
    return [];
  }
}

module.exports = {
  scanStagingDirectory,
  processStagedFile,
  createStrmFile,
  moveFileToUploadQueue,
  updateDatabaseRecord,
  getAvailableSeriesFolders,
  MEDIA_EXTENSIONS
};
