
// soul-forge-electron/src/components/layout/MovieGridDisplay.tsx
import React from 'react';
import { Movie, ViewMode, AppSettings } from '../../types';
import MovieCard from '../MovieCard';
import MovieListItemCompact from '../MovieListItemCompact';
import MovieListItemDetailed from '../MovieListItemDetailed';
import MovieCardPlaceholder from '../MovieCardPlaceholder';
import AutoSizer from 'react-virtualized-auto-sizer';
import { FixedSizeGrid, FixedSizeList, ListChildComponentProps, GridChildComponentProps } from 'react-window';

interface MovieGridDisplayProps {
  movies: Movie[];
  isLoading: boolean;
  viewMode: ViewMode;
  appSettings: AppSettings;
  pageSize: number; // Used for placeholder count
  onCardClick: (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => void;
}

const CARD_WIDTH = 200;
const CARD_HEIGHT = 300;
const COMPACT_ITEM_HEIGHT = 72; // Height of MovieListItemCompact
const DETAILED_ITEM_HEIGHT = 136; // Height of MovieListItemDetailed
const GAP = 16; // Corresponds to gap-4 in Tailwind (p-2 on each side of content = 16px total gap)

// Item (Cell) component for Card Grid
const CardCell: React.FC<GridChildComponentProps> = ({ columnIndex, rowIndex, style, data }) => {
  const { movies, columnCount, onCardClick, appDefaultCover } = data; // imagesGloballyVisible is now implicitly handled by MovieCard via useAppSettings
  const index = rowIndex * columnCount + columnIndex;
  if (index >= movies.length) return null;
  const movie = movies[index];
  return (
    <div style={{
      ...style,
      paddingLeft: GAP / 2,
      paddingRight: GAP / 2,
      paddingTop: GAP / 2,
      paddingBottom: GAP / 2,
      boxSizing: 'border-box'
    }}
    >
      <MovieCard 
        movie={movie}
        onCardClick={onCardClick}
        appDefaultCover={appDefaultCover}
      />
    </div>
  );
};

// Item (Cell) component for Compact List Grid
const CompactListCell: React.FC<GridChildComponentProps> = ({ columnIndex, rowIndex, style, data }) => {
  const { movies, columnCount, onCardClick, appDefaultCover } = data;
  const index = rowIndex * columnCount + columnIndex;
  if (index >= movies.length) return null;
  const movie = movies[index];
  return (
    <div style={{
      ...style,
      paddingLeft: GAP / 2,
      paddingRight: GAP / 2,
      paddingTop: GAP / 2,
      paddingBottom: GAP / 2,
      boxSizing: 'border-box'
    }}
    >
      <MovieListItemCompact 
        movie={movie}
        onCardClick={onCardClick}
        appDefaultCover={appDefaultCover}
      />
    </div>
  );
};

// Row component for Detailed List
const DetailedListRow: React.FC<ListChildComponentProps> = ({ index, style, data }) => {
    const { movies, onCardClick, appDefaultCover } = data;
    if (index >= movies.length) return null;
    const movie = movies[index];
    return (
        <div style={{
          ...style,
          paddingLeft: GAP / 2,
          paddingRight: GAP / 2,
          paddingTop: GAP / 2,
          paddingBottom: GAP / 2,
          boxSizing: 'border-box'
        }}
        >
            <MovieListItemDetailed 
                movie={movie}
                onCardClick={onCardClick}
                appDefaultCover={appDefaultCover}
            />
        </div>
    );
};


const MovieGridDisplay: React.FC<MovieGridDisplayProps> = ({
  movies,
  isLoading,
  viewMode,
  appSettings,
  pageSize,
  onCardClick,
}) => {

  if (isLoading) {
    const numPlaceholders = pageSize || 20; 
    let placeholderLayoutClasses = '';
    let PlaceholderComponent: React.FC<any> = MovieCardPlaceholder;
    let placeholderStyle: React.CSSProperties = {};

    switch (viewMode) {
      case 'card':
        placeholderLayoutClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
        break;
      case 'compactList':
        placeholderLayoutClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5';
        PlaceholderComponent = () => <div style={{ height: `${COMPACT_ITEM_HEIGHT}px` }} className="bg-[#2c2c2c] rounded-md animate-pulse w-full"></div>;
        break;
      case 'detailedList':
        placeholderLayoutClasses = 'grid-cols-1';
        PlaceholderComponent = () => <div style={{ height: `${DETAILED_ITEM_HEIGHT}px` }} className="bg-[#2c2c2c] rounded-md animate-pulse w-full"></div>;
        break;
      case 'waterfall':
         return ( 
          <div className="masonry-wall columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 2xl:columns-6 gap-4 px-4">
            {Array.from({ length: numPlaceholders }).map((_, index) => <div className="mb-4"><MovieCardPlaceholder key={`placeholder-wf-${index}`} /></div>)}
          </div>
        );
    }
    return (
      <div className={`grid ${placeholderLayoutClasses} gap-4 px-4`}>
        {Array.from({ length: numPlaceholders }).map((_, index) => (
          <PlaceholderComponent key={`placeholder-${viewMode}-${index}`} style={placeholderStyle} />
        ))}
      </div>
    );
  }


  if (movies.length === 0 && !isLoading) {
    return <div className="text-center p-10 text-neutral-500">没有找到符合条件的影片。</div>;
  }
  
  const containerStyle: React.CSSProperties = { height: 'calc(100vh - 180px)', width: '100%' }; 

  // Create a new itemData object when appSettings.imagesGloballyVisible changes to force re-render
  const itemData = React.useMemo(() => ({
    movies,
    onCardClick,
    appDefaultCover: appSettings.customDefaultCoverDataUrl,
    // imagesGloballyVisible: appSettings.imagesGloballyVisible, // This specific prop is not directly used by Cell/Row anymore.
                                                                // MovieCard uses useAppSettings() directly.
                                                                // The key is that appSettings (and thus its context) change,
                                                                // which *should* trigger re-render of components consuming that context.
                                                                // The new reference for itemData itself (due to appSettings changing)
                                                                // will also help ensure react-window updates.
  }), [movies, onCardClick, appSettings.customDefaultCoverDataUrl, appSettings.imagesGloballyVisible]);


  if (viewMode === 'card') {
    return (
      <div style={containerStyle} className="px-2">
        <AutoSizer>
          {({ height, width }) => {
            const columnCount = Math.max(1, Math.floor(width / (CARD_WIDTH + GAP)));
            const rowCount = Math.ceil(movies.length / columnCount);
            return (
              <FixedSizeGrid
                columnCount={columnCount}
                columnWidth={CARD_WIDTH + GAP}
                height={height}
                rowCount={rowCount}
                rowHeight={CARD_HEIGHT + GAP}
                width={width}
                itemData={{...itemData, columnCount}} 
                className="settings-scroll-container"
              >
                {CardCell}
              </FixedSizeGrid>
            );
          }}
        </AutoSizer>
      </div>
    );
  }

  if (viewMode === 'compactList') {
     return (
      <div style={containerStyle} className="px-2"> 
        <AutoSizer>
          {({ height, width }) => {
            const avgItemWidthForColCalc = 280; 
            const columnCount = Math.max(1, Math.floor(width / (avgItemWidthForColCalc + GAP)));
            const actualColumnWidth = width / columnCount; 
            
            const rowCount = Math.ceil(movies.length / columnCount);
            return (
              <FixedSizeGrid
                columnCount={columnCount}
                columnWidth={actualColumnWidth} 
                height={height}
                rowCount={rowCount}
                rowHeight={COMPACT_ITEM_HEIGHT + GAP} 
                width={width}
                itemData={{...itemData, columnCount}}
                className="settings-scroll-container"
              >
                {CompactListCell}
              </FixedSizeGrid>
            );
          }}
        </AutoSizer>
      </div>
    );
  }

  if (viewMode === 'detailedList') {
    return (
      <div style={containerStyle} className="px-2">
        <AutoSizer>
          {({ height, width }) => (
            <FixedSizeList
              height={height}
              itemCount={movies.length}
              itemSize={DETAILED_ITEM_HEIGHT + GAP} 
              width={width}
              itemData={itemData} 
              className="settings-scroll-container"
            >
              {DetailedListRow}
            </FixedSizeList>
          )}
        </AutoSizer>
      </div>
    );
  }
  
  if (viewMode === 'waterfall') {
     return (
      <div className="masonry-wall columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 2xl:columns-6 gap-4 px-4">
        {movies.map((movie) => (
          <div key={movie.db_id || movie.filePath} className="mb-4 break-inside-avoid-column">
            <MovieCard 
              movie={movie}
              onCardClick={onCardClick}
              appDefaultCover={appSettings.customDefaultCoverDataUrl}
            />
          </div>
        ))}
      </div>
    );
  }

  return <div className="text-center p-4">视图模式配置错误或未实现。</div>;
};

export default MovieGridDisplay;
