// soul-forge-electron/src/components/detail_modal_sections/MovieDetailCoverSection.tsx
import React from 'react';
import { Movie } from '../../types';
import ImageWithFallback from '../ImageWithFallback';
import PlayIcon from '../PlayIcon';
import { EditableMovieState } from './utils';

interface MovieDetailCoverSectionProps {
  movie: Movie;
  editableMovie: EditableMovieState;
  isEditing: boolean;
  appDefaultCover?: string | null;
  onPlayVideo: () => void;
  onOpenTrailer: () => void;
  onDownloadCover: () => void;
  onBrowseLocalCover: () => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isCoverActionLoading: boolean;
  onCoverClick?: () => void; 
}

const MovieDetailCoverSection: React.FC<MovieDetailCoverSectionProps> = ({
  movie,
  editableMovie,
  isEditing,
  appDefaultCover,
  onPlayVideo,
  onOpenTrailer,
  onDownloadCover,
  onBrowseLocalCover,
  onInputChange,
  isCoverActionLoading,
  onCoverClick, 
}) => {
  
  const coverSrc = isEditing ? editableMovie.coverDataUrl : movie.coverDataUrl;
  const displayObject = isEditing ? editableMovie : movie;

  const placeholderCover = (
    <div className="w-full h-full flex flex-col items-center justify-center text-neutral-500 bg-[#2c2c2c] border border-[#4f4f4f] rounded-lg">
      <PlayIcon className="w-16 h-16 opacity-30 mb-2" />
      <p className="text-sm">暂无封面</p>
    </div>
  );

  return (
    <div className="w-full lg:w-1/3 flex-shrink-0 space-y-3">
      <div 
        className="aspect-[2/3] rounded-lg flex items-center justify-center overflow-hidden shadow-lg border border-[#4f4f4f] cursor-pointer group"
        onClick={onCoverClick} 
        title="点击放大封面"
      >
        <ImageWithFallback
          primarySrc={coverSrc}
          secondarySrc={displayObject.posterUrl}
          tertiarySrc={displayObject.coverUrl}
          appDefaultCoverDataUrl={appDefaultCover}
          alt={`封面: ${displayObject.title || displayObject.fileName}`}
          className="w-full h-full object-contain bg-[#2c2c2c] group-hover:opacity-80 transition-opacity"
          placeholder={placeholderCover}
        />
      </div>
      {!isEditing && (
        <div className="grid grid-cols-2 gap-2">
          <button onClick={onPlayVideo} className="button-primary-app text-sm py-2.5">播放影片</button>
          <button onClick={onOpenTrailer} disabled={!displayObject.trailerUrl} className="button-secondary-app text-sm py-2.5">播放预告</button>
        </div>
      )}
      {isEditing && (
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <button onClick={onDownloadCover} disabled={!editableMovie.posterUrl || isCoverActionLoading} className="button-secondary-app text-xs py-2">下载网络封面</button>
            <button onClick={onBrowseLocalCover} disabled={isCoverActionLoading} className="button-secondary-app text-xs py-2">浏览本地封面</button>
          </div>
          {isCoverActionLoading && <p className="text-xs text-center text-amber-400">封面处理中...</p>}
          <input type="text" name="posterUrl" value={editableMovie.posterUrl || ''} onChange={onInputChange} placeholder="网络封面图 URL" className="form-input-app text-sm" />
          <input type="text" name="trailerUrl" value={editableMovie.trailerUrl || ''} onChange={onInputChange} placeholder="预告片 URL (可选)" className="form-input-app text-sm" />
        </div>
      )}
    </div>
  );
};

export default MovieDetailCoverSection;