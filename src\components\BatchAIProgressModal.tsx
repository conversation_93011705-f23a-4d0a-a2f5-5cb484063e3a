import React from 'react';
import { useArchiveStore } from '../hooks/useArchiveStore';
import { X, Brain, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

export function BatchAIProgressModal() {
  const { isBatchAnalyzing, batchProgress, setBatchStatus } = useArchiveStore();

  if (!isBatchAnalyzing && batchProgress.status !== 'completed') {
    return null;
  }

  const handleClose = () => {
    setBatchStatus({ status: 'idle' });
  };

  const getStatusIcon = () => {
    switch (batchProgress.status) {
      case 'started':
      case 'processing':
        return <Loader2 className="h-6 w-6 text-blue-400 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-6 w-6 text-green-400" />;
      case 'error':
        return <AlertCircle className="h-6 w-6 text-red-400" />;
      default:
        return <Brain className="h-6 w-6 text-purple-400" />;
    }
  };

  const getProgressPercentage = () => {
    if (!batchProgress.total || batchProgress.total === 0) return 0;
    return Math.round((batchProgress.processed || 0) / batchProgress.total * 100);
  };

  const isCompleted = batchProgress.status === 'completed';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <h3 className="text-lg font-semibold text-white">
              批量 AI 分析
            </h3>
          </div>
          {isCompleted && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-300"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* 进度信息 */}
        <div className="space-y-4">
          {/* 进度条 */}
          {batchProgress.total && batchProgress.total > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-300">
                <span>进度</span>
                <span>
                  {batchProgress.processed || 0} / {batchProgress.total} 
                  ({getProgressPercentage()}%)
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    isCompleted ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
            </div>
          )}

          {/* 当前处理的记录 */}
          {batchProgress.currentTitle && (
            <div className="space-y-1">
              <div className="text-sm text-gray-400">正在处理:</div>
              <div className="text-sm text-white bg-gray-700 rounded p-2 truncate">
                {batchProgress.currentTitle}
              </div>
            </div>
          )}

          {/* 状态消息 */}
          {batchProgress.message && (
            <div className="text-sm text-gray-300 bg-gray-700 rounded p-3">
              {batchProgress.message}
            </div>
          )}

          {/* 统计信息 */}
          {isCompleted && (
            <div className="grid grid-cols-2 gap-4 pt-2">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {batchProgress.successCount || 0}
                </div>
                <div className="text-xs text-gray-400">成功</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">
                  {batchProgress.errorCount || 0}
                </div>
                <div className="text-xs text-gray-400">失败</div>
              </div>
            </div>
          )}

          {/* 完成时的操作按钮 */}
          {isCompleted && (
            <div className="flex justify-end pt-4">
              <button
                onClick={handleClose}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
              >
                确定
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
