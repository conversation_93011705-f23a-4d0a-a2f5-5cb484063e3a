
import React from 'react';
import { Movie } from '../types';
import ImageWithFallback from './ImageWithFallback';
import PlayIcon from './PlayIcon'; // Placeholder for thumbnail

interface MovieListItemDetailedProps {
  movie: Movie;
  onCardClick?: (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => void;
  appDefaultCover?: string | null;
}

const formatRuntime = (runtimeMinutes?: number | null): string => {
    if (runtimeMinutes === null || runtimeMinutes === undefined || isNaN(runtimeMinutes) || runtimeMinutes <= 0) return '';
    if (runtimeMinutes < 60) return `${runtimeMinutes}分钟`;
    const hours = Math.floor(runtimeMinutes / 60);
    const minutes = runtimeMinutes % 60;
    return `${hours}小时${minutes > 0 ? ` ${minutes}分钟` : ''}`.trim();
};

const MovieListItemDetailed: React.FC<MovieListItemDetailedProps> = ({ movie, onCardClick, appDefaultCover }) => {
  const displayTitle = movie.title || movie.fileName;
  const isMultiVersion = (movie.versionCount || 0) > 1;
  const isMultiCD = (movie.multiCdCountForNfoId || 0) > 1;


  const thumbnailPlaceholder = (
    <div className="w-full h-full flex items-center justify-center bg-[#333333] text-neutral-500">
      <PlayIcon className="w-10 h-10 opacity-50" />
    </div>
  );

  return (
    <div
      className="flex p-3 bg-[#2c2c2c] rounded-lg border border-[#444444] hover:bg-[#383838] transition-colors duration-150 cursor-pointer group shadow-md"
      onClick={() => onCardClick?.(movie, isMultiVersion, isMultiCD)}
      aria-label={`影片: ${displayTitle}`}
    >
      <div className="flex-shrink-0 w-24 h-36 mr-4 rounded overflow-hidden border border-[#4f4f4f]">
        <ImageWithFallback
          primarySrc={movie.coverDataUrl}
          secondarySrc={movie.posterUrl}
          tertiarySrc={movie.coverUrl}
          appDefaultCoverDataUrl={appDefaultCover}
          alt={`封面: ${displayTitle}`}
          className="w-full h-full object-cover"
          placeholder={thumbnailPlaceholder}
        />
      </div>
      <div className="flex-grow overflow-hidden">
        <div className="flex justify-between items-start mb-1">
          <h3 className="text-base font-semibold text-neutral-100 group-hover:text-amber-400 " title={displayTitle}>
            {displayTitle}
          </h3>
        </div>
        <p className="text-xs text-neutral-400 mb-1.5">
          {movie.year && <span>{movie.year}</span>}
          {movie.runtime && <span className="mx-1.5">|</span>}
          {movie.runtime && <span>{formatRuntime(movie.runtime)}</span>}
          {isMultiVersion && <span className="ml-2 text-[10px] bg-black/50 px-1.5 py-0.5 rounded-sm">{movie.versionCount} 版本</span>}
          {isMultiCD && <span className="ml-2 text-[10px] bg-black/50 px-1.5 py-0.5 rounded-sm">{movie.multiCdCountForNfoId} CD</span>}
        </p>
        {movie.plot && (
          <p className="text-xs text-neutral-300 mb-2 leading-relaxed line-clamp-2" title={movie.plot}>
            {movie.plot}
          </p>
        )}
        {(movie.genres && movie.genres.length > 0) && (
          <div className="mb-1.5">
            <span className="text-xs font-medium text-neutral-500">类型: </span>
            {movie.genres.slice(0, 3).map((genre, index) => (
              <span key={index} className="text-xs text-neutral-400">
                {genre}{index < Math.min(2, movie.genres!.length - 1) ? ', ' : ''}
              </span>
            ))}
            {movie.genres.length > 3 && <span className="text-xs text-neutral-400">...</span>}
          </div>
        )}
        {(movie.actors && movie.actors.length > 0) && (
          <div>
            <span className="text-xs font-medium text-neutral-500">演员: </span>
            {movie.actors.slice(0, 3).map((actor, index) => (
              <span key={index} className="text-xs text-neutral-400">
                {actor}{index < Math.min(2, movie.actors!.length - 1) ? ', ' : ''}
              </span>
            ))}
            {movie.actors.length > 3 && <span className="text-xs text-neutral-400">...</span>}
          </div>
        )}
      </div>
    </div>
  );
};

export default MovieListItemDetailed;