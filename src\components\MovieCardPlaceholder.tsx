
import React from 'react';
import { MovieCardPlaceholderProps } from '../types'; 

const MovieCardPlaceholder: React.FC<MovieCardPlaceholderProps> = ({ className = '' }) => {
  const cardWidth = 'w-[200px]';
  const cardHeight = 'h-[300px]';

  return (
    <div
      className={`
        ${cardWidth} ${cardHeight}
        bg-[#2c2c2c] rounded-lg border border-[#444444] 
        shadow-lg overflow-hidden 
        flex flex-col animate-pulse
        ${className}
      `}
      aria-hidden="true"
    >
      {/* Image Placeholder Area */}
      <div className="w-full h-3/5 bg-[#383838] flex items-center justify-center">
        {/* Optional: subtle icon or pattern */}
      </div>

      {/* Info Section Placeholder */}
      <div className="p-3 flex flex-col flex-grow justify-between">
        <div className="h-4 bg-[#383838] rounded w-3/4 mb-2"></div> {/* Title placeholder */}
        <div className="mt-auto flex justify-end">
          <div className="h-5 bg-[#383838] rounded w-10"></div> {/* Rating placeholder */}
        </div>
      </div>
    </div>
  );
};

export default MovieCardPlaceholder;
