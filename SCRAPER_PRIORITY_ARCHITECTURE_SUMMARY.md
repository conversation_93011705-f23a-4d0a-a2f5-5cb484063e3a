# 刮削源优先级管理 - 架构摘要与开发资料

## 📋 开发指令 [5.4-Pre] 完整资料交付

### 第一部分：当前架构分析

#### 🔄 设置系统架构

**1. 设置的生命周期流程：**
```
用户修改设置 → SettingsPage.tsx → handleSaveSettings() → 
window.sfeElectronAPI.saveSettings() → main.js IPC Handler → 
settingsService.saveSettings() → Electron Store 持久化
```

**2. 设置读取流程：**
```
应用启动/组件加载 → useAppSettings.ts → window.sfeElectronAPI.getSettings() → 
main.js IPC Handler → settingsService.getSettings() → 返回合并后的设置
```

**3. 关键服务方法：**
- `settingsService.saveSettings(newSettings)` - 验证并保存到 Electron Store
- `settingsService.getSettings()` - 合并默认值和用户设置
- `settingsService.initializeSettings(logger, userDataPath)` - 应用启动时调用

#### 🎯 刮削器当前架构

**1. Provider 注册机制：**
```javascript
// scraperManager.js 第21-26行
const providers = {
    'javbus': javbusProvider,
    'dmm': dmmProvider,
    'javdb': javdbProvider,
    // 'freejavbt': freejavbtProvider,
};
```

**2. 当前优先级机制（硬编码）：**
```javascript
// scraperManager.js 第15-18行
const PROVIDER_PRIORITY = [
    'javbus', 'freejavbt', 'jav321', 'dmm', 'javlibrary', 
    '7mmtv', 'javdb', 'avsex', 'airav', 'avsox'
];
```

**3. 刮削执行流程：**
```
用户触发刮削 → ScraperTestTool.tsx → window.sfeElectronAPI.scrapeMovie() → 
main.js IPC Handler → scraperManager.scrapeMovieById() → 
按 PROVIDER_PRIORITY 顺序尝试各个 Provider
```

### 第二部分：需要实现的功能

#### 🎯 目标功能
1. **可视化优先级管理** - 拖拽排序界面
2. **Provider 状态显示** - 已实现/未实现状态
3. **动态优先级配置** - 保存到用户设置
4. **实时生效机制** - 无需重启应用

#### 🔧 技术实现要点

**1. 设置结构扩展：**
```javascript
// 需要在 DEFAULT_SETTINGS 中添加
scraperProviderPriority: [
    'javbus', 'freejavbt', 'jav321', 'dmm', 'javlibrary', 
    '7mmtv', 'javdb', 'avsex', 'airav', 'avsox'
],
```

**2. ScraperManager 改造：**
```javascript
// 从设置服务读取优先级，而不是硬编码
const settings = settingsService.getSettings();
const PROVIDER_PRIORITY = settings.scraperProviderPriority || DEFAULT_PRIORITY;
```

**3. UI 组件结构：**
```
SettingsPage.tsx
├── 新增 'scrapers' 标签页
└── ScraperPriorityTab.tsx (新组件)
    ├── 拖拽排序列表
    ├── Provider 状态显示
    └── 实时保存功能
```

### 第三部分：关键源码资料

#### 1. 设置服务核心代码

**DEFAULT_SETTINGS 结构 (settingsService.js 第10-99行):**
- 包含所有默认设置项
- 需要添加 `scraperProviderPriority` 字段

**saveSettings 方法 (settingsService.js 第307-411行):**
- 验证和类型转换逻辑
- 需要添加数组类型的处理

**getSettings 方法 (settingsService.js 第271-305行):**
- 合并默认值和用户设置
- 返回完整的设置对象

#### 2. 设置页面组件结构

**SettingsPage.tsx 核心结构:**
- 标签页系统 (第209-220行)
- 内容渲染逻辑 (第222-295行)
- 设置保存机制 (已移除统一保存，改为实时保存)

**标签页配置 (第209-220行):**
```javascript
const tabs: { key: TabKey; label: string }[] = [
    { key: 'general', label: '常规与扫描' },
    { key: 'libraries', label: '片库管理' },
    // ... 需要添加 scrapers 标签页
];
```

#### 3. ScraperManager 核心逻辑

**Provider 注册 (第21-33行):**
- 动态加载已实现的 Provider
- 版本信息记录

**刮削执行逻辑 (第40-153行):**
- 按优先级顺序尝试
- 容错机制
- 媒体下载集成

### 第四部分：实现步骤建议

#### 步骤1：扩展设置结构
1. 在 `settingsService.js` 的 `DEFAULT_SETTINGS` 中添加 `scraperProviderPriority`
2. 在 `saveSettings` 方法中添加数组类型处理
3. 在 TypeScript 类型定义中添加相应字段

#### 步骤2：创建 UI 组件
1. 创建 `ScraperPriorityTab.tsx` 组件
2. 实现拖拽排序功能 (使用 react-beautiful-dnd 或类似库)
3. 显示 Provider 状态和描述信息

#### 步骤3：集成到设置页面
1. 在 `SettingsPage.tsx` 中添加新标签页
2. 实现实时保存功能
3. 添加重置为默认值功能

#### 步骤4：改造 ScraperManager
1. 修改 `scraperManager.js` 从设置读取优先级
2. 添加动态重载机制
3. 保持向后兼容性

### 第五部分：Provider 信息清单

#### 已实现的 Provider
1. **javbus** - JavBus 刮削器 (无版本信息)
2. **dmm** - DMM 刮削器 (v1.1.0)
3. **javdb** - JavDB 刮削器 (v1.1.0, 使用 CDP 连接)

#### 计划中的 Provider
4. **freejavbt** - FreeJavBT 刮削器
5. **jav321** - Jav321 刮削器
6. **javlibrary** - JavLibrary 刮削器
7. **7mmtv** - 7mmtv 刮削器
8. **avsex** - AvSex 刮削器
9. **airav** - AirAV 刮削器
10. **avsox** - AvSox 刮削器

#### Provider 特性对比
- **javbus**: 基础信息全面，需要 Cookie
- **dmm**: 官方数据，质量高，可能有地区限制
- **javdb**: 包含磁力链接，用户评分，需要登录

### 第六部分：技术注意事项

#### 1. 拖拽排序实现
- 推荐使用 `@dnd-kit/sortable` 或 `react-beautiful-dnd`
- 需要处理触摸设备兼容性
- 实现平滑的动画效果

#### 2. 实时保存机制
- 每次拖拽结束后立即保存
- 防抖处理避免频繁保存
- 错误处理和回滚机制

#### 3. 向后兼容性
- 保持现有硬编码优先级作为默认值
- 渐进式迁移，不破坏现有功能
- 提供重置为默认值的选项

#### 4. 性能考虑
- Provider 状态检查的缓存机制
- 避免频繁的设置读写操作
- 优化大列表的渲染性能

---

**总结**: 这份架构摘要提供了实现"刮削源优先级管理"功能所需的完整技术资料，包括当前架构分析、实现步骤建议、关键源码位置和技术注意事项。开发团队可以基于这些信息快速开始 UI 开发工作。
