// 测试 site-profiles.json 配置文件的脚本
const fs = require('fs');
const path = require('path');

function testSiteProfiles() {
  console.log('🔍 开始测试 site-profiles.json 配置文件...\n');
  
  try {
    // 检查文件是否存在
    const configPath = path.join(__dirname, 'site-profiles.json');
    if (!fs.existsSync(configPath)) {
      console.error('❌ site-profiles.json 文件不存在');
      return false;
    }
    console.log('✅ site-profiles.json 文件存在');
    
    // 读取并解析JSON文件
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    console.log('✅ JSON 格式验证成功');
    
    // 验证顶层结构
    const expectedForums = ['forumA', 'forumB'];
    const actualForums = Object.keys(config);
    
    console.log('\n📋 配置文件结构验证:');
    console.log(`期望的论坛: ${expectedForums.join(', ')}`);
    console.log(`实际的论坛: ${actualForums.join(', ')}`);
    
    // 检查是否包含所有期望的论坛
    const missingForums = expectedForums.filter(forum => !actualForums.includes(forum));
    if (missingForums.length > 0) {
      console.error(`❌ 缺少论坛配置: ${missingForums.join(', ')}`);
      return false;
    }
    console.log('✅ 所有期望的论坛配置都存在');
    
    // 验证每个论坛的配置字段
    const requiredTopLevelFields = ['name', 'loginUrl', 'config'];
    const requiredConfigFields = [
      'postContainerSelector',
      'postLinkSelector',
      'postDateSelector',
      'postBodyContainerSelector',
      'postTitleSelectorOnPage',
      'previewImageSelector',
      'magnetLinkSelector',
      'ed2kLinkSelector',
      'attachmentUrlSelector',
      'passwordSelector',
      'nextPageSelector'
    ];

    console.log('\n🔍 验证论坛配置字段:');

    for (const forumKey of expectedForums) {
      const forumConfig = config[forumKey];
      console.log(`\n📂 ${forumKey} (${forumConfig.name}):`);

      // 验证顶层字段
      for (const field of requiredTopLevelFields) {
        if (forumConfig.hasOwnProperty(field)) {
          console.log(`  ✅ ${field}: "${forumConfig[field]}"`);
        } else {
          console.error(`  ❌ 缺少顶层字段: ${field}`);
          return false;
        }
      }

      // 验证config内的字段
      if (forumConfig.config) {
        console.log(`  📋 验证config字段:`);
        for (const field of requiredConfigFields) {
          if (forumConfig.config.hasOwnProperty(field)) {
            console.log(`    ✅ ${field}: "${forumConfig.config[field]}"`);
          } else {
            console.error(`    ❌ 缺少config字段: ${field}`);
            return false;
          }
        }
      }

      // 验证boards字段
      if (forumConfig.boards) {
        const boardCount = Object.keys(forumConfig.boards).length;
        console.log(`  ✅ boards: ${boardCount} 个板块配置`);

        // 显示板块信息
        for (const [boardId, boardInfo] of Object.entries(forumConfig.boards)) {
          console.log(`    📌 板块 ${boardId}: ${boardInfo.name} (标签: ${boardInfo.tags?.join(', ') || '无'})`);
        }
      }
    }
    
    // 显示完整配置
    console.log('\n📄 完整配置内容:');
    console.log(JSON.stringify(config, null, 2));
    
    console.log('\n🎉 site-profiles.json 配置文件验证完成！');
    console.log('✅ 文件格式正确');
    console.log('✅ 包含所有必需的论坛配置');
    console.log('✅ 每个论坛都有完整的字段配置');
    console.log('✅ 可以被 Collector 模块正常加载和使用');
    
    return true;
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      console.error('❌ JSON 格式错误:', error.message);
    } else {
      console.error('❌ 测试过程中出错:', error.message);
    }
    return false;
  }
}

// 运行测试
const success = testSiteProfiles();
process.exit(success ? 0 : 1);
