// soul-forge-electron/src/components/MovieDetailModal.tsx
import React from 'react';
import { Movie, DetailFilterType, AppSettings, MovieDetailModalProps as OriginalModalProps } from '../types'; 
import MovieDetailCoverSection from './detail_modal_sections/MovieDetailCoverSection';
import MovieDetailAiActions from './detail_modal_sections/MovieDetailAiActions';
import MovieDetailInfoSection from './detail_modal_sections/MovieDetailInfoSection';
import MovieDetailSnapshotsSection from './detail_modal_sections/MovieDetailSnapshotsSection';
import { VersionCenter } from './VersionCenter';
import { Tabs, TabsList, TabsTrigger, TabsContent } from './ui/Tabs';
import EmbeddedTrailerPlayerModal from './EmbeddedTrailerPlayerModal';
import ImagePreviewModal from './ImagePreviewModal';
import WhatsLinkPreviewModal from './WhatsLinkPreviewModal';
import { useMovieDetailManager } from '../hooks/useMovieDetailManager';
import { useModalManager } from '../hooks/useModalManager';
import { ActorLink } from './common/ActorLink';
import { LuLoader, LuFileText, LuEye, LuDownload, LuTrash2, LuExternalLink } from 'react-icons/lu';

export interface MovieDetailModalProps extends OriginalModalProps {}

const getTagStyle = (tagText: string): string => { 
  const lowerTag = tagText.toLowerCase();
  if (lowerTag.includes('4k')) return 'bg-amber-500 text-black';
  if (lowerTag.includes('vr')) return 'bg-purple-600 text-white';
  if (lowerTag.includes('bd原盘')) return 'bg-sky-700 text-white';
  if (lowerTag.includes('中文字幕')) return 'bg-red-600 text-white';
  if (lowerTag.includes('破解版')) return 'bg-orange-500 text-black';
  if (lowerTag.includes('外挂字幕')) return 'bg-green-600 text-white';
  if (lowerTag.includes('流出版')) return 'bg-neutral-500 text-white';
  return 'bg-slate-600 text-white';
};

export const MovieDetailModal: React.FC<MovieDetailModalProps> = ({
    movie: initialMovieFromProp,
    onClose,
    onUpdateMovie,
    onTagClick,
    appDefaultCover,
    appDefaultActorAvatar,
    currentSettings
}) => {
  const {
    movie,
    isEditing, setIsEditing,
    editableMovie,
    setEditableMovie,
    isSaving,
    isCoverActionLoading,
    isTrailerPlayerOpen, trailerPlayerUrl,

    snapshots, isLoadingSnapshots,
    showSnapshotsSection, setShowSnapshotsSection,
    aiSuggestedSnapshotIndex,
    favoriteStatus,
    isCoverPreviewOpen, coverPreviewUrl,
    actorAvatars, isPreviewAvatarModalOpen, previewAvatarUrl,
    isGeneratingPlot, isEmbellishingPlot, isAnalyzingTags,
    aiGeneratedPlotDisplay, aiEmbellishedPlotDisplay,
    handleInputChange,
    handleSaveChanges,
    handlePlayVideo,
    handleOpenTrailer, handleCloseTrailerPlayer,
    handleDownloadCover, handleBrowseLocalCover, handleSetSnapshotAsCover,
    handleToggleFavorite,
    handleOpenCoverPreview, handleCloseCoverPreview,
    handleOpenAvatarPreview, handleCloseAvatarPreview,
    generatePlot,
    embellishPlot,
    analyzeTags,
    suggestCoverFromSnapshots,
    fetchSnapshots,
    // 版本管理相关
    allVersions,
    isLoadingVersions,
    currentVersion,
    handleVersionChange,
    // 【新增】分组快照相关
    groupedSnapshots,
    isLoadingGroupedSnapshots,
    fetchAllGroupedSnapshots,
    refreshGroupedSnapshots,
    generateSnapshotsForVersion,
    isGeneratingSnapshotsForVersion,
    // NFO导出相关
    isExportingNfo,
    nfoExportResult,
    handleExportNfo,
  } = useMovieDetailManager({
    initialMovie: initialMovieFromProp,
    onUpdateMovieCallback: onUpdateMovie,
    appSettings: currentSettings,
    onCloseCallback: onClose,
  });

  // WhatsLink 预览模态框管理
  const {
    isModalOpen: isWhatsLinkModalOpen,
    modalData: whatsLinkData,
    isLoading: isWhatsLinkLoading,
    error: whatsLinkError,
    openModal: openWhatsLinkModal,
    closeModal: closeWhatsLinkModal
  } = useModalManager();

  // 处理磁力链接预览
  const handlePreviewMagnetLink = async (magnetUrl: string) => {
    try {
      openWhatsLinkModal(null, true); // 打开模态框并显示加载状态

      const result = await window.sfeElectronAPI.queryWhatsLink(magnetUrl);

      if (result.success) {
        openWhatsLinkModal(result.data, false); // 更新数据并隐藏加载状态
      } else {
        openWhatsLinkModal(null, false, result.error || '预览失败');
      }
    } catch (error) {
      console.error('WhatsLink 预览失败:', error);
      openWhatsLinkModal(null, false, '网络请求失败');
    }
  };

  // 智能标签点击处理函数
  const handleSmartTagClick = (value: string, type?: DetailFilterType) => {
    if (type === 'actor') {
      // 演员标签：触发打开名人堂
      const event = new CustomEvent('openActorProfile', {
        detail: { actorName: value }
      });
      document.dispatchEvent(event);
    } else if (type && onTagClick) {
      // 其他标签：执行搜索过滤
      onTagClick(value, type);
    } else if (onTagClick) {
      // 兼容旧的调用方式，默认为tag类型
      onTagClick(value, 'tag');
    }
  };

  if (!movie || !editableMovie) return null;

  const displayObject = isEditing ? editableMovie : movie;

  // 【重构】严格按照"A区优先，旧数据兜底"原则获取数据
  const title = movie.displayData?.title || movie.title || movie.fileName;
  const displayId = movie.displayData?.display_id || movie.nfoId || '';
  const movieType = movie.displayData?.type || 'other';
  const coverPath = movie.displayData?.cover_path || movie.localCoverPath || '';
  const coverOrientation = movie.displayData?.cover_orientation || 'portrait';
  const nfoPrefix = movie.displayData?.nfo_prefix || displayId.replace(/[-\d]+$/, '') || '';

  // 信息区 B 数据
  const year = movie.displayData?.year || movie.year?.toString() || '';
  const releaseDate = movie.displayData?.release_date || movie.releaseDate || '';
  const runtime = movie.displayData?.runtime || movie.runtime || 0;
  const studio = movie.displayData?.studio || movie.studio || '';
  const publisher = movie.displayData?.publisher || studio;
  const series = movie.displayData?.series || movie.series || '';
  const rating = movie.displayData?.rating || null;
  const tags = movie.displayData?.tags || movie.tags || [];

  // 信息区 C 数据
  const actresses = movie.displayData?.actresses ||
                   (movie.actors ? movie.actors.map(name => ({ name, avatar_path: null })) : []);
  const actorsMale = movie.displayData?.actors_male || [];
  const director = movie.displayData?.director ||
                  (movie.director ? { name: movie.director, avatar_path: null } : null);

  // 信息区 D 数据
  const plot = movie.displayData?.plot || movie.plot || '';
  const previewImagePaths = movie.displayData?.preview_image_paths || [];
  const userReviews = movie.displayData?.user_reviews || [];
  const similarMovies = movie.displayData?.similar_movies || [];

  return (
    <>
      <div 
        className="fixed inset-0 z-[65] flex items-center justify-center p-4 bg-black/80 backdrop-blur-md"
        onClick={onClose} 
        aria-modal="true" 
        role="dialog"
        aria-labelledby="movie-detail-title"
      >
        <div 
          className="bg-[#232323] text-neutral-100 rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] flex flex-col overflow-hidden border border-[#4f4f4f]"
          onClick={(e) => e.stopPropagation()} 
        >
          <div className="flex items-center justify-between p-5 border-b border-[#3a3a3a] bg-[#2a2a2a]">
            <h2 id="movie-detail-title" className="text-xl sm:text-2xl font-bold text-[#B8860B] truncate pr-4" title={title}>
              {title}
              {displayObject.cdPartInfo && <span className="text-base text-teal-400 ml-2">({displayObject.cdPartInfo})</span>}
            </h2>
            <div className="flex items-center space-x-2">
              {!isEditing && tags && tags.length > 0 && (
                  <div className="flex items-center gap-1.5">
                  {tags.slice(0, 3).map(tag => (
                      <span
                      key={tag}
                      className={`text-[9px] font-semibold px-1.5 py-0.5 rounded-sm shadow-sm ${getTagStyle(tag)}`}
                      title={tag}
                      >
                      {tag}
                      </span>
                  ))}
                  </div>
              )}
              <button onClick={onClose} className="text-neutral-400 hover:text-white transition-colors p-1 rounded-full hover:bg-[#3a3a3a]" aria-label="关闭详情">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-7 h-7"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
              </button>
            </div>
          </div>

          {/* 【重构】两栏式布局：左侧信息区 + 右侧影片区 */}
          <div className="flex-grow flex overflow-hidden">
            {/* 左侧信息区 - 可独立滚动 */}
            <div className="w-1/2 border-r border-[#3a3a3a] overflow-y-auto settings-scroll-container">
              <div className="p-4 space-y-6">
                {/* 信息区 A: 封面 + 类型/前缀 */}
                <div className="space-y-4">
                  <div className="relative">
                    <img
                      src={movie.coverDataUrl || appDefaultCover}
                      alt={`${title} 封面`}
                      className={`w-full max-w-[300px] mx-auto rounded-lg shadow-lg cursor-pointer
                        ${coverOrientation === 'landscape' ? 'object-cover object-right h-[400px]' : 'object-contain'}`}
                      onClick={handleOpenCoverPreview}
                    />
                    {/* 类型和前缀信息 */}
                    <div className="mt-3 flex items-center gap-3">
                      <span className="px-2 py-1 bg-blue-600 text-white rounded text-sm font-medium">
                        {movieType === 'uncensored' ? '无码' :
                         movieType === 'chinese' ? '国产' :
                         movieType === 'western' ? '欧美' :
                         movieType === 'vr' ? 'VR' : '有码'}
                      </span>
                      {nfoPrefix && (
                        <button
                          className="px-2 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
                          onClick={() => handleSmartTagClick(nfoPrefix, 'nfoId')}
                          title="点击搜索该前缀"
                        >
                          {nfoPrefix}
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* 信息区 B: 基础信息 */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-[#B8860B] border-b border-[#3a3a3a] pb-2">基础信息</h3>
                  {[
                    { label: '番号', value: displayId, clickable: true },
                    { label: '年份', value: year, clickable: true },
                    { label: '发行日期', value: releaseDate, clickable: true },
                    { label: '时长', value: runtime ? `${runtime} 分钟` : '', clickable: false },
                    { label: '片商', value: studio, clickable: true },
                    { label: '发行', value: publisher, clickable: true },
                    { label: '系列', value: series, clickable: true },
                  ].filter(item => item.value).map(item => (
                    <div key={item.label} className="flex items-center justify-between py-1">
                      <span className="text-gray-400 text-sm">{item.label}:</span>
                      <div className="flex items-center gap-2">
                        {item.clickable ? (
                          <button
                            className="text-white hover:text-[#B8860B] transition-colors text-sm"
                            onClick={() => handleSmartTagClick(item.value, 'tag')}
                          >
                            {item.value}
                          </button>
                        ) : (
                          <span className="text-white text-sm">{item.value}</span>
                        )}
                        <button
                          className="text-pink-400 hover:text-pink-300 transition-colors"
                          title="收藏"
                        >
                          ❤️
                        </button>
                      </div>
                    </div>
                  ))}

                  {/* 评分 */}
                  {rating && (
                    <div className="flex items-center justify-between py-1">
                      <span className="text-gray-400 text-sm">评分:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-yellow-400 text-sm">
                          ⭐ {rating.score} ({rating.votes} 票)
                        </span>
                        <button className="text-pink-400 hover:text-pink-300 transition-colors" title="收藏">❤️</button>
                      </div>
                    </div>
                  )}
                </div>

                {/* 信息区 C: 演员和导演 */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-[#B8860B] border-b border-[#3a3a3a] pb-2">演员阵容</h3>

                  {/* 女优 */}
                  {actresses.length > 0 && (
                    <div>
                      <h4 className="text-sm text-gray-400 mb-2">女优:</h4>
                      <div className="space-y-2">
                        {actresses.map((actress, index) => (
                          <div key={index} className="flex items-center justify-between py-1">
                            <button
                              className="text-white hover:text-[#B8860B] transition-colors text-sm"
                              onClick={() => handleSmartTagClick(actress.name, 'actor')}
                            >
                              {actress.name}
                            </button>
                            <button className="text-pink-400 hover:text-pink-300 transition-colors" title="收藏">❤️</button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 男优 */}
                  {actorsMale.length > 0 && (
                    <div>
                      <h4 className="text-sm text-gray-400 mb-2">男优:</h4>
                      <div className="space-y-2">
                        {actorsMale.map((actor, index) => (
                          <div key={index} className="flex items-center justify-between py-1">
                            <button
                              className="text-white hover:text-[#B8860B] transition-colors text-sm"
                              onClick={() => handleSmartTagClick(actor.name, 'actor')}
                            >
                              {actor.name}
                            </button>
                            <button className="text-pink-400 hover:text-pink-300 transition-colors" title="收藏">❤️</button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 导演 */}
                  {director && (
                    <div className="flex items-center justify-between py-1">
                      <span className="text-gray-400 text-sm">导演:</span>
                      <div className="flex items-center gap-2">
                        <button
                          className="text-white hover:text-[#B8860B] transition-colors text-sm"
                          onClick={() => handleSmartTagClick(director.name, 'director')}
                        >
                          {director.name}
                        </button>
                        <button className="text-pink-400 hover:text-pink-300 transition-colors" title="收藏">❤️</button>
                      </div>
                    </div>
                  )}
                </div>

                {/* 信息区 D: 简介和其他 */}
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-[#B8860B] border-b border-[#3a3a3a] pb-2">详细信息</h3>

                  {/* 简介 */}
                  {plot && (
                    <div>
                      <h4 className="text-sm text-gray-400 mb-2">简介:</h4>
                      <p className="text-sm text-gray-300 leading-relaxed">{plot}</p>
                    </div>
                  )}

                  {/* 标签 */}
                  {tags.length > 0 && (
                    <div>
                      <h4 className="text-sm text-gray-400 mb-2">标签:</h4>
                      <div className="flex flex-wrap gap-2">
                        {tags.map(tag => (
                          <button
                            key={tag}
                            className="px-2 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-500 transition-colors"
                            onClick={() => handleSmartTagClick(tag, 'tag')}
                          >
                            {tag}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 预览图 */}
                  {previewImagePaths.length > 0 && (
                    <div>
                      <h4 className="text-sm text-gray-400 mb-2">预览图:</h4>
                      <div className="flex gap-2 overflow-x-auto">
                        {previewImagePaths.slice(0, 5).map((imagePath, index) => (
                          <img
                            key={index}
                            src={imagePath}
                            alt={`预览图 ${index + 1}`}
                            className="w-20 h-12 object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 右侧影片区 - 可独立滚动 */}
            <div className="w-1/2 overflow-y-auto settings-scroll-container">
              <div className="p-4">
                <Tabs defaultValue="versions" className="w-full">
                  <TabsList className="mb-4">
                    <TabsTrigger value="versions">本地影片</TabsTrigger>
                    <TabsTrigger value="virtual">云端影片</TabsTrigger>
                    <TabsTrigger value="trailer">预告片</TabsTrigger>
                    <TabsTrigger value="snapshots">影片快照</TabsTrigger>
                  </TabsList>

                  <TabsContent value="versions">
                    <VersionCenter
                        versions={allVersions}
                        selectedVersion={currentVersion}
                        onSelectVersion={handleVersionChange}
                        isLoading={isLoadingVersions}
                    />
                  </TabsContent>

                  <TabsContent value="virtual">
                    <div className="space-y-4">
                      {/* 云端影片列表 */}
                      {allVersions && allVersions.length > 0 ? (
                        <div className="space-y-3">
                          {/* 筛选出虚拟版本 */}
                          {allVersions.filter(version => version.type === 'virtual').length > 0 ? (
                            allVersions
                              .filter(version => version.type === 'virtual')
                              .map((virtualVersion, index) => (
                                <div
                                  key={`virtual-${virtualVersion.db_id || index}`}
                                  className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
                                >
                                  <div className="flex items-center justify-between">
                                    {/* 左侧：基本信息 */}
                                    <div className="flex-1">
                                      <div className="flex items-center gap-2 mb-2">
                                        <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">
                                          云端
                                        </span>
                                        <h4 className="text-white font-medium truncate">
                                          {virtualVersion.title || virtualVersion.post_title || '未知标题'}
                                        </h4>
                                      </div>

                                      <div className="text-gray-400 text-sm space-y-1">
                                        {virtualVersion.source_forum && (
                                          <div>来源: {virtualVersion.source_forum}</div>
                                        )}
                                        {virtualVersion.collection_date && (
                                          <div>收集时间: {new Date(virtualVersion.collection_date).toLocaleDateString()}</div>
                                        )}
                                        {virtualVersion.download_status && (
                                          <div>状态: {virtualVersion.download_status}</div>
                                        )}
                                      </div>
                                    </div>

                                    {/* 右侧：操作按钮 */}
                                    <div className="flex items-center gap-2 ml-4">
                                      {/* 预览按钮 - 仅当有磁力链接时显示 */}
                                      {virtualVersion.magnet_link && (
                                        <button
                                          onClick={() => handlePreviewMagnetLink(virtualVersion.magnet_link)}
                                          className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center gap-2 text-sm"
                                          title="预览磁力链接内容"
                                        >
                                          <LuEye className="h-4 w-4" />
                                          预览
                                        </button>
                                      )}

                                      {/* 下载按钮 */}
                                      {virtualVersion.magnet_link && (
                                        <button
                                          onClick={() => {
                                            navigator.clipboard.writeText(virtualVersion.magnet_link);
                                            // TODO: 可以添加下载功能
                                          }}
                                          className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center gap-2 text-sm"
                                          title="复制磁力链接"
                                        >
                                          <LuDownload className="h-4 w-4" />
                                          下载
                                        </button>
                                      )}

                                      {/* 查看详情按钮 */}
                                      {virtualVersion.post_url && (
                                        <button
                                          onClick={() => window.open(virtualVersion.post_url, '_blank')}
                                          className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors flex items-center gap-2 text-sm"
                                          title="查看原帖"
                                        >
                                          <LuExternalLink className="h-4 w-4" />
                                          详情
                                        </button>
                                      )}
                                    </div>
                                  </div>

                                  {/* 磁力链接信息 */}
                                  {virtualVersion.magnet_link && (
                                    <div className="mt-3 pt-3 border-t border-gray-700">
                                      <div className="text-gray-400 text-xs">
                                        <span className="font-medium">磁力链接:</span>
                                        <span className="ml-2 font-mono break-all">
                                          {virtualVersion.magnet_link.substring(0, 60)}...
                                        </span>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))
                          ) : (
                            <div className="text-center text-gray-400 py-8">
                              <p>暂无云端影片资源</p>
                              <p className="text-sm mt-2">云端影片来自情报中心的收集数据</p>
                            </div>
                          )}
                        </div>
                      ) : isLoadingVersions ? (
                        <div className="flex items-center justify-center py-12">
                          <LuLoader className="h-8 w-8 text-[#B8860B] animate-spin" />
                          <span className="ml-3 text-white">正在加载云端影片...</span>
                        </div>
                      ) : (
                        <div className="text-center text-gray-400 py-8">
                          <p>暂无云端影片资源</p>
                          <p className="text-sm mt-2">云端影片来自情报中心的收集数据</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="trailer">
                    <div className="space-y-4">
                      {movie.trailerUrl ? (
                        <div className="text-center">
                          <button
                            onClick={handleOpenTrailer}
                            className="button-primary-app px-6 py-3"
                          >
                            播放预告片
                          </button>
                        </div>
                      ) : (
                        <div className="text-center text-gray-400 py-8">
                          <p>暂无预告片</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="snapshots">
                    <MovieDetailSnapshotsSection
                        movie={movie}
                        snapshots={snapshots}
                        isLoadingSnapshots={isLoadingSnapshots}
                        onFetchSnapshots={fetchSnapshots}
                        onSuggestCover={suggestCoverFromSnapshots}
                        onSetSnapshotAsCover={handleSetSnapshotAsCover}
                        isSuggestingCover={isAnalyzingTags}
                        isCoverActionLoading={isCoverActionLoading}
                        aiSuggestedSnapshotIndex={aiSuggestedSnapshotIndex}
                        showSnapshotsSection={showSnapshotsSection}
                        setShowSnapshotsSection={setShowSnapshotsSection}
                        isEditing={isEditing}
                        groupedSnapshots={groupedSnapshots}
                        isLoadingGroupedSnapshots={isLoadingGroupedSnapshots}
                        refreshGroupedSnapshots={refreshGroupedSnapshots}
                        onGenerateSnapshotsForVersion={generateSnapshotsForVersion}
                        isGeneratingSnapshotsForVersion={isGeneratingSnapshotsForVersion}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>

          <div className="p-4 border-t border-[#3a3a3a] bg-[#2a2a2a] flex justify-between items-center">
            <div className="text-sm text-gray-400">
              影片番号: {displayId} | 数据来源: {movie.displayData ? 'A区精炼数据' : '原始数据库'}
            </div>
            <div className="flex items-center space-x-2">
              {isEditing ? (
                <>
                  <button onClick={() => setIsEditing(false)} className="button-neutral-app px-4 py-2 text-sm">取消</button>
                  <button onClick={handleSaveChanges} disabled={isSaving} className="button-primary-app px-6 py-2 text-sm">{isSaving ? '保存中...' : '保存更改'}</button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleExportNfo}
                    disabled={isExportingNfo}
                    className="button-neutral-app px-4 py-2 text-sm flex items-center gap-2"
                    title="导出/更新 .nfo 文件"
                  >
                    {isExportingNfo ? (
                      <>
                        <LuLoader className="h-4 w-4 animate-spin" />
                        导出中...
                      </>
                    ) : (
                      <>
                        <LuFileText className="h-4 w-4" />
                        导出NFO
                      </>
                    )}
                  </button>
                  <button onClick={() => setIsEditing(true)} className="button-primary-app px-6 py-2 text-sm">编辑影片信息</button>
                </>
              )}
            </div>

            {/* NFO导出结果显示 */}
            {nfoExportResult && (
              <div className={`mt-4 p-3 rounded-lg flex items-center gap-2 ${
                nfoExportResult.success
                  ? 'bg-green-900/20 border border-green-500/30'
                  : 'bg-red-900/20 border border-red-500/30'
              }`}>
                <span className={`text-sm ${
                  nfoExportResult.success ? 'text-green-300' : 'text-red-300'
                }`}>
                  {nfoExportResult.message}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
      <EmbeddedTrailerPlayerModal isOpen={isTrailerPlayerOpen} url={trailerPlayerUrl} onClose={handleCloseTrailerPlayer} movieTitle={title} />
      <ImagePreviewModal isOpen={isCoverPreviewOpen} imageUrl={coverPreviewUrl} altText="封面预览" onClose={handleCloseCoverPreview} />
      <ImagePreviewModal isOpen={isPreviewAvatarModalOpen} imageUrl={previewAvatarUrl} altText="演员头像预览" onClose={handleCloseAvatarPreview} />

      {/* WhatsLink 预览模态框 */}
      <WhatsLinkPreviewModal
        isOpen={isWhatsLinkModalOpen}
        onClose={closeWhatsLinkModal}
        data={whatsLinkData}
        isLoading={isWhatsLinkLoading}
        error={whatsLinkError}
      />
    </>
  );
};