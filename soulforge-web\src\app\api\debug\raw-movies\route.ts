import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get all movies without any merging
    const movies = await prisma.movie.findMany({
      select: {
        id: true,
        fileName: true,
        nfoId: true,
        filePath: true,
        title: true,
        year: true,
        fileSize: true,
        preferredStatus: true,
      },
      orderBy: {
        id: 'desc',
      },
      take: 100, // Limit for debugging
    });

    console.log(`Raw movies query returned ${movies.length} movies`);

    // Group analysis
    const nfoIdGroups = new Map<string, any[]>();
    const noNfoIdMovies = [];

    for (const movie of movies) {
      if (movie.nfoId && movie.nfoId.trim() !== '') {
        const normalizedNfoId = movie.nfoId.toLowerCase().trim();
        if (!nfoIdGroups.has(normalizedNfoId)) {
          nfoIdGroups.set(normalizedNfoId, []);
        }
        nfoIdGroups.get(normalizedNfoId)!.push(movie);
      } else {
        noNfoIdMovies.push(movie);
      }
    }

    const duplicateGroups = Array.from(nfoIdGroups.entries()).filter(([_, movies]) => movies.length > 1);

    console.log(`Analysis: ${movies.length} total, ${nfoIdGroups.size} unique nfoIds, ${duplicateGroups.length} duplicate groups, ${noNfoIdMovies.length} without nfoId`);

    return NextResponse.json({
      success: true,
      movies,
      analysis: {
        total: movies.length,
        uniqueNfoIds: nfoIdGroups.size,
        duplicateGroups: duplicateGroups.length,
        withoutNfoId: noNfoIdMovies.length,
        duplicateGroupDetails: duplicateGroups.map(([nfoId, movies]) => ({
          nfoId,
          count: movies.length,
          movies: movies.map(m => ({ id: m.id, fileName: m.fileName }))
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching raw movies:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch raw movies',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
