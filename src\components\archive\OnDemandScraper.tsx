import React, { useState, useEffect } from 'react';
import { useArchiveStore } from '../../hooks/useArchiveStore';
import { Search, Loader2, CheckCircle, AlertCircle, Database, FolderOpen, Download } from 'lucide-react';
import { ArchiveScrapeProgressData } from '../../types';

interface OnDemandScraperProps {
  className?: string;
  initialNfoId?: string;
}

export function OnDemandScraper({ className = '', initialNfoId = '' }: OnDemandScraperProps) {
  const [nfoId, setNfoId] = useState(initialNfoId);
  const [isLoading, setIsLoading] = useState(false);
  const [progressData, setProgressData] = useState<ArchiveScrapeProgressData | null>(null);
  const [lastResult, setLastResult] = useState<{
    success: boolean;
    message: string;
    timestamp: number;
  } | null>(null);

  const { fetchResults } = useArchiveStore();

  // 监听initialNfoId变化
  useEffect(() => {
    if (initialNfoId && initialNfoId !== nfoId) {
      setNfoId(initialNfoId);
    }
  }, [initialNfoId]);

  // 监听进度更新
  useEffect(() => {
    const unsubscribe = window.sfeElectronAPI.onArchiveScrapeProgress((data: ArchiveScrapeProgressData) => {
      setProgressData(data);

      // 如果是完成或错误状态，清除进度数据
      if (data.stage === 'complete' || data.stage === 'error') {
        setTimeout(() => {
          setProgressData(null);
        }, 1000);
      }
    });

    return unsubscribe;
  }, []);

  const handleScrape = async () => {
    if (!nfoId.trim()) {
      setLastResult({
        success: false,
        message: '请输入番号',
        timestamp: Date.now()
      });
      return;
    }

    setIsLoading(true);
    setLastResult(null);
    setProgressData(null);

    try {
      const result = await window.sfeElectronAPI.archiveScrapeAndCreate(nfoId.trim());
      
      if (result.success) {
        setLastResult({
          success: true,
          message: result.message || `${nfoId.trim()} 已成功作为虚拟资产入库！`,
          timestamp: Date.now()
        });
        setNfoId(''); // 清空输入框
        
        // 刷新数据网格以显示新添加的虚拟资产
        setTimeout(() => {
          fetchResults();
        }, 500);
        
      } else {
        setLastResult({
          success: false,
          message: result.error || '侦察失败',
          timestamp: Date.now()
        });
      }
    } catch (error) {
      setLastResult({
        success: false,
        message: error instanceof Error ? error.message : '发生未知错误',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      handleScrape();
    }
  };

  // 自动清除结果消息
  React.useEffect(() => {
    if (lastResult) {
      const timer = setTimeout(() => {
        setLastResult(null);
      }, 5000); // 5秒后自动清除
      
      return () => clearTimeout(timer);
    }
  }, [lastResult]);

  return (
    <div className={`bg-[#2c2c2c] border border-[#444] rounded-lg p-4 ${className}`}>
      <div className="flex items-center gap-3 mb-3">
        <Search className="h-5 w-5 text-[#B8860B]" />
        <h3 className="text-lg font-medium text-white">即时番号侦察</h3>
      </div>
      
      <div className="flex items-center gap-3">
        <div className="flex-1">
          <input
            type="text"
            value={nfoId}
            onChange={(e) => setNfoId(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入番号进行即时侦察..."
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#444] rounded text-white placeholder-gray-400 focus:border-[#B8860B] focus:outline-none"
            disabled={isLoading}
          />
        </div>
        
        <button
          onClick={handleScrape}
          disabled={isLoading || !nfoId.trim()}
          className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[120px] justify-center"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              侦察中...
            </>
          ) : (
            <>
              <Search className="h-4 w-4" />
              开始侦察
            </>
          )}
        </button>
      </div>

      {/* 进度显示 */}
      {progressData && (
        <div className="mt-3 p-3 rounded-lg bg-blue-900/20 border border-blue-500/30">
          <div className="flex items-center gap-3 mb-2">
            {progressData.stage === 'scraping' && <Search className="h-4 w-4 text-blue-400 animate-pulse" />}
            {progressData.stage === 'resolving' && <FolderOpen className="h-4 w-4 text-blue-400 animate-pulse" />}
            {progressData.stage === 'creating' && <Database className="h-4 w-4 text-blue-400 animate-pulse" />}
            {progressData.stage === 'complete' && <CheckCircle className="h-4 w-4 text-green-400" />}
            {progressData.stage === 'error' && <AlertCircle className="h-4 w-4 text-red-400" />}

            <span className="text-sm text-blue-300 flex-1">
              {progressData.message}
            </span>

            <span className="text-xs text-blue-400 font-mono">
              {progressData.progress}%
            </span>
          </div>

          {/* 进度条 */}
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                progressData.stage === 'error'
                  ? 'bg-red-500'
                  : progressData.stage === 'complete'
                  ? 'bg-green-500'
                  : 'bg-blue-500'
              }`}
              style={{ width: `${progressData.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* 结果反馈 */}
      {lastResult && (
        <div className={`mt-3 p-3 rounded-lg flex items-center gap-2 ${
          lastResult.success 
            ? 'bg-green-900/20 border border-green-500/30' 
            : 'bg-red-900/20 border border-red-500/30'
        }`}>
          {lastResult.success ? (
            <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
          )}
          <span className={`text-sm ${
            lastResult.success ? 'text-green-300' : 'text-red-300'
          }`}>
            {lastResult.message}
          </span>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-4 text-xs text-gray-400">
        <p>💡 输入番号后点击"开始侦察"，系统将自动刮削元数据并创建虚拟资产记录</p>
        <p>🔍 支持的来源：JavBus、DMM（更多来源即将添加）</p>
        <p>📊 侦察过程中会显示实时进度：刮削元数据 → 解析路径 → 创建记录</p>
      </div>
    </div>
  );
}
