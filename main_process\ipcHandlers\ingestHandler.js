// main_process/ipcHandlers/ingestHandler.js
const { ipcMain, dialog } = require('electron');
const log = require('electron-log');
const ingestService = require('../services/ingestService');

class IngestHandler {
  constructor() {
    this.log = log.scope('IngestHandler');
    this.setupIpcHandlers();
    this.log.info('情报中心IPC处理器已初始化');
  }

  setupIpcHandlers() {
    // 选择工作区文件夹
    ipcMain.handle('ingest-select-workspace', async (event) => {
      try {
        this.log.info('收到选择工作区请求');
        
        const result = await dialog.showOpenDialog({
          title: '选择情报中心工作区',
          properties: ['openDirectory'],
          buttonLabel: '选择此文件夹'
        });

        if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
          this.log.info('用户取消了工作区选择');
          return { success: false, canceled: true };
        }

        const selectedPath = result.filePaths[0];
        this.log.info(`用户选择了工作区: ${selectedPath}`);

        // 验证选择的路径
        if (!ingestService.validateWorkspacePath(selectedPath)) {
          this.log.warn(`选择的路径无效: ${selectedPath}`);
          return { 
            success: false, 
            error: '选择的路径无效或无法访问' 
          };
        }

        return { 
          success: true, 
          workspacePath: selectedPath 
        };

      } catch (error) {
        this.log.error('选择工作区时发生错误:', error);
        return { 
          success: false, 
          error: `选择工作区失败: ${error.message}` 
        };
      }
    });

    // 开始扫描.md文件
    ipcMain.handle('ingest-start-scan', async (event, workspacePath) => {
      try {
        this.log.info(`收到扫描请求，工作区: ${workspacePath}`);

        // 验证工作区路径
        if (!ingestService.validateWorkspacePath(workspacePath)) {
          throw new Error('工作区路径无效');
        }

        // 发送扫描开始事件
        event.sender.send('ingest-scan-progress', {
          type: 'start',
          message: '正在开始扫描...'
        });

        // 执行扫描
        const mdFiles = await ingestService.scanForMdFiles(workspacePath);

        // 发送扫描进度事件
        event.sender.send('ingest-scan-progress', {
          type: 'progress',
          message: `发现 ${mdFiles.length} 个.md文件，正在获取详细信息...`
        });

        // 获取文件详细信息
        const filesInfo = await ingestService.getMdFilesInfo(mdFiles);

        // 发送扫描完成事件
        event.sender.send('ingest-scan-progress', {
          type: 'complete',
          message: `扫描完成！共发现 ${filesInfo.length} 个.md情报档案`
        });

        this.log.info(`扫描完成，返回 ${filesInfo.length} 个文件信息`);

        return {
          success: true,
          files: filesInfo,
          totalCount: filesInfo.length
        };

      } catch (error) {
        this.log.error('扫描过程中发生错误:', error);
        
        // 发送错误事件
        event.sender.send('ingest-scan-progress', {
          type: 'error',
          message: `扫描失败: ${error.message}`
        });

        return {
          success: false,
          error: error.message
        };
      }
    });

    // 获取单个文件详细信息
    ipcMain.handle('ingest-get-file-info', async (event, filePath) => {
      try {
        this.log.info(`获取文件信息: ${filePath}`);
        const fileInfo = await ingestService.getMdFileInfo(filePath);
        
        return {
          success: true,
          fileInfo: fileInfo
        };

      } catch (error) {
        this.log.error(`获取文件信息失败: ${error.message}`);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 验证工作区路径
    ipcMain.handle('ingest-validate-workspace', async (event, workspacePath) => {
      try {
        const isValid = ingestService.validateWorkspacePath(workspacePath);

        return {
          success: true,
          isValid: isValid
        };

      } catch (error) {
        this.log.error(`验证工作区路径失败: ${error.message}`);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // 开始情报汇入与解析工作流
    ipcMain.handle('ingest-start-workflow', async (event, workspacePath, mdFiles) => {
      try {
        this.log.info(`收到汇入工作流请求，工作区: ${workspacePath}，文件数: ${mdFiles.length}`);

        // 验证工作区路径
        if (!ingestService.validateWorkspacePath(workspacePath)) {
          throw new Error('工作区路径无效');
        }

        // 设置进度回调
        ingestService.setProgressCallback((data) => {
          event.sender.send('ingest-workflow-progress', data);
        });

        // 执行汇入工作流
        const results = await ingestService.startIngestWorkflow(workspacePath, mdFiles);

        this.log.info(`汇入工作流完成，结果: ${JSON.stringify(results)}`);

        return {
          success: true,
          results: results
        };

      } catch (error) {
        this.log.error('汇入工作流失败:', error);

        // 发送错误事件
        event.sender.send('ingest-workflow-progress', {
          type: 'error',
          message: `汇入工作流失败: ${error.message}`
        });

        return {
          success: false,
          error: error.message
        };
      }
    });
  }
}

// 创建并导出处理器实例
const ingestHandler = new IngestHandler();

module.exports = ingestHandler;
