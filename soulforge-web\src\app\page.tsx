'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useMovieStore } from '@/lib/stores/movie-store';
import { useUIStore } from '@/lib/stores/ui-store';
import { AppLayout } from '@/components/layout/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Film, Library, Settings, Plus } from 'lucide-react';

export default function Home() {
  const { movies, loading } = useMovieStore();
  const { addToast } = useUIStore();

  useEffect(() => {
    // Welcome toast
    addToast({
      type: 'info',
      title: '欢迎使用麟琅秘府',
      description: '您的专业电影媒体库管理系统',
    });
  }, [addToast]);

  return (
    <AppLayout>
      <div className="container mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-foreground mb-2">
          麟琅秘府
        </h1>
        <p className="text-muted-foreground text-lg">
          专业的电影媒体库管理系统
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              电影总数
            </CardTitle>
            <Film className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{movies.length}</div>
            <p className="text-xs text-muted-foreground">
              已收录的电影数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              媒体库
            </CardTitle>
            <Library className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              配置的媒体库数量
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              系统状态
            </CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">正常</div>
            <p className="text-xs text-muted-foreground">
              所有服务运行正常
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">快速操作</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/libraries">
            <Button className="h-20 flex-col gap-2 w-full" variant="outline">
              <Plus className="h-6 w-6" />
              添加媒体库
            </Button>
          </Link>
          <Button className="h-20 flex-col gap-2" variant="outline">
            <Film className="h-6 w-6" />
            扫描电影
          </Button>
          <Link href="/movies">
            <Button className="h-20 flex-col gap-2 w-full" variant="outline">
              <Library className="h-6 w-6" />
              浏览电影
            </Button>
          </Link>
          <Link href="/settings">
            <Button className="h-20 flex-col gap-2 w-full" variant="outline">
              <Settings className="h-6 w-6" />
              系统设置
            </Button>
          </Link>
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>最近活动</CardTitle>
          <CardDescription>
            系统的最新动态和操作记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            暂无活动记录
          </div>
        </CardContent>
      </Card>
      </div>
    </AppLayout>
  );
}
