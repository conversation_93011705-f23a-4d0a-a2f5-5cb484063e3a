import React, { useState, useEffect } from 'react';
import { Save, Check, AlertCircle, RotateCcw, Info } from 'lucide-react';

interface CategoryRulesEditorProps {
  className?: string;
}

export function CategoryRulesEditor({ className = '' }: CategoryRulesEditorProps) {
  const [rulesText, setRulesText] = useState<string>('');
  const [originalRules, setOriginalRules] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
    timestamp: number;
  } | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // 加载当前设置
  useEffect(() => {
    loadCurrentRules();
  }, []);

  // 检查是否有变更
  useEffect(() => {
    setHasChanges(rulesText !== originalRules);
  }, [rulesText, originalRules]);

  const loadCurrentRules = async () => {
    try {
      const settings = await window.sfeElectronAPI.getSettings();
      const rulesJson = settings.categoryDirectoryRules || '{}';
      
      // 解析并格式化 JSON
      const rulesObject = JSON.parse(rulesJson);
      const formattedJson = JSON.stringify(rulesObject, null, 2);
      
      setRulesText(formattedJson);
      setOriginalRules(formattedJson);
    } catch (error) {
      console.error('加载分类规则失败:', error);
      setSaveStatus({
        type: 'error',
        message: '加载分类规则失败',
        timestamp: Date.now()
      });
    }
  };

  const handleSaveRules = async () => {
    setIsLoading(true);
    setSaveStatus(null);

    try {
      // 验证 JSON 格式
      let parsedRules;
      try {
        parsedRules = JSON.parse(rulesText);
      } catch (parseError) {
        setSaveStatus({
          type: 'error',
          message: '格式错误，请输入有效的 JSON',
          timestamp: Date.now()
        });
        setIsLoading(false);
        return;
      }

      // 验证规则结构
      if (typeof parsedRules !== 'object' || parsedRules === null) {
        setSaveStatus({
          type: 'error',
          message: '规则必须是一个对象',
          timestamp: Date.now()
        });
        setIsLoading(false);
        return;
      }

      // 验证每个规则的值是否为字符串数组
      for (const [key, value] of Object.entries(parsedRules)) {
        if (!Array.isArray(value) || !value.every(item => typeof item === 'string')) {
          setSaveStatus({
            type: 'error',
            message: `规则 "${key}" 的值必须是字符串数组`,
            timestamp: Date.now()
          });
          setIsLoading(false);
          return;
        }
      }

      // 保存设置
      const settings = await window.sfeElectronAPI.getSettings();
      const updatedSettings = {
        ...settings,
        categoryDirectoryRules: JSON.stringify(parsedRules)
      };
      
      const result = await window.sfeElectronAPI.saveSettings(updatedSettings);
      
      if (result.success) {
        // 重新格式化并更新原始规则
        const formattedJson = JSON.stringify(parsedRules, null, 2);
        setRulesText(formattedJson);
        setOriginalRules(formattedJson);
        
        setSaveStatus({
          type: 'success',
          message: '分类规则已保存',
          timestamp: Date.now()
        });
      } else {
        setSaveStatus({
          type: 'error',
          message: result.error || '保存设置失败',
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('保存分类规则失败:', error);
      setSaveStatus({
        type: 'error',
        message: '保存失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetRules = () => {
    setRulesText(originalRules);
    setSaveStatus(null);
  };

  const handleResetToDefault = () => {
    // 重置为默认的分类规则
    const defaultRules = {
      "jav_censored": ["有码"],
      "jav_uncensored": ["无码"],
      "jav_vr": ["VR"],
      "chinese": ["国产"],
      "western": ["欧美"],
      "anime": ["动画"],
      "live": ["直播"]
    };

    const formattedJson = JSON.stringify(defaultRules, null, 2);
    setRulesText(formattedJson);
    setSaveStatus(null);
  };

  // 自动清除状态消息
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 标题和说明 */}
      <div>
        <h3 className="text-lg font-medium text-white mb-2">分类目录规则</h3>
        <p className="text-sm text-gray-400 mb-4">
          定义影片在'媒体资产主目录'下的一级分类文件夹。规则为一个 JSON 对象，键 (key) 为纯英文的目录名，值 (value) 为一个包含匹配关键词的数组。
        </p>
      </div>

      {/* JSON 编辑器 */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-300">
            规则配置 (JSON 格式)
          </label>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleResetToDefault}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-500 flex items-center gap-1"
            >
              <RotateCcw className="h-3 w-3" />
              重置为默认
            </button>

            {hasChanges && (
              <button
                onClick={handleResetRules}
                className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500 flex items-center gap-1"
              >
                <RotateCcw className="h-3 w-3" />
                重置
              </button>
            )}

            <button
              onClick={handleSaveRules}
              disabled={isLoading || !hasChanges}
              className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[100px] justify-center"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  保存规则
                </>
              )}
            </button>
          </div>
        </div>
        
        <textarea
          value={rulesText}
          onChange={(e) => setRulesText(e.target.value)}
          rows={15}
          className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#444] rounded text-white font-mono text-sm focus:border-[#B8860B] focus:outline-none resize-vertical"
          placeholder="请输入有效的 JSON 格式规则..."
        />
      </div>

      {/* 状态反馈 */}
      {saveStatus && (
        <div className={`p-3 rounded-lg flex items-center gap-2 ${
          saveStatus.type === 'success' 
            ? 'bg-green-900/20 border border-green-500/30' 
            : 'bg-red-900/20 border border-red-500/30'
        }`}>
          {saveStatus.type === 'success' ? (
            <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
          )}
          <span className={`text-sm ${
            saveStatus.type === 'success' ? 'text-green-300' : 'text-red-300'
          }`}>
            {saveStatus.message}
          </span>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-[#1a1a1a] border border-[#333] rounded-lg">
        <div className="flex items-center gap-2 mb-3">
          <Info className="h-4 w-4 text-blue-400" />
          <h4 className="text-sm font-medium text-white">规则格式说明</h4>
        </div>
        
        <div className="text-xs text-gray-400 space-y-2">
          <p>每个规则由目录名和匹配关键词组成：</p>
          <div className="ml-4 space-y-1 font-mono bg-[#0a0a0a] p-2 rounded">
            <p>{`{`}</p>
            <p>{`  "jav_censored": ["有码"],`}</p>
            <p>{`  "jav_uncensored": ["无码"],`}</p>
            <p>{`  "jav_vr": ["VR"],`}</p>
            <p>{`  "chinese": ["国产"],`}</p>
            <p>{`  "western": ["欧美"],`}</p>
            <p>{`  "anime": ["动画"]`}</p>
            <p>{`}`}</p>
          </div>
          <div className="space-y-1">
            <p>• <strong>目录名</strong>：将创建的文件夹名称（建议使用英文）</p>
            <p>• <strong>关键词数组</strong>：用于匹配影片标签或马赛克类型的关键词</p>
            <p>• 系统会按顺序匹配规则，首次匹配成功即确定分类</p>
            <p>• 未匹配任何规则的影片将放入 "uncategorized" 目录</p>
          </div>
        </div>
      </div>
    </div>
  );
}
