import os
import json
import sys
import subprocess
import argparse

def get_video_duration(video_path, ffmpeg_path='ffmpeg'):
    """Gets the duration of a video in seconds using ffprobe (part of ffmpeg)."""
    # Prefer ffprobe if available, as it's more direct for this
    ffprobe_command = ffmpeg_path.replace('ffmpeg', 'ffprobe') # Simple heuristic
    
    command = [
        ffprobe_command,
        '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        video_path
    ]
    try:
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        stdout, stderr = process.communicate(timeout=30)
        if process.returncode == 0 and stdout.strip():
            return float(stdout.strip())
        else:
            # Fallback to trying ffmpeg if ffprobe failed or not found with simple replace
            if ffmpeg_path != ffprobe_command: # Avoid infinite loop if paths were identical
                ffmpeg_command_alt = [
                    ffmpeg_path, 
                    '-i', video_path
                ]
                # ffmpeg prints to stderr for info
                process_alt = subprocess.Popen(ffmpeg_command_alt, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
                _, stderr_alt = process_alt.communicate(timeout=30)
                
                # Parse duration from ffmpeg's stderr (e.g., "  Duration: 00:01:30.50, start: 0.000000...")
                for line in stderr_alt.splitlines():
                    if 'Duration:' in line:
                        parts = line.split('Duration:')[1].split(',')[0].strip() # "00:01:30.50"
                        h, m, s = map(float, parts.split(':'))
                        return h * 3600 + m * 60 + s
            
            # sys.stderr.write(f"Could not get duration for {video_path}. FFprobe stderr: {stderr}\n")
            return None
    except FileNotFoundError:
        # sys.stderr.write(f"FFprobe/FFmpeg not found at paths derived from: {ffmpeg_path}. Cannot get duration.\n")
        return None
    except subprocess.TimeoutExpired:
        # sys.stderr.write(f"Timeout getting duration for {video_path}.\n")
        return None
    except Exception as e:
        # sys.stderr.write(f"Error getting duration for {video_path}: {str(e)}\n")
        return None


def generate_thumbnails(video_path, output_dir, num_thumbnails=10, ffmpeg_path='ffmpeg', snapshot_quality="hd_640p"):
    """Generates a specified number of thumbnails from a video file."""
    
    duration = get_video_duration(video_path, ffmpeg_path)
    if duration is None or duration == 0:
        return {"success": False, "error": "Could not determine video duration or video is empty.", "thumbnails": []}

    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir, exist_ok=True)
        except OSError as e:
            return {"success": False, "error": f"Could not create output directory '{output_dir}': {e}", "thumbnails": []}

    # Clean old thumbnails in the directory if any
    for old_file in os.listdir(output_dir):
        if old_file.startswith('snapshot_') and old_file.endswith('.jpg'):
            try:
                os.remove(os.path.join(output_dir, old_file))
            except OSError:
                pass # Ignore if removal fails for some reason

    interval = duration / (num_thumbnails + 1) # +1 to avoid first and last second mostly
    thumbnail_paths = []

    # Determine scale based on snapshot_quality
    # sd_320p, hd_640p, fhd_1280p_720h
    scale_filter = ""
    if snapshot_quality == "sd_320p":
        scale_filter = "scale=320:-1"
    elif snapshot_quality == "hd_640p":
        scale_filter = "scale=640:-1"
    elif snapshot_quality == "fhd_1280p_720h":
        # Scale to 720p height, or 1280 width if aspect ratio requires it, keeping aspect.
        # min(1280/iw, 720/ih)*iw and min(1280/iw, 720/ih)*ih
        # A simpler approach: scale to width 1280, if height exceeds 720, then scale to height 720.
        # Using a common ffmpeg approach:
        scale_filter = "scale='min(1280,iw)':min'(720,ih)':force_original_aspect_ratio=decrease"
        # A simpler fixed approach for common aspect ratios might be scale=1280:-1 or scale=-1:720
        # but the above is more robust. However, some ffmpeg versions might not like the single quotes.
        # Let's use a very common one: scale to width 1280, keeping aspect ratio for height.
        # Or, scale to a common target like 720p if the video is larger.
        # For simplicity in cross-platform ffmpeg calls, often fixed width or height is used.
        # Let's try a more robust scaling: scale to fit within 1280x720
        scale_filter = "scale=w=1280:h=720:force_original_aspect_ratio=decrease"

    for i in range(num_thumbnails):
        timestamp = (i + 1) * interval
        output_filename = f"snapshot_{i+1:03d}.jpg"
        output_filepath = os.path.join(output_dir, output_filename)
        
        command = [
            ffmpeg_path,
            '-ss', str(timestamp),     # Seek to timestamp
            '-i', video_path,          # Input video
            '-vframes', '1',           # Extract 1 frame
            '-q:v', '3',               # Output quality for JPG (1-31, lower is better)
            '-vf', scale_filter if scale_filter else 'null', # Video filter for scaling
            '-y',                      # Overwrite output files without asking
            output_filepath
        ]
        # Remove 'null' filter if scale_filter is empty
        if not scale_filter:
            command = [c for c in command if c != '-vf' and c != 'null']

        try:
            process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
            _, stderr = process.communicate(timeout=30) # Increased timeout for ffmpeg
            
            if process.returncode != 0:
                # sys.stderr.write(f"FFmpeg error for {output_filepath}: {stderr}\n")
                thumbnail_paths.append(None) # Indicate failure for this thumbnail
                continue
            
            if os.path.exists(output_filepath) and os.path.getsize(output_filepath) > 0:
                 thumbnail_paths.append(output_filepath)
            else:
                # sys.stderr.write(f"FFmpeg created an empty or missing file for {output_filepath}. Stderr: {stderr}\n")
                thumbnail_paths.append(None)

        except FileNotFoundError:
            return {"success": False, "error": f"FFmpeg not found at path: {ffmpeg_path}. Please configure it in settings.", "thumbnails": []}
        except subprocess.TimeoutExpired:
            # sys.stderr.write(f"FFmpeg command timed out for {output_filepath}.\n")
            thumbnail_paths.append(None)
            continue # Try next thumbnail
        except Exception as e:
            # sys.stderr.write(f"Error generating thumbnail {output_filepath}: {str(e)}\n")
            thumbnail_paths.append(None)
            continue

    if not any(thumbnail_paths): # If all thumbnails failed
        return {"success": False, "error": "Failed to generate any thumbnails. Check FFmpeg path and video file.", "thumbnails": []}

    return {"success": True, "thumbnails": thumbnail_paths, "error": None}


def main():
    parser = argparse.ArgumentParser(description="Video utilities: Generate thumbnails.")
    parser.add_argument("--video_path", required=True, help="Path to the video file.")
    parser.add_argument("--output_dir", required=True, help="Directory to save thumbnails.")
    parser.add_argument("--num_thumbnails", type=int, default=10, help="Number of thumbnails to generate.")
    parser.add_argument("--ffmpeg_path", default="ffmpeg", help="Path to the ffmpeg executable.")
    parser.add_argument("--snapshot_quality", default="hd_640p", help="Quality/size of snapshots (sd_320p, hd_640p, fhd_1280p_720h).")

    args = parser.parse_args()

    results = generate_thumbnails(
        args.video_path, 
        args.output_dir, 
        args.num_thumbnails, 
        args.ffmpeg_path,
        args.snapshot_quality
    )
    
    # Ensure output is UTF-8 for Electron
    sys.stdout.reconfigure(encoding='utf-8')
    print(json.dumps(results, ensure_ascii=False))

if __name__ == '__main__':
    main()