// main_process/services/scrapers/dmmProvider.js
const { getBrowser } = require('../browserManager');
const cheerio = require('cheerio');
const log = require('electron-log');
const settingsService = require('../settingsService');

const PROVIDER_NAME = 'dmm';
const PROVIDER_VERSION = '2.0.0'; // 【优化】升级版本，基于对标软件优化

/**
 * 【优化】DMM 刮削器 Provider - 基于对标软件优化
 * 专门负责从 DMM 网站（dmm.co.jp / dmm.com / tv.dmm.com）刮削影片的详细元数据
 */

/**
 * 【新增】在指定DMM域名中搜索 - 基于对标软件逻辑
 * @param {string} searchTerm - 搜索词
 * @param {string} baseUrl - 基础URL
 * @param {Object} context - 浏览器上下文
 * @returns {Promise<string|null>} 详情页URL
 */
async function searchInDmm(searchTerm, baseUrl, context) {
  try {
    const page = await context.newPage();
    const searchUrl = `${baseUrl}/search/=/searchstr=${encodeURIComponent(searchTerm)}/sort=ranking/`;

    log.info(`[DMM Provider] 搜索地址: ${searchUrl}`);

    // 设置Cookie以避免年龄验证
    await page.setExtraHTTPHeaders({
      'Cookie': 'uid=abcd786561031111; age_check_done=1;'
    });

    await page.goto(searchUrl, { timeout: 30000, waitUntil: 'domcontentloaded' });

    const content = await page.content();

    // 【优化】基于对标软件的地域限制检测
    if (content.includes('foreignError')) {
      log.warn('[DMM Provider] 地域限制，请使用日本节点访问！');
      await page.close();
      return null;
    }

    // 【优化】基于对标软件的搜索结果解析
    const realUrl = await getRealUrlFromSearch(content, searchTerm, searchTerm);

    await page.close();
    return realUrl;

  } catch (error) {
    log.error(`[DMM Provider] 搜索失败: ${error.message}`);
    return null;
  }
}

/**
 * 【新增】在tv.dmm.com中搜索 - 基于对标软件逻辑
 * @param {string} number - 番号
 * @param {Object} context - 浏览器上下文
 * @returns {Promise<string|null>} 详情页URL
 */
async function searchInTvDmm(number, context) {
  try {
    // 【优化】基于对标软件的特殊番号前缀处理
    let number00 = number.toLowerCase().replace('-', '00');

    if (number00.toLowerCase().startsWith('lcvr')) {
      number00 = '5125' + number00;
    } else if (number00.toLowerCase().startsWith('ionxt')) {
      number00 = '5125' + number00.replace('-', '');
    } else if (number00.toLowerCase().startsWith('ymd')) {
      number00 = '5394' + number00;
    } else if (number00.toLowerCase().startsWith('fakwm')) {
      number00 = '5497' + number00;
    } else if (number00.toLowerCase().startsWith('ftbd')) {
      number00 = '5533' + number00;
    } else if (number00.toLowerCase().startsWith('ugm') ||
               number00.toLowerCase().startsWith('dmi') ||
               number00.toLowerCase().startsWith('whm')) {
      number00 = '5083' + number00;
    }

    const tvUrl = `https://tv.dmm.com/vod/detail/?season=${number00}`;
    log.info(`[DMM Provider] TV DMM 地址: ${tvUrl}`);

    return tvUrl;

  } catch (error) {
    log.error(`[DMM Provider] TV DMM 搜索失败: ${error.message}`);
    return null;
  }
}

/**
 * 【新增】从搜索结果中获取真实URL - 基于对标软件逻辑
 * @param {string} html - 搜索页面HTML
 * @param {string} number - 原始番号
 * @param {string} number2 - 处理后的番号
 * @returns {string|null} 真实URL
 */
function getRealUrlFromSearch(html, number, number2) {
  try {
    const numberTemp = number2.toLowerCase().replace('-', '');

    // 【优化】基于对标软件的URL提取逻辑
    const urlMatches = html.match(/detailUrl.*?(https.*?)\\",/g) || [];
    const urlList = urlMatches.map(match => {
      const urlMatch = match.match(/https[^"\\]+/);
      return urlMatch ? urlMatch[0] : null;
    }).filter(Boolean);

    if (urlList.length === 0) {
      return null;
    }

    // 【优化】基于对标软件的匹配逻辑
    const number1 = numberTemp.replace('000', '');
    const numberPre = new RegExp(`(?<=[=0-9])${numberTemp.substring(0, 3)}`);
    const numberEnd = new RegExp(`${numberTemp.substring(numberTemp.length - 3)}(?=(-[0-9])|([a-z]*)?[/&])`);
    const numberMid = new RegExp(`[^a-z]${number1}[^0-9]`);

    const tempList = [];
    for (const url of urlList) {
      if ((numberPre.test(url) && numberEnd.test(url)) || numberMid.test(url)) {
        const cidMatch = url.match(/(cid|content)=([^/&]+)/);
        if (cidMatch) {
          tempList.push(url);
        }
      }
    }

    if (tempList.length === 0) {
      // 通过标题搜索
      const titleMatches = html.match(/title\\":\\"(.*?)\\",/g) || [];
      const titleList = titleMatches.map(match => {
        const titleMatch = match.match(/title\\":\\"(.*?)\\",/);
        return titleMatch ? titleMatch[1].replace('...', '').trim() : null;
      }).filter(Boolean);

      if (titleList.length > 0 && urlList.length > 0) {
        for (let i = 0; i < Math.min(titleList.length, urlList.length); i++) {
          if (titleList[i].includes(number)) {
            tempList.push(urlList[i]);
            break;
          }
        }
      }
    }

    if (tempList.length === 0) {
      return null;
    }

    // 【优化】基于对标软件的URL优先级排序
    const digitalList = tempList.filter(url => url.includes('/digital/'));
    const dvdList = tempList.filter(url => url.includes('/dvd/')).sort().reverse();
    const primeList = tempList.filter(url => url.includes('/prime/'));
    const monthlyList = tempList.filter(url => url.includes('/monthly/'));
    const otherList = tempList.filter(url =>
      !url.includes('/digital/') &&
      !url.includes('/dvd/') &&
      !url.includes('/prime/') &&
      !url.includes('/monthly/')
    );

    const sortedList = [...digitalList, ...dvdList, ...primeList, ...monthlyList, ...otherList];
    let realUrl = sortedList[0] || '';

    // 清理URL参数
    if (realUrl.includes('?i3_ref=search&i3_ord')) {
      realUrl = realUrl.substring(0, realUrl.indexOf('?i3_ref=search&i3_ord'));
    }

    return realUrl;

  } catch (error) {
    log.error(`[DMM Provider] 解析搜索结果失败: ${error.message}`);
    return null;
  }
}

/**
 * 【优化】查找详情页 URL - 基于对标软件的智能搜索策略
 * @param {string} nfoId - 影片 ID
 * @param {Object} context - 浏览器上下文
 * @returns {Promise<string|null>} 详情页 URL
 */
async function findDetailPageUrl(nfoId, context) {
  try {
    log.info(`[DMM Provider] 开始查找详情页 URL: ${nfoId}`);

    // 【优化】基于对标软件的番号预处理逻辑
    let processedNumber = nfoId;

    // 处理特殊格式的番号
    const digitMatch = nfoId.match(/[A-Za-z]+-?(\d+)/);
    if (digitMatch) {
      const digits = digitMatch[1];
      if (digits.length >= 5 && digits.startsWith('00')) {
        processedNumber = nfoId.replace(digits, digits.substring(2));
      } else if (digits.length === 4) {
        processedNumber = nfoId.replace('-', '0'); // DSVR-1698 -> dsvr01698
      }
    }

    // 【优化】基于对标软件的多轮搜索策略
    const number00 = processedNumber.toLowerCase().replace('-', '00'); // 带00搜索
    const numberNo00 = processedNumber.toLowerCase().replace('-', ''); // 不带00搜索

    // 第一轮：dmm.co.jp 带00搜索
    let realUrl = await searchInDmm(number00, 'https://www.dmm.co.jp', context);
    if (realUrl) return realUrl;

    // 第二轮：dmm.co.jp 不带00搜索（旧作品）
    if (numberNo00 !== number00) {
      realUrl = await searchInDmm(numberNo00, 'https://www.dmm.co.jp', context);
      if (realUrl) return realUrl;
    }

    // 第三轮：dmm.com 搜索（写真等）
    realUrl = await searchInDmm(numberNo00, 'https://www.dmm.com', context);
    if (realUrl) return realUrl;

    // 第四轮：tv.dmm.com 搜索（特殊内容）
    realUrl = await searchInTvDmm(processedNumber, context);
    if (realUrl) return realUrl;

    log.warn(`[DMM Provider] 未找到详情页 URL: ${nfoId}`);
    return null;

  } catch (error) {
    log.error(`[DMM Provider] 查找详情页 URL 时发生错误: ${error.message}`);
    return null;
  }
}

/**
 * 【优化】获取标题 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 标题
 */
function getTitle($) {
  // 【优化】基于对标软件的精确选择器
  // 对标软件XPath: //h1[@id="title"]/text() 或 //h1[@class="item fn bold"]/text()
  let title = $('#title').text() || $('h1.item.fn.bold').text() || $('h1').first().text();
  return title.trim();
}

/**
 * 【优化】获取演员列表 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string[]} 演员列表
 */
function getActors($) {
  const actors = [];

  // 【优化】基于对标软件的精确选择器
  // 对标软件XPath: //span[@id='performer']/a/text() 或 //td[@id='fn-visibleActor']/div/a/text()
  const selectors = [
    '#performer a',
    '#fn-visibleActor div a',
    'td:contains("出演者") + td a'
  ];

  for (const selector of selectors) {
    $(selector).each((i, el) => {
      const actorName = $(el).text().trim();
      if (actorName && !actors.includes(actorName)) {
        actors.push(actorName);
      }
    });

    if (actors.length > 0) break; // 找到演员就停止
  }

  return actors;
}

/**
 * 【优化】获取封面图片 URL - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {Promise<string>} 封面 URL
 */
async function getCover($) {
  // 【优化】基于对标软件的封面获取逻辑
  // 对标软件XPath: //meta[@property="og:image"]/@content
  let tempResult = $('meta[property="og:image"]').attr('content') || '';

  if (tempResult) {
    // 【优化】基于对标软件的URL替换逻辑
    let result = tempResult.replace('pics.dmm.co.jp', 'awsimgsrc.dmm.co.jp/pics_dig');

    // 尝试检查URL是否可访问（简化版本）
    try {
      // 这里可以添加URL检查逻辑
      return result.replace('ps.jpg', 'pl.jpg');
    } catch (error) {
      return tempResult.replace('ps.jpg', 'pl.jpg');
    }
  }

  // 尝试其他封面选择器
  const fallbackCover = $('.package-image img, .product-image img').first().attr('src') || '';
  return fallbackCover;
}

/**
 * 【优化】获取制作商 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 制作商
 */
function getStudio($) {
  // 【优化】基于对标软件的精确选择器
  // 对标软件XPath: //td[contains(text(),'メーカー')]/following-sibling::td/a/text()
  const selectors = [
    'td:contains("メーカー") + td a',
    'th:contains("メーカー") + td a',
    'td:contains("制作商") + td a',
    '.studio a'
  ];

  for (const selector of selectors) {
    const studio = $(selector).first().text().trim();
    if (studio) return studio;
  }

  return '';
}

/**
 * 【优化】获取系列 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 系列
 */
function getSeries($) {
  // 【优化】基于对标软件的精确选择器
  // 对标软件XPath: //td[contains(text(),'シリーズ')]/following-sibling::td/a/text()
  const selectors = [
    'td:contains("シリーズ") + td a',
    'th:contains("シリーズ") + td a',
    'td:contains("系列") + td a',
    '.series a'
  ];

  for (const selector of selectors) {
    const series = $(selector).first().text().trim();
    if (series) return series;
  }

  return '';
}

/**
 * 获取导演
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 导演
 */
function getDirector($) {
  const director = $('td:contains("監督") + td a, td:contains("导演") + td a, .director a').first().text().trim();
  return director;
}

/**
 * 获取发行日期
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 发行日期
 */
function getReleaseDate($) {
  const dateText = $('td:contains("発売日") + td, td:contains("配信開始日") + td, .release-date').first().text().trim();
  
  // 解析日期格式 (YYYY/MM/DD 或 YYYY年MM月DD日)
  const dateMatch = dateText.match(/(\d{4})[\/年](\d{1,2})[\/月](\d{1,2})/);
  if (dateMatch) {
    const [, year, month, day] = dateMatch;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  return '';
}

/**
 * 获取时长
 * @param {Object} $ - Cheerio 对象
 * @returns {number} 时长（分钟）
 */
function getRuntime($) {
  const runtimeText = $('td:contains("収録時間") + td, td:contains("时长") + td, .runtime').first().text().trim();
  const runtimeMatch = runtimeText.match(/(\d+)/);
  return runtimeMatch ? parseInt(runtimeMatch[1], 10) : 0;
}

/**
 * 获取类型/标签
 * @param {Object} $ - Cheerio 对象
 * @returns {string[]} 类型列表
 */
function getGenres($) {
  const genres = [];
  
  $('td:contains("ジャンル") + td a, td:contains("类型") + td a, .genre a').each((i, el) => {
    const genre = $(el).text().trim();
    if (genre && !genres.includes(genre)) {
      genres.push(genre);
    }
  });
  
  return genres;
}

/**
 * 获取剧情简介
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 剧情简介
 */
function getPlot($) {
  const plot = $('.summary, .description, .product-description, .mg-b20').first().text().trim();
  return plot;
}

/**
 * 【新增】获取所有标签/类别 - 完整采集
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 所有标签
 */
function getAllTags($) {
  const tags = [];

  // 多种标签选择器
  const tagSelectors = [
    'td:contains("ジャンル") + td a',
    'td:contains("类型") + td a',
    '.genre a',
    '.tag a',
    '.category a',
    '.keyword a',
    'a[href*="/genre/"]',
    'a[href*="/tag/"]'
  ];

  tagSelectors.forEach(selector => {
    $(selector).each((i, el) => {
      const tag = $(el).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(tag);
      }
    });
  });

  return tags;
}

/**
 * 【新增】获取详细类别信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 详细类别信息
 */
function getDetailedGenres($) {
  const detailedGenres = [];

  $('td:contains("ジャンル") + td a, td:contains("类型") + td a, .genre a').each((i, el) => {
    const name = $(el).text().trim();
    const url = $(el).attr('href');

    if (name) {
      detailedGenres.push({
        name: name,
        url: url ? (url.startsWith('http') ? url : `https://www.dmm.co.jp${url}`) : ''
      });
    }
  });

  return detailedGenres;
}

/**
 * 【新增】获取用户评分
 * @param {Object} $ - Cheerio 对象
 * @returns {Object|null} 用户评分信息
 */
function getUserRating($) {
  // 查找评分元素
  const ratingSelectors = [
    '.rating-score',
    '.user-rating',
    '.score',
    '.star-rating',
    '[class*="rating"]',
    '[class*="score"]'
  ];

  for (const selector of ratingSelectors) {
    const ratingElement = $(selector).first();
    if (ratingElement.length > 0) {
      const ratingText = ratingElement.text().trim();
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/);

      if (ratingMatch) {
        return {
          score: parseFloat(ratingMatch[1]),
          maxScore: 5, // DMM 通常使用5分制
          votes: 0, // 如果能找到投票数，可以在这里解析
          source: 'dmm',
          text: ratingText
        };
      }
    }
  }

  return null;
}

/**
 * 【新增】获取用户评价/评论
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 用户评论列表
 */
function getUserReviews($) {
  const reviews = [];

  // 查找评论区域
  const reviewSelectors = [
    '.review',
    '.comment',
    '.user-review',
    '.customer-review',
    '[class*="review"]'
  ];

  reviewSelectors.forEach(selector => {
    $(selector).each((i, el) => {
      const reviewText = $(el).text().trim();
      const author = $(el).find('.author, .user-name, .reviewer').text().trim();
      const date = $(el).find('.date, .review-date').text().trim();

      if (reviewText && reviewText.length > 10) { // 过滤太短的内容
        reviews.push({
          text: reviewText,
          author: author || '匿名用户',
          date: date || '',
          source: 'dmm'
        });
      }
    });
  });

  return reviews;
}

/**
 * 【新增】获取相关作品
 * @param {Object} $ - Cheerio 对象
 * @param {string} baseUrl - 基础URL
 * @returns {Array} 相关作品列表
 */
function getRelatedMovies($, baseUrl) {
  const relatedMovies = [];

  // 查找相关作品区域
  const relatedSelectors = [
    '.related-item',
    '.recommend-item',
    '.similar-item',
    'a[href*="/detail/"]'
  ];

  relatedSelectors.forEach(selector => {
    $(selector).each((i, el) => {
      const link = $(el).attr('href') || $(el).find('a').attr('href');
      const title = $(el).find('img').attr('alt') || $(el).text().trim();
      const image = $(el).find('img').attr('src');

      if (link && title && link.includes('/detail/')) {
        relatedMovies.push({
          title: title,
          url: link.startsWith('http') ? link : `https://www.dmm.co.jp${link}`,
          image: image ? (image.startsWith('http') ? image : `https://www.dmm.co.jp${image}`) : ''
        });
      }
    });
  });

  return relatedMovies.slice(0, 20); // 限制数量
}

/**
 * 【新增】获取推荐/相似影片
 * @param {Object} $ - Cheerio 对象
 * @param {string} baseUrl - 基础URL
 * @returns {Array} 相似影片列表
 */
function getSimilarMovies($, baseUrl) {
  const similarMovies = [];

  // 查找推荐区域
  $('.recommend, .similar, .also-bought').find('a[href*="/detail/"]').each((i, el) => {
    const link = $(el).attr('href');
    const title = $(el).find('img').attr('alt') || $(el).attr('title');
    const image = $(el).find('img').attr('src');

    if (link && title) {
      similarMovies.push({
        title: title,
        url: link.startsWith('http') ? link : `https://www.dmm.co.jp${link}`,
        image: image ? (image.startsWith('http') ? image : `https://www.dmm.co.jp${image}`) : ''
      });
    }
  });

  return similarMovies.slice(0, 15); // 限制数量
}

/**
 * 【新增】获取所有描述信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 所有描述信息
 */
function getAllDescriptions($) {
  const descriptions = {};

  // 多种描述选择器
  const descriptionSelectors = {
    summary: '.summary',
    description: '.description',
    productDescription: '.product-description',
    detail: '.detail-info',
    content: '.content-description',
    intro: '.intro'
  };

  Object.entries(descriptionSelectors).forEach(([key, selector]) => {
    const text = $(selector).text().trim();
    if (text) {
      descriptions[key] = text;
    }
  });

  return descriptions;
}

/**
 * 【新增】获取技术规格
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 技术规格信息
 */
function getTechnicalSpecs($) {
  const specs = {};

  // 查找技术信息表格
  $('table tr, .spec-table tr, .info-table tr').each((i, el) => {
    const header = $(el).find('td:first-child, th:first-child').text().trim();
    const value = $(el).find('td:last-child').text().trim();

    if (header && value && header !== value) {
      specs[header] = value;
    }
  });

  return specs;
}

/**
 * 【新增】获取价格信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 价格信息
 */
function getPriceInfo($) {
  const priceInfo = {};

  // 查找价格元素
  const priceSelectors = [
    '.price',
    '.cost',
    '.amount',
    '[class*="price"]'
  ];

  priceSelectors.forEach(selector => {
    $(selector).each((i, el) => {
      const priceText = $(el).text().trim();
      const priceMatch = priceText.match(/(\d+[,\d]*)/);

      if (priceMatch) {
        const label = $(el).prev().text().trim() || `price_${i}`;
        priceInfo[label] = {
          text: priceText,
          amount: parseInt(priceMatch[1].replace(/,/g, ''))
        };
      }
    });
  });

  return priceInfo;
}

/**
 * 【新增】获取销售信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 销售信息
 */
function getSalesInfo($) {
  const salesInfo = {};

  // 查找销售相关信息
  const salesSelectors = {
    rank: '.rank, .ranking',
    popularity: '.popularity',
    views: '.view-count',
    downloads: '.download-count',
    favorites: '.favorite-count'
  };

  Object.entries(salesSelectors).forEach(([key, selector]) => {
    const element = $(selector).first();
    if (element.length > 0) {
      const text = element.text().trim();
      const numberMatch = text.match(/(\d+[,\d]*)/);

      salesInfo[key] = {
        text: text,
        number: numberMatch ? parseInt(numberMatch[1].replace(/,/g, '')) : null
      };
    }
  });

  return salesInfo;
}

/**
 * 【新增】获取预览图
 * @param {Object} $ - Cheerio 对象
 * @param {string} baseUrl - 基础URL
 * @returns {Array} 预览图列表
 */
function getPreviewImages($, baseUrl) {
  const previewImages = [];

  // 查找预览图
  const imageSelectors = [
    '.preview-image img',
    '.sample-image img',
    '.gallery img',
    'img[src*="sample"]',
    'img[src*="preview"]'
  ];

  imageSelectors.forEach(selector => {
    $(selector).each((i, el) => {
      const src = $(el).attr('src') || $(el).attr('data-src');
      const alt = $(el).attr('alt');

      if (src) {
        previewImages.push({
          url: src.startsWith('http') ? src : `https://www.dmm.co.jp${src}`,
          alt: alt || '',
          index: i
        });
      }
    });
  });

  return previewImages;
}

/**
 * 【新增】获取样品图像
 * @param {Object} $ - Cheerio 对象
 * @param {string} baseUrl - 基础URL
 * @returns {Array} 样品图像列表
 */
function getSampleImages($, baseUrl) {
  const sampleImages = [];

  // 查找样品图像链接
  $('a[href*="sample"], a[href*="gallery"]').each((i, el) => {
    const href = $(el).attr('href');
    const img = $(el).find('img');
    const thumbSrc = img.attr('src');
    const alt = img.attr('alt');

    if (href) {
      sampleImages.push({
        full: href.startsWith('http') ? href : `https://www.dmm.co.jp${href}`,
        thumb: thumbSrc ? (thumbSrc.startsWith('http') ? thumbSrc : `https://www.dmm.co.jp${thumbSrc}`) : '',
        alt: alt || '',
        index: i
      });
    }
  });

  return sampleImages;
}

/**
 * 【新增】获取元数据信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 元数据信息
 */
function getMetaInfo($) {
  const metaInfo = {};

  // 获取页面元数据
  metaInfo.title = $('title').text().trim();
  metaInfo.description = $('meta[name="description"]').attr('content') || '';
  metaInfo.keywords = $('meta[name="keywords"]').attr('content') || '';
  metaInfo.ogTitle = $('meta[property="og:title"]').attr('content') || '';
  metaInfo.ogDescription = $('meta[property="og:description"]').attr('content') || '';
  metaInfo.ogImage = $('meta[property="og:image"]').attr('content') || '';

  return metaInfo;
}

/**
 * 【新增】获取面包屑导航
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 面包屑导航
 */
function getBreadcrumbs($) {
  const breadcrumbs = [];

  // 查找面包屑导航
  $('.breadcrumb a, .breadcrumbs a, .nav-path a').each((i, el) => {
    const text = $(el).text().trim();
    const href = $(el).attr('href');

    if (text) {
      breadcrumbs.push({
        text: text,
        url: href ? (href.startsWith('http') ? href : `https://www.dmm.co.jp${href}`) : ''
      });
    }
  });

  return breadcrumbs;
}

/**
 * 获取预告片 URL（动态加载）
 * @param {Object} page - Playwright 页面对象
 * @param {string} htmlContent - 页面 HTML 内容
 * @returns {Promise<string>} 预告片 URL
 */
async function getTrailerUrl(page, htmlContent) {
  try {
    log.info(`[DMM Provider] 开始获取预告片 URL`);
    
    // 从 HTML 中提取 cid
    const cidMatch = htmlContent.match(/onclick="sampleplay\('.+cid=([^/]+)\//);
    if (!cidMatch) {
      log.warn(`[DMM Provider] 未找到 cid`);
      return '';
    }
    
    const cid = cidMatch[1];
    log.info(`[DMM Provider] 找到 cid: ${cid}`);
    
    // 构造 API URL
    const apiUrl = `https://www.dmm.co.jp/service/digitalapi/-/html5_player/=/cid=${cid}/`;
    
    // 使用 Playwright 访问 API
    const apiPage = await page.context().newPage();
    try {
      await apiPage.goto(apiUrl, { waitUntil: 'networkidle' });
      
      // 执行 JavaScript 获取预告片数据
      const trailerData = await apiPage.evaluate(() => {
        // 查找页面中的 JavaScript 变量
        const scripts = Array.from(document.querySelectorAll('script'));
        for (const script of scripts) {
          const content = script.textContent || '';
          if (content.includes('bitrates') && content.includes('src')) {
            // 尝试解析 JSON 数据
            const jsonMatch = content.match(/(\{.*"bitrates".*\})/);
            if (jsonMatch) {
              try {
                const data = JSON.parse(jsonMatch[1]);
                if (data.bitrates && data.bitrates.length > 0) {
                  // 返回最后一个（通常是最高质量的）预告片 URL
                  return data.bitrates[data.bitrates.length - 1].src;
                }
              } catch (e) {
                console.warn('解析预告片数据失败:', e);
              }
            }
          }
        }
        return '';
      });
      
      if (trailerData) {
        log.info(`[DMM Provider] 找到预告片 URL: ${trailerData}`);
        return trailerData;
      }
      
    } finally {
      await apiPage.close();
    }
    
    log.warn(`[DMM Provider] 未找到预告片 URL`);
    return '';
    
  } catch (error) {
    log.error(`[DMM Provider] 获取预告片 URL 时发生错误: ${error.message}`);
    return '';
  }
}

/**
 * 主要刮削函数
 * @param {string} nfoId - 影片 ID
 * @returns {Promise<Object>} 刮削结果
 */
async function scrape(nfoId) {
  let browser = null;
  let context = null;
  
  try {
    log.info(`[DMM Provider v${PROVIDER_VERSION}] 开始刮削: ${nfoId}`);
    
    // 获取浏览器实例
    browser = await getBrowser();
    
    // 创建浏览器上下文，设置 Cookie
    context = await browser.newContext({
      extraHTTPHeaders: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    // 设置年龄验证 Cookie
    await context.addCookies([{
      name: 'age_check_done',
      value: '1',
      domain: '.dmm.co.jp',
      path: '/'
    }]);
    
    // 查找详情页 URL
    const detailUrl = await findDetailPageUrl(nfoId, context);
    if (!detailUrl) {
      throw new Error(`未找到 ${nfoId} 的详情页`);
    }
    
    // 访问详情页
    const page = await context.newPage();
    await page.goto(detailUrl, { waitUntil: 'networkidle' });
    
    // 获取页面 HTML 内容
    const htmlContent = await page.content();
    const $ = cheerio.load(htmlContent);
    
    // 【现代化改造】深度数据采集 - 应采尽采模式
    log.info(`[DMM Provider] 🚀 开始深度数据采集 (应采尽采模式)...`);

    // === 基础元数据采集 ===
    const title = getTitle($);
    const actors = getActors($);
    const cover = getCover($);
    const studio = getStudio($);
    const series = getSeries($);
    const director = getDirector($);
    const releaseDate = getReleaseDate($);
    const runtime = getRuntime($);
    const genres = getGenres($);
    const plot = getPlot($);

    // === 新增：全面标签/类别采集 ===
    const allTags = getAllTags($);
    const detailedGenres = getDetailedGenres($);

    // === 新增：用户评分/评价采集 ===
    const userRating = getUserRating($);
    const userReviews = getUserReviews($);

    // === 新增：相关作品/推荐采集 ===
    const relatedMovies = getRelatedMovies($, detailUrl);
    const similarMovies = getSimilarMovies($, detailUrl);

    // === 新增：多重商品描述采集 ===
    const allDescriptions = getAllDescriptions($);
    const technicalSpecs = getTechnicalSpecs($);

    // === 新增：价格和销售信息 ===
    const priceInfo = getPriceInfo($);
    const salesInfo = getSalesInfo($);

    // === 新增：预览图和媒体采集 ===
    const previewImages = getPreviewImages($, detailUrl);
    const sampleImages = getSampleImages($, detailUrl);

    // === 新增：元数据和SEO信息 ===
    const metaInfo = getMetaInfo($);
    const breadcrumbs = getBreadcrumbs($);

    // 获取预告片 URL（异步）
    const trailerUrl = await getTrailerUrl(page, htmlContent);

    await page.close();
    
    // 【优化】组装兼容对标软件的返回数据 - 应采尽采
    const result = {
      // === 基础信息 ===
      nfoId: nfoId,
      number: nfoId,  // 对标软件字段
      title: title || '',
      originaltitle: title || '',  // 对标软件字段
      plot: plot || '',
      outline: plot || '',  // 对标软件字段
      originalplot: plot || '',  // 对标软件字段
      releaseDate: releaseDate || null,
      release: releaseDate || '',  // 对标软件字段
      year: releaseDate ? releaseDate.substring(0, 4) : '',  // 对标软件字段
      runtime: runtime > 0 ? runtime : null,

      // === 人员信息 ===
      actors: actors.length > 0 ? actors : [],
      actor: actors.length > 0 ? actors.join(',') : '',  // 对标软件字段
      actor_photo: getActorPhoto(actors),  // 对标软件字段
      director: director || null,
      studio: studio || null,
      publisher: studio || '',  // 对标软件字段
      series: series || null,

      // === 标签和分类 (兼容旧格式 + 新详细格式) ===
      genres: genres.length > 0 ? genres : [], // 保持向后兼容
      tags: allTags.length > 0 ? allTags : [], // 新增完整标签
      tag: allTags.length > 0 ? allTags.join(',') : '',  // 对标软件字段
      genresDetailed: detailedGenres, // 新增详细类别信息

      // === 图像信息 ===
      coverUrl: cover || null,
      thumb: cover || '',  // 对标软件字段
      poster: cover || '',  // 对标软件字段
      posterUrl: cover || null,
      previewImages: previewImages,
      extrafanart: previewImages,  // 对标软件字段
      sampleImages: sampleImages,

      // === 评分和社区信息 ===
      userRating: userRating,
      score: userRating || '',  // 对标软件字段
      userReviews: userReviews,

      // === 相关作品 ===
      relatedMovies: relatedMovies,
      similarMovies: similarMovies,

      // === 多重描述信息 ===
      descriptions: allDescriptions,
      technicalSpecs: technicalSpecs,

      // === 商业信息 ===
      priceInfo: priceInfo,
      salesInfo: salesInfo,

      // === 元数据信息 ===
      metaInfo: metaInfo,
      breadcrumbs: breadcrumbs,

      // === 媒体信息 ===
      trailerUrl: trailerUrl || null,
      trailer: trailerUrl || '',  // 对标软件字段

      // === 对标软件标准字段 ===
      image_download: !!(cover || previewImages.length > 0),
      image_cut: 'right',
      mosaic: getMosaic(title, genres),  // 基于标题和类别判断
      wanted: '',

      // === 来源信息 ===
      source: 'dmm',
      sourceUrl: detailUrl,
      website: detailUrl,  // 对标软件字段
      scrapedAt: new Date().toISOString(),
      provider: PROVIDER_NAME,
      version: PROVIDER_VERSION,

      // === 统计信息 ===
      dataRichness: {
        actorsCount: actors.length,
        genresCount: genres.length,
        allTagsCount: allTags.length,
        userReviewsCount: userReviews.length,
        relatedMoviesCount: relatedMovies.length,
        similarMoviesCount: similarMovies.length,
        previewImagesCount: previewImages.length,
        sampleImagesCount: sampleImages.length,
        descriptionsCount: Object.keys(allDescriptions).length,
        technicalSpecsCount: Object.keys(technicalSpecs).length,
        hasUserRating: !!userRating,
        hasPriceInfo: Object.keys(priceInfo).length > 0,
        hasSalesInfo: Object.keys(salesInfo).length > 0,
        hasTrailer: !!trailerUrl
      }
    };

    log.info(`[DMM Provider v${PROVIDER_VERSION}] 🎉 深度刮削完成 ${nfoId}！数据丰富度: 演员${actors.length}个, 标签${allTags.length}个, 评论${userReviews.length}条, 相关作品${relatedMovies.length}部`);
    return result;
    
  } catch (error) {
    log.error(`[DMM Provider] 刮削失败: ${nfoId} - ${error.message}`);
    throw error;
  } finally {
    if (context) {
      await context.close();
    }
  }
}

/**
 * 【新增】获取演员头像映射 - 参考对标软件
 * @param {Array} actors - 演员数组
 * @returns {Object} 演员头像映射
 */
function getActorPhoto(actors) {
  const actorPhoto = {};

  if (Array.isArray(actors)) {
    actors.forEach(actor => {
      const actorName = typeof actor === 'string' ? actor : actor.name;
      if (actorName) {
        actorPhoto[actorName] = (typeof actor === 'object' && actor.image) ? actor.image : '';
      }
    });
  }

  return actorPhoto;
}

/**
 * 【新增】获取马赛克信息 - 基于对标软件逻辑
 * @param {string} title - 标题
 * @param {Array} genres - 类别数组
 * @returns {string} 马赛克信息
 */
function getMosaic(title, genres) {
  // 【优化】基于对标软件的马赛克判断逻辑
  // 对标软件逻辑: 检查活跃标签页是否为"アニメ"
  const isAnime = genres.some(genre =>
    typeof genre === 'string' ?
      genre.includes('アニメ') || genre.includes('动画') :
      (genre.name && (genre.name.includes('アニメ') || genre.name.includes('动画')))
  );

  if (isAnime) {
    return '里番';
  }

  // 检查标题中的无码标识
  if (title && (title.includes('無碼') || title.includes('无码') || title.includes('Uncensored'))) {
    return '无码';
  }

  return '有码';
}

module.exports = {
  name: PROVIDER_NAME,
  version: PROVIDER_VERSION, // 导出版本信息
  scrape,
};
