
// soul-forge-electron/src/components/settings/PrivacySettingsTab.tsx
import React, { useState } from 'react';
import { AppSettings } from '../../types';
import EyeIcon from '../icons/EyeIcon';
import EyeSlashIcon from '../icons/EyeSlashIcon';

interface PrivacySettingsTabProps {
  settings: Partial<AppSettings>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  tempPrivacyHideTags: string;
  setTempPrivacyHideTags: React.Dispatch<React.SetStateAction<string>>;
}

const PrivacySettingsTab: React.FC<PrivacySettingsTabProps> = ({
  settings,
  handleInputChange,
  tempPrivacyHideTags,
  setTempPrivacyHideTags,
}) => {
  const [showPrivacyPassword, setShowPrivacyPassword] = useState(false);

   const handleTempTagsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTempPrivacyHideTags(e.target.value);
  };

  const renderPasswordInputRaw = ( 
    id: 'privacyModePassword', 
    value: string | null | undefined,
    showState: boolean,
    toggleShowState: () => void,
    placeholder: string
  ) => (
    <div className="relative">
      <input
        type={showState ? "text" : "password"}
        id={id}
        name={id}
        value={value || ''}
        onChange={handleInputChange}
        placeholder={placeholder}
        className="form-input-app pr-10"
      />
      <button
        type="button"
        onClick={toggleShowState}
        className="absolute inset-y-0 right-0 px-3 flex items-center text-neutral-400 hover:text-pink-400 focus:outline-none focus:ring-1 focus:ring-pink-400 rounded-r-md"
        aria-label={showState ? "隐藏" : "显示"}
      >
        {showState ? <EyeSlashIcon /> : <EyeIcon />}
      </button>
    </div>
  );


  return (
    <div className="settings-group-content space-y-4">
      <div>
        <div className="flex items-center">
          <input type="checkbox" id="privacyModeEnabled" name="privacyModeEnabled" checked={!!settings.privacyModeEnabled} onChange={handleInputChange} className="form-checkbox-app"/>
          <label htmlFor="privacyModeEnabled" className="ml-2 text-sm text-neutral-100">启用隐私模式</label>
        </div>
        <p className="settings-description">启用后，可以设置密码并隐藏特定标签的影片内容。</p>
      </div>
      {settings.privacyModeEnabled && (
        <>
          <div>
            <label htmlFor="privacyModePassword" className="settings-label">隐私模式密码 (可选)</label>
            {renderPasswordInputRaw("privacyModePassword", settings.privacyModePassword, showPrivacyPassword, () => setShowPrivacyPassword(!showPrivacyPassword), "留空则无需密码")}
            <p className="settings-description">如果设置密码，在查看被隐藏内容或关闭隐私模式时需要输入。请牢记您的密码。</p>
          </div>
          
          <div>
            <label htmlFor="tempPrivacyHideTagsLocal" className="settings-label">在隐私模式下隐藏的标签 (逗号分隔)</label>
            <textarea 
                id="tempPrivacyHideTagsLocal" 
                name="tempPrivacyHideTags" // This name is handled by temp state, actual save reads from temp state
                value={tempPrivacyHideTags} 
                onChange={handleTempTagsChange} 
                rows={3} 
                placeholder="例如: 敏感内容, 特殊题材, 不宜公开"
                className="form-textarea-app"
            />
            <p className="settings-description">包含这些标签的影片将在隐私模式激活且未解锁时被隐藏。</p>
          </div>
        </>
      )}
    </div>
  );
};

export default PrivacySettingsTab;
    