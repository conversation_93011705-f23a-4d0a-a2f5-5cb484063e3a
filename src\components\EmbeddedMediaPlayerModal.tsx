
// soul-forge-electron/src/components/EmbeddedMediaPlayerModal.tsx
import React, { useEffect } from 'react';

interface EmbeddedMediaPlayerModalProps {
  isOpen: boolean;
  url: string | null;
  onClose: () => void;
  movieTitle?: string;
}

const EmbeddedMediaPlayerModal: React.FC<EmbeddedMediaPlayerModalProps> = ({ isOpen, url, onClose, movieTitle }) => {
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen || !url) return null;

  return (
    <div 
      className="fixed inset-0 z-[90] flex items-center justify-center p-2 sm:p-4 bg-black/85 backdrop-blur-md" 
      onClick={onClose}
      aria-modal="true"
      role="dialog"
      aria-labelledby="media-player-title"
    >
      <div 
        className="bg-[#181818] rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden border border-[#333333]" 
        onClick={e => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-[#2f2f2f] bg-[#202020] flex-shrink-0">
          <h3 id="media-player-title" className="text-md sm:text-lg font-semibold text-amber-400 truncate" title={movieTitle || '媒体播放'}>
            {movieTitle || '媒体播放'}
          </h3>
          <button 
            onClick={onClose} 
            className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-[#333333] transition-colors" 
            aria-label="关闭播放器"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>
        <div className="flex-grow p-0.5 sm:p-1 bg-black flex items-center justify-center">
          <video 
            controls 
            autoPlay 
            src={url} 
            className="w-full h-auto max-h-[calc(90vh-80px)] outline-none" 
            onError={(e) => {
                console.error("视频播放错误:", e);
                const videoElement = e.target as HTMLVideoElement;
                if (videoElement.error) {
                    alert(`视频播放失败: ${videoElement.error.message} (错误码: ${videoElement.error.code})`);
                } else {
                    alert("视频播放失败，请检查链接或网络。");
                }
            }}
            onLoadedMetadata={(e) => {
                const videoElement = e.target as HTMLVideoElement;
                videoElement.volume = 0.5; // Set default volume
            }}
          >
            您的浏览器不支持播放此视频。尝试在外部播放器中打开此链接: <a href={url} target="_blank" rel="noopener noreferrer" className="text-sky-400 hover:underline">{url}</a>
          </video>
        </div>
      </div>
    </div>
  );
};

export default EmbeddedMediaPlayerModal;
