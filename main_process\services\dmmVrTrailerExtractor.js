// main_process/services/dmmVrTrailerExtractor.js
// DMM VR预告片破译器 - 专门负责从DMM动态播放器页面中提取真实视频链接

const { chromium } = require('playwright');
const log = require('electron-log');

/**
 * DMM VR预告片破译器
 * 使用Playwright模拟浏览器访问，通过网络监听和DOM解析双重策略提取视频链接
 */
class DmmVrTrailerExtractor {
  constructor() {
    this.name = 'DMM VR预告片破译器';
    this.version = '1.0.0';
    this.timeout = 15000; // 15秒超时
  }

  /**
   * 提取VR预告片真实URL
   * @param {string} playerPageUrl - DMM VR播放器页面URL
   * @returns {Promise<string|null>} 真实的视频URL或null
   */
  async extractVrTrailerUrl(playerPageUrl) {
    if (!playerPageUrl || typeof playerPageUrl !== 'string') {
      log.error(`[${this.name}] 无效的播放器页面URL: ${playerPageUrl}`);
      return null;
    }

    log.info(`[${this.name}] 开始破译VR预告片: ${playerPageUrl}`);
    
    let browser = null;
    let context = null;
    let page = null;

    try {
      // 启动浏览器
      log.debug(`[${this.name}] 启动Playwright浏览器...`);
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      context = await browser.newContext({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1920, height: 1080 },
        ignoreHTTPSErrors: true
      });

      page = await context.newPage();

      // 第一步：处理年龄验证前置障碍
      await this.handleAgeVerification(page);

      // 第二步：执行双重提取策略
      const videoUrl = await this.executeExtractionStrategies(page, playerPageUrl);

      if (videoUrl) {
        log.info(`[${this.name}] 破译成功: ${videoUrl}`);
        return videoUrl;
      } else {
        log.warn(`[${this.name}] 破译失败，未能提取到视频URL: ${playerPageUrl}`);
        return null;
      }

    } catch (error) {
      log.error(`[${this.name}] 破译过程发生错误: ${error.message}`);
      log.error(`[${this.name}] 错误堆栈: ${error.stack}`);
      return null;
    } finally {
      // 资源清理
      try {
        if (page) await page.close();
        if (context) await context.close();
        if (browser) await browser.close();
        log.debug(`[${this.name}] 浏览器资源已清理`);
      } catch (cleanupError) {
        log.error(`[${this.name}] 清理资源时发生错误: ${cleanupError.message}`);
      }
    }
  }

  /**
   * 处理DMM年龄验证
   * @param {Page} page - Playwright页面对象
   */
  async handleAgeVerification(page) {
    log.debug(`[${this.name}] 开始处理年龄验证...`);

    try {
      // 方法1：直接设置年龄验证Cookie
      await page.context().addCookies([
        {
          name: 'age_check_done',
          value: '1',
          domain: '.dmm.co.jp',
          path: '/',
          httpOnly: false,
          secure: false
        },
        {
          name: 'ckcy',
          value: '1',
          domain: '.dmm.co.jp', 
          path: '/',
          httpOnly: false,
          secure: false
        }
      ]);

      // 方法2：访问DMM主站触发年龄验证页面
      log.debug(`[${this.name}] 访问DMM主站进行年龄验证...`);
      await page.goto('https://www.dmm.co.jp/', { 
        waitUntil: 'domcontentloaded',
        timeout: 10000 
      });

      // 检查是否出现年龄验证页面
      const ageVerificationExists = await page.locator('input[value="はい"]').count() > 0;
      
      if (ageVerificationExists) {
        log.debug(`[${this.name}] 检测到年龄验证页面，自动点击确认...`);
        await page.locator('input[value="はい"]').click();
        await page.waitForTimeout(2000); // 等待页面跳转
      }

      log.debug(`[${this.name}] 年龄验证处理完成`);

    } catch (error) {
      log.warn(`[${this.name}] 年龄验证处理失败，但继续执行: ${error.message}`);
    }
  }

  /**
   * 执行双重提取策略
   * @param {Page} page - Playwright页面对象
   * @param {string} playerPageUrl - 播放器页面URL
   * @returns {Promise<string|null>} 提取到的视频URL
   */
  async executeExtractionStrategies(page, playerPageUrl) {
    log.debug(`[${this.name}] 开始执行双重提取策略...`);

    // 策略A：网络监听（首选）
    const networkResult = await this.strategyNetworkListening(page, playerPageUrl);
    if (networkResult) {
      log.info(`[${this.name}] 策略A（网络监听）成功: ${networkResult}`);
      return networkResult;
    }

    // 策略B：DOM解析（后备）
    const domResult = await this.strategyDomParsing(page, playerPageUrl);
    if (domResult) {
      log.info(`[${this.name}] 策略B（DOM解析）成功: ${domResult}`);
      return domResult;
    }

    log.warn(`[${this.name}] 所有提取策略均失败`);
    return null;
  }

  /**
   * 策略A：网络监听提取
   * @param {Page} page - Playwright页面对象
   * @param {string} playerPageUrl - 播放器页面URL
   * @returns {Promise<string|null>} 提取到的视频URL
   */
  async strategyNetworkListening(page, playerPageUrl) {
    log.debug(`[${this.name}] 执行策略A：网络监听...`);

    return new Promise(async (resolve) => {
      let resolved = false;
      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          log.debug(`[${this.name}] 策略A超时，未捕获到视频URL`);
          resolve(null);
        }
      }, this.timeout);

      // 设置网络响应监听器
      const responseHandler = (response) => {
        if (resolved) return;

        const url = response.url();
        
        // 检查是否为视频文件
        if (url.includes('.mp4') || url.includes('.m3u8') ||
            url.includes('video') || url.includes('stream')) {

          // 过滤掉明显不是预告片的URL
          if (!url.includes('thumb') && !url.includes('icon') &&
              !url.includes('poster') && !url.includes('banner') &&
              !url.includes('.jpg') && !url.includes('.jpeg') &&
              !url.includes('.png') && !url.includes('.gif') &&
              !url.includes('pics_dig') && !url.includes('awsimgsrc')) {
            
            log.debug(`[${this.name}] 网络监听捕获到视频URL: ${url}`);
            resolved = true;
            clearTimeout(timeout);
            page.off('response', responseHandler);
            resolve(url);
          }
        }
      };

      page.on('response', responseHandler);

      try {
        // 导航到播放器页面
        await page.goto(playerPageUrl, { 
          waitUntil: 'networkidle',
          timeout: this.timeout 
        });

        // 等待页面加载完成
        await page.waitForTimeout(3000);

        // 尝试触发视频加载
        await this.triggerVideoLoad(page);

      } catch (error) {
        log.debug(`[${this.name}] 策略A导航失败: ${error.message}`);
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          page.off('response', responseHandler);
          resolve(null);
        }
      }
    });
  }

  /**
   * 策略B：DOM解析提取
   * @param {Page} page - Playwright页面对象
   * @param {string} playerPageUrl - 播放器页面URL
   * @returns {Promise<string|null>} 提取到的视频URL
   */
  async strategyDomParsing(page, playerPageUrl) {
    log.debug(`[${this.name}] 执行策略B：DOM解析...`);

    try {
      // 如果页面还没有加载，先导航过去
      if (page.url() !== playerPageUrl) {
        await page.goto(playerPageUrl, { 
          waitUntil: 'domcontentloaded',
          timeout: this.timeout 
        });
      }

      // 等待视频元素加载
      await page.waitForTimeout(5000);

      // 尝试触发视频加载
      await this.triggerVideoLoad(page);

      // 提取video标签的src属性
      const videoSrc = await page.evaluate(() => {
        const video = document.querySelector('video');
        if (video) {
          return video.src || video.currentSrc;
        }
        return null;
      });

      if (videoSrc && videoSrc !== '') {
        log.debug(`[${this.name}] DOM解析提取到视频URL: ${videoSrc}`);
        return videoSrc;
      }

      // 尝试从source标签提取
      const sourceSrc = await page.evaluate(() => {
        const source = document.querySelector('video source');
        if (source) {
          return source.src;
        }
        return null;
      });

      if (sourceSrc && sourceSrc !== '') {
        log.debug(`[${this.name}] DOM解析从source标签提取到视频URL: ${sourceSrc}`);
        return sourceSrc;
      }

      log.debug(`[${this.name}] DOM解析未找到有效的视频URL`);
      return null;

    } catch (error) {
      log.debug(`[${this.name}] 策略B执行失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 尝试触发视频加载
   * @param {Page} page - Playwright页面对象
   */
  async triggerVideoLoad(page) {
    try {
      // 尝试点击播放按钮
      const playButtons = [
        '.play-button',
        '.video-play-button', 
        '[data-play]',
        'button[aria-label*="play"]',
        'button[aria-label*="再生"]'
      ];

      for (const selector of playButtons) {
        const button = page.locator(selector);
        if (await button.count() > 0) {
          log.debug(`[${this.name}] 尝试点击播放按钮: ${selector}`);
          await button.first().click();
          await page.waitForTimeout(2000);
          break;
        }
      }

      // 尝试点击视频元素本身
      const video = page.locator('video');
      if (await video.count() > 0) {
        log.debug(`[${this.name}] 尝试点击视频元素`);
        await video.first().click();
        await page.waitForTimeout(2000);
      }

    } catch (error) {
      log.debug(`[${this.name}] 触发视频加载失败: ${error.message}`);
    }
  }
}

// 创建单例实例
const dmmVrTrailerExtractor = new DmmVrTrailerExtractor();

/**
 * 导出主要函数
 * @param {string} playerPageUrl - DMM VR播放器页面URL
 * @returns {Promise<string|null>} 真实的视频URL或null
 */
async function extractVrTrailerUrl(playerPageUrl) {
  return await dmmVrTrailerExtractor.extractVrTrailerUrl(playerPageUrl);
}

module.exports = {
  extractVrTrailerUrl,
  DmmVrTrailerExtractor
};
