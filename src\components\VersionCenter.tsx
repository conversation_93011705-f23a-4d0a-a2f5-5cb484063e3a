// src/components/VersionCenter.tsx
import React, { useState } from 'react';
import { UnifiedVersion } from '../types';
import WhatsLinkPreviewModal from './WhatsLinkPreviewModal';
import { CheckCircle, Play, Cloud, HardDrive, Monitor, Smartphone, Tablet, Tv, FileVideo, Download, Trash2 } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface VersionCenterProps {
  versions: UnifiedVersion[];
  selectedVersion: UnifiedVersion | null;
  onSelectVersion: (version: UnifiedVersion) => void;
  isLoading?: boolean;
}

export const VersionCenter: React.FC<VersionCenterProps> = ({
  versions,
  selectedVersion,
  onSelectVersion,
  isLoading = false
}) => {
  // WhatsLink 预览状态
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);
  if (isLoading) {
    return (
      <div className="mt-4">
        <h3 className="text-lg font-bold text-neutral-200 mb-2">版本中心</h3>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-neutral-400">正在加载版本信息...</span>
        </div>
      </div>
    );
  }

  if (!versions || versions.length === 0) {
    return (
      <div className="mt-4">
        <h3 className="text-lg font-bold text-neutral-200 mb-2">版本中心</h3>
        <div className="text-center py-8 text-neutral-400">
          没有找到其他版本信息。
        </div>
      </div>
    );
  }

  const formatFileSize = (size: number | null | undefined): string => {
    if (!size) return '-';
    const gb = size / (1024 * 1024 * 1024);
    if (gb >= 1) return `${gb.toFixed(2)} GB`;
    const mb = size / (1024 * 1024);
    return `${mb.toFixed(0)} MB`;
  };

  const getStatusIcon = (version: UnifiedVersion): React.ReactNode => {
    if (version.type === 'local') {
      const localVersion = version as any;
      // 检查是否为预告片
      if (localVersion.sub_type === 'trailer') {
        return <Play className="w-4 h-4 text-orange-400" title="预告片" />;
      }
      return <CheckCircle className="w-4 h-4 text-green-400" title="本地版本" />;
    }
    if (version.type === 'virtual') {
      // 可以根据 download_status 显示不同状态
      const status = (version as any).download_status;
      if (status === 'downloaded') return <HardDrive className="w-4 h-4 text-blue-400" title="已下载" />;
      if (status === 'downloading') return <Download className="w-4 h-4 text-yellow-400" title="下载中" />;
      return <Cloud className="w-4 h-4 text-purple-400" title="虚拟版本" />;
    }
    return <FileVideo className="w-4 h-4 text-gray-400" title="未知类型" />;
  };

  const getStatusText = (version: UnifiedVersion): string => {
    if (version.type === 'local') {
      const localVersion = version as any;
      if (localVersion.sub_type === 'trailer') return '预告片';
      return '本地已有';
    }
    if (version.type === 'virtual') {
      const status = (version as any).download_status;
      if (status === 'downloaded') return '已下载';
      if (status === 'downloading') return '下载中';
      return '虚拟版本';
    }
    return '未知';
  };

  const getSourceText = (version: UnifiedVersion): string => {
    if (version.type === 'local') {
      return version.fileName || version.title || '-';
    }
    if (version.type === 'virtual') {
      const virtualVersion = version as any;
      return virtualVersion.source_forum ?
        `${virtualVersion.source_forum} - ${version.title}` :
        version.title;
    }
    return '-';
  };

  const getResolutionIcon = (resolution: string | null | undefined): React.ReactNode => {
    if (!resolution) return null;
    const res = resolution.toLowerCase();
    if (res.includes('4k') || res.includes('2160')) {
      return <Tv className="w-4 h-4 text-purple-400" title="4K" />;
    }
    if (res.includes('1080') || res.includes('fhd')) {
      return <Monitor className="w-4 h-4 text-blue-400" title="1080p" />;
    }
    if (res.includes('720') || res.includes('hd')) {
      return <Tablet className="w-4 h-4 text-green-400" title="720p" />;
    }
    if (res.includes('480') || res.includes('sd')) {
      return <Smartphone className="w-4 h-4 text-yellow-400" title="480p" />;
    }
    return <FileVideo className="w-4 h-4 text-gray-400" title={resolution} />;
  };

  const handleVersionClick = (version: UnifiedVersion) => {
    onSelectVersion(version);
  };

  const handleActionClick = async (version: UnifiedVersion, action: string) => {
    if (action === 'details') {
      onSelectVersion(version);
    } else if (action === 'play' && version.type === 'local') {
      // 触发播放功能
      const movie = version as any;
      if (movie.filePath) {
        window.sfeElectronAPI.playVideo({
          filePath: movie.filePath,
          title: movie.title
        });
      }
    } else if (action === 'download' && version.type === 'virtual') {
      // 触发下载功能 - 这里可以扩展下载逻辑
      const virtualVersion = version as any;
      if (virtualVersion.magnet_link) {
        window.sfeElectronAPI.openExternalUrl(virtualVersion.magnet_link);
      } else if (virtualVersion.post_url) {
        window.sfeElectronAPI.openExternalUrl(virtualVersion.post_url);
      }
    } else if (action === 'preview' && version.type === 'virtual') {
      // 触发预览功能
      const virtualVersion = version as any;
      if (virtualVersion.magnet_link) {
        await handlePreviewMagnet(virtualVersion.magnet_link);
      }
    } else if (action === 'recycle') {
      // 触发版本回收功能
      await handleRecycleVersion(version);
    }
  };

  const handlePreviewMagnet = async (magnetUrl: string) => {
    setIsLoadingPreview(true);
    setPreviewError(null);
    setIsPreviewModalOpen(true);

    try {
      const result = await window.sfeElectronAPI.queryWhatsLink(magnetUrl);
      if (result.success) {
        setPreviewData(result.data);
      } else {
        setPreviewError(result.error || '获取预览信息失败');
      }
    } catch (error) {
      setPreviewError('网络请求失败，请检查网络连接');
    } finally {
      setIsLoadingPreview(false);
    }
  };

  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    setPreviewData(null);
    setPreviewError(null);
    setIsLoadingPreview(false);
  };

  const handleRecycleVersion = async (version: UnifiedVersion) => {
    try {
      // 确认对话框
      const confirmed = window.confirm(
        `确定要回收版本 "${version.fileName || version.title}" 吗？\n\n` +
        `此操作将：\n` +
        `• 将该版本的文件移动到回收站\n` +
        `• 标记该版本为已回收状态\n` +
        `• 不会影响同一影片的其他版本\n\n` +
        `回收后可以在回收站中管理或恢复。`
      );

      if (!confirmed) {
        return;
      }

      // 获取版本的数据库ID
      const versionDbId = (version as any).db_id;
      if (!versionDbId) {
        toast.error('无法获取版本ID，回收失败');
        return;
      }

      toast.loading('正在回收版本...', { id: 'recycle-version' });

      // 调用回收API
      const result = await window.sfeElectronAPI.recycleVersion(versionDbId);

      if (result.success) {
        toast.success(
          `版本回收成功！\n文件已移动到回收站：${result.recycleBinPath}`,
          {
            id: 'recycle-version',
            duration: 5000
          }
        );

        // 可以触发页面刷新或更新版本列表
        // 这里可以添加回调函数来通知父组件更新数据

      } else {
        toast.error(`版本回收失败：${result.error}`, { id: 'recycle-version' });
      }

    } catch (error) {
      console.error('回收版本时发生错误:', error);
      toast.error('回收版本时发生错误，请重试', { id: 'recycle-version' });
    }
  };

  return (
    <div className="mt-4">
      <h3 className="text-lg font-bold text-neutral-200 mb-2">
        版本中心 ({versions.length} 个版本)
      </h3>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left text-neutral-300">
          <thead className="text-xs text-neutral-400 uppercase bg-slate-700">
            <tr>
              <th scope="col" className="px-4 py-2">状态</th>
              <th scope="col" className="px-4 py-2">来源/描述</th>
              <th scope="col" className="px-4 py-2">大小</th>
              <th scope="col" className="px-4 py-2">分辨率</th>
              <th scope="col" className="px-4 py-2">操作</th>
            </tr>
          </thead>
          <tbody>
            {versions.map((version, index) => {
              const isTrailer = version.type === 'local' && (version as any).sub_type === 'trailer';
              return (
                <tr
                  key={`${version.type}-${version.db_id}-${index}`}
                  className={`border-b border-slate-700 hover:bg-slate-600 cursor-pointer transition-colors ${
                    selectedVersion?.db_id === version.db_id && selectedVersion?.type === version.type
                      ? 'bg-slate-800 ring-2 ring-blue-500'
                      : ''
                  } ${isTrailer ? 'bg-orange-900/20' : ''}`}
                  onClick={() => handleVersionClick(version)}
                >
                  <td className="px-4 py-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(version)}
                      <span className="text-xs">{getStatusText(version)}</span>
                    </div>
                  </td>
                <td className="px-4 py-2">
                  <div className="max-w-xs truncate" title={getSourceText(version)}>
                    {getSourceText(version)}
                  </div>
                  {version.type === 'virtual' && (version as any).source_forum && (
                    <div className="text-xs text-neutral-500 mt-1">
                      来源: {(version as any).source_forum}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2">
                  {formatFileSize(version.fileSize)}
                </td>
                  <td className="px-4 py-2">
                    <div className="flex items-center space-x-2">
                      {getResolutionIcon(version.resolution)}
                      <span className="text-xs">{version.resolution || '-'}</span>
                    </div>
                  </td>
                <td className="px-4 py-2">
                  <div className="flex space-x-2">
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleActionClick(version, 'details');
                      }}
                      className="font-medium text-blue-500 hover:underline text-xs"
                    >
                      详情
                    </button>
                    {version.type === 'local' && (
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActionClick(version, 'play');
                        }}
                        className="font-medium text-green-500 hover:underline text-xs"
                      >
                        播放
                      </button>
                    )}
                    {version.type === 'virtual' && (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleActionClick(version, 'preview');
                          }}
                          className="font-medium text-purple-500 hover:underline text-xs"
                          disabled={!(version as any).magnet_link}
                          title={!(version as any).magnet_link ? '无磁力链接，无法预览' : '预览内容'}
                        >
                          预览
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleActionClick(version, 'download');
                          }}
                          className="font-medium text-orange-500 hover:underline text-xs"
                        >
                          获取
                        </button>
                      </>
                    )}

                    {/* 标记回收按钮 - 只对本地版本显示 */}
                    {version.type === 'local' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActionClick(version, 'recycle');
                        }}
                        className="font-medium text-red-500 hover:underline text-xs flex items-center gap-1"
                        title="将此版本移动到回收站"
                      >
                        <Trash2 className="h-3 w-3" />
                        回收
                      </button>
                    )}
                  </div>
                </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      
      {/* 版本统计信息 */}
      <div className="mt-3 text-xs text-neutral-500 flex space-x-4">
        <span>本地版本: {versions.filter(v => v.type === 'local').length}</span>
        <span>虚拟版本: {versions.filter(v => v.type === 'virtual').length}</span>
      </div>

      {/* WhatsLink 预览弹窗 */}
      <WhatsLinkPreviewModal
        isOpen={isPreviewModalOpen}
        onClose={handleClosePreview}
        data={previewData}
        isLoading={isLoadingPreview}
        error={previewError}
      />
    </div>
  );
};
