// main_process/services/collectorService.js
// 搜集服务 - 混合方案：Chrome 用户配置文件 + 手动验证 + 自动抓取

const { chromium } = require('playwright');
const siteProfileService = require('./siteProfileService');
const NodeNfoParser = require('./nodeNfoParser');
const databaseService = require('./databaseService');
const fileNameBuilder = require('../utils/fileNameBuilder');

let log = null;
let isInitialized = false;

/**
 * 解析论坛日期格式
 * @param {string} dateText - 日期文本
 * @returns {Date|null} 解析后的日期对象，失败返回null
 */
function parseForumDate(dateText) {
  if (!dateText) return null;

  // 清理文本
  const cleanText = dateText.trim();

  // 匹配 YYYY-MM-DD 格式
  const match = cleanText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
  if (match) {
    return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
  }

  // 匹配其他常见格式，如 YYYY/MM/DD
  const match2 = cleanText.match(/(\d{4})\/(\d{1,2})\/(\d{1,2})/);
  if (match2) {
    return new Date(parseInt(match2[1]), parseInt(match2[2]) - 1, parseInt(match2[3]));
  }

  // 匹配 MM-DD 格式（假设是当年）
  const match3 = cleanText.match(/(\d{1,2})-(\d{1,2})/);
  if (match3) {
    const currentYear = new Date().getFullYear();
    return new Date(currentYear, parseInt(match3[1]) - 1, parseInt(match3[2]));
  }

  // 解析相对日期格式
  const now = new Date();

  // 匹配 "X小时前"
  const hoursMatch = cleanText.match(/(\d+)\s*小时前/);
  if (hoursMatch) {
    const hours = parseInt(hoursMatch[1]);
    return new Date(now.getTime() - hours * 60 * 60 * 1000);
  }

  // 匹配 "X分钟前"
  const minutesMatch = cleanText.match(/(\d+)\s*分钟前/);
  if (minutesMatch) {
    const minutes = parseInt(minutesMatch[1]);
    return new Date(now.getTime() - minutes * 60 * 1000);
  }

  // 匹配 "X天前"
  const daysMatch = cleanText.match(/(\d+)\s*天前/);
  if (daysMatch) {
    const days = parseInt(daysMatch[1]);
    return new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
  }

  // 匹配 "昨天"
  if (cleanText.includes('昨天')) {
    return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // 匹配 "前天"
  if (cleanText.includes('前天')) {
    return new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
  }

  // 匹配 "今天"
  if (cleanText.includes('今天')) {
    return new Date(now.getFullYear(), now.getMonth(), now.getDate());
  }

  // 如果无法解析，返回当前日期作为备用
  log && log.warn(`[Collector] 无法解析日期格式: "${dateText}"，使用当前日期作为备用`);
  return new Date();
}

class CollectorService {
  constructor() {
    this.isRunning = false;
    this.isStopping = false; // 任务中断标志
    this.currentTask = null;
    this.taskHistory = [];
    this.browser = null;
    this.context = null;
    this.statusUpdateCallback = null;
    this.workspacePath = null;
    this.enableDownload = false;
    this.forceStop = false; // 强制停止标志
    this.userConfirmationCallback = null; // 用户确认回调
    this.databaseService = databaseService; // 初始化数据库服务
    this.currentStatus = 'idle'; // 当前详细状态
  }

  /**
   * 初始化搜集服务
   */
  static initializeCollectorService(logger, projectRoot) {
    if (isInitialized) {
      return;
    }

    log = logger;
    log.info('[Collector服务] 正在初始化...');

    // 初始化站点配置服务
    siteProfileService.initializeSiteProfileService(logger, projectRoot);

    // 加载站点配置
    const loadResult = siteProfileService.loadSiteProfiles();
    if (loadResult.success) {
      // 排除 chromeUserDataPath，只计算论坛配置数量
      const forumKeys = Object.keys(loadResult.profiles).filter(key => key !== 'chromeUserDataPath');
      const forumCount = forumKeys.length;
      log.info(`[Collector服务] 站点配置加载成功，支持 ${forumCount} 个论坛`);
    } else {
      log.warn(`[Collector服务] 站点配置加载失败: ${loadResult.error}`);
    }

    isInitialized = true;
    log.info('[Collector服务] 初始化完成');
  }

  /**
   * 设置状态更新回调
   */
  setStatusUpdateCallback(callback) {
    this.statusUpdateCallback = callback;
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(status, message, filePath = null) {
    // 更新当前状态
    this.currentStatus = status;

    if (this.statusUpdateCallback) {
      const statusUpdate = {
        status,
        message,
        timestamp: new Date()
      };

      // 如果有文件路径，添加到状态更新中
      if (filePath) {
        statusUpdate.filePath = filePath;
      }

      this.statusUpdateCallback(statusUpdate);
    }
  }

  /**
   * 获取支持的论坛列表
   */
  getSupportedForums() {
    return this.getForums();
  }

  /**
   * 获取支持的论坛列表（内部方法）
   */
  getForums() {
    try {
      const loadResult = siteProfileService.loadSiteProfiles();
      if (!loadResult.success) {
        throw new Error(loadResult.error);
      }

      const profiles = loadResult.profiles;
      // 排除 chromeUserDataPath，只返回论坛配置
      const forums = Object.keys(profiles)
        .filter(key => key !== 'chromeUserDataPath')
        .map(key => ({
          key,
          name: profiles[key].name,
          loginUrl: profiles[key].loginUrl
        }));

      return {
        success: true,
        forums
      };
    } catch (error) {
      log.error(`[Collector] 获取论坛列表失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        forums: []
      };
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      success: true,
      isRunning: this.isRunning,
      status: this.isRunning ? 'running' : 'idle',
      currentStatus: this.currentStatus, // 返回详细状态
      currentTask: this.currentTask,
      taskHistory: this.taskHistory.slice(-10) // 只返回最近10个任务
    };
  }

  /**
   * 获取任务状态（兼容原接口）
   */
  getTaskStatus() {
    return this.getStatus();
  }

  /**
   * 从URL中智能检测起始页面
   * @param {string} url - 目标URL
   * @returns {number} 检测到的页面数，默认为1
   */
  detectStartPageFromUrl(url) {
    try {
      // 支持多种URL格式的页面参数检测
      const patterns = [
        /[&?]page[=:](\d+)/i,           // ?page=5 或 &page=5 或 page:5
        /[&?]p(\d+)/i,                  // ?p5 或 &p5
        /%3Dpage%3D(\d+)/i,             // URL编码的 =page=5
        /page%3D(\d+)/i,                // URL编码的 page=5
        /extra=page%3D(\d+)/i,          // extra=page%3D5 (论坛常见格式)
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
          const pageNum = parseInt(match[1]);
          if (pageNum > 0) {
            log.info(`[Collector] 检测到起始页面: 第 ${pageNum} 页`);
            return pageNum;
          }
        }
      }

      log.info('[Collector] 未检测到页面参数，默认从第 1 页开始');
      return 1;
    } catch (error) {
      log.warn(`[Collector] 页面检测失败: ${error.message}，默认从第 1 页开始`);
      return 1;
    }
  }

  /**
   * 检查页面状态 - 多级风控机制
   * @param {Object} page - Playwright 页面对象
   * @param {Object} siteProfile - 站点配置
   * @returns {Promise<string>} 页面状态: 'ok', 'download_limit_exceeded', 'human_verification_required', 'server_error'
   */
  async checkPageStatus(page, siteProfile) {
    try {
      // 1. 检查是否需要人工干预 (Cloudflare验证等)
      if (siteProfile.humanVerificationIndicatorSelector) {
        const humanVerification = page.locator(siteProfile.humanVerificationIndicatorSelector);
        if (await humanVerification.count() > 0) {
          log.warn('[Collector] 检测到人机验证页面');
          return 'human_verification_required';
        }
      }

      // 2. 检查下载是否超限
      if (siteProfile.downloadLimitIndicatorText) {
        const downloadLimit = page.locator(`body:has-text("${siteProfile.downloadLimitIndicatorText}")`);
        if (await downloadLimit.count() > 0) {
          log.warn('[Collector] 检测到下载次数已达上限');
          return 'download_limit_exceeded';
        }
      }

      // 3. 检查服务器错误
      if (siteProfile.serverErrorIndicatorSelector) {
        const serverError = page.locator(siteProfile.serverErrorIndicatorSelector);
        if (await serverError.count() > 0) {
          log.warn('[Collector] 检测到服务器错误');
          return 'server_error';
        }
      }

      return 'ok'; // 页面状态正常
    } catch (error) {
      log.warn(`[Collector] 页面状态检查失败: ${error.message}`);
      return 'ok'; // 检查失败时假设页面正常，避免误判
    }
  }

  /**
   * 处理页面状态异常 - 执行相应的风控策略
   * @param {string} status - 页面状态
   * @param {Object} page - Playwright 页面对象
   * @param {string} context - 上下文信息
   * @returns {Promise<string>} 处理结果: 'continue', 'stop', 'retry', 'wait_user'
   */
  async handlePageStatusError(status, page, context = '') {
    switch (status) {
      case 'download_limit_exceeded':
        this.updateTaskStatus('stopped', `检测到下载次数已达上限，任务自动停止。${context}`);
        log.info('[Collector] 下载超限，任务停止');
        return 'stop';

      case 'human_verification_required':
        this.updateTaskStatus('paused', `检测到人机验证页面，请手动完成验证后继续。${context}`);
        log.info('[Collector] 需要人工干预，任务暂停');
        // 这里可以实现等待用户操作的逻辑
        return 'wait_user';

      case 'server_error':
        log.warn(`[Collector] 服务器错误，准备重试。${context}`);
        return 'retry';

      default:
        return 'continue';
    }
  }

  /**
   * 启动搜集任务
   */
  async startTask(siteKey, targetUrl, options = {}) {
    if (this.isRunning) {
      return { success: false, error: '已有任务正在运行' };
    }

    if (!isInitialized) {
      return { success: false, error: '服务未初始化' };
    }

    try {
      // 设置任务状态
      this.isRunning = true;
      this.isStopping = false; // 重置中断标志
      this.currentTask = {
        siteKey,
        targetUrl,
        options,
        startTime: new Date(),
        status: 'running'
      };

      log.info(`[Collector] 启动搜集任务: ${siteKey} -> ${targetUrl}`);
      this.updateTaskStatus('initializing', '正在启动搜集任务...');

      // 获取站点配置
      const siteProfile = siteProfileService.getSiteProfile(siteKey);
      if (!siteProfile) {
        throw new Error(`未找到站点配置: ${siteKey}`);
      }

      // 执行搜集任务
      const result = await this.executeCollectionTask(siteProfile, targetUrl, options);

      // 任务完成
      this.currentTask.status = 'completed';
      this.currentTask.endTime = new Date();
      this.currentTask.result = result;
      this.taskHistory.push({ ...this.currentTask });

      log.info(`[Collector] 搜集任务完成: ${result.message}`);
      return { success: true, result };

    } catch (error) {
      log.error(`[Collector] 搜集任务失败: ${error.message}`);
      
      if (this.currentTask) {
        this.currentTask.status = 'failed';
        this.currentTask.endTime = new Date();
        this.currentTask.error = error.message;
        this.taskHistory.push({ ...this.currentTask });
      }

      this.updateTaskStatus('failed', `搜集失败: ${error.message}`);
      return { success: false, error: error.message };

    } finally {
      this.isRunning = false;
      this.currentTask = null;
      this.currentStatus = 'idle'; // 重置状态为idle
    }
  }



  /**
   * 执行搜集任务 - CDP连接方案
   */
  async executeCollectionTask(siteProfile, targetUrl, options) {
    // 重置停止标志
    this.isStopping = false;
    this.forceStop = false;

    // 设置当前站点配置，供其他方法使用
    this.siteProfile = siteProfile;

    // 🔧 智能标记与分类：从targetUrl中提取boardId
    let currentBoardId = null;
    let currentBoardConfig = null;

    const boardIdMatch = targetUrl.match(/fid=(\d+)/);
    if (boardIdMatch) {
      currentBoardId = boardIdMatch[1];
      currentBoardConfig = siteProfile.boards && siteProfile.boards[currentBoardId];

      log.info(`[Collector] 🏷️ 检测到板块ID: ${currentBoardId}`);
      if (currentBoardConfig) {
        log.info(`[Collector] 🏷️ 板块信息: ${currentBoardConfig.name}`);
        log.info(`[Collector] 🏷️ 板块标签: ${JSON.stringify(currentBoardConfig.tags)}`);
      } else {
        log.warn(`[Collector] ⚠️ 未找到板块ID ${currentBoardId} 的配置信息`);
      }
    } else {
      log.warn(`[Collector] ⚠️ 无法从URL中提取板块ID: ${targetUrl}`);
    }

    // 保存板块信息供后续使用
    this.currentBoardId = currentBoardId;
    this.currentBoardConfig = currentBoardConfig;

    log.info(`[Collector] 尝试连接到正在运行的Chrome实例 (端口: 9222)...`);
    this.updateTaskStatus('initializing', '正在连接用户浏览器...');

    let browser;
    try {
      // a. 使用 connectOverCDP 连接到用户手动打开的Chrome
      browser = await chromium.connectOverCDP('http://localhost:9222');
      const context = browser.contexts()[0]; // 获取默认的浏览器上下文

      log.info(`[Collector] 🔇 已连接到Chrome静音模式实例，弹窗将不会干扰用户`);

      // b. 找到用户已经打开的目标页面
      const pages = context.pages();
      let page = pages.find(p => p.url().startsWith(targetUrl));

      if (!page) {
        throw new Error(`无法在已打开的页面中找到目标URL: ${targetUrl}。请确认您已手动打开该页面。`);
      }

      this.updateTaskStatus('scraping', '成功连接并接管页面，开始解析内容...');

      // 先将页面置于最前确保可以操作，然后自动最小化
      await page.bringToFront();

      // 等待一小段时间确保页面激活
      await page.waitForTimeout(500);

      // 自动最小化Chrome窗口，让用户可以继续其他工作
      try {
        // 使用CDP协议最小化窗口
        const client = await context.newCDPSession(page);

        // 获取窗口信息
        const { windowId } = await client.send('Browser.getWindowForTarget', {
          targetId: page.mainFrame()._id
        });

        // 最小化窗口
        await client.send('Browser.setWindowBounds', {
          windowId: windowId,
          bounds: { windowState: 'minimized' }
        });

        log.info('[Collector] Chrome窗口已自动最小化，抓取将在后台进行');
        this.updateTaskStatus('scraping', '窗口已最小化，正在后台解析内容...');
      } catch (error) {
        log.warn(`[Collector] 自动最小化失败: ${error.message}，继续抓取`);
        // 如果CDP方法失败，尝试简单的blur方法
        try {
          await page.evaluate(() => window.blur());
        } catch (e) {
          // 忽略错误，继续抓取
        }
      }

      // c. 后续的解析逻辑 (遍历帖子、抓取数据、下载附件等) 保持不变...
      return await this.executeScrapingLogic(page, siteProfile, targetUrl, options);

    } catch (error) {
      log.error(`[Collector] 任务执行失败: ${error.message}`);
      this.updateTaskStatus('failed', `任务失败: ${error.message}`);
      throw error;
    }
    // d. 注意：我们不再关闭浏览器或上下文
    // 因为这是用户自己的浏览器，不能由程序关闭
  }

  /**
   * 执行抓取逻辑 - 从已连接的页面开始抓取（支持多页面和日期限制）
   */
  async executeScrapingLogic(page, siteProfile, targetUrl, options) {
    log.info('[Collector] 开始执行多页面抓取逻辑');
    const results = [];

    try {
      // 🔧 帖子排序修正：自动添加排序参数
      let finalUrl = targetUrl;
      if (!finalUrl.includes('orderby=dateline')) {
        const separator = finalUrl.includes('?') ? '&' : '?';
        finalUrl += `${separator}filter=author&orderby=dateline`;
        log.info(`[Collector] 🔧 自动添加排序参数: ${finalUrl}`);
      }

      // 智能检测起始页面
      const detectedStartPage = this.detectStartPageFromUrl(finalUrl);
      const startPage = options.startPage || detectedStartPage;

      this.updateTaskStatus('refreshing', `准备从第 ${startPage} 页开始抓取...`);

      // 🔧 设置当前页面URL，用于板块名称提取
      this.currentPageUrl = finalUrl;
      log.info(`[Collector] 🔧 设置当前页面URL: ${this.currentPageUrl}`);

      // 如果检测到的起始页面不是1，保持当前URL；否则导航到第一页
      if (startPage === 1 && detectedStartPage > 1) {
        // 用户想从第一页开始，但当前在其他页面
        let firstPageUrl = finalUrl.replace(/[&?]page=\d+/g, '').replace(/[&?]extra=[^&]*/g, '');
        // 确保第一页也有排序参数
        if (!firstPageUrl.includes('orderby=dateline')) {
          const separator = firstPageUrl.includes('?') ? '&' : '?';
          firstPageUrl += `${separator}filter=author&orderby=dateline`;
        }
        log.info(`[Collector] 导航到第一页: ${firstPageUrl}`);
        await page.goto(firstPageUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        await page.waitForTimeout(2000);
        // 🔧 更新当前页面URL
        this.currentPageUrl = firstPageUrl;
        log.info(`[Collector] 🔧 更新当前页面URL为第一页: ${this.currentPageUrl}`);
      } else {
        // 从当前页面开始抓取
        log.info(`[Collector] 从第 ${startPage} 页开始抓取: ${finalUrl}`);

        // 确保页面在正确的URL上
        const currentUrl = page.url();
        if (currentUrl !== finalUrl) {
          log.info(`[Collector] 当前页面URL不匹配，导航到目标URL: ${finalUrl}`);
          await page.goto(finalUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        }

        await page.waitForTimeout(1000); // 确保页面稳定
      }

      const maxPages = options.maxPages || 10; // 从选项获取要抓取的页面数量，默认为10
      const scrapeDays = options.scrapeDays || 0; // 获取天数设置，0为不限制

      let keepScraping = true;
      let currentPageNum = startPage;
      let pagesProcessed = 0; // 已处理的页面数量

      // 主循环，处理翻页
      while (keepScraping && pagesProcessed < maxPages) {
        this.updateTaskStatus('scraping', `正在分析第 ${currentPageNum} 页...`);

        try {
          // ** 风控检查：检查板块页面的状态 **
          const pageStatus = await this.checkPageStatus(page, siteProfile);
          const handleResult = await this.handlePageStatusError(pageStatus, page, `第 ${currentPageNum} 页`);

          if (handleResult === 'stop') {
            keepScraping = false;
            break;
          } else if (handleResult === 'wait_user') {
            // 暂停任务，等待用户干预
            this.updateTaskStatus('paused', `任务已暂停，等待用户完成人机验证。完成后请重新启动任务。`);
            keepScraping = false;
            break;
          } else if (handleResult === 'retry') {
            // 服务器错误，等待后重试
            log.info('[Collector] 页面错误，等待5秒后重试...');
            await this.delay(5000);
            continue; // 重试当前页面
          }
          // 步骤一：抓取当前页面的所有帖子URL
          log.info(`[Collector] 使用选择器: ${siteProfile.postLinkSelector}`);

          // 添加页面调试信息
          const pageInfo = await page.evaluate(() => {
            return {
              url: window.location.href,
              title: document.title,
              bodyText: document.body ? document.body.textContent.substring(0, 200) : '无body',
              hasLoginIndicator: document.querySelector('a[href*="action=logout"]') ? true : false
            };
          });
          log.info(`[Collector] 页面信息:`, pageInfo);

          let postLinksOnPage = await page.evaluate((selector) => {
            console.log(`[Browser] 查找选择器: ${selector}`);
            const elements = document.querySelectorAll(selector);
            console.log(`[Browser] 找到 ${elements.length} 个元素`);

            // 调试：输出前几个元素的信息
            for (let i = 0; i < Math.min(5, elements.length); i++) {
              console.log(`[Browser] 元素 ${i}: ${elements[i].tagName} - ${elements[i].href || '无href'} - ${elements[i].textContent?.substring(0, 50) || '无文本'}`);
            }

            const hrefs = Array.from(elements).map(a => a.href).filter(href => href);
            console.log(`[Browser] 有效链接数量: ${hrefs.length}`);

            return hrefs;
          }, siteProfile.postLinkSelector);

          // 基于tid参数进行智能去重
          const tidMap = new Map();
          const deduplicatedLinks = [];

          for (const link of postLinksOnPage) {
            try {
              const url = new URL(link);
              const tid = url.searchParams.get('tid');

              if (tid) {
                if (!tidMap.has(tid)) {
                  tidMap.set(tid, link);
                  deduplicatedLinks.push(link);
                } else {
                  log.info(`[Collector] 跳过重复的帖子链接 tid=${tid}: ${link}`);
                }
              } else {
                // 如果没有tid参数，直接添加（可能是其他类型的链接）
                deduplicatedLinks.push(link);
              }
            } catch (e) {
              // 如果URL解析失败，直接添加
              deduplicatedLinks.push(link);
            }
          }

          const originalCount = postLinksOnPage.length;
          postLinksOnPage = deduplicatedLinks;
          log.info(`[Collector] 第 ${currentPageNum} 页去重前 ${originalCount} 个链接，去重后 ${postLinksOnPage.length} 个链接`);

          log.info(`[Collector] 第 ${currentPageNum} 页发现 ${postLinksOnPage.length} 个帖子`);

          // 如果没有找到帖子，尝试备用选择器
          if (postLinksOnPage.length === 0) {
            log.warn(`[Collector] 第 ${currentPageNum} 页没有找到任何帖子链接，尝试备用选择器`);
            log.warn(`[Collector] 当前页面URL: ${page.url()}`);
            log.warn(`[Collector] 使用的选择器: ${siteProfile.postLinkSelector}`);

            // 尝试其他可能的选择器
            const alternativeSelectors = [
              'a[href*="thread"]',
              'a[href*="tid="]',
              '.s.xst a',
              'a.s.xst',
              'tbody[id^="normalthread"] a[href*="tid="]',
              'tr[id^="normalthread"] a[href*="tid="]'
            ];

            for (const altSelector of alternativeSelectors) {
              try {
                const altLinks = await page.evaluate((selector) => {
                  const elements = document.querySelectorAll(selector);
                  const hrefs = Array.from(elements).map(a => a.href).filter(href => href && href.includes('tid='));
                  return hrefs;
                }, altSelector);

                log.info(`[Collector] 备选选择器 "${altSelector}" 找到 ${altLinks.length} 个有效链接`);

                if (altLinks.length > 0) {
                  log.info(`[Collector] 使用备选选择器 "${altSelector}" 找到帖子链接`);
                  postLinksOnPage.push(...altLinks);
                  break; // 找到有效链接就停止尝试
                }
              } catch (e) {
                log.warn(`[Collector] 备选选择器 "${altSelector}" 测试失败: ${e.message}`);
              }
            }
          }

          // 防重复下载：过滤已存在的URL
          let newPostLinks = postLinksOnPage;
          if (postLinksOnPage.length > 0) {
            try {
              // 转换为绝对URL进行检查
              const absoluteUrls = postLinksOnPage.map(url => {
                try {
                  return new URL(url, targetUrl).href;
                } catch (e) {
                  return url; // 如果转换失败，使用原URL
                }
              });

              // 获取已存在的URL
              const existingUrls = this.databaseService.getExistingUrls(absoluteUrls);

              // 过滤出新的URL
              newPostLinks = postLinksOnPage.filter((url, index) => {
                const absoluteUrl = absoluteUrls[index];
                return !existingUrls.has(absoluteUrl);
              });

              const skippedCount = postLinksOnPage.length - newPostLinks.length;
              if (skippedCount > 0) {
                log.info(`[Collector] 第 ${currentPageNum} 页跳过 ${skippedCount} 个已存在的帖子，处理 ${newPostLinks.length} 个新帖子`);
              }
            } catch (error) {
              log.warn(`[Collector] 检查重复URL时出错: ${error.message}，将处理所有帖子`);
              // 如果检查失败，继续处理所有帖子
            }
          }

          // 步骤二：遍历并处理当前页的每个新帖子
          let processedCount = 0;
          for (const postUrl of newPostLinks) {
            // ** 任务中断检查 **
            if (this.isStopping || this.forceStop) {
              log.info('[Collector] 检测到停止信号，中断搜集任务');
              keepScraping = false;
              break;
            }

            try {

              if (!postUrl) {
                log.warn(`[Collector] 跳过无效的帖子链接`);
                continue;
              }

              // 转换为绝对URL
              const absoluteUrl = new URL(postUrl, targetUrl).href;

              // 过滤公告类帖子和无效链接
              const excludePatterns = [
                'tid=148288', // 最新网址公告
                'tid=730414', // 手机端使用公告
                'tid=271670', // VIP入会公告
                'action=redirect',   // 重定向链接
                'action=lastpost'    // 最后回复链接
              ];

              const shouldExclude = excludePatterns.some(pattern => absoluteUrl.includes(pattern));
              if (shouldExclude) {
                log.info(`[Collector] 跳过公告类帖子: ${absoluteUrl}`);
                continue;
              }

              // 只有在确定要处理这个帖子时才递增计数器
              processedCount++;

              // 确保是有效的帖子链接
              if (!absoluteUrl.includes('tid=') || !absoluteUrl.includes('thread')) {
                log.warn(`[Collector] 跳过无效链接: ${absoluteUrl}`);
                continue;
              }

              // 日期检查（如果需要）
              if (scrapeDays > 0) {
                try {
                  // 查找对应的帖子行并获取日期
                  const postRow = await page.locator(`a[href="${postUrl}"]`).locator('xpath=ancestor::tr[1]').first();
                  const dateElement = postRow.locator(siteProfile.postDateSelector).first();

                  // 优先尝试从title属性读取日期（98堂的精确日期）
                  let dateText = await dateElement.getAttribute('title');

                  // 如果title属性不存在，则读取元素的文本内容
                  if (!dateText) {
                    dateText = await dateElement.textContent();
                  }

                  const postDate = parseForumDate(dateText);
                  const thresholdDate = new Date();
                  thresholdDate.setDate(thresholdDate.getDate() - scrapeDays);
                  thresholdDate.setHours(0, 0, 0, 0);

                  if (postDate && postDate < thresholdDate) {
                    log.info(`[Collector] 帖子日期 (${dateText}) 超出设定范围，任务结束。`);
                    keepScraping = false;
                    break;
                  }
                } catch (e) {
                  log.warn(`[Collector] 无法获取帖子日期: ${e.message}`);
                  // 找不到日期元素则忽略，继续处理
                }
              }

              this.updateTaskStatus('scraping', `第 ${currentPageNum} 页：正在处理帖子 ${processedCount}/${newPostLinks.length}: ${absoluteUrl}`);

              // 打开帖子页面并解析内容
              await page.goto(absoluteUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
              await page.waitForTimeout(1000);

              // ** 风控检查：检查帖子页面的状态 **
              const postPageStatus = await this.checkPageStatus(page, siteProfile);
              const postHandleResult = await this.handlePageStatusError(postPageStatus, page, `帖子: ${absoluteUrl}`);

              if (postHandleResult === 'stop') {
                keepScraping = false;
                break; // 停止整个任务
              } else if (postHandleResult === 'wait_user') {
                // 暂停任务，等待用户干预
                this.updateTaskStatus('paused', `任务已暂停，等待用户完成人机验证。完成后请重新启动任务。`);
                keepScraping = false;
                break;
              } else if (postHandleResult === 'retry') {
                // 服务器错误，重试当前帖子
                log.info(`[Collector] 帖子页面错误，等待3秒后重试: ${absoluteUrl}`);
                await this.delay(3000);

                // 重试一次
                try {
                  await page.goto(absoluteUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
                  await page.waitForTimeout(1000);

                  // 再次检查状态
                  const retryStatus = await this.checkPageStatus(page, siteProfile);
                  if (retryStatus !== 'ok') {
                    log.warn(`[Collector] 重试后仍有问题，跳过帖子: ${absoluteUrl}`);
                    continue; // 跳过当前帖子
                  }
                } catch (retryError) {
                  log.warn(`[Collector] 重试失败，跳过帖子: ${absoluteUrl}, 错误: ${retryError.message}`);
                  continue; // 跳过当前帖子
                }
              }

              // 解析帖子内容
              const postData = await this.parsePostContent(page, siteProfile, absoluteUrl);
              if (postData) {
                // 🔧 修复：检查是否有有效的数据（磁力链接、ed2k链接或附件链接）
                const hasValidData = postData.magnetLink || postData.ed2kLink || postData.attachmentUrl;

                // 🔧 修复：默认记录所有有效数据的帖子，不再依赖下载成功与否
                let shouldRecord = hasValidData; // 只要有有效数据就记录

                if (!hasValidData) {
                  log.info(`[Collector] 无有效数据: ${postData.postTitle} - 没有找到磁力链接、ed2k链接或附件链接`);
                  // 🔧 新逻辑：即使没有有效数据，也设置状态并生成档案文件
                  postData.downloadStatus = 'no_attachment';
                  shouldRecord = false; // 不记录到数据库，但会生成档案文件
                } else {
                  log.info(`[Collector] 发现有效数据: ${postData.postTitle}`);
                  log.info(`[Collector] 🔧 将记录到数据库（无论下载是否成功）`);
                }

                // 如果启用了下载功能且有附件链接，则尝试下载
                log.info(`[Collector] 🔍 检查下载条件:`);
                log.info(`[Collector] - enableDownload: ${this.enableDownload}`);
                log.info(`[Collector] - workspacePath: ${this.workspacePath}`);
                log.info(`[Collector] - attachmentUrl: ${postData.attachmentUrl}`);

                // 同时更新任务状态，让前端也能看到这些信息
                this.updateTaskStatus('scraping', `🔍 检查下载条件 - 启用:${this.enableDownload}, 路径:${this.workspacePath ? '已设置' : '未设置'}, 附件:${postData.attachmentUrl ? '有' : '无'}`);

                if (this.enableDownload && this.workspacePath && postData.attachmentUrl) {
                  log.info(`[Collector] ✅ 下载条件满足，开始下载附件: ${postData.postTitle}`);
                  this.updateTaskStatus('downloading', `✅ 下载条件满足，开始下载: ${postData.postTitle}`);

                  try {
                    const downloadResult = await this.downloadAttachments(page, postData, siteProfile);
                    // 下载成功，设置状态并标记为应该记录
                    postData.downloadStatus = 'completed';
                    if (downloadResult && downloadResult.downloadPath) {
                      postData.downloadPath = downloadResult.downloadPath;
                    }
                    // 🔧 修复：不再依赖下载成功来决定是否记录，shouldRecord已在前面设置
                    log.info(`[Collector] 下载成功: ${postData.postTitle}`);
                  } catch (downloadError) {
                    // 设置下载失败状态
                    postData.downloadStatus = 'failed';
                    postData.errorMessage = downloadError.message;

                    // 如果是下载超限或人机验证错误，直接停止任务
                    if (downloadError.message.includes('下载次数已达上限')) {
                      log.warn('[Collector] 检测到下载超限，停止整个任务');
                      this.updateTaskStatus('stopped', '检测到下载次数已达上限，任务自动停止');
                      keepScraping = false;
                      break;
                    } else if (downloadError.message.includes('需要人机验证')) {
                      log.warn('[Collector] 检测到人机验证，暂停任务');
                      this.updateTaskStatus('paused', '任务已暂停，等待用户完成人机验证。完成后请重新启动任务。');
                      keepScraping = false;
                      break;
                    } else {
                      // 🔧 修复：其他下载错误，仍然记录到数据库（因为有有效数据）
                      log.warn(`[Collector] 下载失败，但仍记录到数据库: ${downloadError.message}`);
                      // shouldRecord 保持原值（基于是否有有效数据）
                    }
                  }

                  // ** 风控检查：下载后检查页面状态 **
                  const downloadPageStatus = await this.checkPageStatus(page, siteProfile);
                  const downloadHandleResult = await this.handlePageStatusError(downloadPageStatus, page, `下载后检查: ${postData.postTitle}`);

                  if (downloadHandleResult === 'stop') {
                    keepScraping = false;
                    break; // 下载超限，停止整个任务
                  } else if (downloadHandleResult === 'wait_user') {
                    // 暂停任务，等待用户干预
                    this.updateTaskStatus('paused', `任务已暂停，等待用户完成人机验证。完成后请重新启动任务。`);
                    keepScraping = false;
                    break;
                  }
                } else {
                  log.warn(`[Collector] ❌ 下载条件不满足，跳过下载`);
                  let reasons = [];
                  if (!this.enableDownload) {
                    log.warn(`[Collector] - 下载功能未启用`);
                    reasons.push('下载功能未启用');
                  }
                  if (!this.workspacePath) {
                    log.warn(`[Collector] - 工作区路径未设置`);
                    reasons.push('工作区路径未设置');
                  }
                  if (!postData.attachmentUrl) {
                    log.warn(`[Collector] - 没有附件URL`);
                    reasons.push('没有附件URL');
                  }

                  // 让前端也能看到为什么跳过下载
                  this.updateTaskStatus('scraping', `❌ 跳过下载: ${reasons.join(', ')} - ${postData.postTitle}`);
                }

                // 🔧 新逻辑：无论是否记录到数据库，都生成档案文件（原子化归档）

                // 设置下载状态
                if (!postData.attachmentUrl) {
                  postData.downloadStatus = 'no_attachment';
                } else if (!this.enableDownload || !this.workspacePath) {
                  postData.downloadStatus = 'skipped';
                  postData.errorMessage = '下载功能未启用或工作区路径未设置';
                }

                // 立即生成档案文件
                try {
                  const archiveFilePath = await this._generatePostArchiveFile(postData);
                  if (archiveFilePath) {
                    log.info(`[Collector] 📄 档案文件已生成: ${archiveFilePath}`);
                    // 向前端发送包含文件路径的状态更新
                    this.updateTaskStatus('scraping', `📄 档案已生成: ${postData.postTitle}`, archiveFilePath);
                  }
                } catch (archiveError) {
                  log.error(`[Collector] ❌ 生成档案文件失败: ${archiveError.message}`);
                }

                // 只有在满足条件时才记录到数据库
                if (shouldRecord) {
                  results.push(postData);
                  log.info(`[Collector] 已记录到数据库: ${postData.postTitle}`);
                } else {
                  log.info(`[Collector] 跳过数据库记录: ${postData.postTitle} - 没有成功下载附件且没有磁力/ed2k链接`);
                }
              }

              // 帖子间延迟
              if (options.delay && options.delay > 0) {
                await this.delay(options.delay);
              }

            } catch (error) {
              // 如果是下载超限或人机验证错误，需要停止整个任务
              if (error.message.includes('下载次数已达上限')) {
                log.warn('[Collector] 检测到下载超限，停止整个任务');
                this.updateTaskStatus('stopped', '检测到下载次数已达上限，任务自动停止');
                keepScraping = false;
                break;
              } else if (error.message.includes('需要人机验证')) {
                log.warn('[Collector] 检测到人机验证，暂停任务');
                this.updateTaskStatus('paused', '任务已暂停，等待用户完成人机验证。完成后请重新启动任务。');
                keepScraping = false;
                break;
              } else {
                log.warn(`[Collector] 处理帖子时出错: ${error.message}`);
                // 继续处理下一个帖子
              }
            }
          }

        } catch (error) {
          log.error(`[Collector] 处理第 ${currentPageNum} 页时出错: ${error.message}`);
        }

        // 增加已处理页面计数
        pagesProcessed++;
        log.info(`[Collector] 第 ${currentPageNum} 页处理完成，已处理 ${pagesProcessed}/${maxPages} 页`);

        if (!keepScraping) break; // 如果内层循环已停止，则跳出外层循环

        // 3. 翻页逻辑 - 检查是否还需要继续翻页
        if (pagesProcessed < maxPages) {
          // 检查当前是否在列表页面，如果不是才需要导航回去
          const currentUrl = page.url();
          if (!currentUrl.includes('forumdisplay') && !currentUrl.includes('fid=')) {
            log.info(`[Collector] 当前不在列表页面，导航回到: ${targetUrl}`);
            await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
            await page.waitForTimeout(1000);
          }

          const nextPageButton = page.locator(siteProfile.nextPageSelector);
          if (await nextPageButton.count() > 0) {
            const urlBeforeClick = page.url(); // 重命名避免变量冲突
            this.updateTaskStatus('paging', `准备翻页到第 ${currentPageNum + 1} 页...`);
            try {
              await nextPageButton.first().click();

              // **关键保护机制**：等待URL发生变化，如果URL没变，说明是最后一页
              try {
                await page.waitForURL((url) => url.href !== urlBeforeClick, { timeout: 10000 }); // 等待10秒
                await page.waitForTimeout(2000);
                currentPageNum++;
                log.info(`[Collector] 成功翻页到第 ${currentPageNum} 页`);

                // 页面间延迟
                if (options.pageDelay && options.pageDelay > 0) {
                  await this.delay(options.pageDelay);
                }
              } catch (urlError) {
                log.info('[Collector] 点击"下一页"后URL未改变，判定为最后一页，抓取结束。');
                break; // URL在10秒内没变，退出循环
              }
            } catch (error) {
              log.error(`[Collector] 翻页失败: ${error.message}`);
              break;
            }
          } else {
            log.info('[Collector] 未找到"下一页"按钮，抓取结束。');
            break;
          }
        } else {
          log.info(`[Collector] 已达到页面数量限制 (${pagesProcessed}/${maxPages})，抓取结束。`);
          break;
        }
      }

      // 步骤4: 保存所有数据（即使是强制停止也要保存已搜集的数据）
      const finalResult = await this.saveResults(results, siteProfile);
      log.info(`[Collector] 数据库保存完成: ${finalResult.message}`);

      // 步骤5: 自动导出搜集日志
      const taskInfo = {
        pages: pagesProcessed,
        totalRecords: results.length,
        forceStopped: this.forceStop
      };
      await this._autoExportResults(results, siteProfile, taskInfo);

      // 根据停止方式显示不同的完成消息
      if (this.forceStop) {
        this.updateTaskStatus('force-stopped', `任务已强制停止，已保存 ${results.length} 条记录和搜集日志。`);
      } else if (this.isStopping) {
        this.updateTaskStatus('stopped', `任务已停止，已保存 ${results.length} 条记录和搜集日志。`);
      } else {
        this.updateTaskStatus('completed', `搜集完成，共抓取 ${pagesProcessed} 页，处理 ${results.length} 条记录。`);
      }

      return {
        ...finalResult,
        pages: pagesProcessed,
        totalRecords: results.length,
        forceStopped: this.forceStop
      };

    } catch (error) {
      log.error(`[Collector] 抓取过程中发生错误: ${error.message}`);
      this.updateTaskStatus('failed', `抓取失败: ${error.message}`);
      throw error;
    }
    // 注意：我们不关闭浏览器或页面，因为这是用户自己的浏览器
    log.info('[Collector] 多页面抓取任务完成，用户浏览器保持打开状态');
  }



  /**
   * 解析帖子内容 - 增强版，支持全信息结构化归档
   */
  async parsePostContent(page, siteProfile, postUrl) {
    try {
      // 抓取帖子标题
      let postTitle = '';
      try {
        await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
        postTitle = await page.locator(siteProfile.postTitleSelector).first().textContent();
        postTitle = postTitle ? postTitle.trim() : '';
      } catch (error) {
        postTitle = await page.title() || `帖子`;
      }

      // 🔧 新增：提取帖子正文内容进行元数据解析
      let postBodyText = '';
      let extractedMetadata = {};
      let cloudLinks = []; // 🔧 新增：网盘链接数组

      try {
        if (siteProfile.postBodyContainerSelector) {
          log.info(`[Collector] 🔍 开始提取帖子正文内容...`);
          await page.waitForSelector(siteProfile.postBodyContainerSelector, { timeout: 5000 });

          // 获取主内容区域的纯文本
          const bodyElements = await page.locator(siteProfile.postBodyContainerSelector).all();
          if (bodyElements.length > 0) {
            postBodyText = await bodyElements[0].innerText();
            log.info(`[Collector] 📄 提取到正文内容长度: ${postBodyText.length} 字符`);

            // 解析元数据
            extractedMetadata = this.extractMetadataFromText(postBodyText);
            log.info(`[Collector] 📊 提取的元数据:`, extractedMetadata);

            // 🔧 新增：提取网盘链接与提取码
            cloudLinks = this.extractCloudLinksWithCodes(postBodyText);
            if (cloudLinks.length > 0) {
              log.info(`[Collector] 🌐 提取到 ${cloudLinks.length} 个网盘链接`);
              cloudLinks.forEach((link, index) => {
                log.info(`[Collector] 🌐 网盘链接 ${index + 1}: ${link.url} (提取码: ${link.code})`);
              });
            }
          }
        }
      } catch (error) {
        log.warn(`[Collector] ⚠️ 提取正文内容失败: ${error.message}`);
      }

      // 提取NFO ID（优先使用从正文提取的品番，其次使用标题）
      let nfoId = extractedMetadata.nfoId || NodeNfoParser.extractJavIdFromFilename(postTitle);

      // 抓取磁力链接 - 支持从li标签内提取，修复截断问题
      let magnetLinks = [];
      try {
        const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
        log.info(`[Collector] 🧲 查找磁力链接，使用选择器: ${siteProfile.magnetLinkSelector}`);
        log.info(`[Collector] 🧲 找到 ${magnetElements.length} 个磁力链接元素`);

        for (const element of magnetElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('magnet:')) {
              // 使用更强大的正则表达式提取完整磁力链接
              const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
              if (magnetMatches) {
                magnetMatches.forEach(link => {
                  const cleanLink = link.trim();
                  magnetLinks.push(cleanLink);
                  log.info(`[Collector] 🧲 从li标签HTML提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('magnet:')) {
              magnetLinks.push(href.trim());
              log.info(`[Collector] 🧲 从href属性提取磁力链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('magnet:')) {
                const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
                if (magnetMatches) {
                  magnetMatches.forEach(link => {
                    const cleanLink = link.trim();
                    magnetLinks.push(cleanLink);
                    log.info(`[Collector] 🧲 从HTML内容提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        log.info(`[Collector] 🧲 总共提取到 ${magnetLinks.length} 个磁力链接`);
      } catch (error) {
        log.warn(`[Collector] 无法获取磁力链接: ${error.message}`);
      }

      // 抓取ED2K链接 - 支持从li标签内提取，修复截断问题
      let ed2kLinks = [];
      try {
        const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
        log.info(`[Collector] 🔗 查找ED2K链接，使用选择器: ${siteProfile.ed2kLinkSelector}`);
        log.info(`[Collector] 🔗 找到 ${ed2kElements.length} 个ED2K链接元素`);

        for (const element of ed2kElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('ed2k:')) {
              // 使用更强大的正则表达式提取完整ed2k链接
              const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/g);
              if (ed2kMatches) {
                ed2kMatches.forEach(link => {
                  const cleanLink = link.trim();
                  ed2kLinks.push(cleanLink);
                  log.info(`[Collector] 🔗 从li标签HTML提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('ed2k:')) {
              ed2kLinks.push(href.trim());
              log.info(`[Collector] 🔗 从href属性提取ED2K链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('ed2k:')) {
                const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/g);
                if (ed2kMatches) {
                  ed2kMatches.forEach(link => {
                    const cleanLink = link.trim();
                    ed2kLinks.push(cleanLink);
                    log.info(`[Collector] 🔗 从HTML内容提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        log.info(`[Collector] 🔗 总共提取到 ${ed2kLinks.length} 个ED2K链接`);
      } catch (error) {
        log.warn(`[Collector] 无法获取ED2K链接: ${error.message}`);
      }

      // 抓取附件下载链接
      let attachmentUrls = [];
      try {
        log.info(`[Collector] 查找附件链接，使用选择器: ${siteProfile.attachmentUrlSelector}`);
        const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
        log.info(`[Collector] 找到 ${attachmentElements.length} 个附件元素`);

        for (const element of attachmentElements) {
          const href = await element.getAttribute('href');
          const id = await element.getAttribute('id');
          const text = await element.textContent();
          log.info(`[Collector] 附件元素: id=${id}, href=${href}, text=${text}`);

          if (href) {
            // 转换为绝对URL
            const absoluteUrl = new URL(href, postUrl).href;
            attachmentUrls.push(absoluteUrl);
            log.info(`[Collector] 添加附件URL: ${absoluteUrl}`);
          }
        }
        log.info(`[Collector] 总共找到 ${attachmentUrls.length} 个附件下载链接`);
      } catch (error) {
        log.warn(`[Collector] 无法获取附件链接: ${error.message}`);
      }

      // 抓取解压密码
      let decompressionPassword = '';
      try {
        const passwordElements = await page.locator(siteProfile.passwordSelector).all();
        for (const element of passwordElements) {
          const text = await element.textContent();
          if (text) {
            // 增强的密码模式匹配，支持98堂等论坛的多种格式
            const passwordPatterns = [
              // 标准格式
              /密码[：:]\s*([^\s\n\r]+)/i,
              /解压密码[：:]\s*([^\s\n\r]+)/i,
              /password[：:]\s*([^\s\n\r]+)/i,
              /pwd[：:]\s*([^\s\n\r]+)/i,
              /pass[：:]\s*([^\s\n\r]+)/i,

              // 带括号的格式
              /【解压密码】[：:]\s*([^\s\n\r]+)/i,
              /【密码】[：:]\s*([^\s\n\r]+)/i,
              /\[解压密码\][：:]\s*([^\s\n\r]+)/i,
              /\[密码\][：:]\s*([^\s\n\r]+)/i,

              // 其他常见格式
              /解压码[：:]\s*([^\s\n\r]+)/i,
              /压缩密码[：:]\s*([^\s\n\r]+)/i,
              /提取码[：:]\s*([^\s\n\r]+)/i,
              /访问密码[：:]\s*([^\s\n\r]+)/i,

              // 英文格式
              /extract\s*password[：:]\s*([^\s\n\r]+)/i,
              /archive\s*password[：:]\s*([^\s\n\r]+)/i,
              /unzip\s*password[：:]\s*([^\s\n\r]+)/i,

              // 简化格式（无冒号）
              /密码\s+([^\s\n\r]+)/i,
              /解压密码\s+([^\s\n\r]+)/i,
              /password\s+([^\s\n\r]+)/i
            ];

            for (const pattern of passwordPatterns) {
              const match = text.match(pattern);
              if (match && match[1]) {
                decompressionPassword = match[1].trim();
                // 清理可能的多余字符
                decompressionPassword = decompressionPassword.replace(/[，。！？；：""''（）【】\[\]]/g, '');
                log.info(`[Collector] 🔑 找到解压密码: ${decompressionPassword}`);
                break;
              }
            }

            // 如果没有找到模式匹配，检查是否整个文本就是密码
            if (!decompressionPassword && text.trim().length > 0 && text.trim().length < 50) {
              const cleanText = text.trim();
              // 改进的密码检测：不包含中文且长度合理，排除常见的非密码文本
              const excludePatterns = [
                /点击|下载|链接|附件|文件|大小|时间|日期|作者|回复|查看|分享|转载/,
                /http|www|\.com|\.net|\.org/,
                /magnet:|ed2k:|thunder:/
              ];

              const isExcluded = excludePatterns.some(pattern => pattern.test(cleanText));

              if (!isExcluded && !/[\u4e00-\u9fa5]/.test(cleanText) && cleanText.length >= 3) {
                decompressionPassword = cleanText;
                log.info(`[Collector] 🔑 推测解压密码: ${decompressionPassword}`);
              }
            }

            if (decompressionPassword) break;
          }
        }

        if (decompressionPassword) {
          log.info(`[Collector] ✅ 成功提取解压密码`);
        } else {
          log.info(`[Collector] ❌ 未找到解压密码`);
        }
      } catch (error) {
        log.warn(`[Collector] 无法获取解压密码: ${error.message}`);
      }

      // 抓取预览图URL
      let previewImageUrl = '';
      try {
        if (siteProfile.previewImageSelector) {
          const previewImageElement = await page.locator(siteProfile.previewImageSelector).first();
          if (previewImageElement) {
            previewImageUrl = await previewImageElement.getAttribute('src') || '';
            if (previewImageUrl && !previewImageUrl.startsWith('http')) {
              // 处理相对URL
              const baseUrl = new URL(postUrl).origin;
              previewImageUrl = new URL(previewImageUrl, baseUrl).href;
            }
          }
        }
      } catch (error) {
        log.warn(`[Collector] 无法获取预览图: ${error.message}`);
      }

      // 注意：发布日期需要在列表页面抓取，这里暂时设置为空
      // 在实际的列表页面处理中会设置正确的日期
      const postDate = '';

      // 🔧 增强：添加板块信息和提取的元数据
      const boardInfo = this.currentBoardConfig ? {
        boardId: this.currentBoardId,
        boardName: this.currentBoardConfig.name,
        boardTags: this.currentBoardConfig.tags
      } : null;

      // 🔧 新增：检测回帖可见状态
      let status = 'normal';
      try {
        // 检查是否存在回复按钮（回帖可见的标志）
        const hasReplyButton = siteProfile.replyToViewSelector &&
          await page.locator(siteProfile.replyToViewSelector).count() > 0;

        // 检查是否有任何下载链接
        const hasDownloadLinks = magnetLinks.length > 0 ||
          ed2kLinks.length > 0 ||
          attachmentUrls.length > 0;

        if (hasReplyButton && !hasDownloadLinks) {
          status = 'requires_reply';
          log.info(`[Collector] 🔒 检测到回帖可见状态：只有回复按钮，无下载链接`);
        } else if (hasReplyButton && hasDownloadLinks) {
          log.info(`[Collector] ✅ 检测到回复按钮和下载链接，内容正常可见`);
        } else if (!hasReplyButton && !hasDownloadLinks) {
          log.info(`[Collector] ❓ 未检测到回复按钮和下载链接，可能是无资源帖子`);
        } else {
          log.info(`[Collector] ✅ 内容正常，有下载链接可用`);
        }
      } catch (error) {
        log.warn(`[Collector] 检测回帖可见状态时出错: ${error.message}`);
      }

      return {
        postUrl,
        postTitle,
        nfoId,
        magnetLink: magnetLinks.join('\n'),
        ed2kLink: ed2kLinks.join('\n'),
        attachmentUrl: attachmentUrls.join('\n'),
        decompressionPassword,
        previewImageUrl,
        postDate,
        collectionDate: new Date().toISOString(),
        downloadStatus: 'pending', // 初始状态
        errorMessage: null,
        downloadPath: null,
        status, // 🔧 新增：帖子状态（normal/requires_reply）
        // 🔧 新增字段
        boardInfo,
        fileSize: extractedMetadata.fileSize || null,
        performers: extractedMetadata.performers || null,
        studio: extractedMetadata.studio || null,
        duration: extractedMetadata.duration || null,
        cloudLinks: cloudLinks, // 🔧 新增：网盘链接数组
        postBodyText: postBodyText.substring(0, 1000) // 保存前1000字符用于调试
      };

    } catch (error) {
      log.error(`[Collector] 解析帖子内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 从帖子正文中提取元数据
   * @param {string} text - 帖子正文文本
   * @returns {Object} 提取的元数据
   */
  extractMetadataFromText(text) {
    const metadata = {};

    try {
      // 文件大小提取 - 支持多种格式
      const fileSizePatterns = [
        /文件大小[：:]\s*(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|KB))/i,
        /大小[：:]\s*(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|KB))/i,
        /(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|KB))/i
      ];

      for (const pattern of fileSizePatterns) {
        const match = text.match(pattern);
        if (match) {
          metadata.fileSize = match[1].trim();
          log.info(`[Collector] 📏 提取文件大小: ${metadata.fileSize}`);
          break;
        }
      }

      // 出演者提取
      const performerPatterns = [
        /出演者[：:]\s*(.+?)(?:\n|$)/i,
        /演员[：:]\s*(.+?)(?:\n|$)/i,
        /主演[：:]\s*(.+?)(?:\n|$)/i
      ];

      for (const pattern of performerPatterns) {
        const match = text.match(pattern);
        if (match) {
          metadata.performers = match[1].trim();
          log.info(`[Collector] 👥 提取出演者: ${metadata.performers}`);
          break;
        }
      }

      // 厂商/制作商提取
      const studioPatterns = [
        /メーカー[：:]\s*(.+?)(?:\n|$)/i,
        /厂商[：:]\s*(.+?)(?:\n|$)/i,
        /制作商[：:]\s*(.+?)(?:\n|$)/i,
        /发行商[：:]\s*(.+?)(?:\n|$)/i
      ];

      for (const pattern of studioPatterns) {
        const match = text.match(pattern);
        if (match) {
          metadata.studio = match[1].trim();
          log.info(`[Collector] 🏢 提取厂商: ${metadata.studio}`);
          break;
        }
      }

      // 品番/番号提取（作为nfoId的备用来源）
      const nfoIdPatterns = [
        /品番[：:]\s*([A-Z]{1,5}-\d{3,5}[A-Z]*)/i,
        /番号[：:]\s*([A-Z]{1,5}-\d{3,5}[A-Z]*)/i,
        /型号[：:]\s*([A-Z]{1,5}-\d{3,5}[A-Z]*)/i,
        /品番[：:]\s*([A-Z]{1,5}\s*\d{3,5}[A-Z]*)/i // 支持S-cute这种格式
      ];

      for (const pattern of nfoIdPatterns) {
        const match = text.match(pattern);
        if (match) {
          metadata.nfoId = match[1].trim().toUpperCase();
          log.info(`[Collector] 🔢 提取品番: ${metadata.nfoId}`);
          break;
        }
      }

      // 时长提取
      const durationPatterns = [
        /収録時間[：:]\s*(\d+)分/i,
        /时长[：:]\s*(\d+)分/i,
        /片长[：:]\s*(\d+)分/i,
        /(\d+)分钟/i
      ];

      for (const pattern of durationPatterns) {
        const match = text.match(pattern);
        if (match) {
          metadata.duration = `${match[1]}分钟`;
          log.info(`[Collector] ⏱️ 提取时长: ${metadata.duration}`);
          break;
        }
      }

      log.info(`[Collector] 📊 元数据提取完成，共提取 ${Object.keys(metadata).length} 个字段`);

    } catch (error) {
      log.error(`[Collector] ❌ 元数据提取失败: ${error.message}`);
    }

    return metadata;
  }

  /**
   * 🔧 新增：从文本中提取网盘链接与提取码
   * @param {string} text - 帖子正文内容
   * @returns {Array} 包含url和code的对象数组
   */
  extractCloudLinksWithCodes(text) {
    const cloudLinks = [];

    try {
      // 网盘链接与提取码的匹配模式
      const patterns = [
        // 标准格式：链接X：[URL] 提取码：[CODE]
        /链接\d*[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
        // 变体格式：下载链接：[URL] 密码：[CODE]
        /下载链接[：:]\s*(https?:\/\/[^\s]+)\s*密码[：:]\s*(\w+)/gi,
        // 变体格式：网盘：[URL] 提取码：[CODE]
        /网盘[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
        // 变体格式：百度网盘：[URL] 提取码：[CODE]
        /百度网盘[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
        // 变体格式：分享链接：[URL] 提取码：[CODE]
        /分享链接[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
        // 英文格式：Link: [URL] Code: [CODE]
        /Link[：:]\s*(https?:\/\/[^\s]+)\s*Code[：:]\s*(\w+)/gi,
        // 简化格式：[URL] 提取码 [CODE]
        /(https?:\/\/[^\s]+)\s*提取码\s*(\w+)/gi,
        // 更宽松的格式：[URL] [CODE]（仅限已知网盘域名）
        /(https?:\/\/(?:pan\.baidu\.com|cloud\.189\.cn|www\.lanzou\.com|www\.lanzous\.com)[^\s]+)\s+(\w{4,})/gi
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(text)) !== null) {
          const url = match[1].trim();
          const code = match[2].trim();

          // 验证URL格式
          try {
            new URL(url);
            cloudLinks.push({
              url: url,
              code: code,
              type: this.detectCloudType(url)
            });
            log.info(`[Collector] 🌐 提取网盘链接: ${url} (提取码: ${code})`);
          } catch (e) {
            log.warn(`[Collector] ⚠️ 无效的网盘URL: ${url}`);
          }
        }
      }

      // 去重处理
      const uniqueLinks = [];
      const seen = new Set();

      for (const link of cloudLinks) {
        const key = `${link.url}|${link.code}`;
        if (!seen.has(key)) {
          seen.add(key);
          uniqueLinks.push(link);
        }
      }

      log.info(`[Collector] 🌐 网盘链接提取完成，共找到 ${uniqueLinks.length} 个有效链接`);
      return uniqueLinks;

    } catch (error) {
      log.error(`[Collector] ❌ 网盘链接提取失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 🔧 新增：检测网盘类型
   * @param {string} url - 网盘URL
   * @returns {string} 网盘类型
   */
  detectCloudType(url) {
    if (url.includes('pan.baidu.com')) return '百度网盘';
    if (url.includes('cloud.189.cn')) return '天翼云盘';
    if (url.includes('lanzou.com') || url.includes('lanzous.com')) return '蓝奏云';
    if (url.includes('weiyun.com')) return '微云';
    if (url.includes('onedrive.live.com')) return 'OneDrive';
    if (url.includes('drive.google.com')) return 'Google Drive';
    return '其他网盘';
  }

  /**
   * 保存结果到数据库
   */
  async saveResults(results, siteProfile) {
    let dbResult = null;
    if (results.length > 0) {
      this.updateTaskStatus('saving', '正在保存数据到数据库...');

      try {
        dbResult = await databaseService.insertCollectedLinks(results, siteProfile.key);

        if (dbResult && dbResult.success) {
          log.info(`[Collector] 数据库保存成功: ${dbResult.message}`);
        } else {
          log.warn(`[Collector] 数据库保存失败: ${dbResult ? dbResult.error : '未知错误'}`);
        }
      } catch (error) {
        log.error(`[Collector] 数据库保存异常: ${error.message}`);
        dbResult = { success: false, error: error.message };
      }
    }

    return {
      collectedCount: results.length,
      pages: 1,
      links: results,
      message: `成功搜集 ${results.length} 个帖子信息`,
      dbResult: dbResult
    };
  }

  /**
   * 停止当前搜集任务
   */
  async stopTask() {
    if (!this.isRunning) {
      return { success: false, error: '当前没有运行中的任务' };
    }

    log.info('[Collector] 正在停止搜集任务...');

    // 设置中断标志，让正在执行的任务优雅停止
    this.isStopping = true;

    try {
      this.isRunning = false;
      if (this.currentTask) {
        this.currentTask.status = 'stopped';
        this.currentTask.endTime = new Date();
        this.taskHistory.push({ ...this.currentTask });
        this.currentTask = null;
      }

      this.updateTaskStatus('idle', '搜集任务已被用户停止');
      this.isRunning = false;
      log.info('[Collector] 搜集任务已停止');
      return { success: true, message: '搜集任务已停止' };

    } catch (error) {
      log.error(`[Collector] 停止任务失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 配置工作区设置
   */
  configureDownload(config) {
    this.workspacePath = config.workspacePath;
    this.enableDownload = config.enableDownload;
    log.info(`[Collector] 工作区配置已更新: ${this.enableDownload ? '启用' : '禁用'}, 路径: ${this.workspacePath}`);
  }

  /**
   * 停止搜集任务
   */
  stopCollectionTask() {
    this.isStopping = true;
    log.info('[Collector] 收到停止指令');
  }

  /**
   * 强制停止搜集任务
   */
  forceStopCollectionTask() {
    this.forceStop = true;
    this.isStopping = true;
    log.info('[Collector] 收到强制停止指令，将立即中断任务');
  }

  /**
   * 用户确认回调（由前端调用）
   */
  handleUserConfirmation(confirmed) {
    if (this.userConfirmationCallback) {
      this.userConfirmationCallback(confirmed);
      this.userConfirmationCallback = null;
    }
  }

  /**
   * 实例方法：初始化搜集服务（兼容原接口）
   */
  initializeCollectorService(logger, projectRoot) {
    return CollectorService.initializeCollectorService(logger, projectRoot);
  }

  /**
   * 导出搜集数据到文件
   */
  async exportDataToFile(exportConfig) {
    const { folderPath, forumName, includeDate = true } = exportConfig;

    try {
      log.info(`[Collector] 开始导出数据到: ${folderPath}`);

      // 获取所有搜集数据
      const dataResult = databaseService.getCollectedLinks({ page: 1, pageSize: 10000 });
      if (!dataResult.success) {
        throw new Error(`获取数据失败: ${dataResult.error}`);
      }

      const links = dataResult.data || [];
      if (links.length === 0) {
        return { success: false, error: '没有可导出的数据' };
      }

      // 生成文件名
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS

      let fileName = forumName || '论坛搜集数据';
      if (includeDate) {
        fileName += `_${dateStr}_${timeStr}`;
      }
      fileName += '.txt';

      // 生成文件内容
      const lines = [];
      lines.push(`# ${forumName || '论坛'} 搜集数据导出`);
      lines.push(`# 导出时间: ${now.toLocaleString()}`);
      lines.push(`# 总计: ${links.length} 条记录`);
      lines.push('');

      // 按论坛分组
      const groupedByForum = {};
      links.forEach(link => {
        const forum = link.source_forum || '未知论坛';
        if (!groupedByForum[forum]) {
          groupedByForum[forum] = [];
        }
        groupedByForum[forum].push(link);
      });

      // 输出每个论坛的数据
      Object.keys(groupedByForum).forEach(forum => {
        lines.push(`## ${forum} (${groupedByForum[forum].length} 条记录)`);
        lines.push('');

        groupedByForum[forum].forEach((link, index) => {
          lines.push(`${index + 1}. ${link.post_title || '无标题'}`);
          lines.push(`   URL: ${link.post_url}`);
          if (link.nfoId) {
            lines.push(`   NFO ID: ${link.nfoId}`);
          }
          if (link.magnet_link) {
            lines.push(`   磁力链接: ${link.magnet_link}`);
          }
          if (link.ed2k_link) {
            lines.push(`   ED2K链接: ${link.ed2k_link}`);
          }
          if (link.attachment_url) {
            lines.push(`   附件链接: ${link.attachment_url}`);
          }
          if (link.decompression_password) {
            lines.push(`   解压密码: ${link.decompression_password}`);
          }
          lines.push(`   搜集时间: ${new Date(link.collection_date).toLocaleString()}`);
          lines.push('');
        });

        lines.push('');
      });

      // 写入文件
      const fs = require('fs');
      const path = require('path');
      const fullPath = path.join(folderPath, fileName);

      fs.writeFileSync(fullPath, lines.join('\n'), 'utf-8');

      log.info(`[Collector] 数据导出成功: ${fullPath}`);
      return {
        success: true,
        filePath: fullPath,
        fileName,
        recordCount: links.length,
        message: `成功导出 ${links.length} 条记录到 ${fileName}`
      };

    } catch (error) {
      log.error(`[Collector] 导出数据失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 抓取单个页面的帖子
   * @param {Object} page - Playwright 页面对象
   * @param {Object} siteProfile - 站点配置
   * @param {number} pageNumber - 当前页码
   * @param {string} targetUrl - 目标URL（用于返回列表页面）
   * @param {Object} options - 抓取选项
   * @returns {Promise<Array>} 抓取到的帖子数据数组
   */
  async scrapeSinglePage(page, siteProfile, pageNumber, targetUrl, options) {
    const results = [];

    try {
      // 提取帖子链接
      const allPostLinks = await page.evaluate((selector) => {
        return Array.from(document.querySelectorAll(selector)).map(a => a.href);
      }, siteProfile.postLinkSelector);

      // 去重并过滤
      const uniquePostLinks = [...new Set(allPostLinks)];

      // 进一步过滤，排除公告类帖子
      const postLinks = uniquePostLinks.filter(url => {
        const excludePatterns = [
          'tid=148288', // 最新网址公告
          'tid=730414', // 手机端使用公告
          'tid=271670', // VIP入会公告
          'redirect',   // 重定向链接
          'lastpost'    // 最后回复链接
        ];

        return !excludePatterns.some(pattern => url.includes(pattern));
      });

      log.info(`[Collector] 第 ${pageNumber} 页：发现 ${allPostLinks.length} 个链接，去重后 ${uniquePostLinks.length} 个，过滤后 ${postLinks.length} 个有效帖子`);

      if (postLinks.length === 0) {
        log.warn(`[Collector] 第 ${pageNumber} 页未找到有效帖子`);
        return results;
      }

      // 处理所有找到的帖子
      for (let i = 0; i < postLinks.length; i++) {
        if (this.isStopping) {
          log.info('[Collector] 收到停止指令，中断当前页面抓取...');
          break;
        }

        const postUrl = postLinks[i];
        this.updateTaskStatus('scraping', `第 ${pageNumber} 页：正在处理帖子 ${i + 1}/${postLinks.length}: ${postUrl}`);

        try {
          await page.goto(postUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
          await page.waitForTimeout(1000);

          // 解析帖子内容
          const postData = await this.parsePostContent(page, siteProfile, postUrl);
          if (postData) {
            // 检查是否有有效的数据（磁力链接、ed2k链接或附件链接）
            const hasValidData = postData.magnetLink || postData.ed2kLink || postData.attachmentUrl;

            if (!hasValidData) {
              log.info(`[Collector] 跳过记录: ${postData.postTitle} - 没有找到磁力链接、ed2k链接或附件链接`);
              continue; // 跳过这个帖子，不记录到results中
            }

            log.info(`[Collector] 发现有效数据: ${postData.postTitle}`);

            let shouldRecord = false; // 标记是否应该记录这个帖子

            // 如果有磁力链接或ed2k链接，直接记录
            if (postData.magnetLink || postData.ed2kLink) {
              shouldRecord = true;
              log.info(`[Collector] 发现磁力/ed2k链接，将记录: ${postData.postTitle}`);
            }

            // 如果启用了下载功能且有附件链接，则尝试下载
            log.info(`[Collector] 🔍 检查下载条件:`);
            log.info(`[Collector] - enableDownload: ${this.enableDownload}`);
            log.info(`[Collector] - workspacePath: ${this.workspacePath}`);
            log.info(`[Collector] - attachmentUrl: ${postData.attachmentUrl}`);

            // 同时更新任务状态，让前端也能看到这些信息
            this.updateTaskStatus('scraping', `🔍 检查下载条件 - 启用:${this.enableDownload}, 路径:${this.workspacePath ? '已设置' : '未设置'}, 附件:${postData.attachmentUrl ? '有' : '无'}`);

            if (this.enableDownload && this.workspacePath && postData.attachmentUrl) {
              try {
                await this.downloadAttachments(page, postData, siteProfile);
                // 下载成功，标记为应该记录
                shouldRecord = true;
                log.info(`[Collector] 下载成功，将记录: ${postData.postTitle}`);
              } catch (downloadError) {
                // 如果是下载超限或人机验证错误，需要停止整个任务
                if (downloadError.message.includes('下载次数已达上限')) {
                  log.warn('[Collector] 检测到下载超限，停止整个任务');
                  this.updateTaskStatus('stopped', '检测到下载次数已达上限，任务自动停止');
                  throw downloadError; // 重新抛出错误，让外层处理
                } else if (downloadError.message.includes('需要人机验证')) {
                  log.warn('[Collector] 检测到人机验证，暂停任务');
                  this.updateTaskStatus('paused', '任务已暂停，等待用户完成人机验证。完成后请重新启动任务。');
                  throw downloadError; // 重新抛出错误，让外层处理
                } else {
                  // 其他下载错误，不记录这个帖子
                  log.warn(`[Collector] 下载失败，不记录此帖子: ${downloadError.message}`);
                  shouldRecord = false;
                }
              }
            } else {
              log.warn(`[Collector] ❌ 下载条件不满足，跳过下载`);
              let reasons = [];
              if (!this.enableDownload) {
                log.warn(`[Collector] - 下载功能未启用`);
                reasons.push('下载功能未启用');
              }
              if (!this.workspacePath) {
                log.warn(`[Collector] - 工作区路径未设置`);
                reasons.push('工作区路径未设置');
              }
              if (!postData.attachmentUrl) {
                log.warn(`[Collector] - 没有附件URL`);
                reasons.push('没有附件URL');
              }

              // 让前端也能看到为什么跳过下载
              this.updateTaskStatus('scraping', `❌ 跳过下载: ${reasons.join(', ')} - ${postData.postTitle}`);
            }

            // 只有在满足条件时才记录数据
            if (shouldRecord) {
              results.push(postData);
              log.info(`[Collector] 已记录: ${postData.postTitle}`);
            } else {
              log.info(`[Collector] 跳过记录: ${postData.postTitle} - 没有成功下载附件且没有磁力/ed2k链接`);
            }
          }

        } catch (error) {
          // 如果是下载超限或人机验证错误，需要停止整个任务
          if (error.message.includes('下载次数已达上限') || error.message.includes('需要人机验证')) {
            log.warn(`[Collector] 检测到关键错误，停止任务: ${error.message}`);
            throw error; // 重新抛出错误，让更外层处理
          } else {
            log.warn(`[Collector] 跳过帖子 ${postUrl}，原因: ${error.message}`);
          }
        }

        if (options.delay && options.delay > 0) {
          await this.delay(options.delay);
        }
      }

      // 返回到列表页面准备翻页
      try {
        await page.goBack();
        await page.waitForTimeout(1000);

        // 验证是否成功返回到列表页面
        const currentUrl = page.url();
        if (!currentUrl.includes('forumdisplay') && !currentUrl.includes('forum.php')) {
          log.warn(`[Collector] 返回列表页面后URL异常: ${currentUrl}`);
          // 尝试重新导航到目标URL
          await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        }
      } catch (error) {
        log.error(`[Collector] 返回列表页面失败: ${error.message}`);
        // 尝试重新导航到目标URL
        try {
          await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        } catch (navError) {
          log.error(`[Collector] 重新导航到目标URL失败: ${navError.message}`);
          throw new Error(`无法返回到列表页面: ${error.message}`);
        }
      }

    } catch (error) {
      log.error(`[Collector] 抓取第 ${pageNumber} 页时发生错误: ${error.message}`);
    }

    return results;
  }

  /**
   * 翻页到下一页
   * @param {Object} page - Playwright 页面对象
   * @param {Object} siteProfile - 站点配置
   * @param {number} currentPage - 当前页码
   * @returns {Promise<boolean>} 是否成功翻页
   */
  async goToNextPage(page, siteProfile, currentPage) {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        this.updateTaskStatus('paging', `准备翻页到第 ${currentPage + 1} 页... (尝试 ${retryCount + 1}/${maxRetries})`);

        // 等待页面稳定
        await page.waitForTimeout(1000);

        // 查找下一页按钮
        const nextPageButton = page.locator(siteProfile.nextPageSelector);
        const buttonCount = await nextPageButton.count();

        if (buttonCount === 0) {
          log.info(`[Collector] 第 ${currentPage} 页未找到"下一页"按钮，可能已到最后一页`);
          return false;
        }

        // 检查按钮是否可见和可点击
        const isVisible = await nextPageButton.first().isVisible();
        const isEnabled = await nextPageButton.first().isEnabled();

        if (!isVisible || !isEnabled) {
          log.info(`[Collector] 第 ${currentPage} 页的"下一页"按钮不可用，可能已到最后一页`);
          return false;
        }

        // 获取当前页面URL作为参考
        const currentUrl = page.url();
        log.info(`[Collector] 当前页面URL: ${currentUrl}`);

        // 点击下一页按钮
        log.info(`[Collector] 点击"下一页"按钮，从第 ${currentPage} 页翻到第 ${currentPage + 1} 页`);

        // 使用Promise.race来处理点击和页面导航
        await Promise.race([
          nextPageButton.first().click(),
          page.waitForNavigation({ waitUntil: 'domcontentloaded', timeout: 15000 })
        ]);

        // 等待页面加载完成
        await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
        await page.waitForTimeout(2000); // 额外等待确保页面完全加载

        // 验证翻页是否成功
        const newUrl = page.url();
        if (newUrl === currentUrl) {
          throw new Error('页面URL未发生变化，翻页可能失败');
        }

        // 验证页面内容是否正确加载
        const hasPostLinks = await page.locator(siteProfile.postLinkSelector).count() > 0;
        if (!hasPostLinks) {
          throw new Error('新页面未找到帖子链接，可能加载失败');
        }

        log.info(`[Collector] 成功翻页到第 ${currentPage + 1} 页，新URL: ${newUrl}`);
        return true;

      } catch (error) {
        retryCount++;
        log.warn(`[Collector] 翻页尝试 ${retryCount} 失败: ${error.message}`);

        if (retryCount < maxRetries) {
          log.info(`[Collector] 等待 ${retryCount * 2} 秒后重试翻页...`);
          await this.delay(retryCount * 2000); // 递增延迟

          // 尝试刷新页面
          try {
            await page.reload({ waitUntil: 'domcontentloaded', timeout: 30000 });
            await page.waitForTimeout(1000);
          } catch (reloadError) {
            log.warn(`[Collector] 页面刷新失败: ${reloadError.message}`);
          }
        } else {
          log.error(`[Collector] 翻页失败，已达到最大重试次数: ${error.message}`);
          return false;
        }
      }
    }

    return false;
  }

  /**
   * 下载附件
   * @param {Object} page - Playwright 页面对象
   * @param {Object} postData - 帖子数据
   * @param {Object} siteProfile - 站点配置
   * @returns {Promise<void>}
   */
  async downloadAttachments(page, postData, siteProfile) {
    const { postTitle, decompressionPassword, attachmentUrl } = postData;

    if (!attachmentUrl) {
      log.info(`[Collector] 跳过下载: 没有附件链接`);
      return;
    }

    const attachmentUrls = attachmentUrl.split('\n').filter(url => url.trim());
    log.info(`[Collector] 发现 ${attachmentUrls.length} 个附件链接`);

    for (let i = 0; i < attachmentUrls.length; i++) {
      const url = attachmentUrls[i].trim();

      try {
        // 🔧 使用新的标准化文件名生成方法进行重复检测
        const fileNameResult = this.generateStandardFileName(postData, 'rar'); // 使用rar作为默认扩展名进行检测

        if (!fileNameResult.success) {
          log.error(`[Collector] ❌ 无法生成期望文件名进行重复检测: ${fileNameResult.error}`);
          // 如果无法生成文件名，跳过重复检测，继续下载流程
        }

        // 🔧 检查附件是否已经下载过 - 使用全局统一的attachments目录
        const path = require('path');
        const fs = require('fs');

        // 🔧 修正：使用全局统一的attachments目录，不按论坛分类
        const attachmentsDir = path.join(this.workspacePath, 'attachments');

        log.info(`[Collector] 检查下载状态 - 工作区: ${this.workspacePath}`);
        log.info(`[Collector] 检查下载状态 - 论坛: ${siteProfile.name || 'Unknown'}`);
        log.info(`[Collector] 检查下载状态 - 附件目录: ${attachmentsDir}`);

        if (fileNameResult.success) {
          const expectedFileNameBase = fileNameResult.fileName.replace(/\.[^.]+$/, ''); // 移除扩展名
          log.info(`[Collector] 检查下载状态 - 期望文件名基础: ${expectedFileNameBase}`);
        }

        if (fs.existsSync(attachmentsDir) && fileNameResult.success) {
          const existingFiles = fs.readdirSync(attachmentsDir);
          log.info(`[Collector] 附件目录存在，包含 ${existingFiles.length} 个文件: ${existingFiles.join(', ')}`);

          const expectedFileNameBase = fileNameResult.fileName.replace(/\.[^.]+$/, ''); // 移除扩展名
          const existingFile = existingFiles.find(file => {
            // 移除扩展名进行比较
            const fileNameWithoutExt = file.replace(/\.[^/.]+$/, '');
            return fileNameWithoutExt === expectedFileNameBase;
          });

          if (existingFile) {
            log.info(`[Collector] ✅ 附件已存在，跳过下载: ${existingFile}`);
            databaseService.updateDownloadStatus(postData.postUrl, 'completed', path.join(attachmentsDir, existingFile));
            continue;
          } else {
            log.info(`[Collector] 附件目录中未找到匹配的文件，继续下载`);
          }
        } else {
          if (!fs.existsSync(attachmentsDir)) {
            log.info(`[Collector] 附件目录不存在，继续下载`);
          } else {
            log.warn(`[Collector] ⚠️ 无法生成期望文件名，跳过重复检测，继续下载`);
          }
        }

        this.updateTaskStatus('downloading', `正在下载附件 ${i + 1}/${attachmentUrls.length}: ${postTitle}`);

        // 更新数据库状态为下载中
        log.info(`[Collector] 更新数据库状态为下载中: ${postData.postUrl}`);
        databaseService.updateDownloadStatus(postData.postUrl, 'downloading');

        // 使用Playwright popup事件模型实现无干扰下载
        log.info(`[Collector] 🎯 使用popup事件模型处理下载弹窗`);
        log.info(`[Collector] 🔇 Chrome静音模式已启用，弹窗将在屏幕外运行`);

        try {
          // 🔧 首先配置浏览器下载路径到论坛的attachments目录
          const path = require('path');
          const fs = require('fs');

          // 🔧 修正：确保全局统一的attachments目录存在
          const attachmentsDir = path.join(this.workspacePath, 'attachments');
          log.info(`[Collector] 📁 目标下载目录: ${attachmentsDir}`);

          if (!fs.existsSync(attachmentsDir)) {
            log.info(`[Collector] 📁 创建下载目录: ${attachmentsDir}`);
            fs.mkdirSync(attachmentsDir, { recursive: true });
          }

          // 设置浏览器下载路径 - 使用正确的Playwright方法
          try {
            // 方法1: 通过CDP设置下载行为（最可靠的方法）
            const client = await page.context().newCDPSession(page);
            await client.send('Page.setDownloadBehavior', {
              behavior: 'allow',
              downloadPath: attachmentsDir
            });
            log.info(`[Collector] 📁 已通过CDP设置下载路径: ${attachmentsDir}`);

            // 方法2: 验证设置是否成功
            try {
              // 注意：Browser.getDownloadPath 可能不存在，我们尝试其他方法验证
              log.info(`[Collector] 📁 下载路径设置完成，目标目录: ${attachmentsDir}`);
            } catch (getPathError) {
              log.info(`[Collector] 📁 无法验证下载路径，但设置命令已发送`);
            }

          } catch (downloadPathError) {
            log.warn(`[Collector] ⚠️ 设置下载路径失败: ${downloadPathError.message}`);
            log.info(`[Collector] 📁 将依赖备用检测机制检查下载文件`);
          }

          // 🔧 修正：查找所有附件链接并逐一下载
          const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
          log.info(`[Collector] 🔗 发现 ${attachmentElements.length} 个附件元素`);

          if (attachmentElements.length === 0) {
            log.warn(`[Collector] ⚠️ 未找到附件元素，跳过下载`);
            continue;
          }

          // 🔧 修正：遍历所有附件元素进行下载
          for (let attachmentIndex = 0; attachmentIndex < attachmentElements.length; attachmentIndex++) {
            log.info(`[Collector] 🔗 准备下载附件 ${attachmentIndex + 1}/${attachmentElements.length}`);

            // 1. 设置一个Promise来等待'popup'事件
            const popupPromise = page.waitForEvent('popup');
            log.info(`[Collector] 📋 已设置popup事件监听器`);

            // 2. 点击链接，这个动作会触发弹窗
            log.info(`[Collector] 🖱️ 点击附件 ${attachmentIndex + 1} 触发弹窗...`);
            await attachmentElements[attachmentIndex].click();

            // 3. 等待并捕获弹窗页面的对象
            log.info(`[Collector] ⏳ 等待弹窗出现...`);
            const popupPage = await popupPromise;
            log.info(`[Collector] 🎉 成功捕获弹窗页面！URL: ${popupPage.url()}`);
            log.info(`[Collector] 🔇 弹窗已在屏幕外运行（Chrome静音模式）`);

            // 4. 同时监听主页面和弹窗页面的下载事件
            let downloadCompleted = false;
            let downloadPath = null;

            // 监听主页面的下载事件
            const mainPageDownloadHandler = async (download) => {
              if (!downloadCompleted) {
                log.info(`[Collector] 📥 主页面检测到下载事件: ${download.suggestedFilename()}`);
                await handleDownload(download);
              }
            };

            // 监听弹窗页面的下载事件
            const popupPageDownloadHandler = async (download) => {
              if (!downloadCompleted) {
                log.info(`[Collector] 📥 弹窗页面检测到下载事件: ${download.suggestedFilename()}`);
                await handleDownload(download);
              }
            };

            // 统一的下载处理函数
            const handleDownload = async (download) => {
              try {
                // 获取原始文件名
                const originalFileName = download.suggestedFilename();
                const fileExtension = originalFileName.split('.').pop() || 'rar';

                log.info(`[Collector] 🔍 开始处理下载文件:`);
                log.info(`[Collector] - 原始文件名: ${originalFileName}`);
                log.info(`[Collector] - 文件扩展名: ${fileExtension}`);

                // 🔧 使用新的标准化文件名生成方法，带校验
                const fileNameResult = this.generateStandardFileName(postData, fileExtension);

                if (!fileNameResult.success) {
                  log.error(`[Collector] ❌ 文件名生成失败: ${fileNameResult.error}`);
                  this.updateTaskStatus('error', `文件重命名失败: ${fileNameResult.error}`);
                  return;
                }

                const newFileName = fileNameResult.fileName;
                const fullPath = path.join(attachmentsDir, newFileName);

                log.info(`[Collector] ✅ 文件名校验通过:`);
                log.info(`[Collector] - 新文件名: ${newFileName}`);
                log.info(`[Collector] - 完整路径: ${fullPath}`);

                this.updateTaskStatus('downloading', `正在下载附件: ${originalFileName}`);
                log.info(`[Collector] 💾 开始保存文件: ${originalFileName} -> ${newFileName}`);
                log.info(`[Collector] 💾 保存到路径: ${fullPath}`);

                // 保存文件到指定路径
                await download.saveAs(fullPath);
                log.info(`[Collector] 💾 saveAs调用成功`);

                // 等待一小段时间确保文件写入完成
                await page.waitForTimeout(1000);

                // 检查是否有重复文件在Downloads文件夹
                const defaultDownloadPath = path.join(require('os').homedir(), 'Downloads', originalFileName);
                if (fs.existsSync(defaultDownloadPath)) {
                  log.warn(`[Collector] ⚠️ 发现Downloads文件夹中有重复文件，正在删除: ${defaultDownloadPath}`);
                  try {
                    fs.unlinkSync(defaultDownloadPath);
                    log.info(`[Collector] ✅ 删除Downloads文件夹中的重复文件成功`);
                  } catch (deleteError) {
                    log.error(`[Collector] ❌ 无法删除Downloads文件夹中的重复文件: ${deleteError.message}`);
                  }
                }

                // 验证文件是否真的存在
                if (fs.existsSync(fullPath)) {
                  const stats = fs.statSync(fullPath);
                  log.info(`[Collector] ✅ 文件确认存在: ${fullPath}, 大小: ${stats.size} bytes`);

                  downloadCompleted = true;
                  downloadPath = fullPath;

                  log.info(`[Collector] ✅ 附件下载完成: ${newFileName}`);
                  this.updateTaskStatus('download-completed', `附件下载完成: ${newFileName}`);

                  // 🔧 文件内容校验 - 检查文件名是否与内容匹配
                  const contentValidation = await this.validateFileContent(fullPath, postData);
                  if (!contentValidation.isValid) {
                    log.warn(`[Collector] ⚠️ 文件内容校验失败: ${contentValidation.reason}`);
                    log.warn(`[Collector] ⚠️ 预期NFO ID: ${postData.nfoId}, 实际检测到: ${contentValidation.detectedId || '无'}`);

                    // 如果检测到不同的NFO ID，使用检测到的ID重新命名文件
                    if (contentValidation.detectedId && contentValidation.detectedId !== postData.nfoId) {
                      const correctedFileName = `${contentValidation.detectedId}.${fileExtension}`;
                      const correctedPath = path.join(attachmentsDir, correctedFileName);

                      try {
                        fs.renameSync(fullPath, correctedPath);
                        log.info(`[Collector] ✅ 文件已重新命名为正确的NFO ID: ${correctedFileName}`);
                        downloadPath = correctedPath;

                        // 更新数据库中的路径
                        databaseService.updateDownloadStatus(postData.postUrl, 'completed', correctedPath);
                      } catch (renameError) {
                        log.error(`[Collector] ❌ 重新命名文件失败: ${renameError.message}`);
                        // 继续使用原文件名
                        databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                      }
                    } else {
                      // 更新数据库状态为已完成
                      databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                    }
                  } else {
                    log.info(`[Collector] ✅ 文件内容校验通过`);
                    // 更新数据库状态为已完成
                    databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                  }

                } else {
                  // 如果指定路径没有文件，检查默认下载文件夹
                  const defaultDownloadPath = path.join(require('os').homedir(), 'Downloads', originalFileName);
                  log.warn(`[Collector] ⚠️ 指定路径没有文件，检查默认下载文件夹: ${defaultDownloadPath}`);

                  if (fs.existsSync(defaultDownloadPath)) {
                    log.info(`[Collector] 📁 在默认下载文件夹找到文件，正在移动并重命名...`);
                    log.info(`[Collector] 📁 从: ${defaultDownloadPath}`);
                    log.info(`[Collector] 📁 到: ${fullPath}`);
                    log.info(`[Collector] 📁 重命名: ${originalFileName} -> ${newFileName}`);

                    // 🔧 添加重命名前的安全验证
                    const validation = this.validateFileRename(defaultDownloadPath, fullPath, postData);
                    if (!validation.shouldProceed) {
                      log.error(`[Collector] ❌ 重命名验证失败: ${validation.error}`);
                      log.error(`[Collector] 为安全起见，保持原文件名: ${originalFileName}`);
                      // 仍然移动文件，但保持原名
                      const safeTargetPath = path.join(path.dirname(fullPath), originalFileName);
                      try {
                        fs.renameSync(defaultDownloadPath, safeTargetPath);
                        downloadCompleted = true;
                        downloadPath = safeTargetPath;
                        databaseService.updateDownloadStatus(postData.postUrl, 'completed', safeTargetPath);
                        log.info(`[Collector] ✅ 文件已移动但保持原名: ${safeTargetPath}`);
                      } catch (safeError) {
                        log.error(`[Collector] ❌ 安全移动也失败: ${safeError.message}`);
                      }
                    } else {
                      try {
                        // 移动文件到目标位置并重命名（这会删除原文件）
                        fs.renameSync(defaultDownloadPath, fullPath);

                        const stats = fs.statSync(fullPath);
                        log.info(`[Collector] ✅ 文件移动并重命名成功: ${fullPath}, 大小: ${stats.size} bytes`);

                        // 确认原文件已被删除
                        if (!fs.existsSync(defaultDownloadPath)) {
                          log.info(`[Collector] ✅ 原文件已从Downloads文件夹删除: ${originalFileName}`);
                        } else {
                          log.warn(`[Collector] ⚠️ 原文件仍存在于Downloads文件夹，尝试手动删除...`);
                          try {
                            fs.unlinkSync(defaultDownloadPath);
                            log.info(`[Collector] ✅ 手动删除原文件成功: ${originalFileName}`);
                          } catch (deleteError) {
                            log.error(`[Collector] ❌ 无法删除原文件: ${deleteError.message}`);
                          }
                        }

                        downloadCompleted = true;
                        downloadPath = fullPath;

                        log.info(`[Collector] ✅ 附件下载完成: ${newFileName}`);
                        this.updateTaskStatus('download-completed', `附件下载完成: ${newFileName}`);

                        // 🔧 文件内容校验 - 检查文件名是否与内容匹配
                        const contentValidation = await this.validateFileContent(fullPath, postData);
                        if (!contentValidation.isValid) {
                          log.warn(`[Collector] ⚠️ 文件内容校验失败: ${contentValidation.reason}`);
                          log.warn(`[Collector] ⚠️ 预期NFO ID: ${postData.nfoId}, 实际检测到: ${contentValidation.detectedId || '无'}`);

                          // 如果检测到不同的NFO ID，使用检测到的ID重新命名文件
                          if (contentValidation.detectedId && contentValidation.detectedId !== postData.nfoId) {
                            const correctedFileName = `${contentValidation.detectedId}.${fileExtension}`;
                            const correctedPath = path.join(attachmentsDir, correctedFileName);

                            try {
                              fs.renameSync(fullPath, correctedPath);
                              log.info(`[Collector] ✅ 文件已重新命名为正确的NFO ID: ${correctedFileName}`);
                              downloadPath = correctedPath;

                              // 更新数据库中的路径
                              databaseService.updateDownloadStatus(postData.postUrl, 'completed', correctedPath);
                            } catch (renameError) {
                              log.error(`[Collector] ❌ 重新命名文件失败: ${renameError.message}`);
                              // 继续使用原文件名
                              databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                            }
                          } else {
                            // 更新数据库状态为已完成
                            databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                          }
                        } else {
                          log.info(`[Collector] ✅ 文件内容校验通过`);
                          // 更新数据库状态为已完成
                          databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                        }

                      } catch (moveError) {
                        log.error(`[Collector] ❌ 文件移动失败: ${moveError.message}`);
                        // 如果移动失败，至少记录原文件位置
                        downloadCompleted = true;
                        downloadPath = defaultDownloadPath;
                        databaseService.updateDownloadStatus(postData.postUrl, 'completed', defaultDownloadPath);
                      }
                    }
                  } else {
                    log.error(`[Collector] ❌ 文件在任何位置都不存在`);
                    throw new Error('文件下载失败，在指定路径和默认下载文件夹都找不到文件');
                  }
                }

              } catch (error) {
                log.error(`[Collector] ❌ 下载处理失败: ${error.message}`);
                databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, `下载处理失败: ${error.message}`);
                throw error;
              }
            };

            // 注册下载事件监听器
            page.on('download', mainPageDownloadHandler);
            popupPage.on('download', popupPageDownloadHandler);

            // 5. 等待下载事件发生，给一个合理的超时时间
            try {
              // 等待下载开始，最多等待20秒
              const downloadStartTime = Date.now();
              let checkCount = 0;

              while (!downloadCompleted && (Date.now() - downloadStartTime) < 20000) {
                await page.waitForTimeout(500);
                checkCount++;

                // 每2秒检查一次下载文件夹
                if (checkCount % 4 === 0) {
                  try {
                    const files = fs.readdirSync(attachmentsDir);
                    const currentFileCount = files.length;
                    log.info(`[Collector] 📁 当前文件夹文件数量: ${currentFileCount}`);

                    // 检查是否有新文件（比之前的164个多）
                    if (currentFileCount > 164) {
                      log.info(`[Collector] 🎉 检测到新文件！文件数量从164增加到${currentFileCount}`);

                      // 查找最新的文件
                      const fileStats = files.map(file => {
                        const filePath = path.join(attachmentsDir, file);
                        const stats = fs.statSync(filePath);
                        return { name: file, mtime: stats.mtime, path: filePath };
                      }).sort((a, b) => b.mtime - a.mtime);

                      const newestFile = fileStats[0];
                      log.info(`[Collector] 📄 最新文件: ${newestFile.name}`);

                      // 检查文件是否包含预期的内容
                      if (newestFile.name.toLowerCase().includes('nghj') ||
                          newestFile.name.toLowerCase().includes('020')) {
                        log.info(`[Collector] ✅ 确认下载成功: ${newestFile.name}`);

                        // 重命名文件以保持一致性
                        const originalFileName = newestFile.name;
                        const fileExtension = originalFileName.split('.').pop() || 'rar';

                        log.info(`[Collector] 🔍 备用重命名处理:`);
                        log.info(`[Collector] - 原始文件名: ${originalFileName}`);

                        // 🔧 使用新的标准化文件名生成方法，带校验
                        const fileNameResult = this.generateStandardFileName(postData, fileExtension);

                        if (!fileNameResult.success) {
                          log.error(`[Collector] ❌ 备用重命名失败: ${fileNameResult.error}`);
                          downloadPath = newestFile.path; // 使用原路径
                        } else {
                          const newFileName = fileNameResult.fileName;
                          const newPath = path.join(attachmentsDir, newFileName);

                          log.info(`[Collector] ✅ 备用重命名校验通过:`);
                          log.info(`[Collector] - 新文件名: ${newFileName}`);

                          // 如果新文件名与当前文件名不同，进行重命名
                          if (originalFileName !== newFileName) {
                            try {
                              fs.renameSync(newestFile.path, newPath);
                              log.info(`[Collector] 📁 文件重命名成功: ${originalFileName} -> ${newFileName}`);
                              downloadPath = newPath;
                            } catch (renameError) {
                              log.warn(`[Collector] ⚠️ 文件重命名失败: ${renameError.message}`);
                              downloadPath = newestFile.path; // 使用原路径
                            }
                          } else {
                            downloadPath = newestFile.path;
                            finalDownloadPath = newestFile.path;
                          }
                        }

                        downloadCompleted = true;

                        // 更新数据库状态
                        databaseService.updateDownloadStatus(postData.postUrl, 'completed', downloadPath);
                        break;
                      }
                    }
                  } catch (dirError) {
                    log.warn(`[Collector] ⚠️ 检查目录失败: ${dirError.message}`);
                  }
                }
              }

              if (downloadCompleted) {
                log.info(`[Collector] 📥 下载事件已触发并完成`);
              } else {
                log.warn('[Collector] ⏰ 等待下载事件超时，检查默认下载文件夹...');

                // 检查多个可能的下载文件夹
                const possibleDownloadPaths = [
                  // 首先检查目标文件夹（可能已经下载到正确位置）
                  attachmentsDir,
                  // 然后检查常见的下载位置
                  path.join(require('os').homedir(), 'Downloads'),
                  path.join(require('os').homedir(), 'Desktop'),
                  'C:\\Users\\<USER>\\Downloads',
                  'D:\\Downloads',
                  'C:\\Downloads',
                  // 检查浏览器特定的下载文件夹
                  path.join(require('os').homedir(), 'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'Downloads'),
                  path.join(require('os').homedir(), 'AppData', 'Local', 'Microsoft', 'Edge', 'User Data', 'Default', 'Downloads'),
                  // 临时目录
                  path.join(require('os').tmpdir()),
                ];

                log.info(`[Collector] 📁 开始检查多个可能的下载位置...`);

                for (const downloadDir of possibleDownloadPaths) {
                  try {
                    if (fs.existsSync(downloadDir)) {
                      log.info(`[Collector] 📁 检查下载文件夹: ${downloadDir}`);

                      const files = fs.readdirSync(downloadDir);
                      const recentFiles = files.filter(file => {
                        try {
                          const filePath = path.join(downloadDir, file);
                          const stats = fs.statSync(filePath);
                          const fileAge = Date.now() - stats.mtime.getTime();
                          return fileAge < 120000; // 2分钟内的文件
                        } catch (e) {
                          return false;
                        }
                      });

                      log.info(`[Collector] 📄 ${downloadDir} 中的最近文件: ${recentFiles.length}个`);

                      // 列出最近的文件名
                      if (recentFiles.length > 0) {
                        log.info(`[Collector] 📄 最近文件列表: ${recentFiles.slice(0, 5).join(', ')}`);
                      }

                      for (const file of recentFiles) {
                        // 智能文件匹配条件
                        const fileName = file.toLowerCase();
                        const postTitleLower = postData.postTitle ? postData.postTitle.toLowerCase() : '';
                        const nfoIdLower = postData.nfoId ? postData.nfoId.toLowerCase() : '';

                        // 检查文件是否匹配当前帖子
                        const isTargetFile = (
                          // 匹配NFO ID
                          (nfoIdLower && fileName.includes(nfoIdLower)) ||
                          // 匹配帖子标题中的关键词
                          (postTitleLower && (
                            fileName.includes('nghj') ||
                            fileName.includes('nsfs') ||
                            fileName.includes('ipzz') ||
                            fileName.includes('jur') ||
                            fileName.includes('dass')
                          )) ||
                          // 匹配常见压缩文件格式
                          (fileName.endsWith('.rar') ||
                           fileName.endsWith('.zip') ||
                           fileName.endsWith('.7z') ||
                           fileName.endsWith('.tar.gz'))
                        );

                        if (isTargetFile) {
                          // 构建重命名后的文件名
                          const originalFileName = file;
                          const fileExtension = originalFileName.split('.').pop() || 'rar';

                          log.info(`[Collector] 📁 发现可能的目标文件: ${file}`);

                          // 🔧 使用新的标准化文件名生成方法，带校验
                          const fileNameResult = this.generateStandardFileName(postData, fileExtension);

                          if (!fileNameResult.success) {
                            log.error(`[Collector] ❌ Downloads文件夹重命名失败: ${fileNameResult.error}`);
                            continue; // 跳过这个文件，继续检查其他文件
                          }

                          const newFileName = fileNameResult.fileName;
                          const sourcePath = path.join(downloadDir, file);
                          const targetPath = path.join(attachmentsDir, newFileName);

                          log.info(`[Collector] 📁 正在移动并重命名文件:`);
                          log.info(`[Collector] 📁 从: ${sourcePath}`);
                          log.info(`[Collector] 📁 到: ${targetPath}`);
                          log.info(`[Collector] 📁 重命名: ${originalFileName} -> ${newFileName}`);

                          try {
                            fs.renameSync(sourcePath, targetPath);

                            downloadCompleted = true;
                            downloadPath = targetPath; // 这里赋值给外层变量
                            finalDownloadPath = targetPath;

                            // 更新数据库状态
                            databaseService.updateDownloadStatus(postData.postUrl, 'completed', targetPath);
                            log.info(`[Collector] ✅ 文件移动并重命名成功: ${targetPath}`);
                            break;
                          } catch (moveError) {
                            log.warn(`[Collector] ⚠️ 文件移动失败: ${moveError.message}`);
                            // 尝试复制而不是移动
                            try {
                              fs.copyFileSync(sourcePath, targetPath);
                              log.info(`[Collector] ✅ 文件复制并重命名成功: ${targetPath}`);
                              downloadCompleted = true;
                              downloadPath = targetPath; // 这里赋值给外层变量
                              finalDownloadPath = targetPath;
                              databaseService.updateDownloadStatus(postData.postUrl, 'completed', targetPath);
                              break;
                            } catch (copyError) {
                              log.warn(`[Collector] ⚠️ 文件复制也失败: ${copyError.message}`);
                            }
                          }
                        }
                      }

                      if (downloadCompleted) break;
                    } else {
                      log.info(`[Collector] 📁 路径不存在: ${downloadDir}`);
                    }
                  } catch (dirError) {
                    log.warn(`[Collector] ⚠️ 检查目录失败 ${downloadDir}: ${dirError.message}`);
                  }
                }

                if (!downloadCompleted) {
                  log.warn(`[Collector] ⚠️ 在所有可能的下载位置都没有找到文件`);
                }
              }
            } catch (timeoutError) {
              log.warn(`[Collector] ⏰ 下载等待异常: ${timeoutError.message}`);
            }

            // 6. 清理事件监听器
            try {
              page.off('download', mainPageDownloadHandler);
              popupPage.off('download', popupPageDownloadHandler);
              log.info(`[Collector] 🧹 已清理下载事件监听器`);
            } catch (cleanupError) {
              log.warn(`[Collector] ⚠️ 清理事件监听器失败: ${cleanupError.message}`);
            }

            // 7. 延迟关闭弹窗（Chrome静音模式下弹窗已不可见，可以稍后关闭）
            try {
              // 给下载更多时间，延迟3秒后关闭弹窗
              await page.waitForTimeout(3000);
              await popupPage.close();
              log.info(`[Collector] 🚪 弹窗已关闭（Chrome静音模式下用户无感知）`);
            } catch (closeError) {
              log.warn(`[Collector] ⚠️ 关闭弹窗失败: ${closeError.message}`);
            }

            // 8. 最终检查下载结果
            if (downloadCompleted && downloadPath) {
              log.info(`[Collector] 🎉 附件 ${attachmentIndex + 1} 下载成功: ${downloadPath}`);
            } else {
              log.warn(`[Collector] ⚠️ 附件 ${attachmentIndex + 1} 下载可能未完成，但弹窗已处理`);
              databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, `附件 ${attachmentIndex + 1} 下载未完成`);
            }

            // 🔧 多附件下载间隔
            if (attachmentIndex < attachmentElements.length - 1) {
              log.info(`[Collector] ⏳ 等待 2 秒后下载下一个附件...`);
              await page.waitForTimeout(2000);
            }
          } // 结束多附件循环

          log.info(`[Collector] ✅ 所有附件处理完成，共处理 ${attachmentElements.length} 个附件`);

      } catch (error) {
          log.error(`[Collector] ❌ popup事件模型下载失败: ${error.message}`);
          databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, `popup下载失败: ${error.message}`);
        }

        // ** 风控检查：下载完成后检查页面状态 **
        await page.waitForTimeout(1000); // 等待页面稳定
        const downloadStatus = await this.checkPageStatus(page, siteProfile);
        if (downloadStatus === 'download_limit_exceeded') {
          log.warn('[Collector] 检测到下载超限，停止后续下载');
          databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, '下载次数已达上限');
          throw new Error('下载次数已达上限'); // 抛出错误，让上层处理
        } else if (downloadStatus === 'human_verification_required') {
          log.warn('[Collector] 检测到人机验证，停止后续下载');
          databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, '需要人机验证');
          throw new Error('需要人机验证'); // 抛出错误，让上层处理
        }

      } catch (error) {
        log.error(`[Collector] 下载附件失败: ${error.message}`);
        databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, `下载附件失败: ${error.message}`);

        // 如果是下载超限或人机验证错误，需要重新抛出以停止整个任务
        if (error.message.includes('下载次数已达上限') || error.message.includes('需要人机验证')) {
          throw error;
        }
      }

      // 下载间隔
      if (i < attachmentUrls.length - 1) {
        await this.delay(2000); // 2秒间隔
      }
    }

    // 返回下载结果
    return {
      success: true,
      downloadPath: finalDownloadPath || null // 确保有默认值
    };
  }

  /**
   * 自动导出搜集结果到日志文件
   * @param {Array} results - 搜集到的结果数组
   * @param {Object} siteProfile - 站点配置
   * @param {Object} taskInfo - 任务信息
   * @returns {Promise<string>} 导出的文件路径
   */
  async _autoExportResults(results, siteProfile, taskInfo = {}) {
    if (!this.workspacePath || results.length === 0) {
      log.info('[Collector] 跳过自动导出：无工作区路径或无搜集结果');
      return null;
    }

    try {
      const path = require('path');
      const fs = require('fs');

      // 确保logs目录存在
      const logsDir = path.join(this.workspacePath, 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      // 生成文件名：论坛名_日期_时间.txt
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
      const forumName = this.sanitizeFileName(siteProfile.name || '论坛');
      const fileName = `${forumName}_${dateStr}_${timeStr}.txt`;
      const filePath = path.join(logsDir, fileName);

      // 构建导出内容
      let content = '';
      content += `# SoulForge 搜集日志\n`;
      content += `论坛名称: ${siteProfile.name || '未知论坛'}\n`;
      content += `搜集时间: ${now.toLocaleString('zh-CN')}\n`;
      content += `搜集页数: ${taskInfo.pages || 1}\n`;
      content += `搜集结果: ${results.length} 条记录\n`;
      content += `工作区路径: ${this.workspacePath}\n`;
      if (taskInfo.forceStopped) {
        content += `任务状态: 强制停止\n`;
      }
      content += `\n${'='.repeat(80)}\n\n`;

      // 按论坛分组显示结果
      const groupedResults = {};
      results.forEach(item => {
        const forum = siteProfile.name || '未知论坛';
        if (!groupedResults[forum]) {
          groupedResults[forum] = [];
        }
        groupedResults[forum].push(item);
      });

      Object.keys(groupedResults).forEach(forumName => {
        const items = groupedResults[forumName];
        content += `## ${forumName} (${items.length} 条记录)\n\n`;

        items.forEach((item, index) => {
          content += `### ${index + 1}. ${item.postTitle || '无标题'}\n`;
          content += `链接: ${item.postUrl || '无链接'}\n`;

          if (item.nfoId) {
            content += `NFO ID: ${item.nfoId}\n`;
          }

          if (item.magnetLink) {
            content += `磁力链接:\n${item.magnetLink}\n`;
          }

          if (item.ed2kLink) {
            content += `ED2K链接:\n${item.ed2kLink}\n`;
          }

          if (item.attachmentUrl) {
            content += `附件链接:\n${item.attachmentUrl}\n`;
          }

          if (item.decompressionPassword) {
            content += `解压密码: ${item.decompressionPassword}\n`;
          }

          content += `搜集时间: ${item.collectionDate || '未知'}\n`;
          content += `\n${'-'.repeat(60)}\n\n`;
        });
      });

      // 添加统计信息
      content += `\n${'='.repeat(80)}\n`;
      content += `## 搜集统计\n\n`;
      content += `总计帖子: ${results.length} 个\n`;

      const withMagnet = results.filter(r => r.magnetLink).length;
      const withEd2k = results.filter(r => r.ed2kLink).length;
      const withAttachment = results.filter(r => r.attachmentUrl).length;
      const withPassword = results.filter(r => r.decompressionPassword).length;

      content += `包含磁力链接: ${withMagnet} 个\n`;
      content += `包含ED2K链接: ${withEd2k} 个\n`;
      content += `包含附件: ${withAttachment} 个\n`;
      content += `包含解压密码: ${withPassword} 个\n`;

      content += `\n导出时间: ${now.toLocaleString('zh-CN')}\n`;
      content += `导出工具: SoulForge v1.0\n`;

      // 写入文件
      fs.writeFileSync(filePath, content, 'utf8');

      log.info(`[Collector] 自动导出完成: ${filePath}`);
      this.updateTaskStatus('export-completed', `搜集日志已自动导出: ${fileName}`);

      return filePath;

    } catch (error) {
      log.error(`[Collector] 自动导出失败: ${error.message}`);
      this.updateTaskStatus('export-failed', `自动导出失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 清理文件名中的非法字符
   * @param {string} filename - 原始文件名
   * @returns {string} 清理后的文件名
   */
  sanitizeFileName(filename) {
    if (!filename) return 'untitled';

    // 移除或替换非法字符
    let sanitized = filename
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows非法字符
      .replace(/[\x00-\x1f\x80-\x9f]/g, '')  // 移除控制字符
      .replace(/^\.+/, '')  // 移除开头的点
      .replace(/\.+$/, '')  // 移除结尾的点
      .replace(/\s+/g, ' ')  // 合并多个空格
      .trim();

    // 限制长度（Windows路径限制）
    if (sanitized.length > 200) {
      sanitized = sanitized.substring(0, 200);
    }

    // 确保不为空
    if (!sanitized) {
      sanitized = 'untitled';
    }

    return sanitized;
  }

  /**
   * 生成标准化的文件名 - 带校验
   * @param {Object} postData - 帖子数据
   * @param {string} fileExtension - 文件扩展名
   * @returns {Object} 包含文件名和校验信息的对象
   */
  generateStandardFileName(postData, fileExtension) {
    // 数据校验
    if (!postData) {
      log.error('[Collector] ❌ 文件重命名失败：postData为空');
      return {
        success: false,
        error: 'postData为空',
        fileName: `error_${Date.now()}.${fileExtension}`
      };
    }

    if (!postData.postTitle) {
      log.error('[Collector] ❌ 文件重命名失败：帖子标题为空');
      log.error('[Collector] postData内容:', JSON.stringify(postData, null, 2));
      return {
        success: false,
        error: '帖子标题为空',
        fileName: `no_title_${Date.now()}.${fileExtension}`
      };
    }

    try {
      // 🔧 使用新的标准化文件名构建器
      log.info('[Collector] 🔍 使用标准化文件名构建器生成文件名');
      const standardFileName = fileNameBuilder.buildStandardFileName(postData);

      // 添加扩展名
      const fileName = `${standardFileName}.${fileExtension}`;

      // 最终校验
      let finalFileName = fileName;
      if (fileName.length > 255) {
        log.warn('[Collector] ⚠️ 文件名过长，进行截断处理');
        const maxBaseLength = 255 - fileExtension.length - 1; // 减去扩展名和点的长度
        const baseName = standardFileName.substring(0, maxBaseLength);
        finalFileName = `${baseName}.${fileExtension}`;
      }

      log.info('[Collector] ✅ 生成的最终文件名:', finalFileName);

      return {
        success: true,
        fileName: finalFileName,
        originalTitle: postData.postTitle,
        cleanTitle: postData.postTitle, // 保持兼容性
        nfoId: postData.nfoId || null,
        password: postData.decompressionPassword || null
      };

    } catch (error) {
      log.error('[Collector] ❌ 标准化文件名生成失败:', error.message);
      log.error('[Collector] ❌ 错误堆栈:', error.stack);
      log.error('[Collector] ❌ postData内容:', JSON.stringify(postData, null, 2));

      // 🚨 重要：回退逻辑使用原始标题，这会导致板块名称不被清理！
      log.warn('[Collector] ⚠️ 使用回退逻辑：直接使用原始标题，板块名称不会被清理！');
      const fallbackFileName = this.sanitizeFileName(postData.postTitle || `fallback_${Date.now()}`);
      const fileName = `${fallbackFileName}.${fileExtension}`;

      return {
        success: false, // 🔧 改为false，表示使用了回退逻辑
        fileName: fileName,
        originalTitle: postData.postTitle,
        cleanTitle: postData.postTitle,
        nfoId: postData.nfoId || null,
        password: postData.decompressionPassword || null,
        usedFallback: true // 🔧 添加标记
      };
    }
  }

  /**
   * 验证文件内容是否与预期的NFO ID匹配
   * @param {string} filePath - 文件路径
   * @param {Object} postData - 帖子数据
   * @returns {Promise<Object>} 验证结果
   */
  async validateFileContent(filePath, postData) {
    const fs = require('fs');
    const path = require('path');

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return {
          isValid: false,
          reason: '文件不存在',
          detectedId: null
        };
      }

      const fileName = path.basename(filePath, path.extname(filePath));
      const expectedNfoId = postData.nfoId;

      log.info(`[Collector] 🔍 文件内容校验:`);
      log.info(`[Collector] - 文件路径: ${filePath}`);
      log.info(`[Collector] - 文件名: ${fileName}`);
      log.info(`[Collector] - 预期NFO ID: ${expectedNfoId}`);

      // 如果没有预期的NFO ID，跳过校验
      if (!expectedNfoId) {
        log.info(`[Collector] - 没有预期NFO ID，跳过内容校验`);
        return {
          isValid: true,
          reason: '没有预期NFO ID',
          detectedId: null
        };
      }

      // 从文件名中检测NFO ID
      const detectedId = NodeNfoParser.extractJavIdFromFilename(fileName);
      log.info(`[Collector] - 从文件名检测到的NFO ID: ${detectedId}`);

      // 比较预期ID和检测到的ID
      if (detectedId && detectedId.toLowerCase() === expectedNfoId.toLowerCase()) {
        return {
          isValid: true,
          reason: 'NFO ID匹配',
          detectedId: detectedId
        };
      } else if (detectedId) {
        return {
          isValid: false,
          reason: 'NFO ID不匹配',
          detectedId: detectedId
        };
      } else {
        return {
          isValid: false,
          reason: '无法从文件名检测到NFO ID',
          detectedId: null
        };
      }

    } catch (error) {
      log.error(`[Collector] ❌ 文件内容校验出错: ${error.message}`);
      return {
        isValid: false,
        reason: `校验出错: ${error.message}`,
        detectedId: null
      };
    }
  }

  /**
   * 验证文件重命名操作的安全性
   * @param {string} originalPath - 原始文件路径
   * @param {string} newPath - 新文件路径
   * @param {Object} postData - 帖子数据
   * @returns {Object} 验证结果
   */
  validateFileRename(originalPath, newPath, postData) {
    const fs = require('fs');
    const path = require('path');

    // 检查原始文件是否存在
    if (!fs.existsSync(originalPath)) {
      return {
        success: false,
        error: '原始文件不存在',
        shouldProceed: false
      };
    }

    // 检查新路径是否合理
    const newFileName = path.basename(newPath);
    if (newFileName.length < 5) { // 至少要有几个字符
      return {
        success: false,
        error: '生成的文件名过短，可能有问题',
        shouldProceed: false
      };
    }

    // 🔧 优化：对于标准化文件名，放宽验证条件
    // 检查是否包含番号或标题的关键部分
    let hasRelevantContent = false;

    // 1. 检查是否包含番号
    if (postData.nfoId) {
      const nfoIdBase = postData.nfoId.replace(/-[A-Z]+$/i, ''); // 移除后缀如-C, -U
      if (newFileName.includes(nfoIdBase)) {
        hasRelevantContent = true;
        log.info(`[Collector] ✅ 文件名包含番号: ${nfoIdBase}`);
      }
    }

    // 2. 如果没有番号，检查标题关键词
    if (!hasRelevantContent) {
      const titleWords = postData.postTitle.split(/[\s\-_\[\]]+/).filter(word => word.length > 3);
      hasRelevantContent = titleWords.some(word =>
        newFileName.toLowerCase().includes(word.toLowerCase())
      );

      if (hasRelevantContent) {
        log.info(`[Collector] ✅ 文件名包含标题关键词`);
      }
    }

    // 3. 如果仍然没有匹配，记录警告但不阻止操作（因为标准化文件名可能与原标题差异较大）
    if (!hasRelevantContent) {
      log.warn(`[Collector] ⚠️ 警告：生成的文件名可能与帖子内容不匹配，但继续执行`);
      log.warn(`[Collector] - 帖子标题: ${postData.postTitle}`);
      log.warn(`[Collector] - 番号: ${postData.nfoId || '无'}`);
      log.warn(`[Collector] - 生成的文件名: ${newFileName}`);
      // 不阻止操作，因为标准化文件名可能与原标题格式差异很大
    }

    // 检查目标文件是否已存在
    if (fs.existsSync(newPath)) {
      return {
        success: false,
        error: '目标文件已存在',
        shouldProceed: false
      };
    }

    return {
      success: true,
      shouldProceed: true
    };
  }



  /**
   * 生成帖子档案文件 - 原子化归档
   * @param {Object} postData - 帖子数据
   * @returns {Promise<string>} 返回生成的文件路径
   */
  async _generatePostArchiveFile(postData) {
    const fs = require('fs');
    const path = require('path');

    try {
      if (!this.workspacePath) {
        log.warn('[Collector] 工作区路径未设置，跳过档案文件生成');
        return null;
      }

      // 1. 🔧 新的层级化目录结构：论坛/板块/
      const forumName = this.sanitizeFileName(this.siteProfile?.name || 'unknown');

      // 🔧 优化：优先使用postData中的板块信息，其次从URL提取
      let boardName = 'unknown';

      // 优先使用postData中的板块信息
      if (postData.boardInfo && postData.boardInfo.boardName) {
        boardName = this.sanitizeFileName(postData.boardInfo.boardName);
        log.info(`[Collector] 🏷️ 使用postData中的板块名称: ${boardName}`);
      } else {
        // 备用方案：从URL提取
        log.info(`[Collector] 🔍 板块名称提取调试:`);
        log.info(`[Collector] - 帖子URL: ${postData.postUrl}`);
        log.info(`[Collector] - 当前页面URL: ${this.currentPageUrl || '未设置'}`);
        log.info(`[Collector] - 站点配置: ${JSON.stringify(this.siteProfile?.boards || {}, null, 2)}`);

        let boardIdMatch = null;
        let sourceUrl = '';

        // 优先从当前页面URL中提取（列表页URL包含fid参数）
        if (this.currentPageUrl) {
          sourceUrl = this.currentPageUrl;
          boardIdMatch = sourceUrl.match(/fid=(\d+)/);
        }

        // 如果列表页URL没有，尝试从帖子URL中提取（备用方案）
        if (!boardIdMatch && postData.postUrl) {
          boardIdMatch = postData.postUrl.match(/fid=(\d+)/);
          sourceUrl = postData.postUrl;
          log.info(`[Collector] - 从帖子URL提取: ${sourceUrl}`);
        }

        log.info(`[Collector] - fid正则匹配结果: ${JSON.stringify(boardIdMatch)}`);
        log.info(`[Collector] - 匹配来源URL: ${sourceUrl}`);

        if (boardIdMatch) {
          const boardId = boardIdMatch[1];
          log.info(`[Collector] - 提取的板块ID: ${boardId}`);

          const boardConfig = this.siteProfile?.boards && this.siteProfile.boards[boardId];
          log.info(`[Collector] - 板块配置: ${JSON.stringify(boardConfig)}`);

          if (boardConfig && boardConfig.name) {
            boardName = this.sanitizeFileName(boardConfig.name);
            log.info(`[Collector] - 使用配置中的板块名称: ${boardName}`);
          } else {
            boardName = `FID-${boardId}`;
            log.info(`[Collector] - 使用默认板块名称: ${boardName}`);
          }
        } else {
          log.warn(`[Collector] - 无法从URL中提取板块ID，使用默认名称: unknown`);
          log.warn(`[Collector] - 检查的URL: 列表页=${this.currentPageUrl}, 帖子=${postData.postUrl}`);
        }
      }

      // 创建论坛目录
      const forumDir = path.join(this.workspacePath, forumName);
      if (!fs.existsSync(forumDir)) {
        fs.mkdirSync(forumDir, { recursive: true });
        log.info(`[Collector] 📁 创建论坛目录: ${forumDir}`);
      }

      // 创建板块目录
      const boardDir = path.join(forumDir, boardName);
      if (!fs.existsSync(boardDir)) {
        fs.mkdirSync(boardDir, { recursive: true });
        log.info(`[Collector] 📁 创建板块目录: ${boardDir}`);
      }

      // 创建论坛的attachments目录（统一存放附件）
      const attachmentsDir = path.join(forumDir, 'attachments');
      if (!fs.existsSync(attachmentsDir)) {
        fs.mkdirSync(attachmentsDir, { recursive: true });
        log.info(`[Collector] 📁 创建附件目录: ${attachmentsDir}`);
      }

      // 🔧 使用新的标准化文件名构建器
      const standardFileName = fileNameBuilder.buildStandardFileName(postData);
      const fileName = `${standardFileName}.md`;
      const filePath = path.join(boardDir, fileName);

      log.info(`[Collector] 📁 层级化归档结构:`);
      log.info(`[Collector] - 论坛: ${forumName}`);
      log.info(`[Collector] - 板块: ${boardName}`);
      log.info(`[Collector] - 文件: ${fileName}`);

      // 2. 🔧 构建增强的Markdown格式档案内容，包含完整元数据
      let content = `---\n`;
      content += `title: "${postData.postTitle}"\n`;
      content += `forum: "${forumName}"\n`;
      content += `board: "${boardName}"\n`;
      content += `url: "${postData.postUrl}"\n`;

      if (postData.nfoId) {
        content += `nfo_id: "${postData.nfoId}"\n`;
      }

      if (postData.postDate) {
        content += `date: "${postData.postDate}"\n`;
      }

      if (postData.decompressionPassword) {
        content += `password: "${postData.decompressionPassword}"\n`;
      }

      // 🔧 新增：板块信息
      if (postData.boardInfo) {
        content += `board_id: "${postData.boardInfo.boardId}"\n`;
        content += `board_tags: [${postData.boardInfo.boardTags.map(tag => `"${tag}"`).join(', ')}]\n`;
      }

      // 🔧 新增：提取的元数据
      if (postData.fileSize) {
        content += `file_size: "${postData.fileSize}"\n`;
      }

      if (postData.performers) {
        content += `performers: "${postData.performers}"\n`;
      }

      if (postData.studio) {
        content += `studio: "${postData.studio}"\n`;
      }

      if (postData.duration) {
        content += `duration: "${postData.duration}"\n`;
      }

      // 下载状态和链接信息
      content += `download_status: "${postData.downloadStatus}"\n`;

      if (postData.magnetLink) {
        content += `has_magnet: true\n`;
      }

      if (postData.ed2kLink) {
        content += `has_ed2k: true\n`;
      }

      if (postData.attachmentUrl) {
        content += `has_attachment: true\n`;
      }

      if (postData.previewImageUrl) {
        content += `preview_image: "${postData.previewImageUrl}"\n`;
      }

      content += `created_at: "${new Date().toISOString()}"\n`;
      content += `---\n\n`;

      // 标题
      content += `# ${postData.postTitle}\n\n`;

      // 基本信息
      content += `## 📋 基本信息\n\n`;
      content += `- **论坛**: ${forumName}\n`;
      content += `- **板块**: ${boardName}`;

      if (postData.boardInfo && postData.boardInfo.boardTags) {
        content += ` (${postData.boardInfo.boardTags.join(', ')})`;
      }
      content += `\n`;

      content += `- **来源URL**: [${postData.postUrl}](${postData.postUrl})\n`;

      if (postData.nfoId) {
        content += `- **番号**: ${postData.nfoId}\n`;
      }

      if (postData.postDate) {
        content += `- **发布日期**: ${postData.postDate}\n`;
      }

      if (postData.decompressionPassword) {
        content += `- **解压密码**: \`${postData.decompressionPassword}\`\n`;
      }

      // 🔧 新增：详细元数据
      if (postData.fileSize) {
        content += `- **文件大小**: ${postData.fileSize}\n`;
      }

      if (postData.performers) {
        content += `- **出演者**: ${postData.performers}\n`;
      }

      if (postData.studio) {
        content += `- **厂商**: ${postData.studio}\n`;
      }

      if (postData.duration) {
        content += `- **时长**: ${postData.duration}\n`;
      }

      content += `\n`;

      // 媒体内容
      if (postData.previewImageUrl || postData.magnetLink || postData.ed2kLink) {
        content += `## 🎬 媒体内容\n\n`;

        if (postData.previewImageUrl) {
          content += `### 预览图\n\n`;
          content += `![预览图](${postData.previewImageUrl})\n\n`;
        }

        if (postData.magnetLink) {
          content += `### 磁力链接\n\n`;
          content += `\`\`\`\n${postData.magnetLink}\n\`\`\`\n\n`;
        }

        if (postData.ed2kLink) {
          content += `### eD2k链接\n\n`;
          content += `\`\`\`\n${postData.ed2kLink}\n\`\`\`\n\n`;
        }
      }

      // 3. 🔧 附件状态（Markdown格式）
      content += `## 📎 附件状态\n\n`;

      switch (postData.downloadStatus) {
        case 'completed':
          content += `- **状态**: ✅ 下载成功\n`;
          if (postData.downloadPath) {
            const relativePath = path.relative(boardDir, postData.downloadPath);
            content += `- **文件路径**: \`${relativePath}\`\n`;

            // 🔧 修正：如果是在全局attachments目录中，提供相对路径
            if (postData.downloadPath.includes(path.join(this.workspacePath, 'attachments'))) {
              const attachmentName = path.basename(postData.downloadPath);
              content += `- **附件文件**: [${attachmentName}](../attachments/${attachmentName})\n`;
            }
          }
          break;
        case 'failed':
          content += `- **状态**: ❌ 下载失败\n`;
          content += `- **错误原因**: ${postData.errorMessage || '未知错误'}\n`;
          break;
        case 'no_attachment':
          content += `- **状态**: ℹ️ 未发现附件\n`;
          break;
        case 'skipped':
          content += `- **状态**: ⏭️ 已跳过（文件已存在）\n`;
          break;
        default:
          content += `- **状态**: 🕒 未处理或未知\n`;
      }

      content += `\n---\n\n`;
      content += `*档案生成时间: ${new Date().toLocaleString('zh-CN')}*\n`;

      // 4. 写入文件
      fs.writeFileSync(filePath, content, 'utf-8');
      log.info(`[Collector] ✅ 已生成档案文件: ${fileName}`);

      return filePath;

    } catch (error) {
      log.error(`[Collector] ❌ 生成档案文件失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new CollectorService();
