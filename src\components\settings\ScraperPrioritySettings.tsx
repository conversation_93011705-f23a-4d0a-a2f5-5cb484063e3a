// src/components/settings/ScraperPrioritySettings.tsx
import React, { useState, useEffect } from 'react';
import { ScraperPriorityRules } from '../../types';

interface ScraperPrioritySettingsProps {
  initialRules: ScraperPriorityRules;
  onRulesChange: (rules: ScraperPriorityRules) => void;
}

// 可配置字段列表 - 与 DisplayData 接口保持一致
const CONFIGURABLE_FIELDS = [
  { key: 'title', label: '影片标题', description: '影片的主要标题' },
  { key: 'plot', label: '剧情简介', description: '影片的详细剧情描述' },
  { key: 'cover', label: '封面图片', description: '影片的封面图片URL' },
  { key: 'releaseDate', label: '发行日期', description: '影片的发行日期' },
  { key: 'runtime', label: '影片时长', description: '影片的播放时长（分钟）' },
  { key: 'display_id', label: '展示番号', description: '用于展示的番号' },
  { key: 'year', label: '发行年份', description: '影片的发行年份' },
  { key: 'studio', label: '制作商', description: '影片的制作公司' },
  { key: 'publisher', label: '发行商', description: '影片的发行公司' },
  { key: 'series', label: '系列名称', description: '影片所属的系列' },
  { key: 'director', label: '导演', description: '影片的导演信息' },
  { key: 'rating', label: '评分', description: '影片的用户评分' },
  { key: 'actresses', label: '女优', description: '影片的女演员列表' },
  { key: 'actors_male', label: '男优', description: '影片的男演员列表' },
  { key: 'tags', label: '标签', description: '影片的分类标签（合并去重）' },
  { key: 'preview_image_paths', label: '预览图', description: '影片的预览图片' },
  { key: 'user_reviews', label: '用户评价', description: '用户的评价和评论' },
  { key: 'similar_movies', label: '相似影片', description: '推荐的相似影片' },
  { key: 'has_4k', label: '4K标识', description: '是否有4K版本' },
  { key: 'has_bluray', label: '蓝光标识', description: '是否有蓝光版本' },
  { key: 'has_subtitles', label: '字幕标识', description: '是否有字幕' },
  { key: 'is_uncensored_cracked', label: '破解标识', description: '是否为破解版' },
  { key: 'is_leaked', label: '流出标识', description: '是否为流出版' },
  { key: 'type', label: '影片类型', description: '影片的分类类型' }
];

// 可用的刮削源
const AVAILABLE_PROVIDERS = [
  { key: 'dmm', label: 'DMM', description: '日本官方数字媒体市场' },
  { key: 'javbus', label: 'JavBus', description: '综合影片信息站点' },
  { key: 'javdb', label: 'JavDB', description: '影片数据库站点' },
  { key: 'javlibrary', label: 'JavLibrary', description: '影片图书馆' },
  { key: 'freejavbt', label: 'FreeJavBT', description: '免费资源站点' },
  { key: 'jav321', label: 'Jav321', description: '影片信息站点' },
  { key: '7mmtv', label: '7mmTV', description: '影片信息站点' },
  { key: 'avsex', label: 'AvSex', description: '影片信息站点' },
  { key: 'airav', label: 'AirAV', description: '影片信息站点' },
  { key: 'avsox', label: 'AvSox', description: '影片信息站点' }
];

export const ScraperPrioritySettings: React.FC<ScraperPrioritySettingsProps> = ({
  initialRules,
  onRulesChange
}) => {
  const [rules, setRules] = useState<ScraperPriorityRules>(initialRules);
  const [draggedItem, setDraggedItem] = useState<{ field: string; index: number } | null>(null);

  // 当初始规则变化时更新本地状态
  useEffect(() => {
    setRules(initialRules);
  }, [initialRules]);

  // 当规则变化时通知父组件
  useEffect(() => {
    onRulesChange(rules);
  }, [rules, onRulesChange]);

  // 处理拖拽开始
  const handleDragStart = (e: React.DragEvent, field: string, index: number) => {
    setDraggedItem({ field, index });
    e.dataTransfer.effectAllowed = 'move';
  };

  // 处理拖拽结束
  const handleDragEnd = () => {
    setDraggedItem(null);
  };

  // 处理拖拽悬停
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // 处理放置
  const handleDrop = (e: React.DragEvent, field: string, targetIndex: number) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem.field !== field) return;

    const newRules = { ...rules };
    const fieldRules = [...(newRules[field] || [])];
    
    // 移动项目
    const [movedItem] = fieldRules.splice(draggedItem.index, 1);
    fieldRules.splice(targetIndex, 0, movedItem);
    
    newRules[field] = fieldRules;
    setRules(newRules);
  };

  // 添加刮削源到字段
  const addProviderToField = (field: string, provider: string) => {
    const newRules = { ...rules };
    const fieldRules = newRules[field] || [];
    
    if (!fieldRules.includes(provider)) {
      newRules[field] = [...fieldRules, provider];
      setRules(newRules);
    }
  };

  // 从字段移除刮削源
  const removeProviderFromField = (field: string, provider: string) => {
    const newRules = { ...rules };
    const fieldRules = newRules[field] || [];
    
    newRules[field] = fieldRules.filter(p => p !== provider);
    setRules(newRules);
  };

  // 重置字段为默认值
  const resetFieldToDefault = (field: string) => {
    const newRules = { ...rules };
    // 使用默认的优先级顺序
    newRules[field] = ['dmm', 'javbus', 'javdb'];
    setRules(newRules);
  };

  return (
    <div className="space-y-6">
      <div className="bg-[#2a2a2a] border border-[#3a3a3a] rounded-lg p-4">
        <h3 className="text-lg font-semibold text-[#B8860B] mb-2">刮削源优先级配置</h3>
        <p className="text-sm text-gray-300">
          配置数据精炼厂的择优规则。拖拽调整优先级顺序，排在前面的刮削源优先级更高。
          特殊字段如"标签"会合并所有来源的数据并去重。
        </p>
      </div>

      <div className="grid gap-6">
        {CONFIGURABLE_FIELDS.map(field => {
          const fieldRules = rules[field.key] || [];
          const isCalculatedField = fieldRules.includes?.('calculated');
          
          return (
            <div key={field.key} className="bg-[#2a2a2a] border border-[#3a3a3a] rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-medium text-white">{field.label}</h4>
                  <p className="text-sm text-gray-400">{field.description}</p>
                </div>
                <button
                  onClick={() => resetFieldToDefault(field.key)}
                  className="text-sm text-[#B8860B] hover:text-[#D4AF37] transition-colors"
                >
                  重置为默认
                </button>
              </div>

              {isCalculatedField ? (
                <div className="bg-[#3a3a2a] border border-[#4a4a3a] rounded p-3">
                  <p className="text-sm text-yellow-400">
                    此字段为计算字段，由系统自动计算，无需配置刮削源优先级。
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* 当前优先级列表 */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-300">优先级顺序（拖拽调整）：</h5>
                    <div className="space-y-1">
                      {fieldRules.map((provider, index) => {
                        const providerInfo = AVAILABLE_PROVIDERS.find(p => p.key === provider);
                        return (
                          <div
                            key={`${provider}-${index}`}
                            draggable
                            onDragStart={(e) => handleDragStart(e, field.key, index)}
                            onDragEnd={handleDragEnd}
                            onDragOver={handleDragOver}
                            onDrop={(e) => handleDrop(e, field.key, index)}
                            className={`flex items-center justify-between p-2 bg-[#1a1a1a] border border-[#3a3a3a] rounded cursor-move hover:bg-[#2a2a2a] transition-colors ${
                              draggedItem?.field === field.key && draggedItem?.index === index ? 'opacity-50' : ''
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <span className="text-sm font-medium text-gray-400">#{index + 1}</span>
                              <span className="text-sm font-medium text-white">{providerInfo?.label || provider}</span>
                              <span className="text-xs text-gray-500">{providerInfo?.description}</span>
                            </div>
                            <button
                              onClick={() => removeProviderFromField(field.key, provider)}
                              className="text-red-400 hover:text-red-300 transition-colors"
                              title="移除此刮削源"
                            >
                              ✕
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 添加刮削源 */}
                  <div>
                    <h5 className="text-sm font-medium text-gray-300 mb-2">添加刮削源：</h5>
                    <div className="flex flex-wrap gap-2">
                      {AVAILABLE_PROVIDERS.filter(provider => !fieldRules.includes(provider.key)).map(provider => (
                        <button
                          key={provider.key}
                          onClick={() => addProviderToField(field.key, provider.key)}
                          className="px-3 py-1 text-sm bg-[#3a3a3a] text-[#B8860B] rounded hover:bg-[#4a4a4a] transition-colors"
                          title={provider.description}
                        >
                          + {provider.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
