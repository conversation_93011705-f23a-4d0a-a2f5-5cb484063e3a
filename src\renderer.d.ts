// soul-forge-electron/src/renderer.d.ts

import { 
  Movie, AppSettings, SortableMovieField, MovieVersionInfo, 
  ScanCompleteData, MovieFetchParams, MovieFetchResult, SettingsResult, 
  GenerateThumbnailsParams, GenerateThumbnailsResult, SaveVersionMarksParams, 
  SaveVersionMarksResult, RenameFilesByNfoIdResult,
  MoveToRecycleBinResult,
  ScanProgressData,
  FavoriteItem, FavoriteItemType, ToggleFavoriteResult, FavoritesResult as GetFavoritesResultType,
  AiAnalyzeTagsParams, AiAnalyzeTagsResult,
  AiRecommendation, AiAnalyzeRecommendationParams, AiAnalyzeRecommendationResult,
  AiEmbellishPlotParams, AiEmbellishPlotResult, 
  AiTranslatePlotParams, AiTranslatePlotResult,
  AiCleanupStats, AiCleanupSuggestionResult,
  FavoriteBasedRecommendationParams, FavoriteBasedRecommendationResult, 
  FormatRecommendationsAsAiMessageParams, FormatRecommendationsAsAiMessageResult, 
  PrivacyModeState, AttemptUnlockResult, TogglePrivacyResult,
  SaveChatHistoryParams, SaveChatHistoryResult,
  AiSuggestCoverParams, AiSuggestCoverResult,
  AiGeneratePlotParams, AiGeneratePlotResult,
  ActorAvatarInfoResult, CdPartsFetchResult, BackupDatabaseResult, RestoreDatabaseResult,
  SnapshotInfo, MovieVersionsResult, ExistingSnapshotsResult, 
  NfoPolishScanProgressData, NfoPolishScanCompleteData, 
  NfoPolishProcessProgressData, NfoPolishProcessCompleteData, NfoPolishErrorData, 
  ScrapeActorAvatarsResult, ScrapeActorAvatarsProgress,
  MovieLibrary, ManageMovieLibraryParams, ManageMovieLibraryResult, MovieLibrariesResult, ScanLibraryResult, // Added MovieLibrariesResult
  SmartLibrary, SmartLibraryRules, SmartLibraryResult, // Added Smart Library types
  ArchiveScrapeProgressData, // Added Archive Scrape Progress types
  PlayVideoParams, PlayStrmUrlPayload // Added PlayVideoParams and PlayStrmUrlPayload
} from './types'; 


export interface BrowseImageResult {
  success: boolean;
  dataUrl?: string | null;
  error?: string;
}
export interface NfoCoverResult {
  success: boolean;
  message?: string;
  newCoverPath?: string;
  coverDataUrl?: string;
  error?: string;
}

export interface TestAiConnectionResult {
  success: boolean;
  message: string;
}

export interface GroupedSnapshotsResult {
  success: boolean;
  data?: Array<{
    versionInfo: {
      db_id: number;
      filePath: string;
      fileName: string;
      fileSize?: number;
      resolution?: string;
    };
    snapshots: Array<{
      filePath: string;
      dataUrl: string;
    }>;
  }>;
  error?: string;
}

export interface InvokeLinLuoChatResult {
  streamStarted?: boolean; 
  error?: string; 
}

// PlayVideoParams now imported from ./types
// PlayStrmUrlPayload now imported from ./types


export interface SFE_WindowApi {
  // Scan and Movie Data
  triggerScan: (pathsToScan?: string[], libraryId?: string) => void; 
  onScanComplete: (callback: (data: ScanCompleteData | ScanLibraryResult) => void) => () => void; 
  onScanStatusUpdate: (callback: (status: string) => void) => () => void;
  onScanProgressUpdate: (callback: (progress: ScanProgressData) => void) => () => void;
  onScanError: (callback: (error: string) => void) => () => void;
  getMovies: (params: MovieFetchParams) => Promise<MovieFetchResult>;
  playVideo: (params: PlayVideoParams) => void;
  onPlayStrmUrl: (callback: (payload: PlayStrmUrlPayload) => void) => () => void; // For STRM playback
  openExternalUrl: (url: string) => void;
  saveNfoData: (videoFilePath: string, movieData: Partial<Movie>) => Promise<{success: boolean, updatedMovie?: Movie, error?: string}>;
  
  // AI Functions
  generatePlotSummary: (params: AiGeneratePlotParams) => Promise<AiGeneratePlotResult>;
  translatePlotWithAI: (params: AiTranslatePlotParams) => Promise<AiTranslatePlotResult>;
  embellishPlotWithAI: (params: AiEmbellishPlotParams) => Promise<AiEmbellishPlotResult>;
  analyzeMovieTagsWithAI: (params: AiAnalyzeTagsParams) => Promise<AiAnalyzeTagsResult>;
  suggestCoverFromSnapshotsWithAI: (params: AiSuggestCoverParams) => Promise<AiSuggestCoverResult>;
  analyzeMovieRecommendationIndex: (params: AiAnalyzeRecommendationParams) => Promise<AiAnalyzeRecommendationResult>;
  getCleanupSuggestionsFromAI: () => Promise<AiCleanupSuggestionResult>;
  
  invokeLinLuoChat: (userInput: string) => Promise<InvokeLinLuoChatResult>;
  onLinLuoChatChunk: (callback: (chunk: string) => void) => () => void;
  onLinLuoChatEnd: (callback: () => void) => () => void;
  onLinLuoChatError: (callback: (errorMsg: string) => void) => () => void;
  saveChatHistory: (params: SaveChatHistoryParams) => Promise<SaveChatHistoryResult>;

  // File and Image Utilities
  downloadNfoCover: (videoFilePath: string, imageUrl: string, desiredLocalFilename: string) => Promise<NfoCoverResult>;
  browseLocalCover: (videoFilePath: string, desiredLocalFilenameTemplate: string) => Promise<NfoCoverResult>;
  browseImageForDataUrl: () => Promise<BrowseImageResult>;
  imagePathToDataUrl: (filePath: string) => Promise<string | null>;
  
  // Settings
  getSettings: () => Promise<AppSettings>;
  saveSettings: (settings: AppSettings) => Promise<SettingsResult>;
  selectDirectory: () => Promise<string[] | null>; // Changed return type
  getPathSep: () => Promise<string>;
  testAiConnection: (provider: string, config?: any) => Promise<TestAiConnectionResult>;

  // Actor Avatars
  getActorAvatarDetails: (actorName: string) => Promise<ActorAvatarInfoResult>;
  scrapeActorAvatars: () => Promise<ScrapeActorAvatarsResult>;
  onScrapeAvatarsProgress: (callback: (progress: ScrapeActorAvatarsProgress) => void) => () => void;
  onScrapeAvatarsComplete: (callback: (result: ScrapeActorAvatarsResult) => void) => () => void;
  onScrapeAvatarsError: (callback: (errorMsg: string) => void) => () => void;

  // Actor Profiles
  getActorProfile: (actorName: string) => Promise<any>;
  getAllActorProfiles: (limit?: number, offset?: number) => Promise<any>;
  deleteActorProfile: (actorName: string) => Promise<any>;
  getMoviesByActor: (actorName: string) => Promise<any>;
  getActorCompleteFilmography: (actorName: string) => Promise<any>;

  // Movie Versions and Snapshots
  getMovieVersions: (nfoId: string) => Promise<MovieVersionsResult>;
  getMovieCdParts: (nfoId: string) => Promise<CdPartsFetchResult>;
  generateThumbnails: (params: GenerateThumbnailsParams) => Promise<GenerateThumbnailsResult>;
  getExistingSnapshots: (params: { movieDbId: number }) => Promise<ExistingSnapshotsResult>;
  getAllSnapshotsForNfoId: (nfoId: string) => Promise<GroupedSnapshotsResult>;
  saveVersionMarks: (params: SaveVersionMarksParams) => Promise<SaveVersionMarksResult>;
  
  // File Management
  renameFilesByNfoId: (nfoId: string) => Promise<RenameFilesByNfoIdResult>;
  moveToRecycleBin: (filePath: string) => Promise<MoveToRecycleBinResult>;

  // Database Management
  backupDatabase: () => Promise<BackupDatabaseResult>;
  restoreDatabase: () => Promise<RestoreDatabaseResult>;
  onDatabaseRestored: (callback: (result: RestoreDatabaseResult) => void) => () => void;

  // Favorites
  toggleFavorite: (itemType: FavoriteItemType, itemValue: string) => Promise<ToggleFavoriteResult>;
  isFavorite: (itemType: FavoriteItemType, itemValue: string) => Promise<{success: boolean, isFavorite: boolean, error?: string}>;
  getFavorites: (itemType?: FavoriteItemType) => Promise<GetFavoritesResultType>; 

  // Privacy Mode
  getPrivacyModeState: () => Promise<PrivacyModeState>;
  attemptUnlockPrivacyMode: (password: string) => Promise<AttemptUnlockResult>;
  togglePrivacyModeNoPassword: () => Promise<TogglePrivacyResult>;
  onPrivacySettingsChanged: (callback: () => void) => () => void;
  
  // Recommendations
  getInitialRecommendations: (params: FavoriteBasedRecommendationParams) => Promise<FavoriteBasedRecommendationResult>;
  formatRecommendationsAsAiMessage: (params: FormatRecommendationsAsAiMessageParams) => Promise<FormatRecommendationsAsAiMessageResult>;

  // NFO Plot Polisher Tool
  nfoPolishToolScanDirectories: (directories: string[]) => void;
  onNfoPolishToolScanProgress: (callback: (data: NfoPolishScanProgressData) => void) => () => void;
  onNfoPolishToolScanComplete: (callback: (data: NfoPolishScanCompleteData) => void) => () => void;
  nfoPolishToolProcessFiles: (nfoFilePaths: string[]) => void;
  onNfoPolishToolProcessProgress: (callback: (data: NfoPolishProcessProgressData) => void) => () => void;
  onNfoPolishToolProcessComplete: (callback: (summary: NfoPolishProcessCompleteData) => void) => () => void;
  nfoPolishToolCancel: () => void;
  onNfoPolishToolLog: (callback: (message: string) => void) => () => void;
  onNfoPolishToolError: (callback: (errorData: NfoPolishErrorData) => void) => () => void;

  // Movie Libraries
  manageMovieLibrary: (params: ManageMovieLibraryParams | { id: string, delete: true }) => Promise<ManageMovieLibraryResult>; // Allow for delete structure
  getMovieLibraries: () => Promise<MovieLibrariesResult>;
  getRecentMovies: (fetchType: 'recent_played' | 'recent_added', limit: number) => Promise<MovieFetchResult>;

  // Smart Libraries
  getSmartLibraries: () => Promise<SmartLibraryResult>;
  createSmartLibrary: (libraryData: Omit<SmartLibrary, 'sort_order'> & { sort_order?: number }) => Promise<{ success: boolean; id?: string; error?: string }>;
  updateSmartLibrary: (id: string, updateData: Partial<Omit<SmartLibrary, 'id'>>) => Promise<{ success: boolean; id?: string; error?: string }>;
  deleteSmartLibrary: (id: string) => Promise<{ success: boolean; id?: string; error?: string }>;

  // Collector Service
  collectorStartTask: (siteKey: string, targetUrl: string, options?: any) => Promise<any>;
  collectorStopTask: () => Promise<any>;
  collectorGetStatus: () => Promise<any>;
  collectorGetForums: () => Promise<any>;
  collectorGetData: (options?: any) => Promise<any>;
  collectorDeleteLink: (id: number) => Promise<any>;
  collectorUpdateMdPath: (id: number, mdDocumentPath: string) => Promise<any>;
  collectorAnalyzeWithAi: (recordId: number) => Promise<any>;
  collectorConfigureDownload: (downloadConfig: any) => Promise<any>;

  // AI Categories Management
  aiCategoriesGetAll: () => Promise<any>;
  aiCategoriesAdd: (name: string) => Promise<any>;
  aiCategoriesUpdate: (id: number, newName: string) => Promise<any>;
  aiCategoriesDelete: (id: number) => Promise<any>;

  // Archive Complex Query
  archiveComplexQuery: (filters: any) => Promise<any>;

  // Get All Links for Query
  archiveGetAllLinksForQuery: (filters: any) => Promise<any>;

  // 即时番号侦察服务
  archiveScrapeAndCreate: (nfoId: string) => Promise<{ success: boolean; data?: any; assetPaths?: any; message?: string; error?: string }>;
  onArchiveScrapeProgress: (callback: (data: ArchiveScrapeProgressData) => void) => () => void;

  // Batch AI Analysis
  archiveBatchAnalyzeAI: (recordIds: number[]) => Promise<any>;
  onBatchAIProgress: (callback: (data: any) => void) => () => void;
  onCollectorStatusUpdate: (callback: (statusUpdate: any) => void) => () => void;

  // File Operations
  fileExists: (filePath: string) => Promise<boolean>;
  openFileInDefaultApp: (filePath: string) => Promise<{ success: boolean; message?: string; error?: string }>;
  selectFile: (options: { title?: string; filters?: Array<{ name: string; extensions: string[] }> }) => Promise<{ canceled: boolean; filePaths?: string[]; error?: string }>;

  // WhatsLink 预览服务
  queryWhatsLink: (magnetUrl: string) => Promise<{ success: boolean; data?: any; error?: string }>;

  // Dashboard 看板服务
  getRecentActivity: () => Promise<{ success: boolean; data?: any[]; error?: string }>;
  getDashboardStats: () => Promise<{ success: boolean; data?: { movieCount: number; linkCount: number; actorCount: number }; error?: string }>;

  // Ingest Center (情报中心)
  ingestSelectWorkspace: () => Promise<{ success: boolean; workspacePath?: string; canceled?: boolean; error?: string }>;
  ingestStartScan: (workspacePath: string) => Promise<{ success: boolean; files?: any[]; totalCount?: number; error?: string }>;
  ingestGetFileInfo: (filePath: string) => Promise<{ success: boolean; fileInfo?: any; error?: string }>;
  ingestValidateWorkspace: (workspacePath: string) => Promise<{ success: boolean; isValid?: boolean; error?: string }>;
  ingestStartWorkflow: (workspacePath: string, mdFiles: any[]) => Promise<{ success: boolean; results?: any; error?: string }>;
  onIngestScanProgress: (callback: (data: { type: string; message: string }) => void) => () => void;
  onIngestWorkflowProgress: (callback: (data: { type: string; message: string }) => void) => () => void;
}

declare global {
  interface Window {
    sfeElectronAPI: SFE_WindowApi;
  }
}
