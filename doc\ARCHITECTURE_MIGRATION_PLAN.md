# 麟琅秘府架构迁移计划

## 当前架构分析

### 技术栈
- **前端**: React 18 + TypeScript + Tailwind CSS + Vite
- **后端**: Electron 主进程 + Node.js 服务
- **数据库**: better-sqlite3 (本地 SQLite)
- **外部依赖**: Python 脚本 + FFmpeg
- **AI服务**: Google Gemini, Custom GPT, Grok

### 核心功能模块

#### 1. 数据库层 (databaseService.js)
- **表结构**:
  - `movies`: 电影主表 (29个字段)
  - `favorites`: 收藏表
  - `actor_metadata`: 演员元数据
  - `movie_libraries`: 电影库
  - `movie_library_links`: 电影库关联
- **主要功能**: CRUD操作、数据迁移、备份恢复

#### 2. Python 脚本功能
- **my_scanner.py**: 文件夹扫描、视频文件发现
- **scan_movies.py**: NFO解析、媒体信息提取
- **generate_thumbnails.py**: 视频缩略图生成 (依赖FFmpeg)
- **nfo_utils.py**: NFO文件读写操作
- **file_renamer.py**: 文件重命名
- **video_utils.py**: 视频技术信息提取

#### 3. 主进程服务
- **settingsService**: 应用设置管理
- **aiService**: AI功能集成
- **avatarFileTreeService**: 演员头像管理
- **pythonRunnerService**: Python脚本执行器
- **fileUtils**: 文件操作工具
- **snapshotUtils**: 快照管理

#### 4. IPC 通信机制
- 50+ IPC handlers 处理前后端通信
- 主要类别: 电影数据、设置、AI功能、文件操作、扫描

#### 5. 前端组件架构
- **Hooks**: 状态管理和业务逻辑
- **Components**: UI组件 (模态框、卡片、布局等)
- **Types**: TypeScript 类型定义

## 目标架构设计

### 新技术栈
- **前端**: Next.js 14 (App Router) + React 18 + TypeScript 5
- **状态管理**: Zustand + React Context
- **UI框架**: Tailwind CSS 3 + Radix UI + Framer Motion 10
- **图标**: Lucide React
- **构建工具**: Vite + ESLint + Prettier
- **数据库**: Prisma + PostgreSQL (或 SQLite for development)

### 架构变更策略

#### 1. 数据库层重构
**目标**: 将 better-sqlite3 迁移到 Prisma ORM
- 使用 Prisma Schema 重新定义数据模型
- 支持 PostgreSQL (生产) + SQLite (开发)
- 保持现有表结构和数据完整性
- 实现数据迁移脚本

#### 2. Python 功能替换
**策略**: 使用 Node.js 生态替换所有 Python 依赖

| Python 功能 | Node.js 替换方案 |
|-------------|------------------|
| 文件扫描 | Node.js fs/glob + 自定义扫描器 |
| NFO 解析 | xml2js + 自定义解析器 |
| 视频信息提取 | node-ffprobe + fluent-ffmpeg |
| 缩略图生成 | fluent-ffmpeg + sharp |
| 文件重命名 | Node.js fs + path |

#### 3. API 路由设计
**目标**: 使用 Next.js API Routes 替换 IPC 通信

```
/api/
├── movies/
│   ├── route.ts (GET, POST)
│   ├── [id]/route.ts (GET, PUT, DELETE)
│   └── scan/route.ts (POST)
├── libraries/
│   ├── route.ts (GET, POST)
│   └── [id]/route.ts (GET, PUT, DELETE)
├── ai/
│   ├── analyze/route.ts
│   ├── translate/route.ts
│   └── chat/route.ts
├── media/
│   ├── thumbnails/route.ts
│   └── upload/route.ts
└── settings/
    └── route.ts
```

#### 4. 状态管理重构
**使用 Zustand 创建模块化状态存储**:
- `useMovieStore`: 电影数据和过滤
- `useSettingsStore`: 应用设置
- `useLibraryStore`: 电影库管理
- `useUIStore`: UI状态 (模态框、加载等)
- `useAIStore`: AI功能状态

#### 5. 组件架构升级
**使用 Radix UI + Tailwind CSS 重构**:
- 无障碍性改进
- 一致的设计系统
- 响应式设计优化
- 动画效果增强 (Framer Motion)

## 迁移步骤

### Phase 1: 项目基础设施
1. 创建 Next.js 14 项目
2. 配置 TypeScript + Tailwind + Zustand
3. 设置 Prisma + 数据库
4. 配置开发工具 (ESLint, Prettier)

### Phase 2: 数据层迁移
1. 定义 Prisma Schema
2. 创建数据迁移脚本
3. 实现基础 CRUD API
4. 数据验证和测试

### Phase 3: 核心功能替换
1. 文件扫描功能 (Node.js)
2. NFO 解析功能
3. 媒体处理功能
4. AI 服务集成

### Phase 4: UI 组件迁移
1. 基础组件库 (Radix UI)
2. 布局组件
3. 业务组件
4. 状态管理集成

### Phase 5: 高级功能
1. 文件上传和处理
2. 实时功能 (WebSocket)
3. 性能优化
4. 测试和部署

## 技术挑战与解决方案

### 1. 文件系统访问限制
**问题**: Web 应用无法直接访问本地文件系统
**解决方案**: 
- 文件上传 API
- 云存储集成
- 本地服务器模式

### 2. 视频处理性能
**问题**: 浏览器端视频处理能力有限
**解决方案**:
- 服务端处理 (Node.js + FFmpeg)
- 流式处理
- 后台任务队列

### 3. 数据迁移复杂性
**问题**: 现有 SQLite 数据需要完整迁移
**解决方案**:
- 渐进式迁移
- 数据验证脚本
- 回滚机制

### 4. AI 服务集成
**问题**: 保持现有 AI 功能完整性
**解决方案**:
- API 适配器模式
- 统一 AI 服务接口
- 错误处理和重试机制

## 预期收益

### 技术收益
- 更好的开发体验和工具链
- 更强的类型安全性
- 更好的性能和SEO
- 更容易的部署和维护

### 用户体验收益
- 更快的加载速度
- 更好的响应式设计
- 更流畅的动画效果
- 更好的无障碍性

### 维护收益
- 统一的技术栈
- 更好的代码组织
- 更容易的测试
- 更简单的部署流程
