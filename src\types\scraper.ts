// src/types/scraper.ts

/**
 * 所有刮削器成功后必须返回的标准化数据结构。
 * 字段的命名和结构，严格参考 javbus.py 的返回字典。
 */
export interface ScrapedMovieData {
  nfoId: string;
  title: string;
  plot: string; // 剧情简介
  releaseDate: string; // 发行日期, 格式: YYYY-MM-DD
  year: string; // 年份, 格式: YYYY
  runtime: string; // 片长 (分钟)
  director: string;
  studio: string; // 制作商
  publisher: string; // 发行商
  series: string;
  actors: string[]; // 演员列表
  tags: string[]; // 标签/类型
  coverUrl: string; // 封面图链接 (大图)
  posterUrl: string; // 海报图链接 (小图/列表图)
  previewImages: string[]; // 预览图/剧照链接数组
  trailerUrl: string; // 预告片链接
  sourceUrl: string; // 刮削的原始页面URL
  mosaic: '有码' | '无码' | '其他'; // 马赛克类型
}

/**
 * 定义所有 Provider 必须实现的接口。
 * 每个 Provider 都是一个独立的"特工"。
 */
export interface IScraperProvider {
  readonly name: string; // Provider 的唯一标识名, e.g., 'javbus'
  /**
   * 根据番号刮削单个影片的元数据。
   * @param nfoId 要刮削的番号
   * @throws {Error} 如果刮削失败（如未找到、被屏蔽等），必须抛出错误。
   * @returns {Promise<ScrapedMovieData>} 刮削成功后的标准数据对象。
   */
  scrape(nfoId: string): Promise<ScrapedMovieData>;
}
