import { NextRequest, NextResponse } from 'next/server';
import { MovieService } from '@/lib/services/movie-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const movie = await MovieService.getMovieById(params.id);

    if (!movie) {
      return NextResponse.json(
        {
          success: false,
          error: 'Movie not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: movie,
    });
  } catch (error) {
    console.error('Error fetching movie:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch movie',
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    const movie = await MovieService.updateMovie(params.id, body);

    return NextResponse.json({
      success: true,
      data: movie,
    });
  } catch (error) {
    console.error('Error updating movie:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update movie',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await MovieService.deleteMovie(params.id);

    return NextResponse.json({
      success: true,
      message: 'Movie deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting movie:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete movie',
      },
      { status: 500 }
    );
  }
}
