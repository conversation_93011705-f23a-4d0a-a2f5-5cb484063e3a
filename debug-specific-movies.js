// 调试特定电影的脚本
// 使用方法：在 Electron 应用的开发者控制台中运行

// 修复 nfoId 问题 - 实际执行版本
async function fixNfoIds() {
  try {
    console.log('=== 修复 nfoId 问题 ===');

    // JAV ID 提取函数
    function extractJavIdFromFilename(filename) {
      // 移除扩展名
      const baseName = filename.replace(/\.[^.]+$/, '');

      // JAV ID 模式
      const patterns = [
        /([A-Z]{2,5}-\d{3,5})/i,  // 标准 JAV ID 格式：ABC-123
        /([A-Z]{2,4}\d{3,5})/i,   // 无连字符格式：ABC123
        /([A-Z]+[-_]\d+)/i,       // 通用格式：ABC_123 或 ABC-123
      ];

      for (const pattern of patterns) {
        const match = baseName.match(pattern);
        if (match) {
          let id = match[1].toUpperCase();
          // 标准化格式：确保有连字符
          if (!/[-_]/.test(id)) {
            // 如果没有连字符，在字母和数字之间添加
            id = id.replace(/([A-Z]+)(\d+)/, '$1-$2');
          } else {
            // 将下划线替换为连字符
            id = id.replace('_', '-');
          }
          return id;
        }
      }

      return null;
    }

    // 获取所有电影
    const result = await window.sfeElectronAPI.getMovies({
      sortField: 'db_id',
      sortOrder: 'asc',
      filterText: '',
      pageNumber: 1,
      pageSize: 1000
    });

    if (!result.success) {
      console.error('获取电影列表失败:', result.error);
      return;
    }

    // 找到没有 nfoId 的电影
    const moviesWithoutNfoId = result.movies.filter(movie =>
      !movie.nfoId || movie.nfoId.trim() === ''
    );

    console.log(`总电影数: ${result.movies.length}`);
    console.log(`没有 nfoId 的电影: ${moviesWithoutNfoId.length}`);

    if (moviesWithoutNfoId.length === 0) {
      console.log('所有电影都已有 nfoId，无需修复。');
      return;
    }

    console.log('\n开始修复...');

    let updatedCount = 0;
    let skippedCount = 0;
    const updatePromises = [];

    for (const movie of moviesWithoutNfoId) {
      const extractedId = extractJavIdFromFilename(movie.fileName);

      if (extractedId) {
        console.log(`${movie.db_id}: ${movie.fileName}`);
        console.log(`  提取的 ID: ${extractedId}`);

        // 创建更新数据
        const updateData = {
          ...movie,
          nfoId: extractedId
        };

        // 调用更新 API
        const updatePromise = window.sfeElectronAPI.updateMovieNfoId(movie.db_id, extractedId)
          .then(result => {
            if (result.success) {
              console.log(`  ✅ 已更新: ${extractedId}`);
              return { success: true, id: movie.db_id };
            } else {
              console.log(`  ❌ 更新失败: ${result.error}`);
              return { success: false, id: movie.db_id, error: result.error };
            }
          })
          .catch(error => {
            console.log(`  ❌ 更新异常: ${error.message}`);
            return { success: false, id: movie.db_id, error: error.message };
          });

        updatePromises.push(updatePromise);
        updatedCount++;
      } else {
        console.log(`${movie.db_id}: ${movie.fileName}`);
        console.log(`  ⚠️  无法提取 ID`);
        skippedCount++;
      }
    }

    // 等待所有更新完成
    if (updatePromises.length > 0) {
      console.log(`\n等待 ${updatePromises.length} 个更新操作完成...`);
      const results = await Promise.all(updatePromises);

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      console.log('\n=== 修复完成 ===');
      console.log(`成功更新: ${successCount} 部`);
      console.log(`更新失败: ${failCount} 部`);
      console.log(`跳过: ${skippedCount} 部`);

      if (failCount > 0) {
        console.log('\n失败的更新:');
        results.filter(r => !r.success).forEach(r => {
          console.log(`  ID ${r.id}: ${r.error}`);
        });
      }

      if (successCount > 0) {
        console.log('\n✅ 修复成功！请刷新页面查看效果。');
        console.log('现在相同 NFO ID 的电影应该会合并显示。');
      }
    } else {
      console.log('\n=== 修复分析完成 ===');
      console.log(`无法提取: ${skippedCount} 部`);
      console.log('没有可以修复的电影。');
    }

  } catch (error) {
    console.error('修复失败:', error);
  }
}

// 检查 NFO 文件情况
async function checkNFOFiles() {
  try {
    console.log('=== 检查 NFO 文件情况 ===');

    // 获取前10部电影
    const result = await window.sfeElectronAPI.getMovies({
      sortField: 'db_id',
      sortOrder: 'desc',
      filterText: '',
      pageNumber: 1,
      pageSize: 10
    });

    if (!result.success) {
      console.error('获取电影列表失败:', result.error);
      return;
    }

    console.log(`检查前 ${result.movies.length} 部电影的 NFO 文件:`);

    for (let i = 0; i < result.movies.length; i++) {
      const movie = result.movies[i];
      console.log(`\n${i + 1}. 电影 ID: ${movie.db_id}`);
      console.log(`   文件名: ${movie.fileName}`);
      console.log(`   文件路径: ${movie.filePath}`);
      console.log(`   nfoId: ${movie.nfoId || 'null'}`);

      // 检查 NFO 文件是否存在
      const videoDir = movie.filePath.substring(0, movie.filePath.lastIndexOf('\\'));
      const videoBaseName = movie.fileName.substring(0, movie.fileName.lastIndexOf('.'));
      const nfoPath = `${videoDir}\\${videoBaseName}.nfo`;

      console.log(`   预期 NFO 路径: ${nfoPath}`);

      // 尝试读取 NFO 文件（如果有相关 API）
      // 这里我们只能显示路径，无法直接检查文件是否存在
    }

  } catch (error) {
    console.error('检查 NFO 文件失败:', error);
  }
}

// 分析多版本合并问题
async function analyzeMergeIssue() {
  try {
    console.log('=== 分析多版本合并问题 ===');

    // 获取所有电影
    const result = await window.sfeElectronAPI.getMovies({
      sortField: 'db_id',
      sortOrder: 'desc',
      filterText: '',
      pageNumber: 1,
      pageSize: 100
    });

    if (!result.success) {
      console.error('获取电影列表失败:', result.error);
      return;
    }

    console.log(`获取到 ${result.movies.length} 部电影`);

    // 分析 nfoId 分布
    const nfoIdMap = new Map();
    const noNfoIdMovies = [];

    result.movies.forEach(movie => {
      if (movie.nfoId && movie.nfoId.trim() !== '') {
        const normalizedNfoId = movie.nfoId.toLowerCase().trim();
        if (!nfoIdMap.has(normalizedNfoId)) {
          nfoIdMap.set(normalizedNfoId, []);
        }
        nfoIdMap.get(normalizedNfoId).push(movie);
      } else {
        noNfoIdMovies.push(movie);
      }
    });

    console.log(`\n=== NFO ID 分析 ===`);
    console.log(`有 NFO ID 的电影: ${result.movies.length - noNfoIdMovies.length}`);
    console.log(`没有 NFO ID 的电影: ${noNfoIdMovies.length}`);
    console.log(`唯一 NFO ID 数量: ${nfoIdMap.size}`);

    // 查找重复的 NFO ID
    const duplicateGroups = [];
    nfoIdMap.forEach((movies, nfoId) => {
      if (movies.length > 1) {
        duplicateGroups.push({ nfoId, movies });
      }
    });

    console.log(`\n=== 重复 NFO ID 分析 ===`);
    console.log(`有重复的 NFO ID 组数: ${duplicateGroups.length}`);

    if (duplicateGroups.length > 0) {
      console.log('\n重复组详情:');
      duplicateGroups.forEach((group, index) => {
        console.log(`\n${index + 1}. NFO ID: "${group.nfoId}" (${group.movies.length} 个版本)`);
        group.movies.forEach((movie, movieIndex) => {
          console.log(`  ${movieIndex + 1}. ID:${movie.db_id} | vCount:${movie.versionCount || 'undef'} | cdCount:${movie.multiCdCountForNfoId || 'undef'}`);
          console.log(`     文件名: ${movie.fileName}`);
          console.log(`     原始 nfoId: "${movie.nfoId}"`);
          console.log(`     首选状态: ${movie.preferredStatus || 'null'}`);
        });
      });

      // 检查第一个重复组的版本信息
      const firstGroup = duplicateGroups[0];
      console.log(`\n=== 检查第一个重复组的版本信息 ===`);
      console.log(`NFO ID: "${firstGroup.nfoId}"`);

      try {
        const versionResult = await window.sfeElectronAPI.getMovieVersions(firstGroup.movies[0].nfoId);
        if (versionResult.success) {
          console.log(`API 返回的版本数: ${versionResult.versions.length}`);
          console.log('版本详情:');
          versionResult.versions.forEach((version, index) => {
            console.log(`  ${index + 1}. ID:${version.db_id} | 文件名: ${version.fileName}`);
          });
        } else {
          console.error('获取版本信息失败:', versionResult.error);
        }
      } catch (error) {
        console.error('调用版本 API 失败:', error);
      }
    } else {
      console.log('❌ 没有找到重复的 NFO ID，这可能是问题所在！');

      // 检查是否有相似的文件名
      console.log('\n=== 检查相似文件名 ===');
      const fileNameGroups = new Map();

      result.movies.forEach(movie => {
        // 提取文件名的主要部分（去除扩展名和可能的版本标识）
        const baseName = movie.fileName
          .replace(/\.[^.]+$/, '') // 去除扩展名
          .replace(/[-_\s]*\d+p?$/i, '') // 去除分辨率标识
          .replace(/[-_\s]*\d{4}$/i, '') // 去除年份
          .replace(/[-_\s]*v\d+$/i, '') // 去除版本号
          .toLowerCase()
          .trim();

        if (!fileNameGroups.has(baseName)) {
          fileNameGroups.set(baseName, []);
        }
        fileNameGroups.get(baseName).push(movie);
      });

      const similarFileGroups = [];
      fileNameGroups.forEach((movies, baseName) => {
        if (movies.length > 1) {
          similarFileGroups.push({ baseName, movies });
        }
      });

      console.log(`找到 ${similarFileGroups.length} 组相似文件名:`);
      similarFileGroups.slice(0, 5).forEach((group, index) => {
        console.log(`\n${index + 1}. 基础名称: "${group.baseName}" (${group.movies.length} 个文件)`);
        group.movies.forEach((movie, movieIndex) => {
          console.log(`  ${movieIndex + 1}. ID:${movie.db_id} | NFO:"${movie.nfoId || 'null'}" | ${movie.fileName}`);
        });
      });
    }

  } catch (error) {
    console.error('分析失败:', error);
  }
}

// 检查特定 nfoId 的所有版本
async function debugMovieVersions(nfoId) {
  try {
    console.log(`=== 调试 NFO ID: ${nfoId} ===`);
    
    const result = await window.sfeElectronAPI.getMovieVersions(nfoId);
    
    if (result.success) {
      console.log(`找到 ${result.versions.length} 个版本:`);
      result.versions.forEach((version, index) => {
        console.log(`版本 ${index + 1}:`);
        console.log(`  - ID: ${version.db_id}`);
        console.log(`  - 文件名: ${version.fileName}`);
        console.log(`  - NFO ID: ${version.nfoId}`);
        console.log(`  - 文件路径: ${version.filePath}`);
        console.log(`  - 首选状态: ${version.preferredStatus || 'null'}`);
        console.log(`  - 年份: ${version.year || 'null'}`);
        console.log(`  - 文件大小: ${version.fileSize || 'null'}`);
        console.log('');
      });
    } else {
      console.error('获取版本失败:', result.error);
    }
  } catch (error) {
    console.error('调试失败:', error);
  }
}

// 检查特定 ID 的电影信息
async function debugMovieById(movieId) {
  try {
    console.log(`=== 调试电影 ID: ${movieId} ===`);
    
    // 通过获取电影列表来找到特定ID的电影
    const result = await window.sfeElectronAPI.getMovies({
      sortField: 'db_id',
      sortOrder: 'asc',
      filterText: '',
      pageNumber: 1,
      pageSize: 1000
    });
    
    if (result.success) {
      const movie = result.movies.find(m => m.db_id === movieId);
      if (movie) {
        console.log('电影信息:');
        console.log(`  - ID: ${movie.db_id}`);
        console.log(`  - 文件名: ${movie.fileName}`);
        console.log(`  - 标题: ${movie.title || 'null'}`);
        console.log(`  - NFO ID: ${movie.nfoId || 'null'}`);
        console.log(`  - 版本数: ${movie.versionCount || 'undef'}`);
        console.log(`  - CD数: ${movie.multiCdCountForNfoId || 'undef'}`);
        console.log(`  - 文件路径: ${movie.filePath}`);
        console.log(`  - 首选状态: ${movie.preferredStatus || 'null'}`);
        
        // 如果有 nfoId，检查同组的其他版本
        if (movie.nfoId) {
          console.log('\n检查同组版本...');
          await debugMovieVersions(movie.nfoId);
        }
      } else {
        console.log('未找到指定ID的电影');
      }
    } else {
      console.error('获取电影列表失败:', result.error);
    }
  } catch (error) {
    console.error('调试失败:', error);
  }
}

// 查找可能的重复 nfoId
async function findDuplicateNfoIds() {
  try {
    console.log('=== 查找重复的 NFO ID ===');
    
    const result = await window.sfeElectronAPI.getMovies({
      sortField: 'nfoId',
      sortOrder: 'asc',
      filterText: '',
      pageNumber: 1,
      pageSize: 1000
    });
    
    if (result.success) {
      const nfoIdGroups = {};
      
      result.movies.forEach(movie => {
        if (movie.nfoId && movie.nfoId.trim() !== '') {
          const normalizedNfoId = movie.nfoId.toLowerCase().trim();
          if (!nfoIdGroups[normalizedNfoId]) {
            nfoIdGroups[normalizedNfoId] = [];
          }
          nfoIdGroups[normalizedNfoId].push(movie);
        }
      });
      
      const duplicates = Object.entries(nfoIdGroups).filter(([_, movies]) => movies.length > 1);
      
      if (duplicates.length > 0) {
        console.log(`找到 ${duplicates.length} 个重复的 NFO ID:`);
        duplicates.forEach(([nfoId, movies]) => {
          console.log(`\nNFO ID: ${nfoId} (${movies.length} 个版本)`);
          movies.forEach((movie, index) => {
            console.log(`  ${index + 1}. ID:${movie.db_id} | vCount:${movie.versionCount || 'undef'} | ${movie.fileName}`);
          });
        });
      } else {
        console.log('没有找到重复的 NFO ID');
      }
    } else {
      console.error('获取电影列表失败:', result.error);
    }
  } catch (error) {
    console.error('查找失败:', error);
  }
}

// 使用说明
console.log(`
调试工具已加载！使用方法：

1. 修复 nfoId 问题：
   fixNfoIds()

2. 检查 NFO 文件情况：
   checkNFOFiles()

3. 分析多版本合并问题：
   analyzeMergeIssue()

4. 调试特定 NFO ID 的版本：
   debugMovieVersions('ABC-123')

5. 调试特定电影 ID：
   debugMovieById(123)

6. 查找所有重复的 NFO ID：
   findDuplicateNfoIds()

请根据界面上显示的调试信息来使用这些函数。
`);

// 导出函数到全局作用域
window.fixNfoIds = fixNfoIds;
window.checkNFOFiles = checkNFOFiles;
window.analyzeMergeIssue = analyzeMergeIssue;
window.debugMovieVersions = debugMovieVersions;
window.debugMovieById = debugMovieById;
window.findDuplicateNfoIds = findDuplicateNfoIds;
