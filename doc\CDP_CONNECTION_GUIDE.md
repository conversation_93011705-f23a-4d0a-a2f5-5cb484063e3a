# CDP连接方案使用指南

## 概述

新的CDP（Chrome DevTools Protocol）连接方案不再启动新的浏览器实例，而是连接到用户手动启动并开启了调试端口的Chrome实例。这样可以保持用户的登录状态和会话信息。

## 使用步骤

### 1. 启动带调试端口的Chrome

用户需要使用特殊参数启动Chrome，开启调试端口9222：

```bash
chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-debug"
```

或者创建Chrome快捷方式，在目标路径后添加参数：
```
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-debug"
```

### 2. 手动登录和导航

在启动的Chrome中：
1. 访问目标论坛网站
2. 完成登录验证
3. 导航到要抓取的板块页面
4. 确保页面完全加载

### 3. 启动搜集任务

在SoulForge应用中：
1. 选择对应的论坛配置
2. 输入目标URL（必须与Chrome中打开的页面匹配）
3. 点击"开始任务"

## 工作原理

1. **连接检测**: 程序通过CDP协议连接到localhost:9222
2. **页面查找**: 在已打开的页面中查找匹配目标URL的页面
3. **页面接管**: 接管找到的页面，开始自动抓取
4. **保持状态**: 抓取完成后不关闭浏览器，保持用户登录状态

## 错误处理

### 连接失败
- **错误**: `ECONNREFUSED`
- **原因**: Chrome未启动或未开启调试端口
- **解决**: 按步骤1重新启动Chrome

### 找不到目标页面
- **错误**: `无法在已打开的页面中找到目标URL`
- **原因**: 用户未在Chrome中打开目标页面
- **解决**: 手动在Chrome中打开目标URL

### 页面访问失败
- **错误**: 页面操作超时或失败
- **原因**: 页面未完全加载或需要额外验证
- **解决**: 刷新页面或重新登录

## 优势

1. **保持登录**: 不会丢失用户的登录状态
2. **绕过验证**: 用户已手动完成的验证不会重复
3. **稳定性**: 减少自动化检测的风险
4. **可观察**: 用户可以实时观察抓取过程

## 测试

运行测试脚本验证连接：
```bash
node test_cdp_connection.js
```

## 注意事项

1. 确保Chrome版本支持CDP协议
2. 调试端口9222不能被其他程序占用
3. 用户数据目录路径要有写入权限
4. 抓取过程中不要关闭Chrome或目标页面
