// 简单的修复测试脚本
// 在 Electron 应用开发者控制台中运行

async function testAndFix() {
  try {
    console.log('=== 测试和修复多版本合并问题 ===');
    
    // 1. 测试 API 是否可用
    console.log('1. 测试 API 可用性...');
    
    if (!window.sfeElectronAPI) {
      console.error('❌ sfeElectronAPI 不可用');
      return;
    }
    
    if (!window.sfeElectronAPI.batchFixNfoIds) {
      console.error('❌ batchFixNfoIds API 不可用');
      console.log('可用的 API:', Object.keys(window.sfeElectronAPI));
      return;
    }
    
    console.log('✅ API 可用');
    
    // 2. 获取当前电影数据
    console.log('\n2. 获取当前电影数据...');
    const moviesResult = await window.sfeElectronAPI.getMovies({
      sortField: 'db_id',
      sortOrder: 'desc',
      filterText: '',
      pageNumber: 1,
      pageSize: 20
    });
    
    if (!moviesResult.success) {
      console.error('❌ 获取电影失败:', moviesResult.error);
      return;
    }
    
    console.log(`✅ 获取到 ${moviesResult.movies.length} 部电影`);
    
    // 3. 检查 nfoId 状态
    console.log('\n3. 检查 nfoId 状态...');
    const moviesWithNfoId = moviesResult.movies.filter(m => m.nfoId && m.nfoId.trim() !== '');
    const moviesWithoutNfoId = moviesResult.movies.filter(m => !m.nfoId || m.nfoId.trim() === '');
    
    console.log(`有 nfoId: ${moviesWithNfoId.length}`);
    console.log(`缺少 nfoId: ${moviesWithoutNfoId.length}`);
    
    // 显示前几个电影的状态
    console.log('\n前5部电影的状态:');
    moviesResult.movies.slice(0, 5).forEach((movie, index) => {
      console.log(`${index + 1}. ${movie.fileName}`);
      console.log(`   nfoId: "${movie.nfoId || 'null'}"`);
      console.log(`   versionCount: ${movie.versionCount || 'undef'}`);
    });
    
    // 4. 如果有缺失的 nfoId，尝试修复
    if (moviesWithoutNfoId.length > 0) {
      console.log(`\n4. 发现 ${moviesWithoutNfoId.length} 部电影缺少 nfoId，开始修复...`);
      
      const shouldFix = confirm(`发现 ${moviesWithoutNfoId.length} 部电影缺少 nfoId。是否立即修复？`);
      if (!shouldFix) {
        console.log('用户取消修复');
        return;
      }
      
      // 调用批量修复
      console.log('调用批量修复 API...');
      const fixResult = await window.sfeElectronAPI.batchFixNfoIds();
      
      if (fixResult.success) {
        console.log('✅ 批量修复完成！');
        console.log(`处理: ${fixResult.processed}, 成功: ${fixResult.updated}, 跳过: ${fixResult.skipped}, 错误: ${fixResult.errors}`);
        
        if (fixResult.updated > 0) {
          console.log('\n🎉 修复成功！');
          
          // 显示修复的详情
          if (fixResult.details) {
            const successful = fixResult.details.filter(d => d.action === 'updated');
            console.log(`\n成功修复的电影 (前5个):`);
            successful.slice(0, 5).forEach((detail, index) => {
              console.log(`${index + 1}. ${detail.fileName} -> ${detail.extractedNfoId}`);
            });
          }
          
          const shouldRefresh = confirm('修复完成！是否立即刷新页面查看合并效果？');
          if (shouldRefresh) {
            location.reload();
          }
        } else {
          console.log('没有电影被修复');
        }
      } else {
        console.error('❌ 批量修复失败:', fixResult.error);
      }
    } else {
      console.log('\n4. 所有电影都有 nfoId');
      
      // 检查是否有重复的 nfoId（应该合并的电影）
      const nfoIdGroups = {};
      moviesWithNfoId.forEach(movie => {
        const nfoId = movie.nfoId.toLowerCase().trim();
        if (!nfoIdGroups[nfoId]) {
          nfoIdGroups[nfoId] = [];
        }
        nfoIdGroups[nfoId].push(movie);
      });
      
      const duplicateGroups = Object.entries(nfoIdGroups).filter(([_, movies]) => movies.length > 1);
      
      if (duplicateGroups.length > 0) {
        console.log(`\n发现 ${duplicateGroups.length} 组重复的 nfoId (应该合并):`);
        duplicateGroups.slice(0, 3).forEach(([nfoId, movies], index) => {
          console.log(`${index + 1}. "${nfoId}" - ${movies.length} 个版本:`);
          movies.forEach((movie, movieIndex) => {
            console.log(`   ${movieIndex + 1}. ${movie.fileName} (vCount: ${movie.versionCount || 'undef'})`);
          });
        });
        
        console.log('\n如果这些电影仍然显示为独立卡片，说明前端合并逻辑有问题。');
      } else {
        console.log('\n没有发现重复的 nfoId');
      }
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 简单的手动修复单个电影
async function manualFixMovie(movieId, nfoId) {
  try {
    console.log(`手动修复电影 ${movieId} -> ${nfoId}`);
    const result = await window.sfeElectronAPI.updateMovieNfoId(movieId, nfoId);
    
    if (result.success) {
      console.log('✅ 修复成功');
    } else {
      console.error('❌ 修复失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 修复异常:', error);
  }
}

// 导出函数
window.testAndFix = testAndFix;
window.manualFixMovie = manualFixMovie;

console.log(`
🛠️ 简单修复工具已加载！

使用方法:
1. testAndFix() - 测试并自动修复
2. manualFixMovie(movieId, nfoId) - 手动修复单个电影

立即运行: testAndFix()
`);

// 自动运行测试
testAndFix();
