// Node.js 批量 NFO ID 修复器
const NodeNfoParser = require('./nodeNfoParser');

class NodeBatchNfoFixer {
  static async fixAllMissingNfoIds(databaseService, log) {
    const results = {
      processed: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
      details: []
    };

    try {
      log.info('[批量 NFO 修复] 开始修复缺失的 NFO ID...');
      
      // 获取所有没有 nfoId 的电影
      const db = databaseService.getDb();
      const moviesWithoutNfoId = db.prepare(`
        SELECT db_id, filePath, fileName, nfoId 
        FROM movies 
        WHERE nfoId IS NULL OR nfoId = ''
      `).all();

      log.info(`[批量 NFO 修复] 找到 ${moviesWithoutNfoId.length} 部没有 NFO ID 的电影`);

      if (moviesWithoutNfoId.length === 0) {
        return {
          success: true,
          message: '所有电影都已有 NFO ID，无需修复',
          ...results
        };
      }

      // 准备更新语句
      const updateStmt = db.prepare('UPDATE movies SET nfoId = ?, lastScanned = datetime(\'now\', \'localtime\') WHERE db_id = ?');

      for (const movie of moviesWithoutNfoId) {
        results.processed++;
        
        try {
          // 尝试从文件名提取 NFO ID
          const extractedId = NodeNfoParser.extractJavIdFromFilename(movie.fileName);
          
          const detail = {
            id: movie.db_id,
            fileName: movie.fileName,
            currentNfoId: movie.nfoId,
            extractedNfoId: extractedId,
            action: 'none'
          };

          if (extractedId) {
            try {
              const info = updateStmt.run(extractedId, movie.db_id);
              if (info.changes > 0) {
                detail.action = 'updated';
                results.updated++;
                log.info(`[批量 NFO 修复] 已更新: ${movie.fileName} -> ${extractedId}`);
              } else {
                detail.action = 'update_failed';
                results.errors++;
                detail.error = '数据库更新失败';
              }
            } catch (updateError) {
              detail.action = 'update_error';
              detail.error = updateError.message;
              results.errors++;
              log.error(`[批量 NFO 修复] 更新失败: ${movie.fileName} - ${updateError.message}`);
            }
          } else {
            detail.action = 'skipped';
            results.skipped++;
            log.warn(`[批量 NFO 修复] 无法提取 ID: ${movie.fileName}`);
          }

          results.details.push(detail);
        } catch (error) {
          results.errors++;
          results.details.push({
            id: movie.db_id,
            fileName: movie.fileName,
            action: 'error',
            error: error.message
          });
          log.error(`[批量 NFO 修复] 处理失败: ${movie.fileName} - ${error.message}`);
        }
      }

      log.info(`[批量 NFO 修复] 修复完成: 成功 ${results.updated}, 跳过 ${results.skipped}, 错误 ${results.errors}`);

      return {
        success: true,
        message: `修复完成: 成功更新 ${results.updated} 部电影`,
        ...results
      };

    } catch (error) {
      log.error(`[批量 NFO 修复] 修复过程失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        ...results
      };
    }
  }

  static async analyzeNfoIdStatus(databaseService, log) {
    try {
      const db = databaseService.getDb();
      
      // 统计信息
      const stats = db.prepare(`
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN nfoId IS NOT NULL AND nfoId != '' THEN 1 END) as with_nfo_id,
          COUNT(CASE WHEN nfoId IS NULL OR nfoId = '' THEN 1 END) as without_nfo_id
        FROM movies
      `).get();

      // 重复的 NFO ID
      const duplicates = db.prepare(`
        SELECT nfoId, COUNT(*) as count
        FROM movies 
        WHERE nfoId IS NOT NULL AND nfoId != ''
        GROUP BY LOWER(TRIM(nfoId))
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT 10
      `).all();

      // 可以从文件名提取 ID 的电影
      const moviesWithoutNfoId = db.prepare(`
        SELECT db_id, fileName 
        FROM movies 
        WHERE nfoId IS NULL OR nfoId = ''
      `).all();

      let extractableCount = 0;
      const extractableDetails = [];

      for (const movie of moviesWithoutNfoId) {
        const extractedId = NodeNfoParser.extractJavIdFromFilename(movie.fileName);
        if (extractedId) {
          extractableCount++;
          extractableDetails.push({
            id: movie.db_id,
            fileName: movie.fileName,
            extractedId
          });
        }
      }

      log.info(`[NFO ID 分析] 总计: ${stats.total}, 有 ID: ${stats.with_nfo_id}, 缺失: ${stats.without_nfo_id}, 可提取: ${extractableCount}`);

      return {
        success: true,
        statistics: stats,
        duplicateGroups: duplicates,
        extractableCount,
        extractableDetails: extractableDetails.slice(0, 20), // 只返回前20个示例
        canFixCount: extractableCount
      };

    } catch (error) {
      log.error(`[NFO ID 分析] 分析失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  static async fixSpecificMovies(movieIds, databaseService, log) {
    const results = {
      processed: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
      details: []
    };

    try {
      const db = databaseService.getDb();
      const updateStmt = db.prepare('UPDATE movies SET nfoId = ?, lastScanned = datetime(\'now\', \'localtime\') WHERE db_id = ?');

      for (const movieId of movieIds) {
        results.processed++;
        
        try {
          // 获取电影信息
          const movie = db.prepare('SELECT db_id, fileName, nfoId FROM movies WHERE db_id = ?').get(movieId);
          
          if (!movie) {
            results.errors++;
            results.details.push({
              id: movieId,
              action: 'error',
              error: '电影不存在'
            });
            continue;
          }

          const extractedId = NodeNfoParser.extractJavIdFromFilename(movie.fileName);
          
          const detail = {
            id: movie.db_id,
            fileName: movie.fileName,
            currentNfoId: movie.nfoId,
            extractedNfoId: extractedId,
            action: 'none'
          };

          if (extractedId) {
            const info = updateStmt.run(extractedId, movie.db_id);
            if (info.changes > 0) {
              detail.action = 'updated';
              results.updated++;
            } else {
              detail.action = 'update_failed';
              results.errors++;
            }
          } else {
            detail.action = 'skipped';
            results.skipped++;
          }

          results.details.push(detail);
        } catch (error) {
          results.errors++;
          results.details.push({
            id: movieId,
            action: 'error',
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: `处理完成: 成功更新 ${results.updated} 部电影`,
        ...results
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        ...results
      };
    }
  }

  static async validateNfoIds(databaseService, log) {
    try {
      const db = databaseService.getDb();
      
      // 检查重复的 NFO ID
      const duplicates = db.prepare(`
        SELECT nfoId, COUNT(*) as count, GROUP_CONCAT(db_id) as movie_ids
        FROM movies 
        WHERE nfoId IS NOT NULL AND nfoId != ''
        GROUP BY LOWER(TRIM(nfoId))
        HAVING COUNT(*) > 1
        ORDER BY count DESC
      `).all();

      // 检查格式异常的 NFO ID
      const allNfoIds = db.prepare(`
        SELECT db_id, fileName, nfoId 
        FROM movies 
        WHERE nfoId IS NOT NULL AND nfoId != ''
      `).all();

      const invalidFormats = [];
      const validPattern = /^[A-Z]{2,5}-\d{3,5}$/i;

      for (const movie of allNfoIds) {
        if (!validPattern.test(movie.nfoId)) {
          invalidFormats.push({
            id: movie.db_id,
            fileName: movie.fileName,
            nfoId: movie.nfoId,
            issue: 'invalid_format'
          });
        }
      }

      return {
        success: true,
        duplicates: duplicates.map(d => ({
          nfoId: d.nfoId,
          count: d.count,
          movieIds: d.movie_ids.split(',').map(id => parseInt(id))
        })),
        invalidFormats,
        summary: {
          totalWithNfoId: allNfoIds.length,
          duplicateGroups: duplicates.length,
          invalidFormats: invalidFormats.length
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = NodeBatchNfoFixer;
