// main_process/services/aiAnalysisService.js

// 不直接导入，而是从主进程传递的模块中获取

let log;
let GoogleGenAI; // 从主进程传递的模块中获取

/**
 * 初始化AI分析服务
 * @param {Object} logger - 日志记录器
 * @param {Object} googleGenAiModule - Google GenAI 模块
 */
function initializeAiAnalysisService(logger, googleGenAiModule = null) {
  log = logger;
  if (googleGenAiModule) {
    // 从模块中解构 GoogleGenAI（正确的导出名称）
    ({ GoogleGenAI } = googleGenAiModule);

    // 验证是否成功获取
    if (GoogleGenAI) {
      log.info('[AI分析服务] GoogleGenAI 模块已注入');
    } else {
      log.error('[AI分析服务] GoogleGenAI 解构失败，模块中可能没有此导出');
    }
  } else {
    log.error('[AI分析服务] GoogleGenAI 模块未提供，AI分析功能将不可用');
  }
  log.info('[AI分析服务] 初始化完成（API密钥将在使用时动态获取）');
}

/**
 * 使用 LLM 分析帖子文本并返回分类标签
 * @param {string} postText - 要分析的帖子正文纯文本
 * @param {string[]} userDefinedCategories - 用户在设置中定义的分类标签数组
 * @param {Object} settings - 用户设置对象
 * @returns {Promise<string[]|null>} - 返回 AI 生成的标签数组，或在失败时返回 null
 */
async function analyzePostWithLLM(postText, userDefinedCategories, settings = null) {
  if (!postText || postText.trim().length === 0) {
    log.info('[AI分析服务] 帖子内容为空，跳过分析');
    return null;
  }

  if (!userDefinedCategories || userDefinedCategories.length === 0) {
    log.warn('[AI分析服务] 用户未定义分类标签，跳过分析');
    return null;
  }

  // 动态获取API密钥和模型
  let apiKey = null;
  let modelName = null;

  if (settings) {
    apiKey = settings.geminiApiKey;
    modelName = settings.geminiModel;
  }

  // 如果设置中没有API密钥，尝试从环境变量获取
  if (!apiKey) {
    apiKey = process.env.API_KEY;
  }

  if (!apiKey) {
    log.warn('[AI分析服务] 未找到Gemini API密钥，请在设置中配置或设置环境变量API_KEY');
    return null;
  }

  if (!modelName || modelName.trim() === '') {
    log.warn('[AI分析服务] 未配置Gemini模型名称，请在设置中指定模型');
    return null;
  }

  try {
    // 检查 GoogleGenAI 是否可用
    if (!GoogleGenAI) {
      log.error('[AI分析服务] GoogleGenAI 模块不可用');
      return null;
    }

    // 每次分析时创建新的GoogleGenAI实例
    // 根据官方文档，设置正确的环境变量
    process.env.GOOGLE_API_KEY = apiKey;

    // 使用正确的初始化方式，传递空对象
    const tempGenAI = new GoogleGenAI({});

    const prompt = `
请扮演一个专业的媒体内容分类专家。你的任务是根据下面提供的"帖子内容"，将其精准地归类到"预设分类列表"中的一个或多个类别。

**规则:**
1. 仔细阅读"帖子内容"。
2. 从"预设分类列表"中选择最贴切的标签。
3. 如果内容符合多个分类，可以选择多个。
4. 如果内容与任何分类都不匹配，请返回一个空数组 []。
5. 你的回答必须是一个严格的 JSON 数组格式的字符串，例如 ["国产精品", "人物合集"]。不要添加任何额外的解释或文本。

**预设分类列表:**
${JSON.stringify(userDefinedCategories)}

**帖子内容:**
"""
${postText.substring(0, 4000)} ${postText.length > 4000 ? '...(内容已截断)' : ''}
"""

**你的回答 (仅 JSON 数组):**
`;

    log.info(`[AI分析服务] 开始分析帖子内容，长度: ${postText.length} 字符`);

    // 使用正确的 API 调用方式
    const result = await tempGenAI.models.generateContent({
      model: modelName,
      contents: prompt,
      config: {
        generationConfig: {
          responseMimeType: "application/json"
        }
      }
    });
    const jsonString = result.text;
    
    log.info(`[AI分析服务] AI原始回复: ${jsonString}`);
    
    // 清理并解析返回的 JSON
    const cleanedJsonString = jsonString.replace(/```json|```/g, '').trim();
    const tags = JSON.parse(cleanedJsonString);

    if (Array.isArray(tags)) {
      log.info(`[AI分析服务] 分析完成，生成标签: ${JSON.stringify(tags)}`);
      return tags;
    } else {
      log.warn(`[AI分析服务] AI返回的不是数组格式: ${cleanedJsonString}`);
      return null;
    }
  } catch (error) {
    log.error(`[AI分析服务] 调用Gemini API失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取默认的分类标签列表
 * @returns {string[]} 默认分类标签数组
 */
function getDefaultCategories() {
  return [
    '国产精品',
    '人物合集', 
    'VR',
    '4K',
    '动画',
    '无码',
    '有码',
    '中文字幕',
    '破解版',
    '高清',
    '素人',
    '制服',
    '丝袜',
    '熟女',
    '萝莉',
    '巨乳',
    '美腿',
    '口交',
    '肛交',
    '群交',
    'SM',
    '户外',
    '偷拍',
    '自拍',
    '直播',
    '网红',
    '明星',
    '欧美',
    '日韩',
    '亚洲',
    '其他'
  ];
}

module.exports = {
  initializeAiAnalysisService,
  analyzePostWithLLM,
  getDefaultCategories
};
