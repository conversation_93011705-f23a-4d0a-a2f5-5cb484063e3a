#!/usr/bin/env node

// test-javdb-version.js - 测试 JavDB Provider 版本和加载情况
const path = require('path');
const os = require('os');

async function testJavdbVersion() {
  console.log('🔍 JavDB Provider 版本检查...\n');

  try {
    // 初始化设置服务
    console.log('初始化设置服务...');
    const settingsService = require('./main_process/services/settingsService');
    const userDataPath = path.join(os.tmpdir(), 'soulforge-test');
    settingsService.initializeSettings(console, userDataPath);
    console.log('✅ 设置服务初始化成功\n');

    // 测试 JavDB Provider 加载
    console.log('加载 JavDB Provider...');
    const javdbProvider = require('./main_process/services/scrapers/javdbProvider');
    console.log(`✅ JavDB Provider 加载成功`);
    console.log(`📦 版本: ${javdbProvider.version}`);
    console.log(`🔧 类型: ${typeof javdbProvider.scrape === 'function' ? 'Function' : 'Unknown'}\n`);

    // 测试 ScraperManager 注册
    console.log('检查 ScraperManager 注册...');
    const scraperManager = require('./main_process/services/scraperManager');
    console.log('✅ ScraperManager 加载成功\n');

    // 检查 Provider 是否正确注册
    console.log('📋 已注册的 Provider:');
    
    // 由于 ScraperManager 在加载时会输出注册信息，我们可以通过检查模块来验证
    const fs = require('fs');
    const scraperManagerPath = './main_process/services/scraperManager.js';
    const scraperManagerContent = fs.readFileSync(scraperManagerPath, 'utf8');
    
    if (scraperManagerContent.includes("'javdb': javdbProvider")) {
      console.log('✅ JavDB Provider 已在 ScraperManager 中注册');
    } else {
      console.log('❌ JavDB Provider 未在 ScraperManager 中注册');
    }

    // 检查优先级设置
    const priorityMatch = scraperManagerContent.match(/PROVIDER_PRIORITY\s*=\s*\[([\s\S]*?)\]/);
    if (priorityMatch) {
      const priorityList = priorityMatch[1].split(',').map(p => p.trim().replace(/['"]/g, ''));
      const javdbIndex = priorityList.indexOf('javdb');
      if (javdbIndex !== -1) {
        console.log(`✅ JavDB Provider 在优先级列表中排第 ${javdbIndex + 1} 位`);
      } else {
        console.log('❌ JavDB Provider 不在优先级列表中');
      }
    }

    // 检查设置服务中的配置
    console.log('\n🔧 检查配置项:');
    const settings = settingsService.getSettings();
    console.log(`javdbBaseUrl: ${settings.javdbBaseUrl || '未设置'}`);
    console.log(`javdbCookie: ${settings.javdbCookie ? '已设置' : '未设置'}`);

    // 检查关键函数是否存在
    console.log('\n🔍 检查关键函数:');
    const javdbProviderPath = './main_process/services/scrapers/javdbProvider.js';
    const javdbProviderContent = fs.readFileSync(javdbProviderPath, 'utf8');
    
    const functions = [
      'connectToUserChrome',
      'checkUserLogin', 
      'promptUserLogin',
      'getMagnetLinksWithSafeClick',
      'extractMagnetLinksFromPage'
    ];

    functions.forEach(funcName => {
      if (javdbProviderContent.includes(`function ${funcName}`) || javdbProviderContent.includes(`${funcName}(`)) {
        console.log(`✅ ${funcName} 函数存在`);
      } else {
        console.log(`❌ ${funcName} 函数缺失`);
      }
    });

    // 检查 CDP 连接相关代码
    console.log('\n🔗 检查 CDP 连接功能:');
    if (javdbProviderContent.includes('chromium.connectOverCDP')) {
      console.log('✅ CDP 连接功能已实现');
    } else {
      console.log('❌ CDP 连接功能缺失');
    }

    if (javdbProviderContent.includes('remote-debugging-port=9222')) {
      console.log('✅ 调试端口配置正确');
    } else {
      console.log('❌ 调试端口配置缺失');
    }

    console.log('\n🎉 JavDB Provider 版本检查完成!');
    console.log('\n💡 使用提示:');
    console.log('1. 启动 Chrome: chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\\temp\\chrome-debug"');
    console.log('2. 在浏览器中登录 https://javdb.com');
    console.log('3. 使用 SoulForge 的刮削功能');

  } catch (error) {
    console.error('💥 检查过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行检查
if (require.main === module) {
  testJavdbVersion().catch(console.error);
}

module.exports = { testJavdbVersion };
