# soul-forge-electron/python_scripts/generate_thumbnails.py
import argparse
import subprocess
import json
import os
import sys
import shlex

def get_video_duration(video_path, ffprobe_path):
    """Gets the duration of a video using ffprobe."""
    command = [
        ffprobe_path,
        '-v', 'error',
        '-show_entries', 'format=duration',
        '-of', 'default=noprint_wrappers=1:nokey=1',
        video_path
    ]
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True, encoding='utf-8')
        return float(result.stdout.strip())
    except subprocess.CalledProcessError as e:
        sys.stderr.write(f"FFprobe error getting duration for {video_path}: {e.stderr}\n")
        return None
    except FileNotFoundError:
        sys.stderr.write(f"FFprobe not found at {ffprobe_path}. Please check the path.\n")
        return None
    except Exception as e:
        sys.stderr.write(f"An unexpected error occurred with <PERSON><PERSON><PERSON> for {video_path}: {str(e)}\n")
        return None

def get_ffmpeg_scale_filter(quality, input_width, input_height):
    """Determines the ffmpeg scale filter based on quality setting."""
    if quality == 'sd_320p':
        return f"scale=320:-2" # Scale to width 320, maintain aspect ratio
    elif quality == 'hd_640p':
        return f"scale=640:-2" # Scale to width 640, maintain aspect ratio
    elif quality == 'fhd_1280p_720h':
        # If input is wider than 16:9, scale to height 720.
        # If input is narrower than 16:9 (e.g. portrait), scale to width 1280.
        # This aims to fit within a 1280x720 box while preserving aspect.
        if input_width / input_height > 16/9: # wider
             return f"scale=-2:720" # scale to height 720
        else: # narrower or 16:9
             return f"scale=1280:-2" # scale to width 1280
    return None # Default or unknown quality

def main():
    parser = argparse.ArgumentParser(description="Generate thumbnails for a video.")
    parser.add_argument("video_file_path", help="Path to the video file.")
    parser.add_argument("video_db_id", help="Database ID of the video, used for subfolder naming.")
    parser.add_argument("snapshot_quality", choices=['sd_320p', 'hd_640p', 'fhd_1280p_720h'], help="Quality of snapshots.")
    parser.add_argument("ffmpeg_path", help="Path to the ffmpeg executable.")
    parser.add_argument("cache_base_path", help="Base path for the thumbnail cache.")
    
    args = parser.parse_args()

    # Determine ffprobe_path based on ffmpeg_path
    ffmpeg_dir = os.path.dirname(args.ffmpeg_path)
    ffprobe_exe_name = 'ffprobe.exe' if sys.platform == 'win32' else 'ffprobe'
    ffprobe_path = os.path.join(ffmpeg_dir, ffprobe_exe_name)

    if not os.path.exists(args.ffmpeg_path):
        sys.stderr.write(f"Error: FFmpeg not found at {args.ffmpeg_path}\n")
        sys.exit(1)
    if not os.path.exists(ffprobe_path):
        sys.stderr.write(f"Error: FFprobe not found at {ffprobe_path} (derived from FFmpeg path)\n")
        sys.exit(1)
    if not os.path.exists(args.video_file_path):
        sys.stderr.write(f"Error: Video file not found at {args.video_file_path}\n")
        sys.exit(1)

    duration = get_video_duration(args.video_file_path, ffprobe_path)
    if duration is None or duration <= 0:
        sys.stderr.write(f"Error: Could not get valid duration for {args.video_file_path}.\n")
        sys.exit(1)

    output_dir = os.path.join(args.cache_base_path, args.video_db_id)
    os.makedirs(output_dir, exist_ok=True)

    num_thumbnails = 10
    interval = duration / (num_thumbnails + 1) # +1 to avoid start/end too closely
    generated_thumbnails = []

    # Get video dimensions for scaling
    input_width, input_height = -1, -1
    try:
        probe_dims_cmd = [
            ffprobe_path,
            '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height',
            '-of', 'csv=s=x:p=0',
            args.video_file_path
        ]
        dims_result = subprocess.run(probe_dims_cmd, capture_output=True, text=True, check=True, encoding='utf-8')
        input_width, input_height = map(int, dims_result.stdout.strip().split('x'))
    except Exception as e:
        sys.stderr.write(f"Warning: Could not get video dimensions for scaling: {str(e)}. Scale filter might be suboptimal.\n")


    for i in range(num_thumbnails):
        timestamp = interval * (i + 1)
        output_filename = f"snapshot_{i+1}.jpg"
        output_path = os.path.join(output_dir, output_filename)
        
        scale_filter_str = get_ffmpeg_scale_filter(args.snapshot_quality, input_width, input_height)
        
        ffmpeg_command = [
            args.ffmpeg_path,
            '-ss', str(timestamp),
            '-i', args.video_file_path,
            '-vframes', '1',
            '-q:v', '3', # Good quality for JPEG
        ]
        if scale_filter_str:
            ffmpeg_command.extend(['-vf', scale_filter_str])
        
        ffmpeg_command.append(output_path)

        try:
            subprocess.run(ffmpeg_command, check=True, capture_output=True, text=True, encoding='utf-8')
            generated_thumbnails.append(os.path.abspath(output_path))
        except subprocess.CalledProcessError as e:
            sys.stderr.write(f"FFmpeg error generating thumbnail for {args.video_file_path} at {timestamp}s: {e.stderr}\n")
        except Exception as e:
            sys.stderr.write(f"Unexpected error during FFmpeg thumbnail generation for {args.video_file_path}: {str(e)}\n")


    print(json.dumps(generated_thumbnails))
    sys.stdout.flush()

if __name__ == "__main__":
    main()