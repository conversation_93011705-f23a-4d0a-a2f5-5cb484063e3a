// 优先在最顶部初始化 electron-log
const log = require('electron-log');
const nodePathModule = require('node:path'); // Renamed to avoid conflict

// 配置 electron-log
try {
    log.transports.file.resolvePathFn = () => nodePathModule.join(require('electron').app.getPath('userData'), 'logs', 'main.log');
    log.transports.file.level = 'info';
    log.transports.console.level = 'info';
    log.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] {text}';

    log.errorHandler.startCatching({ // Updated from log.catchErrors
      showDialog: false, 
      onError: (error) => {
        log.error("electron-log.errorHandler 捕获到未处理的主进程错误:");
        log.error(error); 
      }
    });
    Object.assign(console, log.functions);
    log.info('麟琅秘府主进程 V1.6 (多片库支持) 启动中...'); // Version Update for Multi-Library
    log.info(`初始 __dirname: ${__dirname}`);
    log.info(`初始 process.cwd(): ${process.cwd()}`);
} catch (e) { console.error("致命错误: 初始化 electron-log 或获取 userData 路径失败:", e); }


const { app, BrowserWindow, ipcMain, dialog, shell, session } = require('electron');
log.info('Electron 模块 (app, BrowserWindow 等) 已成功加载。');

const path = require('node:path'); // Node.js path module
const http = require('http');
const { spawn } = require('child_process');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

let googleGenAiModule;
try {
  googleGenAiModule = require('@google/genai');
  log.info('@google/genai 已成功加载并暂存。');
} catch (e) {
  log.warn('@google/genai 模块加载失败，AI 功能可能受限:', e.message);
  googleGenAiModule = null;
}

// 加载自定义服务和处理器模块
const settingsService = require('./main_process/services/settingsService');
const databaseService = require('./main_process/services/databaseService');
const pythonRunnerService = require('./main_process/services/pythonRunnerService');
const fileUtils = require('./main_process/utils/fileUtils');
const httpClient = require('./main_process/utils/httpClient');
const snapshotUtils = require('./main_process/utils/snapshotUtils');
const aiService = require('./main_process/services/aiService');
const avatarFileTreeService = require('./main_process/services/avatarFileTreeService');
const scanHandler = require('./main_process/handlers/scanHandler');
const nfoPlotPolisherService = require('./main_process/services/nfoPlotPolisherService');
const collectorService = require('./main_process/services/collectorService');

log.info('自定义服务和处理器模块已加载。');

const isDev = !app.isPackaged;
log.info(`是否为开发模式 (isDev using !app.isPackaged): ${isDev}`);

let userDataPath, dbPath, defaultThumbnailCacheBasePathInternal;

try {
    userDataPath = app.getPath('userData');
