import { promises as fs } from 'fs';
import path from 'path';
import { prisma } from '@/lib/db';
import { NFOIdExtractor } from './nfo-id-extractor';
import { NFOParser } from './nfo-parser';

export interface ScanOptions {
  libraryPath: string;
  libraryId: string;
  recursive?: boolean;
  updateExisting?: boolean;
  extractTechnicalInfo?: boolean;
}

export interface ScanResult {
  success: boolean;
  processed: number;
  added: number;
  updated: number;
  skipped: number;
  errors: number;
  errorDetails: string[];
}

export class MovieScanner {
  private static readonly VIDEO_EXTENSIONS = [
    '.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v',
    '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.m2ts', '.mts'
  ];

  private static readonly IMAGE_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'
  ];

  /**
   * Scan a library directory for movies
   */
  static async scanLibrary(options: ScanOptions): Promise<ScanResult> {
    const result: ScanResult = {
      success: false,
      processed: 0,
      added: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
      errorDetails: [],
    };

    try {
      console.log(`Starting scan of library: ${options.libraryPath}`);

      // Verify library path exists
      try {
        await fs.access(options.libraryPath);
      } catch {
        throw new Error(`Library path does not exist: ${options.libraryPath}`);
      }

      // Find all video files
      const videoFiles = await this.findVideoFiles(options.libraryPath, options.recursive);
      console.log(`Found ${videoFiles.length} video files`);

      // Process each video file
      for (const videoFile of videoFiles) {
        result.processed++;
        
        try {
          const movieData = await this.processVideoFile(videoFile, options);
          
          if (movieData) {
            // Check if movie already exists
            const existingMovie = await prisma.movie.findFirst({
              where: { filePath: videoFile },
            });

            if (existingMovie) {
              if (options.updateExisting) {
                await prisma.movie.update({
                  where: { id: existingMovie.id },
                  data: movieData,
                });
                result.updated++;
              } else {
                result.skipped++;
              }
            } else {
              // Create new movie
              await prisma.movie.create({
                data: {
                  ...movieData,
                  libraryLinks: {
                    create: {
                      libraryId: options.libraryId,
                    },
                  },
                },
              });
              result.added++;
            }
          } else {
            result.skipped++;
          }
        } catch (error) {
          result.errors++;
          const errorMsg = `Error processing ${videoFile}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errorDetails.push(errorMsg);
          console.error(errorMsg);
        }
      }

      result.success = true;
      console.log(`Scan completed: ${result.added} added, ${result.updated} updated, ${result.skipped} skipped, ${result.errors} errors`);

    } catch (error) {
      result.success = false;
      const errorMsg = `Scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errorDetails.push(errorMsg);
      console.error(errorMsg);
    }

    return result;
  }

  /**
   * Find all video files in a directory
   */
  private static async findVideoFiles(dirPath: string, recursive = true): Promise<string[]> {
    const videoFiles: string[] = [];

    const processDirectory = async (currentPath: string) => {
      try {
        const entries = await fs.readdir(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);

          if (entry.isDirectory() && recursive) {
            await processDirectory(fullPath);
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (this.VIDEO_EXTENSIONS.includes(ext)) {
              videoFiles.push(fullPath);
            }
          }
        }
      } catch (error) {
        console.error(`Error reading directory ${currentPath}:`, error);
      }
    };

    await processDirectory(dirPath);
    return videoFiles;
  }

  /**
   * Process a single video file and extract metadata
   */
  private static async processVideoFile(filePath: string, options: ScanOptions): Promise<any | null> {
    try {
      const fileName = path.basename(filePath);
      const fileDir = path.dirname(filePath);
      
      // Get file stats
      const stats = await fs.stat(filePath);
      
      // Initialize movie data
      const movieData: any = {
        filePath,
        fileName,
        fileSize: stats.size,
        lastScanned: new Date(),
        title: path.basename(fileName, path.extname(fileName)), // Default title
      };

      // Extract NFO ID
      const nfoIdResult = await NFOIdExtractor.extractNfoId(filePath);
      if (nfoIdResult.nfoId) {
        movieData.nfoId = nfoIdResult.nfoId;
      }

      // Try to parse NFO file for additional metadata
      const nfoPath = path.join(fileDir, `${path.basename(fileName, path.extname(fileName))}.nfo`);
      try {
        await fs.access(nfoPath);
        const nfoData = await NFOParser.parseNFOFile(nfoPath);
        
        // Merge NFO data
        Object.assign(movieData, {
          title: nfoData.title || movieData.title,
          originalTitle: nfoData.originalTitle,
          year: nfoData.year,
          releaseDate: nfoData.releaseDate,
          runtime: nfoData.runtime,
          plot: nfoData.plot,
          studio: nfoData.studio,
          director: nfoData.director,
          genres: nfoData.genres ? JSON.stringify(nfoData.genres) : null,
          actors: nfoData.actors ? JSON.stringify(nfoData.actors) : null,
          tags: nfoData.tags ? JSON.stringify(nfoData.tags) : null,
          posterUrl: nfoData.posterUrl,
          nfoLastModified: stats.mtime,
        });

        // Override nfoId from NFO if available
        if (nfoData.id) {
          movieData.nfoId = nfoData.id;
        }
      } catch {
        // NFO file doesn't exist or couldn't be parsed, continue with extracted data
      }

      // Find local cover image
      const coverPath = await this.findLocalCover(fileDir, fileName);
      if (coverPath) {
        movieData.localCoverPath = coverPath;
      }

      // Extract technical information if requested
      if (options.extractTechnicalInfo) {
        const techInfo = await this.extractTechnicalInfo(filePath);
        Object.assign(movieData, techInfo);
      }

      // Extract CD part info
      const cdPartInfo = this.extractCdPartInfo(fileName);
      if (cdPartInfo) {
        movieData.cdPartInfo = cdPartInfo;
      }

      return movieData;
    } catch (error) {
      console.error(`Error processing video file ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Find local cover image for a video file
   */
  private static async findLocalCover(videoDir: string, videoFileName: string): Promise<string | null> {
    const baseName = path.basename(videoFileName, path.extname(videoFileName));
    
    // Try specific cover names first
    const specificNames = [baseName, `${baseName}-poster`, `${baseName}-cover`];
    
    for (const name of specificNames) {
      for (const ext of this.IMAGE_EXTENSIONS) {
        const coverPath = path.join(videoDir, `${name}${ext}`);
        try {
          await fs.access(coverPath);
          return coverPath;
        } catch {
          // Continue to next option
        }
      }
    }

    // Try generic names
    const genericNames = ['poster', 'folder', 'cover', 'thumb', 'movie'];
    
    for (const name of genericNames) {
      for (const ext of this.IMAGE_EXTENSIONS) {
        const coverPath = path.join(videoDir, `${name}${ext}`);
        try {
          await fs.access(coverPath);
          return coverPath;
        } catch {
          // Continue to next option
        }
      }
    }

    return null;
  }

  /**
   * Extract CD part information from filename
   */
  private static extractCdPartInfo(fileName: string): string | null {
    const patterns = [
      /(?:CD|DISK|DISC|PART|PT)[\s._-]*([A-D1-9])\b/i,
      /CD_(\d+)/i,
      /DISC(\d)/i,
      /FILE[\s._-]*(\d+)/i,
      /(\d+)[\s._-]*OF[\s._-]*(\d+)/i,
      /[\s._-]([A-D])$/i,
      /[\s._-](\d)$/i,
    ];

    const baseName = fileName.replace(/\.[^.]+$/, '').toUpperCase();

    for (const pattern of patterns) {
      const match = baseName.match(pattern);
      if (match) {
        if (pattern.source.includes('OF')) {
          return `Part${match[1]}_Total${match[2]}`;
        } else {
          return `Part${match[1]}`;
        }
      }
    }

    return null;
  }

  /**
   * Extract technical information using ffprobe (placeholder)
   */
  private static async extractTechnicalInfo(filePath: string): Promise<any> {
    // This would use ffprobe or similar tool to extract technical info
    // For now, return empty object
    return {
      resolution: null,
      videoHeight: null,
      videoCodec: null,
      audioCodec: null,
      fps: null,
      videoBitrate: null,
      audioChannelsDesc: null,
      audioSampleRate: null,
      audioBitrate: null,
    };
  }
}
