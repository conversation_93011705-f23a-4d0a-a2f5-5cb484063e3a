// soul-forge-electron/src/components/modals/RecommendationsModal.tsx
import React from 'react';
import { Movie } from '../../types';
import MovieCard from '../MovieCard'; 
import { LuMessageSquare } from 'react-icons/lu';

interface RecommendationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  movies: Movie[];
  isLoading: boolean;
  appDefaultCover?: string | null;
  onMovieClick: (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => void;
  onFormatForLinLuo: () => void;
  linLuoFormattedMessage: string | null;
  isFormatting: boolean;
  onSendToLinLuo: (message: string) => void; 
}

const RecommendationsModal: React.FC<RecommendationsModalProps> = ({
  isOpen,
  onClose,
  movies,
  isLoading,
  appDefaultCover,
  onMovieClick,
  onFormatForLinLuo,
  linLuoFormattedMessage,
  isFormatting,
  onSendToLinLuo, 
}) => {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/70 backdrop-blur-md z-[70] flex items-center justify-center p-4"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="recommendations-title"
    >
      <div 
        className="bg-neutral-800 text-neutral-100 rounded-lg shadow-xl w-full max-w-3xl max-h-[85vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <header className="p-4 border-b border-neutral-700 flex justify-between items-center flex-shrink-0">
          <h2 id="recommendations-title" className="text-xl font-semibold text-amber-400">为你推荐</h2>
          <button onClick={onClose} className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-neutral-700 transition-colors" aria-label="关闭推荐">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </header>
        <main className="p-4 overflow-y-auto flex-grow settings-scroll-container">
          {isLoading && <p className="text-center text-neutral-300">正在加载推荐...</p>}
          {!isLoading && (!movies || movies.length === 0) && <p className="text-center text-neutral-400">暂无推荐。</p>}
          {!isLoading && movies && movies.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {movies.map(movie => (
                <MovieCard key={movie.db_id || movie.filePath} movie={movie} onCardClick={onMovieClick} appDefaultCover={appDefaultCover} />
              ))}
            </div>
          )}
          {linLuoFormattedMessage && (
            <div className="mt-4 p-3 bg-neutral-700 rounded">
              <h4 className="text-sm font-semibold mb-1 text-pink-400">林珞的私语:</h4>
              <pre className="text-xs whitespace-pre-wrap text-neutral-200">{linLuoFormattedMessage}</pre>
              <button
                  onClick={() => onSendToLinLuo(linLuoFormattedMessage)}
                  className="button-primary-app text-xs mt-2 !py-1 !px-2.5 flex items-center"
                  disabled={isFormatting} // isFormatting could also mean LinLuo is busy, use appropriate state
                  title="将此推荐语发送到林珞聊天"
                >
                  <LuMessageSquare size={12} className="mr-1"/> 发送给林珞姐姐
                </button>
            </div>
          )}
        </main>
        <footer className="p-4 border-t border-neutral-700 flex justify-end space-x-2 flex-shrink-0">
          {!linLuoFormattedMessage && (
            <button
                onClick={onFormatForLinLuo}
                disabled={isFormatting || !movies || movies.length === 0}
                className="button-secondary-app text-sm"
            >
                {isFormatting ? "格式化中..." : "让林珞姐姐说说"}
            </button>
          )}
          <button onClick={onClose} className="button-primary-app text-sm">关闭</button>
        </footer>
      </div>
    </div>
  );
};

export default RecommendationsModal;