// soul-forge-electron/src/hooks/useMovieDetailManager.ts
import { useState, useEffect, useCallback } from 'react';
import {
  Movie, AppSettings, SnapshotInfo, FavoriteItemType, ActorAvatarInfoResult,
  AiGeneratePlotParams, AiGeneratePlotResult, AiEmbellishPlotParams, AiEmbellishPlotResult,
  AiAnalyzeTagsParams, AiAnalyzeTagsResult, AiSuggestCoverParams, AiSuggestCoverResult,
  UnifiedVersion
} from '../types';
import { movieToEditableState, EditableMovieState } from '../components/detail_modal_sections/utils';

export interface UseMovieDetailManagerProps {
  initialMovie: Movie | null;
  onUpdateMovieCallback: (updatedMovie: Movie) => void; // This callback updates the main list
  appSettings: AppSettings;
}

export function useMovieDetailManager({
  initialMovie,
  onUpdateMovieCallback,
  appSettings,
}: UseMovieDetailManagerProps) {
  const [movie, setMovie] = useState<Movie | null>(initialMovie);
  const [isEditing, setIsEditing] = useState(false);
  const [editableMovie, setEditableMovie] = useState<EditableMovieState | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const [isCoverActionLoading, setIsCoverActionLoading] = useState(false);
  const [isTrailerPlayerOpen, setIsTrailerPlayerOpen] = useState(false);
  const [trailerPlayerUrl, setTrailerPlayerUrl] = useState<string | null>(null);
  
  const [snapshots, setSnapshots] = useState<SnapshotInfo[]>([]);
  const [isLoadingSnapshots, setIsLoadingSnapshots] = useState(false);
  const [showSnapshotsSection, setShowSnapshotsSection] = useState(true); // 默认展开快照区域
  const [aiSuggestedSnapshotIndex, setAiSuggestedSnapshotIndex] = useState<number | null>(null);

  // 【新增】分组快照状态管理
  const [groupedSnapshots, setGroupedSnapshots] = useState<Array<{
    versionInfo: {
      db_id: number;
      filePath: string;
      fileName: string;
      fileSize?: number;
      resolution?: string;
    };
    snapshots: SnapshotInfo[];
  }>>([]);
  const [isLoadingGroupedSnapshots, setIsLoadingGroupedSnapshots] = useState(false);
  const [isGeneratingSnapshotsForVersion, setIsGeneratingSnapshotsForVersion] = useState(false);

  const [favoriteStatus, setFavoriteStatus] = useState<Record<FavoriteItemType, Record<string, boolean>>>({ director: {}, studio: {}, series: {}, actress: {}, tag: {}, genre: {}, movie_nfo_id: {} });

  // NFO导出相关状态
  const [isExportingNfo, setIsExportingNfo] = useState(false);
  const [nfoExportResult, setNfoExportResult] = useState<{
    success: boolean;
    message: string;
    timestamp: number;
  } | null>(null);
  const [isCoverPreviewOpen, setIsCoverPreviewOpen] = useState(false);
  const [coverPreviewUrl, setCoverPreviewUrl] = useState<string | null>(null);
  
  const [actorAvatars, setActorAvatars] = useState<Record<string, string | null>>({});
  const [isPreviewAvatarModalOpen, setIsPreviewAvatarModalOpen] = useState(false);
  const [previewAvatarUrl, setPreviewAvatarUrl] = useState<string | null>(null);

  // 版本管理相关状态
  const [allVersions, setAllVersions] = useState<UnifiedVersion[]>([]);
  const [isLoadingVersions, setIsLoadingVersions] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<UnifiedVersion | null>(null);

  const [isGeneratingPlot, setIsGeneratingPlot] = useState(false);
  const [isEmbellishingPlot, setIsEmbellishingPlot] = useState(false);
  const [isAnalyzingTags, setIsAnalyzingTags] = useState(false);

  const [aiGeneratedPlotDisplay, setAiGeneratedPlotDisplay] = useState<string | null>(null);
  const [aiEmbellishedPlotDisplay, setAiEmbellishedPlotDisplay] = useState<string | null>(null);


  useEffect(() => {
    // When initialMovie prop changes (new movie selected for detail view)
    // Reset local movie state, including any session-specific overrides like sessionAssignedCover.
    // Determine initial coverDataUrl: prefer derived from localCoverPath (will be null here, fetched by main.js),
    // then posterUrl, then coverUrl. If all are null, ImageWithFallback handles it.
    // sessionAssignedCover must be reset.
    let baseCoverDataUrl = initialMovie?.posterUrl || initialMovie?.coverUrl || null;
    if (initialMovie?.localCoverPath) { // If local path exists, main process will generate dataURL
        baseCoverDataUrl = undefined; // Signal to use system default or fetch logic
    }

    const movieToSet = initialMovie 
      ? { 
          ...initialMovie, 
          sessionAssignedCover: undefined, 
          coverDataUrl: baseCoverDataUrl // Reset coverDataUrl based on original sources
        } 
      : null;
                                                                                                                        
    setMovie(movieToSet);
    setEditableMovie(movieToSet ? movieToEditableState(movieToSet) : null);
    setSnapshots([]);
    setIsLoadingSnapshots(false);
    setAiSuggestedSnapshotIndex(null);
    setShowSnapshotsSection(true);
    setAiGeneratedPlotDisplay(null); 
    setAiEmbellishedPlotDisplay(null);
    
    if (initialMovie) {
      const itemsToCheck: { type: FavoriteItemType; value: string }[] = [];
      if (initialMovie.director) itemsToCheck.push({ type: 'director', value: initialMovie.director });
      if (initialMovie.studio) itemsToCheck.push({ type: 'studio', value: initialMovie.studio });
      if (initialMovie.series) itemsToCheck.push({ type: 'series', value: initialMovie.series });
      initialMovie.actors?.forEach(actor => itemsToCheck.push({ type: 'actress', value: actor }));
      initialMovie.genres?.forEach(genre => itemsToCheck.push({ type: 'genre', value: genre }));
      initialMovie.tags?.forEach(tag => itemsToCheck.push({ type: 'tag', value: tag }));
      
      const fetchStatuses = async () => {
        // 准备所有需要检查的项目，包括影片NFO ID
        const allItemsToCheck = [...itemsToCheck];
        if (initialMovie.nfoId) {
          allItemsToCheck.push({ type: 'movie_nfo_id' as FavoriteItemType, value: initialMovie.nfoId });
        }

        // 如果没有项目需要检查，直接返回
        if (allItemsToCheck.length === 0) {
          setFavoriteStatus({ director: {}, studio: {}, series: {}, actress: {}, tag: {}, genre: {}, movie_nfo_id: {} });
          return;
        }

        try {
          // 使用批量查询API
          const batchResult = await window.sfeElectronAPI.batchCheckFavorites(allItemsToCheck);
          if (batchResult && batchResult.success) {
            // 确保所有类型都有初始化的对象
            const newFavoriteStatus: Record<FavoriteItemType, Record<string, boolean>> = {
              director: {}, studio: {}, series: {}, actress: {}, tag: {}, genre: {}, movie_nfo_id: {}
            };

            // 合并批量查询结果
            if (batchResult.favoriteStatus) {
              Object.keys(batchResult.favoriteStatus).forEach(type => {
                if (newFavoriteStatus[type as FavoriteItemType]) {
                  Object.assign(newFavoriteStatus[type as FavoriteItemType], batchResult.favoriteStatus[type]);
                }
              });
            }

            setFavoriteStatus(newFavoriteStatus);
          } else {
            console.error("批量检查收藏状态失败:", batchResult?.error);
            // 降级到逐个查询
            await fetchStatusesIndividually();
          }
        } catch (e) {
          console.error("批量检查收藏状态时发生错误:", e);
          // 降级到逐个查询
          await fetchStatusesIndividually();
        }
      };

      // 降级方案：逐个查询
      const fetchStatusesIndividually = async () => {
        const newFavoriteStatus: Record<FavoriteItemType, Record<string, boolean>> = { director: {}, studio: {}, series: {}, actress: {}, tag: {}, genre: {}, movie_nfo_id: {} };
        for (const item of itemsToCheck) {
          try {
            const favResult = await window.sfeElectronAPI.isFavorite(item.type, item.value);
            if (favResult.success) {
              if (!newFavoriteStatus[item.type]) newFavoriteStatus[item.type] = {};
              newFavoriteStatus[item.type][item.value] = favResult.isFavorite;
            }
          } catch (e) { console.error("Error fetching favorite status for", item, e); }
        }
        if (initialMovie.nfoId) {
            try {
                const favResult = await window.sfeElectronAPI.isFavorite('movie_nfo_id', initialMovie.nfoId);
                if (favResult.success) {
                    if (!newFavoriteStatus.movie_nfo_id) newFavoriteStatus.movie_nfo_id = {};
                    newFavoriteStatus.movie_nfo_id[initialMovie.nfoId] = favResult.isFavorite;
                }
            } catch (e) { console.error("Error fetching favorite status for movie NFO ID", initialMovie.nfoId, e); }
        }
        setFavoriteStatus(newFavoriteStatus);
      };
      fetchStatuses();
      
      if (initialMovie.actors && initialMovie.actors.length > 0) {
        const fetchAvatars = async () => {
          const newAvatars: Record<string, string | null> = {};
          for (const actorName of initialMovie.actors!) {
            try {
              const result: ActorAvatarInfoResult = await window.sfeElectronAPI.getActorAvatarDetails(actorName);
              newAvatars[actorName] = result.success ? (result.dataUrl || result.avatarUrl || null) : null;
            } catch (e) { newAvatars[actorName] = null; }
          }
          setActorAvatars(newAvatars);
        };
        fetchAvatars();
      } else {
        setActorAvatars({});
      }
    } else {
       setEditableMovie(null);
       setFavoriteStatus({ director: {}, studio: {}, series: {}, actress: {}, tag: {}, genre: {}, movie_nfo_id: {} });
       setActorAvatars({});
       setAiGeneratedPlotDisplay(null);
       setAiEmbellishedPlotDisplay(null);
    }
  }, [initialMovie?.nfoId, initialMovie?.db_id]);

  // 获取统一版本数据的 useEffect
  useEffect(() => {
    const fetchVersions = async () => {
      if (!initialMovie?.nfoId) {
        setAllVersions([]);
        setCurrentVersion(null);
        return;
      }

      setIsLoadingVersions(true);
      try {
        const versions = await window.sfeElectronAPI.getUnifiedVersions(initialMovie.nfoId);
        setAllVersions(versions);

        // 设置当前版本：优先选择与 initialMovie 匹配的本地版本
        const currentLocalVersion = versions.find(v =>
          v.type === 'local' && v.db_id === initialMovie.db_id
        );

        if (currentLocalVersion) {
          setCurrentVersion(currentLocalVersion);
        } else if (versions.length > 0) {
          // 如果没有找到匹配的本地版本，选择第一个本地版本，或者第一个版本
          const firstLocalVersion = versions.find(v => v.type === 'local');
          setCurrentVersion(firstLocalVersion || versions[0]);
        } else {
          setCurrentVersion(null);
        }
      } catch (error) {
        console.error('获取版本数据失败:', error);
        setAllVersions([]);
        setCurrentVersion(null);
      } finally {
        setIsLoadingVersions(false);
      }
    };

    fetchVersions();
  }, [initialMovie?.nfoId, initialMovie?.db_id]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (!editableMovie) return;
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    let processedValue: any = value;
    if (type === 'checkbox') {
        processedValue = checked;
    } else if (type === 'number' && name !== 'personalRating') { 
        processedValue = value === '' ? null : Number(value);
    } else if (name === 'personalRating') { 
        processedValue = value === '' || value === 'null' || value === '0' ? null : Number(value);
    }

    setEditableMovie(prev => ({ ...prev!, [name]: processedValue }));
  }, [editableMovie]);

  const handleSaveChanges = useCallback(async () => {
    if (!editableMovie || !movie?.filePath) return;
    setIsSaving(true);
    try {
      const movieDataToSave: Partial<Movie> = {
        ...editableMovie,
        year: editableMovie.year ? parseInt(editableMovie.year, 10) : null,
        runtime: editableMovie.runtime ? parseInt(editableMovie.runtime, 10) : null,
        actors: editableMovie.actors.split(',').map(s => s.trim()).filter(Boolean),
        genres: editableMovie.genres.split(',').map(s => s.trim()).filter(Boolean),
        tags: editableMovie.tags.split(',').map(s => s.trim()).filter(Boolean),
        aiAnalyzedTags: editableMovie.aiAnalyzedTags.split(',').map(s => s.trim()).filter(Boolean),
      };
      delete (movieDataToSave as any).sessionAssignedCover; // Ensure temporary property is not saved

      const result = await window.sfeElectronAPI.saveNfoData(movie.filePath, movieDataToSave);
      if (result.success && result.updatedMovie) {
        // The result.updatedMovie is clean from DB/NFO.
        // Preserve sessionAssignedCover if it was set on the local 'movie' state.
        const finalMovieForDisplay = { 
            ...result.updatedMovie, 
            sessionAssignedCover: movie.sessionAssignedCover,
            // If sessionAssignedCover is active, ensure coverDataUrl reflects it for UI
            coverDataUrl: movie.sessionAssignedCover || result.updatedMovie.coverDataUrl 
        };
        onUpdateMovieCallback(result.updatedMovie); // Send the clean movie to parent
        setMovie(finalMovieForDisplay); // Update local state, preserving session cover if any
        setIsEditing(false);
      } else {
        alert(`保存失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) {
      alert(`保存时发生前端错误: ${e.message}`);
    } finally {
      setIsSaving(false);
    }
  }, [editableMovie, movie, onUpdateMovieCallback]);

  const handlePlayVideo = useCallback(() => {
    if (movie?.filePath) {
      window.sfeElectronAPI.playVideo({ filePath: movie.filePath, title: movie.title || movie.fileName, strmUrl: movie.strmUrl });
    }
  }, [movie]);

  const handleOpenTrailer = useCallback(() => {
    const url = isEditing ? editableMovie?.trailerUrl : movie?.trailerUrl;
    if (url) {
      setTrailerPlayerUrl(url);
      setIsTrailerPlayerOpen(true);
    }
  }, [isEditing, editableMovie, movie]);
  
  const handleCloseTrailerPlayer = useCallback(() => {
    setIsTrailerPlayerOpen(false);
    setTrailerPlayerUrl(null);
  }, []);

  const handleDownloadCover = useCallback(async () => {
    if (!editableMovie?.posterUrl || !movie?.filePath) return;
    setIsCoverActionLoading(true);
    try {
      const result = await window.sfeElectronAPI.downloadNfoCover(movie.filePath, editableMovie.posterUrl, movie.fileName.split('.')[0] + "-poster");
      if (result.success && result.coverDataUrl && result.newCoverPath) {
        const updatedMovieData = { 
          ...movie, 
          coverDataUrl: result.coverDataUrl, 
          localCoverPath: result.newCoverPath, 
          sessionAssignedCover: undefined // Clear session override as a permanent cover is set
        };
        onUpdateMovieCallback({ ...updatedMovieData, coverDataUrl: result.coverDataUrl, localCoverPath: result.newCoverPath }); // Send clean data to parent
        setMovie(updatedMovieData); 
        if (isEditing) setEditableMovie(movieToEditableState(updatedMovieData));
      } else {
        alert(`下载封面失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) { alert(`下载封面时发生错误: ${e.message}`); }
    finally { setIsCoverActionLoading(false); }
  }, [editableMovie, movie, onUpdateMovieCallback, isEditing]);

  const handleBrowseLocalCover = useCallback(async () => {
    if (!movie?.filePath) return;
    setIsCoverActionLoading(true);
    try {
      const result = await window.sfeElectronAPI.browseLocalCover(movie.filePath, movie.fileName.split('.')[0] + "-poster");
      if (result.success && result.coverDataUrl && result.newCoverPath) {
        const updatedMovieData = { 
          ...movie, 
          coverDataUrl: result.coverDataUrl, 
          localCoverPath: result.newCoverPath, 
          sessionAssignedCover: undefined 
        };
        onUpdateMovieCallback({ ...updatedMovieData, coverDataUrl: result.coverDataUrl, localCoverPath: result.newCoverPath });
        setMovie(updatedMovieData); 
        if (isEditing) setEditableMovie(movieToEditableState(updatedMovieData));
      } else {
        alert(`浏览本地封面失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) { alert(`浏览本地封面时发生错误: ${e.message}`); }
    finally { setIsCoverActionLoading(false); }
  }, [movie, onUpdateMovieCallback, isEditing]);
  
  const handleSetSnapshotAsCover = useCallback(async (snapshotFilePath: string) => {
    if (!movie) return; 
    setIsCoverActionLoading(true);
    try {
      const dataUrl = await window.sfeElectronAPI.imagePathToDataUrl(snapshotFilePath);
      if (dataUrl) {
        const movieWithTemporaryCover = { 
          ...movie, 
          sessionAssignedCover: dataUrl, 
          coverDataUrl: dataUrl         
        };
        setMovie(movieWithTemporaryCover);

        if (isEditing && editableMovie) {
          setEditableMovie(prev => prev ? { 
            ...prev, 
            sessionAssignedCover: dataUrl, 
            coverDataUrl: dataUrl 
          } : null);
        }
      } else {
        alert("无法将快照设为封面：无法生成DataURL。");
      }
    } catch (error: any) {
      alert(`设置快照封面时出错: ${error.message}`);
    } finally {
      setIsCoverActionLoading(false);
    }
  }, [movie, isEditing, editableMovie]);

  const handleToggleFavorite = useCallback(async (type: FavoriteItemType, value: string) => {
    const result = await window.sfeElectronAPI.toggleFavorite(type, value);
    if (result.success) {
      setFavoriteStatus(prev => ({
        ...prev,
        [type]: { ...prev[type], [value]: result.isFavorite }
      }));
    } else {
      alert(`更新收藏状态失败: ${result.error}`);
    }
  }, []);

  const handleOpenCoverPreview = useCallback(() => {
    const currentCover = movie?.sessionAssignedCover || movie?.coverDataUrl || movie?.posterUrl || movie?.coverUrl || appSettings.customDefaultCoverDataUrl;
    if (currentCover) {
      setCoverPreviewUrl(currentCover);
      setIsCoverPreviewOpen(true);
    }
  }, [movie, appSettings.customDefaultCoverDataUrl]);
  
  const handleCloseCoverPreview = useCallback(() => setIsCoverPreviewOpen(false), []);

  const handleOpenAvatarPreview = useCallback((actorName: string) => {
    const url = actorAvatars[actorName] || appSettings.defaultActorAvatarDataUrl;
    if (url) {
      setPreviewAvatarUrl(url);
      setIsPreviewAvatarModalOpen(true);
    }
  }, [actorAvatars, appSettings.defaultActorAvatarDataUrl]);

  const handleCloseAvatarPreview = useCallback(() => setIsPreviewAvatarModalOpen(false), []);

  const generatePlot = useCallback(async () => {
    if (!movie) return;
    setIsGeneratingPlot(true);
    setAiGeneratedPlotDisplay("AI 正在努力思考中，请稍候...");
    try {
      const params: AiGeneratePlotParams = { title: movie.title, year: movie.year };
      const result: AiGeneratePlotResult = await window.sfeElectronAPI.generatePlotSummary(params);
      if (result.success && result.summary) {
        setAiGeneratedPlotDisplay(result.summary);
      } else {
        setAiGeneratedPlotDisplay(`AI 生成剧情失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) { setAiGeneratedPlotDisplay(`AI 生成剧情时发生前端错误: ${e.message}`); }
    finally { setIsGeneratingPlot(false); }
  }, [movie]);

  const embellishPlot = useCallback(async () => {
    if (!movie || !movie.db_id) return;
    setIsEmbellishingPlot(true);
    setAiEmbellishedPlotDisplay("林珞姐姐正在精心润色中，请稍候...");
    try {
      const params: AiEmbellishPlotParams = { 
        db_id: movie.db_id,
        title: movie.title,
        originalTitle: movie.originalTitle,
        currentPlot: movie.plot,
        plotJa: movie.plotJa,
        plotZh: movie.plotZh,
        year: movie.year,
        genres: movie.genres,
        actors: movie.actors,
        tags: movie.tags,
        aiAnalyzedTags: movie.aiAnalyzedTags,
      };
      const result: AiEmbellishPlotResult = await window.sfeElectronAPI.embellishPlotWithAI(params);
      if (result.success && result.embellishedPlot) {
        setAiEmbellishedPlotDisplay(result.embellishedPlot);
      } else {
        setAiEmbellishedPlotDisplay(`林珞姐姐润色剧情失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) { setAiEmbellishedPlotDisplay(`林珞姐姐润色剧情时发生前端错误: ${e.message}`); }
    finally { setIsEmbellishingPlot(false); }
  }, [movie]);

  const analyzeTags = useCallback(async () => {
    if (!movie || movie.db_id === undefined) return;
    setIsAnalyzingTags(true);
    try {
      const params: AiAnalyzeTagsParams = {
        db_id: movie.db_id,
        title: movie.title,
        plot: movie.plot,
        year: movie.year,
        genres: movie.genres,
        currentTags: movie.tags,
        currentAiTags: movie.aiAnalyzedTags,
      };
      const result: AiAnalyzeTagsResult = await window.sfeElectronAPI.analyzeMovieTagsWithAI(params);
      if (result.success && result.analyzedTags) {
        const updatedMovieFields = { aiAnalyzedTags: result.analyzedTags };
        const movieAfterTagAnalysis = { ...movie, ...updatedMovieFields };
        onUpdateMovieCallback(movieAfterTagAnalysis); // Update parent list
        setMovie(movieAfterTagAnalysis); // Update local state for modal
        if (isEditing) setEditableMovie(movieToEditableState(movieAfterTagAnalysis));
        alert("AI 标签分析完成并已更新。");
      } else {
        alert(`AI 分析标签失败: ${result.error || '未知错误'}`);
      }
    } catch (e: any) { alert(`AI 分析标签时发生前端错误: ${e.message}`); }
    finally { setIsAnalyzingTags(false); }
  }, [movie, onUpdateMovieCallback, isEditing]);
  
  const suggestCoverFromSnapshots = useCallback(async () => {
    if (!movie || movie.db_id === undefined || snapshots.length === 0) return;
    setIsAnalyzingTags(true); // Reuse loading state
    try {
      const params: AiSuggestCoverParams = { movieTitle: movie.title, movieDbId: movie.db_id, numberOfSnapshots: snapshots.length };
      const result: AiSuggestCoverResult = await window.sfeElectronAPI.suggestCoverFromSnapshotsWithAI(params);
      if (result.success && result.suggestedIndex !== undefined) {
        setAiSuggestedSnapshotIndex(result.suggestedIndex);
        alert(`AI 建议使用第 ${result.suggestedIndex + 1} 张快照作为封面。`);
      } else {
        alert(`AI 推荐封面失败: ${result.error || '未知错误'}`);
      }
    } catch (e:any) { alert(`AI 推荐封面时发生前端错误: ${e.message}`); }
    finally { setIsAnalyzingTags(false); }
  }, [movie, snapshots]);

  // 内部快照获取函数
  const internalFetchSnapshots = useCallback(async (movieDbId: number, forceGenerate: boolean = false) => {
    try {
      setIsLoadingSnapshots(true);

      if (forceGenerate) {
        // 生成新快照需要视频文件路径，这里暂时跳过
        console.warn('internalFetchSnapshots: forceGenerate=true 需要视频文件路径，暂时跳过');
        setSnapshots([]);
      } else {
        // 获取已存在的快照
        const result = await window.sfeElectronAPI.getExistingSnapshots({ movieDbId });
        if (result.success && result.snapshots) {
          setSnapshots(result.snapshots);
        } else {
          setSnapshots([]);
        }
      }
    } catch (error) {
      console.error('获取快照失败:', error);
      setSnapshots([]);
    } finally {
      setIsLoadingSnapshots(false);
    }
  }, [appSettings.snapshotQuality]);

  // 内部版本的 fetchSnapshots，支持指定版本
  const fetchSnapshotsForVersion = useCallback(async (forVersion?: Movie | UnifiedVersion, forceGenerate: boolean = false) => {
    // 确定要获取快照的版本
    const targetVersion = forVersion || movie;

    // 只为本地版本获取快照
    if (targetVersion && targetVersion.type !== 'virtual' && targetVersion.db_id !== undefined) {
      await internalFetchSnapshots(targetVersion.db_id, forceGenerate);
    } else if (forVersion && forVersion.type === 'virtual') {
      // 如果是虚拟版本，清空快照
      setSnapshots([]);
    }
  }, [movie, internalFetchSnapshots]);

  // 【新增】获取全量分组快照数据
  const fetchAllGroupedSnapshots = useCallback(async () => {
    if (!movie?.nfoId) {
      console.warn('[useMovieDetailManager] fetchAllGroupedSnapshots: 无效的 nfoId');
      return;
    }

    setIsLoadingGroupedSnapshots(true);
    try {
      const result = await window.sfeElectronAPI.getAllSnapshotsForNfoId(movie.nfoId);
      if (result.success && result.data) {
        setGroupedSnapshots(result.data);
        console.log(`[useMovieDetailManager] 成功获取 ${result.data.length} 个版本的快照数据`);
      } else {
        console.error('[useMovieDetailManager] 获取分组快照失败:', result.error);
        setGroupedSnapshots([]);
      }
    } catch (error) {
      console.error('[useMovieDetailManager] fetchAllGroupedSnapshots 异常:', error);
      setGroupedSnapshots([]);
    } finally {
      setIsLoadingGroupedSnapshots(false);
    }
  }, [movie?.nfoId]);

  // 兼容原有接口的 fetchSnapshots
  const fetchSnapshots = useCallback(async (forceGenerate: boolean = false) => {
    await fetchSnapshotsForVersion(movie, forceGenerate);
    // 快照生成完成后，自动刷新分组快照
    if (forceGenerate && movie?.nfoId) {
      // 直接调用 API，避免函数依赖问题
      try {
        const result = await window.sfeElectronAPI.getAllSnapshotsForNfoId(movie.nfoId);
        if (result.success && result.data) {
          setGroupedSnapshots(result.data);
        }
      } catch (error) {
        console.error('[fetchSnapshots] 刷新分组快照失败:', error);
      }
    }
  }, [fetchSnapshotsForVersion, movie?.nfoId]);

  // 【新增】当 movie 变化时自动获取全量快照数据（仅获取已存在的快照，不生成新的）
  useEffect(() => {
    if (movie?.nfoId) {
      fetchAllGroupedSnapshots();
    } else {
      setGroupedSnapshots([]);
    }
  }, [movie?.nfoId]);

  // 【新增】手动刷新分组快照的函数
  const refreshGroupedSnapshots = useCallback(async () => {
    if (movie?.nfoId) {
      await fetchAllGroupedSnapshots();
    }
  }, [movie?.nfoId, fetchAllGroupedSnapshots]);

  // 【新增】为特定版本生成快照的函数
  const generateSnapshotsForVersion = useCallback(async (versionDbId: number, versionFilePath: string) => {
    console.log("🎬 [useMovieDetailManager] 开始为特定版本生成快照", {
      versionDbId,
      versionFilePath: versionFilePath.substring(0, 50) + '...'
    });

    setIsGeneratingSnapshotsForVersion(true);
    try {
      const params = {
        videoDbId: versionDbId,
        videoFilePath: versionFilePath,
        snapshotQuality: appSettings.snapshotQuality || 'hd_640p',
      };

      console.log("🎬 [useMovieDetailManager] 快照生成参数:", params);

      if (!params.videoFilePath) {
        console.error("🎬 [useMovieDetailManager] ❌ 视频文件路径为空，无法生成快照", { versionDbId });
        alert(`无法生成快照：视频文件路径为空\n版本ID：${versionDbId}`);
        return;
      }

      if (window.sfeElectronAPI && params.videoFilePath) {
        console.log("🎬 [useMovieDetailManager] ✅ 开始调用主进程生成快照...");
        const generateResult = await window.sfeElectronAPI.generateThumbnails(params);
        console.log("🎬 [useMovieDetailManager] 快照生成结果:", generateResult);

        if (!generateResult.success) {
          console.error("🎬 [useMovieDetailManager] ❌ 快照生成失败:", generateResult.error);
          alert(`快照生成失败: ${generateResult.error}`);
          return;
        } else {
          console.log("🎬 [useMovieDetailManager] ✅ 快照生成成功！");
          // 刷新分组快照数据，直接调用 API 避免函数依赖问题
          if (movie?.nfoId) {
            try {
              const result = await window.sfeElectronAPI.getAllSnapshotsForNfoId(movie.nfoId);
              if (result.success && result.data) {
                setGroupedSnapshots(result.data);
              }
            } catch (error) {
              console.error('[generateSnapshotsForVersion] 刷新分组快照失败:', error);
            }
          }
        }
      } else {
        console.error("🎬 [useMovieDetailManager] ❌ sfeElectronAPI 不可用或文件路径为空");
      }
    } catch (error) {
      console.error("🎬 [useMovieDetailManager] generateSnapshotsForVersion 异常:", error);
      alert(`快照生成异常: ${error}`);
    } finally {
      setIsGeneratingSnapshotsForVersion(false);
    }
  }, [appSettings.snapshotQuality, movie?.nfoId]);

  // 建立 currentVersion 和快照之间的联动（仅获取已存在的快照，不自动生成）
  useEffect(() => {
    if (currentVersion && currentVersion.type === 'local' && currentVersion.db_id !== undefined) {
      // 直接处理快照加载，避免函数依赖问题，forceGenerate=false 确保不自动生成
      internalFetchSnapshots(currentVersion.db_id, false);
    } else if (currentVersion && currentVersion.type === 'virtual') {
      // 如果选中的是虚拟版本，则清空快照
      setSnapshots([]);
    }
  }, [currentVersion?.db_id, currentVersion?.type, internalFetchSnapshots]); // 使用具体的属性作为依赖

  // 版本切换处理函数
  const handleVersionChange = useCallback((version: UnifiedVersion) => {
    setCurrentVersion(version);

    // 如果切换到本地版本，更新主要的 movie 状态
    if (version.type === 'local') {
      const localMovie = version as Movie & { type: 'local' };
      const { type, ...movieData } = localMovie; // 移除 type 字段
      setMovie(movieData);
      setEditableMovie(movieToEditableState(movieData));

      // 通知父组件更新
      onUpdateMovieCallback(movieData);

      // 快照获取现在由 useEffect 自动处理，无需在这里手动调用
    }
    // 如果是虚拟版本，保持当前的 movie 状态不变，只更新 currentVersion
    // 快照清空也由 useEffect 自动处理
  }, [onUpdateMovieCallback]);

  // NFO导出函数
  const handleExportNfo = useCallback(async () => {
    if (!movie || !movie.nfoId) {
      setNfoExportResult({
        success: false,
        message: '无法导出NFO：缺少影片信息或NFO ID',
        timestamp: Date.now()
      });
      return;
    }

    setIsExportingNfo(true);
    setNfoExportResult(null);

    try {
      const result = await window.sfeElectronAPI.exportNfo(movie.nfoId);

      setNfoExportResult({
        success: result.success,
        message: result.message || (result.success ? 'NFO文件导出成功' : 'NFO文件导出失败'),
        timestamp: Date.now()
      });

      // 8秒后自动清除结果消息
      setTimeout(() => {
        setNfoExportResult(null);
      }, 8000);

    } catch (error) {
      setNfoExportResult({
        success: false,
        message: error instanceof Error ? error.message : '导出过程中发生未知错误',
        timestamp: Date.now()
      });

      setTimeout(() => {
        setNfoExportResult(null);
      }, 8000);
    } finally {
      setIsExportingNfo(false);
    }
  }, [movie]);

  return {
    movie, setMovie, 
    isEditing, setIsEditing,
    editableMovie, setEditableMovie,
    isSaving,
    isCoverActionLoading,
    isTrailerPlayerOpen, trailerPlayerUrl,
    snapshots, isLoadingSnapshots, 
    showSnapshotsSection, setShowSnapshotsSection,
    aiSuggestedSnapshotIndex,
    favoriteStatus,
    isCoverPreviewOpen, coverPreviewUrl,
    actorAvatars, isPreviewAvatarModalOpen, previewAvatarUrl,
    isGeneratingPlot, isEmbellishingPlot, isAnalyzingTags,
    aiGeneratedPlotDisplay, aiEmbellishedPlotDisplay, 
    handleInputChange,
    handleSaveChanges,
    handlePlayVideo,
    handleOpenTrailer, handleCloseTrailerPlayer,
    handleDownloadCover, handleBrowseLocalCover, handleSetSnapshotAsCover,
    handleToggleFavorite,
    handleOpenCoverPreview, handleCloseCoverPreview,
    handleOpenAvatarPreview, handleCloseAvatarPreview,
    generatePlot,
    embellishPlot,
    analyzeTags,
    suggestCoverFromSnapshots,
    fetchSnapshots,
    // 版本管理相关
    allVersions,
    isLoadingVersions,
    currentVersion,
    handleVersionChange,
    // 【新增】分组快照相关
    groupedSnapshots,
    isLoadingGroupedSnapshots,
    fetchAllGroupedSnapshots,
    refreshGroupedSnapshots,
    generateSnapshotsForVersion,
    isGeneratingSnapshotsForVersion,
    // NFO导出相关
    isExportingNfo,
    nfoExportResult,
    handleExportNfo,
  };
}
