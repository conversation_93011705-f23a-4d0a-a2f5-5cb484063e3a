// 验证 collected_links 表创建的脚本
const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// 获取数据库路径
const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'linlang-secret-manor');
const dbPath = path.join(userDataPath, 'soul-forge-library.sqlite');

console.log('数据库路径:', dbPath);

try {
  // 连接数据库
  const db = new Database(dbPath, { readonly: true });
  
  // 检查数据库版本
  const versionRow = db.prepare("SELECT value FROM db_info WHERE key = 'version'").get();
  console.log('当前数据库版本:', versionRow ? versionRow.value : '未知');
  
  // 检查 collected_links 表是否存在
  const tableInfo = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='collected_links'").get();
  
  if (tableInfo) {
    console.log('✅ collected_links 表已成功创建');
    
    // 获取表结构
    const columns = db.prepare("PRAGMA table_info(collected_links)").all();
    console.log('\n📋 表结构:');
    columns.forEach(col => {
      console.log(`  ${col.name}: ${col.type}${col.notnull ? ' NOT NULL' : ''}${col.dflt_value ? ` DEFAULT ${col.dflt_value}` : ''}`);
    });
    
    // 检查索引
    const indexes = db.prepare("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='collected_links'").all();
    console.log('\n🔍 索引:');
    indexes.forEach(idx => {
      console.log(`  ${idx.name}`);
    });
    
    // 测试插入一条记录
    console.log('\n🧪 测试插入记录...');
    const insertStmt = db.prepare(`
      INSERT INTO collected_links (
        source_forum, post_url, post_title, nfoId, magnet_link, download_status
      ) VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    try {
      const info = insertStmt.run(
        'TestForum',
        'https://test.com/post/123',
        '测试帖子标题 [ABC-123]',
        'ABC-123',
        'magnet:?xt=urn:btih:test',
        'pending'
      );
      console.log('✅ 测试记录插入成功, ID:', info.lastInsertRowid);
      
      // 查询刚插入的记录
      const record = db.prepare("SELECT * FROM collected_links WHERE id = ?").get(info.lastInsertRowid);
      console.log('📄 插入的记录:', record);
      
      // 清理测试记录
      db.prepare("DELETE FROM collected_links WHERE id = ?").run(info.lastInsertRowid);
      console.log('🧹 测试记录已清理');
      
    } catch (error) {
      console.error('❌ 测试插入失败:', error.message);
    }
    
  } else {
    console.log('❌ collected_links 表不存在');
  }
  
  db.close();
  console.log('\n🎉 验证完成！');
  
} catch (error) {
  console.error('❌ 验证过程中出错:', error.message);
}
