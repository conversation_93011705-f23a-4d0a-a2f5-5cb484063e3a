# soul-forge-electron/python_scripts/nfo_writer.py
import json
import os
import sys
from xml.etree import ElementTree as ET
from xml.dom import minidom
from datetime import datetime

# 确保以 UTF-8 编码输出到 stdout，尤其是在 Windows 上
sys.stdout.reconfigure(encoding='utf-8')

def create_or_update_nfo(video_file_path, movie_data_json):
    """
    为给定的视频文件创建或更新NFO文件。
    movie_data_json: 包含电影信息的JSON字符串。
    """
    try:
        movie_data = json.loads(movie_data_json)
        
        base_name = os.path.splitext(video_file_path)[0]
        nfo_file_path = base_name + ".nfo"

        root = None
        tree = None

        # 尝试加载现有的NFO文件
        if os.path.exists(nfo_file_path):
            try:
                tree = ET.parse(nfo_file_path)
                root = tree.getroot()
                # print(f"成功加载现有NFO文件: {nfo_file_path}", file=sys.stderr)
            except ET.ParseError:
                # print(f"警告: 现有NFO文件 {nfo_file_path} 解析失败，将创建新的。", file=sys.stderr)
                root = ET.Element("movie") # 如果解析失败，则创建一个新的根元素
        else:
            # print(f"NFO文件 {nfo_file_path} 不存在，将创建新的。", file=sys.stderr)
            root = ET.Element("movie")

        # 更新或添加元素
        def set_text_element(parent, tag, text_content):
            if text_content is None or text_content == "": # 如果内容为空或None，则尝试移除该元素
                element_to_remove = parent.find(tag)
                if element_to_remove is not None:
                    parent.remove(element_to_remove)
                return

            element = parent.find(tag)
            if element is None:
                element = ET.SubElement(parent, tag)
            element.text = str(text_content)

        set_text_element(root, "title", movie_data.get("title"))
        set_text_element(root, "originaltitle", movie_data.get("originalTitle"))
        set_text_element(root, "plot", movie_data.get("plot"))
        
        # 处理 plot_ja (日文剧情) 和 plot_zh (中文剧情)
        # 假设：如果 movie_data 中有 plot_ja，则将其写入 <originalplot>
        # 主 <plot> 标签由上面的 set_text_element(root, "plot", movie_data.get("plot")) 控制，
        # 通常 Electron 应用会确保 movie_data.plot 是处理后的中文剧情。
        set_text_element(root, "originalplot", movie_data.get("plot_ja"))


        set_text_element(root, "year", str(movie_data.get("year")) if movie_data.get("year") else None)
        
        # 处理日期：优先使用premiered，其次releasedate
        release_date_val = movie_data.get("releaseDate") or movie_data.get("premiered")
        set_text_element(root, "releasedate", release_date_val) # Kodi通常读releasedate
        set_text_element(root, "premiered", release_date_val) # 有些刮削器可能用这个
        
        set_text_element(root, "id", movie_data.get("nfoId"))
        
        # uniqueid (如果需要，假设是字符串或列表)
        uniqueids = movie_data.get("uniqueid")
        if uniqueids:
            # 先移除所有现有的 uniqueid 元素
            for uid_elem in root.findall("uniqueid"):
                root.remove(uid_elem)
            if isinstance(uniqueids, list):
                for uid_val in uniqueids:
                    if uid_val: # 确保值不为空
                        uid_elem = ET.SubElement(root, "uniqueid", type="SOD") # 假设类型都是SOD
                        uid_elem.text = str(uid_val)
            elif isinstance(uniqueids, str) and uniqueids:
                uid_elem = ET.SubElement(root, "uniqueid", type="SOD")
                uid_elem.text = uniqueids


        set_text_element(root, "studio", movie_data.get("studio"))
        set_text_element(root, "director", movie_data.get("director"))
        set_text_element(root, "maker", movie_data.get("studio")) # Kodi 可能用 maker 或 studio
        set_text_element(root, "publisher", movie_data.get("studio")) # 有些也用 publisher

        set_text_element(root, "series", movie_data.get("series"))
        set_text_element(root, "runtime", str(movie_data.get("runtime")) if movie_data.get("runtime") else None)
        set_text_element(root, "trailer", movie_data.get("trailerUrl"))
        set_text_element(root, "poster", movie_data.get("posterUrl"))
        set_text_element(root, "cover", movie_data.get("coverUrl"))
        # localCoverPath 不直接写入NFO，它由主应用管理

        set_text_element(root, "watched", "true" if movie_data.get("watched") else "false")
        set_text_element(root, "playcount", "1" if movie_data.get("watched") else "0") # 根据watched状态设置playcount

        if movie_data.get("watched"): # 如果已观看，记录最后播放时间
             set_text_element(root, "lastplayed", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        else: # 如果未观看，则移除 lastplayed 标签
            lastplayed_elem = root.find("lastplayed")
            if lastplayed_elem is not None:
                root.remove(lastplayed_elem)


        personal_rating = movie_data.get("personalRating")
        set_text_element(root, "userrating", str(personal_rating) if personal_rating is not None and personal_rating > 0 else None)
        # 如果需要，也可以同时写入 <rating>，但Kodi通常以 <userrating> 为准
        # set_text_element(root, "rating", str(personal_rating) if personal_rating is not None and personal_rating > 0 else None)


        # 处理列表类型的元素：genres, tags, actors
        def update_list_elements(parent, tag_name, items_list, item_sub_tag_name=None, item_attributes=None):
            if not isinstance(items_list, list): # 确保是列表
                return
            
            # 先移除所有现有的该类型的元素
            for existing_element in parent.findall(tag_name):
                parent.remove(existing_element)
            
            # 添加新的元素
            for item_content in items_list:
                if not item_content: # 跳过空内容
                    continue
                
                if item_sub_tag_name: # 例如演员有 <actor><name>...</name></actor>
                    list_item_element = ET.SubElement(parent, tag_name)
                    sub_element = ET.SubElement(list_item_element, item_sub_tag_name)
                    if isinstance(item_content, dict) and item_sub_tag_name in item_content:
                        sub_element.text = str(item_content[item_sub_tag_name])
                        if item_attributes: # 为actor添加thumb等属性
                             for attr_key, attr_val_key_in_dict in item_attributes.items():
                                if item_content.get(attr_val_key_in_dict):
                                    attr_sub_elem = ET.SubElement(list_item_element, attr_key)
                                    attr_sub_elem.text = str(item_content[attr_val_key_in_dict])
                    elif isinstance(item_content, str): # 如果item_content本身就是字符串，直接用
                         sub_element.text = item_content
                else: # 例如 genres, tags 直接是 <genre>...</genre>
                    element = ET.SubElement(parent, tag_name)
                    element.text = str(item_content)
        
        update_list_elements(root, "genre", movie_data.get("genres"))
        update_list_elements(root, "tag", movie_data.get("tags"))
        
        # 处理演员（可能包含名字和头像URL）
        actors_input = movie_data.get("actors", [])
        processed_actors = []
        if actors_input and isinstance(actors_input, list):
            for actor_item in actors_input:
                if isinstance(actor_item, str): # 如果是字符串列表
                    processed_actors.append({"name": actor_item, "thumb": None})
                elif isinstance(actor_item, dict) and "name" in actor_item: # 如果是对象列表
                    processed_actors.append({
                        "name": actor_item.get("name"),
                        "thumb": actor_item.get("thumb", actor_item.get("avatarUrl")) # 兼容thumb和avatarUrl
                    })
        update_list_elements(root, "actor", processed_actors, item_sub_tag_name="name", item_attributes={"thumb": "thumb"})


        # 美化XML输出
        xml_str = ET.tostring(root, encoding='utf-8', method='xml')
        # minidom会添加 <?xml version="1.0" ?>，这对于NFO通常是好的
        pretty_xml_str = minidom.parseString(xml_str).toprettyxml(indent="  ", encoding='utf-8')
        
        with open(nfo_file_path, 'wb') as f: # 以二进制写入，因为minidom输出的是带编码声明的bytes
            f.write(pretty_xml_str)
        
        return json.dumps({"success": True, "message": f"NFO文件已成功保存到: {nfo_file_path}", "nfo_path": nfo_file_path})

    except json.JSONDecodeError as e:
        return json.dumps({"success": False, "error": f"解析输入的JSON数据失败: {str(e)}"})
    except ET.ParseError as e:
        return json.dumps({"success": False, "error": f"解析XML时出错: {str(e)}"})
    except IOError as e:
        return json.dumps({"success": False, "error": f"文件操作失败: {str(e)}"})
    except Exception as e:
        return json.dumps({"success": False, "error": f"发生未知错误: {str(e)}"})


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print(json.dumps({"success": False, "error": "用法: python nfo_writer.py <video_file_path> <movie_data_json_string>"}, ensure_ascii=False))
        sys.exit(1)

    video_path_arg = sys.argv[1]
    movie_json_arg = sys.argv[2]

    # print(f"接收到 Video Path: {video_path_arg}", file=sys.stderr)
    # print(f"接收到 Movie JSON (前100字符): {movie_json_arg[:100]}...", file=sys.stderr)

    result_json = create_or_update_nfo(video_path_arg, movie_json_arg)
    print(result_json)