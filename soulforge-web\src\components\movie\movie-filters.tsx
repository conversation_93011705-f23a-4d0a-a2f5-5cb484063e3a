'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { FilterOptions, ViewMode, SortField, SortOrder } from '@/lib/types';
import { cn, debounce } from '@/lib/utils';
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  LayoutGrid, 
  Columns,
  SortAsc,
  SortDesc,
  X
} from 'lucide-react';

interface MovieFiltersProps {
  filters: FilterOptions;
  viewMode: ViewMode;
  sortField: SortField;
  sortOrder: SortOrder;
  onFiltersChange: (filters: Partial<FilterOptions>) => void;
  onViewModeChange: (mode: ViewMode) => void;
  onSortChange: (field: SortField, order: SortOrder) => void;
  onClearFilters: () => void;
  className?: string;
}

export function MovieFilters({
  filters,
  viewMode,
  sortField,
  sortOrder,
  onFiltersChange,
  onViewModeChange,
  onSortChange,
  onClearFilters,
  className,
}: MovieFiltersProps) {
  const [searchValue, setSearchValue] = React.useState(filters.search || '');
  const [showAdvanced, setShowAdvanced] = React.useState(false);

  // Debounced search
  const debouncedSearch = React.useMemo(
    () => debounce((value: string) => {
      onFiltersChange({ search: value });
    }, 300),
    [onFiltersChange]
  );

  React.useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleSortFieldChange = (field: SortField) => {
    if (field === sortField) {
      // Toggle order if same field
      onSortChange(field, sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to desc
      onSortChange(field, 'desc');
    }
  };

  const viewModeButtons = [
    { mode: 'grid' as ViewMode, icon: Grid3X3, label: '网格' },
    { mode: 'list' as ViewMode, icon: List, label: '列表' },
    { mode: 'detailed' as ViewMode, icon: LayoutGrid, label: '详细' },
    { mode: 'waterfall' as ViewMode, icon: Columns, label: '瀑布流' },
  ];

  const sortOptions = [
    { field: 'lastScanned' as SortField, label: '最近扫描' },
    { field: 'title' as SortField, label: '标题' },
    { field: 'year' as SortField, label: '年份' },
    { field: 'releaseDate' as SortField, label: '发布日期' },
    { field: 'fileSize' as SortField, label: '文件大小' },
    { field: 'personalRating' as SortField, label: '个人评分' },
    { field: 'runtime' as SortField, label: '时长' },
  ];

  const hasActiveFilters = Boolean(
    filters.search ||
    (filters.genres && filters.genres.length > 0) ||
    (filters.actors && filters.actors.length > 0) ||
    (filters.studios && filters.studios.length > 0) ||
    (filters.years && filters.years.length > 0) ||
    filters.watched !== undefined ||
    filters.favorited !== undefined ||
    filters.libraryId
  );

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main toolbar */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索电影标题、导演、演员..."
            value={searchValue}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>

        {/* View mode */}
        <div className="flex rounded-md border">
          {viewModeButtons.map(({ mode, icon: Icon, label }) => (
            <Button
              key={mode}
              variant={viewMode === mode ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange(mode)}
              className="rounded-none first:rounded-l-md last:rounded-r-md"
              title={label}
            >
              <Icon className="h-4 w-4" />
            </Button>
          ))}
        </div>

        {/* Sort */}
        <div className="flex items-center space-x-2">
          <select
            value={sortField}
            onChange={(e) => handleSortFieldChange(e.target.value as SortField)}
            className="px-3 py-2 border rounded-md text-sm bg-background"
          >
            {sortOptions.map(({ field, label }) => (
              <option key={field} value={field}>
                {label}
              </option>
            ))}
          </select>
          <Button
            variant="outline"
            size="icon"
            onClick={() => onSortChange(sortField, sortOrder === 'asc' ? 'desc' : 'asc')}
            title={sortOrder === 'asc' ? '升序' : '降序'}
          >
            {sortOrder === 'asc' ? (
              <SortAsc className="h-4 w-4" />
            ) : (
              <SortDesc className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Advanced filters toggle */}
        <Button
          variant="outline"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center space-x-2"
        >
          <Filter className="h-4 w-4" />
          <span>筛选</span>
          {hasActiveFilters && (
            <div className="w-2 h-2 bg-primary rounded-full" />
          )}
        </Button>
      </div>

      {/* Advanced filters */}
      {showAdvanced && (
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Watched filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">观看状态</label>
                <select
                  value={filters.watched === undefined ? '' : filters.watched.toString()}
                  onChange={(e) => {
                    const value = e.target.value;
                    onFiltersChange({
                      watched: value === '' ? undefined : value === 'true'
                    });
                  }}
                  className="w-full px-3 py-2 border rounded-md text-sm bg-background"
                >
                  <option value="">全部</option>
                  <option value="true">已观看</option>
                  <option value="false">未观看</option>
                </select>
              </div>

              {/* Favorited filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">收藏状态</label>
                <select
                  value={filters.favorited === undefined ? '' : filters.favorited.toString()}
                  onChange={(e) => {
                    const value = e.target.value;
                    onFiltersChange({
                      favorited: value === '' ? undefined : value === 'true'
                    });
                  }}
                  className="w-full px-3 py-2 border rounded-md text-sm bg-background"
                >
                  <option value="">全部</option>
                  <option value="true">已收藏</option>
                  <option value="false">未收藏</option>
                </select>
              </div>

              {/* Year range */}
              <div>
                <label className="text-sm font-medium mb-2 block">年份</label>
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    placeholder="开始年份"
                    className="text-sm"
                  />
                  <Input
                    type="number"
                    placeholder="结束年份"
                    className="text-sm"
                  />
                </div>
              </div>

              {/* Clear filters */}
              <div className="flex items-end">
                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    onClick={onClearFilters}
                    className="w-full"
                  >
                    <X className="h-4 w-4 mr-2" />
                    清除筛选
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
