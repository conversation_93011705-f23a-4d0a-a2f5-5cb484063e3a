// main_process/services/scrapers/avsoxProvider.js
// AVSOX Provider - 新特工，专门从 AVSOX 网站采集详尽的影片信息和磁力链接

const { chromium } = require('playwright');
const cheerio = require('cheerio');
const log = require('electron-log');

const PROVIDER_NAME = 'avsox';
const PROVIDER_VERSION = '1.0.0';

/**
 * AVSOX Provider - 应采尽采模式
 * 
 * 核心价值：
 * 1. 详尽的磁力链接表采集
 * 2. 完整的影片元数据
 * 3. 高清封面和预览图
 * 4. 全面的演员和标签信息
 */

/**
 * 主要刮削函数
 * @param {string} nfoId - 影片番号
 * @returns {Promise<Object>} 刮削结果
 */
async function scrape(nfoId) {
    log.info(`[AVSOX Provider v${PROVIDER_VERSION}] 开始刮削: ${nfoId}`);
    
    let browser;
    let context;
    let page;
    
    try {
        // 启动浏览器
        browser = await chromium.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        context = await browser.newContext({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });
        
        page = await context.newPage();
        
        // 第一步：搜索与定位
        const searchResult = await searchAndLocateMovie(page, nfoId);
        if (!searchResult || !searchResult.url) {
            throw new Error(`未找到番号 ${nfoId} 的详情页`);
        }

        // 第二步：详情页刮削 - 应采尽采
        const movieData = await scrapeMovieDetails(page, searchResult.url, nfoId, searchResult.posterUrl);
        
        log.info(`[AVSOX Provider v${PROVIDER_VERSION}] 刮削完成: ${nfoId}`);
        return movieData;
        
    } catch (error) {
        log.error(`[AVSOX Provider v${PROVIDER_VERSION}] 刮削失败 ${nfoId}: ${error.message}`);
        throw error;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 【优化】搜索并定位影片详情页 - 基于对标软件优化
 * @param {Object} page - Playwright 页面对象
 * @param {string} nfoId - 影片番号
 * @returns {Promise<Object|null>} 详情页信息 {url, posterUrl}
 */
async function searchAndLocateMovie(page, nfoId) {
    try {
        // 构造搜索URL - 使用cn版本获得中文界面
        const searchUrl = `https://avsox.click/cn/search/${encodeURIComponent(nfoId)}`;
        log.info(`[AVSOX Provider] 搜索URL: ${searchUrl}`);

        // 访问搜索页
        await page.goto(searchUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        // 等待页面加载
        await page.waitForTimeout(2000);

        // 获取页面内容
        const content = await page.content();
        const $ = cheerio.load(content);

        // 【优化】使用更精确的选择器 - 参考对标软件
        const searchResults = [];
        $('#waterfall > div').each((i, el) => {
            const $el = $(el);
            const link = $el.find('a').attr('href');

            // 【优化】使用更精确的番号提取 - 参考对标软件的XPath逻辑
            const numberElement = $el.find('.photo-info span date').first();
            const extractedNumber = numberElement.text().trim();

            // 获取海报URL - 从搜索页面直接获取
            const posterImg = $el.find('.photo-frame img');
            const posterUrl = posterImg.attr('src') || posterImg.attr('data-src');

            if (link && extractedNumber) {
                searchResults.push({
                    url: link.startsWith('http') ? link : `https://avsox.click${link}`,
                    number: extractedNumber,
                    posterUrl: posterUrl ? (posterUrl.startsWith('http') ? posterUrl : `https://avsox.click${posterUrl}`) : null,
                    index: i + 1
                });
            }
        });

        log.info(`[AVSOX Provider] 找到 ${searchResults.length} 个搜索结果`);

        // 【优化】精确匹配番号 - 参考对标软件的匹配逻辑
        for (const result of searchResults) {
            const normalizedInput = nfoId.toUpperCase().replace("-PPV", "");
            const normalizedResult = result.number.toUpperCase().replace("-PPV", "");

            if (normalizedInput === normalizedResult) {
                log.info(`[AVSOX Provider] 精确匹配成功: ${result.url}`);
                return {
                    url: result.url,
                    posterUrl: result.posterUrl
                };
            }
        }

        // 如果没有精确匹配，尝试包含匹配
        for (const result of searchResults) {
            if (result.number.toUpperCase().includes(nfoId.toUpperCase())) {
                log.warn(`[AVSOX Provider] 使用包含匹配: ${result.url}`);
                return {
                    url: result.url,
                    posterUrl: result.posterUrl
                };
            }
        }

        // 最后尝试第一个结果
        if (searchResults.length > 0) {
            log.warn(`[AVSOX Provider] 使用第一个搜索结果: ${searchResults[0].url}`);
            return {
                url: searchResults[0].url,
                posterUrl: searchResults[0].posterUrl
            };
        }

        return null;

    } catch (error) {
        log.error(`[AVSOX Provider] 搜索失败: ${error.message}`);
        throw error;
    }
}

/**
 * 【优化】刮削影片详情页 - 应采尽采模式，基于对标软件优化
 * @param {Object} page - Playwright 页面对象
 * @param {string} detailUrl - 详情页URL
 * @param {string} nfoId - 影片番号
 * @param {string} posterUrl - 海报URL（从搜索页获取）
 * @returns {Promise<Object>} 影片数据
 */
async function scrapeMovieDetails(page, detailUrl, nfoId, posterUrl = null) {
    try {
        log.info(`[AVSOX Provider] 访问详情页: ${detailUrl}`);
        
        // 访问详情页
        await page.goto(detailUrl, { 
            waitUntil: 'domcontentloaded',
            timeout: 30000 
        });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        // 获取页面内容
        const content = await page.content();
        const $ = cheerio.load(content);
        
        log.info(`[AVSOX Provider] 🚀 开始应采尽采模式数据采集...`);
        
        // === 基础信息采集 ===
        const title = getTitle($, nfoId);  // 传递nfoId用于清理标题
        const coverUrl = getCoverImage($);
        const releaseDate = getReleaseDate($);
        const runtime = getRuntime($);
        const director = getDirector($);
        const studio = getStudio($);
        const series = getSeries($);
        
        // === 人员信息采集 ===
        const actors = getActors($);
        
        // === 标签和类别采集 ===
        const genres = getGenres($);
        const tags = getAllTags($);
        
        // === 媒体资源采集 ===
        const previewImages = getPreviewImages($);
        const sampleImages = getSampleImages($);
        
        // === 核心功能：磁力链接表采集 ===
        const magnetLinks = getMagnetLinksTable($);
        
        // === 额外信息采集 ===
        const plot = getPlot($);
        const rating = getRating($);
        const technicalInfo = getTechnicalInfo($);
        const relatedMovies = getRelatedMovies($);
        
        // 【优化】组装完整的原始数据对象 - 基于对标软件标准化
        const movieData = {
            // 基础标识
            nfoId: nfoId,
            number: nfoId,  // 添加number字段，对标软件使用
            title: title || '',
            originalTitle: title || '',
            originaltitle: title || '',  // 对标软件字段
            plot: plot || '',
            outline: plot || '',  // 对标软件字段
            originalplot: plot || '',  // 对标软件字段

            // 时间和规格信息
            releaseDate: releaseDate || null,
            release: releaseDate || null,  // 对标软件字段
            year: releaseDate ? releaseDate.substring(0, 4) : null,  // 对标软件字段
            runtime: runtime || null,

            // 人员信息
            actors: actors || [],
            actor: actors?.map(a => a.name).join(',') || '',  // 对标软件字段
            actor_photo: getActorPhoto(actors),  // 对标软件字段
            director: director || null,
            studio: studio || null,
            publisher: studio || null,  // 对标软件字段
            series: series || null,

            // 分类信息
            genres: genres || [],
            tags: tags || [],
            tag: tags?.join(',') || '',  // 对标软件字段

            // 媒体资源
            coverUrl: coverUrl || null,
            thumb: coverUrl || null,  // 对标软件字段
            posterUrl: posterUrl || coverUrl || null,  // 优先使用搜索页获取的海报
            poster: posterUrl || coverUrl || null,  // 对标软件字段
            previewImages: previewImages || [],
            sampleImages: sampleImages || [],
            extrafanart: previewImages || [],  // 对标软件字段

            // 评分信息
            rating: rating || null,
            score: rating?.score || '',  // 对标软件字段

            // 【核心价值】磁力链接表
            magnetLinks: magnetLinks || [],

            // 相关信息
            relatedMovies: relatedMovies || [],
            technicalInfo: technicalInfo || {},

            // 对标软件标准字段
            trailer: '',
            image_download: !!posterUrl,
            image_cut: 'center',
            mosaic: '无码',  // AVSOX主要是无码内容
            wanted: '',

            // 来源信息
            source: 'avsox',
            sourceUrl: detailUrl,
            website: detailUrl,  // 对标软件字段
            scrapedAt: new Date().toISOString(),
            provider: PROVIDER_NAME,
            version: PROVIDER_VERSION,

            // 数据丰富度统计
            dataRichness: {
                actorsCount: actors?.length || 0,
                genresCount: genres?.length || 0,
                tagsCount: tags?.length || 0,
                previewImagesCount: previewImages?.length || 0,
                sampleImagesCount: sampleImages?.length || 0,
                magnetLinksCount: magnetLinks?.length || 0,
                relatedMoviesCount: relatedMovies?.length || 0,
                hasCover: !!coverUrl,
                hasPoster: !!posterUrl,
                hasPlot: !!plot,
                hasRating: !!rating,
                hasTechnicalInfo: Object.keys(technicalInfo || {}).length > 0
            }
        };
        
        log.info(`[AVSOX Provider] 🎉 应采尽采完成！数据丰富度: 演员${actors?.length || 0}个, 标签${tags?.length || 0}个, 磁力${magnetLinks?.length || 0}个, 预览图${previewImages?.length || 0}张`);
        
        return movieData;
        
    } catch (error) {
        log.error(`[AVSOX Provider] 详情页刮削失败: ${error.message}`);
        throw error;
    }
}

/**
 * 【优化】获取影片标题 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @param {string} nfoId - 番号，用于清理标题
 * @returns {string} 标题
 */
function getTitle($, nfoId) {
    // 【优化】使用对标软件的精确选择器
    const selectors = [
        '.container h3',  // 对标软件使用的选择器
        'h3',
        '.title',
        '.movie-title',
        'h1'
    ];

    for (const selector of selectors) {
        const titleElement = $(selector).first();
        if (titleElement.length > 0) {
            let title = titleElement.text().trim();

            // 【优化】清理标题 - 移除番号前缀，参考对标软件逻辑
            if (title && nfoId) {
                // 获取页面上显示的番号
                const webNumber = getWebNumber($);
                if (webNumber && title.startsWith(webNumber)) {
                    title = title.replace(webNumber, '').trim();
                }
            }

            if (title) {
                return title;
            }
        }
    }

    return '';
}

/**
 * 【新增】获取网页显示的番号 - 参考对标软件
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 网页番号
 */
function getWebNumber($) {
    // 【优化】使用对标软件的精确选择器
    const selectors = [
        '.col-md-3.info p span[style="color:#CC0000;"]',  // 对标软件的选择器
        '.info span[style*="color:#CC0000"]',
        '.info span[style*="color:red"]',
        '.movie-code',
        '.code'
    ];

    for (const selector of selectors) {
        const number = $(selector).first().text().trim();
        if (number) {
            return number;
        }
    }

    return '';
}

/**
 * 【新增】获取演员头像映射 - 参考对标软件
 * @param {Array} actors - 演员数组
 * @returns {Object} 演员头像映射
 */
function getActorPhoto(actors) {
    const actorPhoto = {};

    if (Array.isArray(actors)) {
        actors.forEach(actor => {
            if (actor.name) {
                actorPhoto[actor.name] = actor.image || '';
            }
        });
    }

    return actorPhoto;
}

/**
 * 【优化】获取封面图 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 封面图URL
 */
function getCoverImage($) {
    // 【优化】使用对标软件的精确选择器
    const selectors = [
        'a.bigImage',  // 对标软件使用的选择器，获取href而不是img src
        '.bigImage img',
        '.photo-frame img',
        '.movie-box img',
        '.cover img',
        'img[src*="cover"]',
        'img[src*="jacket"]'
    ];

    // 首先尝试获取大图链接
    const bigImageLink = $('a.bigImage').attr('href');
    if (bigImageLink) {
        return bigImageLink.startsWith('http') ? bigImageLink : `https://avsox.click${bigImageLink}`;
    }

    // 然后尝试其他选择器
    for (const selector of selectors) {
        const img = $(selector).first();
        if (img.length > 0) {
            let src = img.attr('src') || img.attr('data-src') || img.attr('data-original');
            if (src) {
                // 【优化】更智能的高清版本转换
                if (src.includes('thumbs')) {
                    src = src.replace('/thumbs/', '/covers/');
                }
                if (src.includes('_s.jpg')) {
                    src = src.replace('_s.jpg', '.jpg');
                }
                if (src.includes('_m.jpg')) {
                    src = src.replace('_m.jpg', '.jpg');
                }

                return src.startsWith('http') ? src : `https://avsox.click${src}`;
            }
        }
    }

    return null;
}

/**
 * 【优化】获取发行时间 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 发行时间
 */
function getReleaseDate($) {
    // 【优化】使用对标软件的XPath等价选择器
    const selectors = [
        '.info p:contains("发行时间") span',
        '.info p:contains("發行時間") span',
        '.info p:contains("Release Date") span'
    ];

    // 特殊处理：查找包含发行时间的span，然后获取父元素的文本节点
    const releaseSpans = $('span').filter(function() {
        const text = $(this).text();
        return text.includes('发行时间:') || text.includes('發行日期:') || text.includes('発売日:');
    });

    if (releaseSpans.length > 0) {
        const parentText = releaseSpans.first().parent().text();
        const dateMatch = parentText.match(/(\d{4}-\d{2}-\d{2})/);
        if (dateMatch) {
            return dateMatch[1];
        }
    }

    // 备用选择器
    for (const selector of selectors) {
        if (typeof selector === 'string') {
            const date = $(selector).text().trim();
            if (date && date.match(/\d{4}-\d{2}-\d{2}/)) {
                return date;
            }
        }
    }

    return null;
}

/**
 * 【优化】获取影片时长 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {number} 时长（分钟）
 */
function getRuntime($) {
    // 【优化】使用对标软件的XPath等价选择器
    // 对标软件的XPath: //span[contains(text(),"长度:")]/../text()
    const runtimeSpans = $('span').filter(function() {
        const text = $(this).text();
        return text.includes('长度:') || text.includes('長度:') || text.includes('収録時間:');
    });

    if (runtimeSpans.length > 0) {
        const parentText = runtimeSpans.first().parent().text();
        const runtimeMatch = parentText.match(/(\d+)/);
        if (runtimeMatch) {
            return parseInt(runtimeMatch[1]);
        }
    }

    // 备用选择器
    const selectors = [
        '.info p:contains("时长") span',
        '.info p:contains("時長") span',
        '.info p:contains("Runtime") span',
        'span:contains("时长") + span'
    ];

    for (const selector of selectors) {
        const runtime = $(selector).text().trim();
        const match = runtime.match(/(\d+)/);
        if (match) {
            return parseInt(match[1]);
        }
    }

    return null;
}

/**
 * 获取导演
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 导演
 */
function getDirector($) {
    const selectors = [
        '.info p:contains("导演") span',
        '.info p:contains("導演") span',
        '.info p:contains("Director") span',
        'span:contains("导演") + span'
    ];

    for (const selector of selectors) {
        const director = $(selector).text().trim();
        if (director) {
            return director;
        }
    }

    return null;
}

/**
 * 【优化】获取制作商 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 制作商
 */
function getStudio($) {
    // 【优化】使用对标软件的精确选择器
    // 对标软件的XPath: //p/a[contains(@href,"/studio/")]/text()
    const studioLinks = $('p a[href*="/studio/"]');
    if (studioLinks.length > 0) {
        const studio = studioLinks.first().text().trim();
        if (studio) {
            return studio;
        }
    }

    // 备用选择器
    const selectors = [
        '.info p:contains("制作商") span',
        '.info p:contains("製作商") span',
        '.info p:contains("Studio") span',
        '.info p:contains("Maker") span',
        'span:contains("制作商") + span'
    ];

    for (const selector of selectors) {
        const studio = $(selector).text().trim();
        if (studio) {
            return studio;
        }
    }

    return null;
}

/**
 * 【优化】获取系列 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 系列
 */
function getSeries($) {
    // 【优化】使用对标软件的精确选择器
    // 对标软件的XPath: //p/a[contains(@href,"/series/")]/text()
    const seriesLinks = $('p a[href*="/series/"]');
    if (seriesLinks.length > 0) {
        const series = seriesLinks.first().text().trim();
        if (series) {
            return series;
        }
    }

    // 备用选择器
    const selectors = [
        '.info p:contains("系列") span',
        '.info p:contains("Series") span',
        'span:contains("系列") + span'
    ];

    for (const selector of selectors) {
        const series = $(selector).text().trim();
        if (series) {
            return series;
        }
    }

    return null;
}

/**
 * 【优化】获取演员列表 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 演员列表
 */
function getActors($) {
    const actors = [];

    // 【优化】使用对标软件的精确选择器
    // 对标软件的XPath: //div[@id='avatar-waterfall']/a/span/text()
    const avatarWaterfallActors = $('#avatar-waterfall a span');
    if (avatarWaterfallActors.length > 0) {
        avatarWaterfallActors.each((i, el) => {
            const name = $(el).text().trim();
            const url = $(el).parent('a').attr('href');

            if (name && !actors.find(actor => actor.name === name)) {
                actors.push({
                    name: name,
                    url: url ? (url.startsWith('http') ? url : `https://avsox.click${url}`) : ''
                });
            }
        });

        return actors;
    }

    // 备用选择器
    const selectors = [
        '.info p:contains("演员") span a',
        '.info p:contains("演員") span a',
        '.info p:contains("Actress") span a',
        '.star-name a',
        '.actress a'
    ];

    selectors.forEach(selector => {
        $(selector).each((i, el) => {
            const name = $(el).text().trim();
            const url = $(el).attr('href');

            if (name && !actors.find(actor => actor.name === name)) {
                actors.push({
                    name: name,
                    url: url ? (url.startsWith('http') ? url : `https://avsox.click${url}`) : ''
                });
            }
        });
    });

    return actors;
}

/**
 * 【优化】获取类别标签 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 类别列表
 */
function getGenres($) {
    const genres = [];

    // 【优化】使用对标软件的精确选择器
    // 对标软件的XPath: //span[@class="genre"]/a/text()
    const genreSpans = $('span.genre a');
    if (genreSpans.length > 0) {
        genreSpans.each((i, el) => {
            const genre = $(el).text().trim();
            if (genre && !genres.includes(genre)) {
                genres.push(genre);
            }
        });

        return genres;
    }

    // 备用选择器
    const selectors = [
        '.info p:contains("类别") span a',
        '.info p:contains("類別") span a',
        '.info p:contains("Genre") span a',
        '.genre a'
    ];

    selectors.forEach(selector => {
        $(selector).each((i, el) => {
            const genre = $(el).text().trim();
            if (genre && !genres.includes(genre)) {
                genres.push(genre);
            }
        });
    });

    return genres;
}

/**
 * 获取所有标签 - 完整采集
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 所有标签
 */
function getAllTags($) {
    const tags = [];

    const tagSelectors = [
        '.info p:contains("标签") span a',
        '.info p:contains("標籤") span a',
        '.info p:contains("Tag") span a',
        '.tag a',
        '.keyword a',
        'a[href*="/tag/"]'
    ];

    tagSelectors.forEach(selector => {
        $(selector).each((i, el) => {
            const tag = $(el).text().trim();
            if (tag && !tags.includes(tag)) {
                tags.push(tag);
            }
        });
    });

    return tags;
}

/**
 * 获取预览图
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 预览图列表
 */
function getPreviewImages($) {
    const previewImages = [];

    const selectors = [
        '.sample-box img',
        '.preview-images img',
        '.screenshot img',
        'img[src*="sample"]',
        'img[src*="screenshot"]'
    ];

    selectors.forEach(selector => {
        $(selector).each((i, el) => {
            const src = $(el).attr('src') || $(el).attr('data-src') || $(el).attr('data-original');
            if (src) {
                const fullUrl = src.startsWith('http') ? src : `https://avsox.click${src}`;
                if (!previewImages.includes(fullUrl)) {
                    previewImages.push(fullUrl);
                }
            }
        });
    });

    return previewImages;
}

/**
 * 获取样品图像
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 样品图像列表
 */
function getSampleImages($) {
    const sampleImages = [];

    // 查找样品图像链接
    $('a[href*="sample"], a[href*="screenshot"]').each((i, el) => {
        const href = $(el).attr('href');
        const img = $(el).find('img');
        const thumbSrc = img.attr('src') || img.attr('data-src');

        if (href) {
            sampleImages.push({
                full: href.startsWith('http') ? href : `https://avsox.click${href}`,
                thumb: thumbSrc ? (thumbSrc.startsWith('http') ? thumbSrc : `https://avsox.click${thumbSrc}`) : '',
                index: i
            });
        }
    });

    return sampleImages;
}

/**
 * 【核心功能】获取磁力链接表 - 完整采集
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 磁力链接列表
 */
function getMagnetLinksTable($) {
    const magnetLinks = [];

    try {
        // 查找磁力链接表格
        const tableSelectors = [
            '#magnet-table tbody tr',
            '.magnet-table tbody tr',
            'table tbody tr',
            '.table tbody tr'
        ];

        for (const selector of tableSelectors) {
            const rows = $(selector);
            if (rows.length > 0) {
                rows.each((i, row) => {
                    const $row = $(row);

                    // 提取磁力链接
                    const magnetLink = $row.find('a[href^="magnet:"]').attr('href');
                    if (!magnetLink) return;

                    // 提取文件大小
                    const sizeText = $row.find('td').eq(1).text().trim() ||
                                   $row.find('.size').text().trim() ||
                                   $row.find('td:contains("GB"), td:contains("MB")').text().trim();

                    // 提取分享日期
                    const dateText = $row.find('td').eq(2).text().trim() ||
                                   $row.find('.date').text().trim() ||
                                   $row.find('td:contains("-")').text().trim();

                    // 检查是否有字幕
                    const hasSubtitles = $row.text().includes('字幕') ||
                                       $row.text().includes('中文') ||
                                       $row.find('.subtitle').length > 0;

                    // 提取清晰度信息
                    const quality = $row.find('.quality').text().trim() ||
                                  ($row.text().match(/(1080p|720p|4K|HD)/i) || [''])[0];

                    // 提取标题/描述
                    const title = $row.find('td').first().text().trim() ||
                                $row.find('.title').text().trim() ||
                                magnetLink.split('&dn=')[1]?.split('&')[0] || '';

                    magnetLinks.push({
                        link: magnetLink,
                        title: decodeURIComponent(title),
                        size: sizeText,
                        date: dateText,
                        hasSubtitles: hasSubtitles,
                        quality: quality,
                        source: 'avsox',
                        scrapedAt: new Date().toISOString()
                    });
                });

                break; // 找到表格后退出循环
            }
        }

        // 如果表格方式没找到，尝试其他方式
        if (magnetLinks.length === 0) {
            $('a[href^="magnet:"]').each((i, el) => {
                const link = $(el).attr('href');
                const title = $(el).text().trim() || $(el).attr('title') || '';

                magnetLinks.push({
                    link: link,
                    title: title,
                    size: '',
                    date: '',
                    hasSubtitles: title.includes('字幕') || title.includes('中文'),
                    quality: (title.match(/(1080p|720p|4K|HD)/i) || [''])[0],
                    source: 'avsox',
                    scrapedAt: new Date().toISOString()
                });
            });
        }

        log.info(`[AVSOX Provider] 获取到 ${magnetLinks.length} 个磁力链接`);

    } catch (error) {
        log.error(`[AVSOX Provider] 获取磁力链接失败: ${error.message}`);
    }

    return magnetLinks;
}

/**
 * 获取剧情简介
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 剧情简介
 */
function getPlot($) {
    const selectors = [
        '.info p:contains("简介") + p',
        '.info p:contains("簡介") + p',
        '.info p:contains("Plot") + p',
        '.description',
        '.plot',
        '.summary'
    ];

    for (const selector of selectors) {
        const plot = $(selector).text().trim();
        if (plot && plot.length > 10) {
            return plot;
        }
    }

    return '';
}

/**
 * 获取评分信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object|null} 评分信息
 */
function getRating($) {
    const ratingSelectors = [
        '.rating-score',
        '.score',
        '.star-rating',
        '[class*="rating"]'
    ];

    for (const selector of ratingSelectors) {
        const ratingElement = $(selector).first();
        if (ratingElement.length > 0) {
            const ratingText = ratingElement.text().trim();
            const ratingMatch = ratingText.match(/(\d+\.?\d*)/);

            if (ratingMatch) {
                return {
                    score: parseFloat(ratingMatch[1]),
                    maxScore: 5,
                    votes: 0,
                    source: 'avsox',
                    text: ratingText
                };
            }
        }
    }

    return null;
}

/**
 * 获取技术信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 技术信息
 */
function getTechnicalInfo($) {
    const technicalInfo = {};

    // 查找技术信息
    $('.info p').each((i, el) => {
        const text = $(el).text().trim();
        const colonIndex = text.indexOf('：') || text.indexOf(':');

        if (colonIndex > 0) {
            const key = text.substring(0, colonIndex).trim();
            const value = text.substring(colonIndex + 1).trim();

            if (key && value) {
                technicalInfo[key] = value;
            }
        }
    });

    return technicalInfo;
}

/**
 * 获取相关影片
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 相关影片列表
 */
function getRelatedMovies($) {
    const relatedMovies = [];

    const selectors = [
        '.related-movie',
        '.similar-movie',
        '.recommend-movie',
        '.movie-box'
    ];

    selectors.forEach(selector => {
        $(selector).each((i, el) => {
            const $el = $(el);
            const link = $el.find('a').attr('href');
            const title = $el.find('.photo-info span').text().trim() ||
                         $el.find('img').attr('alt') ||
                         $el.find('.title').text().trim();
            const image = $el.find('img').attr('src') || $el.find('img').attr('data-src');

            if (link && title && relatedMovies.length < 20) {
                relatedMovies.push({
                    title: title,
                    url: link.startsWith('http') ? link : `https://avsox.click${link}`,
                    image: image ? (image.startsWith('http') ? image : `https://avsox.click${image}`) : ''
                });
            }
        });
    });

    return relatedMovies;
}

module.exports = {
    name: PROVIDER_NAME,
    scrape,
    version: PROVIDER_VERSION
};
