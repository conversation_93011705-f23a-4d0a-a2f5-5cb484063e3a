#!/usr/bin/env node

// test-javdb-cdp.js - 测试改进后的 JavDB Provider (使用 CDP 连接)
const path = require('path');
const os = require('os');

async function testJavdbCDP() {
  console.log('🧪 JavDB Provider CDP 连接测试开始...\n');

  try {
    // 初始化设置服务
    console.log('初始化设置服务...');
    const settingsService = require('./main_process/services/settingsService');
    const userDataPath = path.join(os.tmpdir(), 'soulforge-test');
    settingsService.initializeSettings(console, userDataPath);
    console.log('✅ 设置服务初始化成功\n');

    // 加载改进后的 JavDB Provider
    console.log('加载 JavDB Provider...');
    const javdbProvider = require('./main_process/services/scrapers/javdbProvider');
    console.log(`✅ JavDB Provider 加载成功 (版本: ${javdbProvider.version})\n`);

    // 显示使用说明
    console.log('📋 使用说明:');
    console.log('1. 请先启动 Chrome 浏览器（使用调试模式）:');
    console.log('   chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\\temp\\chrome-debug"');
    console.log('2. 在浏览器中访问 https://javdb.com 并完成登录');
    console.log('3. 确保能正常访问内容后，按 Enter 继续测试\n');

    // 等待用户确认
    await waitForUserConfirmation();

    // 测试番号列表
    const testNfoIds = [
      'SSIS-001',  // 河北彩花的作品
      'JUFE-585',  // 常见测试番号
      'MIDV-018'   // 另一个常见番号
    ];

    for (const nfoId of testNfoIds) {
      console.log(`🔍 测试番号: ${nfoId}`);
      
      try {
        const startTime = Date.now();
        const scrapedData = await javdbProvider.scrape(nfoId);
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`✅ ${nfoId} 刮削成功! (耗时: ${duration.toFixed(2)}s)`);
        console.log(`📺 标题: ${scrapedData.title}`);
        console.log(`🎭 演员: ${scrapedData.actors.join(', ')}`);
        console.log(`📅 发行日期: ${scrapedData.releaseDate}`);
        console.log(`🏢 制作商: ${scrapedData.studio}`);
        console.log(`⭐ 评分: ${scrapedData.rating || '无'}`);
        console.log(`🏷️  标签: ${scrapedData.tags.slice(0, 3).join(', ')}${scrapedData.tags.length > 3 ? '...' : ''}`);
        console.log(`🖼️  封面: ${scrapedData.coverUrl ? '✅' : '❌'}`);
        console.log(`🎬 预览图: ${scrapedData.previewImages.length} 张`);
        console.log(`🧲 磁力链接: ${scrapedData.magnet_links.length} 个`);
        
        if (scrapedData.magnet_links.length > 0) {
          console.log('\n🧲 磁力链接详情:');
          scrapedData.magnet_links.forEach((magnet, index) => {
            console.log(`   ${index + 1}. ${magnet.name}`);
            console.log(`      大小: ${magnet.size || '未知'}`);
            console.log(`      字幕: ${magnet.has_subtitles ? '✅' : '❌'}`);
            console.log(`      链接: ${magnet.link.substring(0, 50)}...`);
          });
        }
        
        console.log(''); // 空行分隔
        
        // 测试成功一个就够了
        break;
        
      } catch (error) {
        console.log(`❌ ${nfoId} 刮削失败: ${error.message}`);
        
        if (error.message.includes('请先启动 Chrome')) {
          console.log('💡 建议: 请按照说明启动 Chrome 浏览器');
          break; // 如果是连接问题，不继续测试其他番号
        } else if (error.message.includes('Cloudflare')) {
          console.log('💡 建议: 被 Cloudflare 拦截，请在浏览器中手动完成验证');
        } else if (error.message.includes('未匹配到番号')) {
          console.log('💡 建议: 该番号在 JavDB 中不存在');
        } else if (error.message.includes('需要 VIP')) {
          console.log('💡 建议: 该内容需要 VIP 权限');
        } else if (error.message.includes('需要登入')) {
          console.log('💡 建议: 需要登录，请在浏览器中手动登录');
        }
        
        console.log(''); // 空行分隔
        
        // 继续测试下一个番号
        continue;
      }
    }
    
    console.log('🎉 JavDB Provider CDP 连接测试完成!');
    console.log('💡 注意: 浏览器保持打开状态，您可以继续使用');
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
    process.exit(1);
  }
}

/**
 * 等待用户确认
 */
function waitForUserConfirmation() {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('按 Enter 键继续测试...', () => {
      rl.close();
      resolve();
    });
  });
}

// 运行测试
if (require.main === module) {
  testJavdbCDP().catch(console.error);
}

module.exports = { testJavdbCDP };
