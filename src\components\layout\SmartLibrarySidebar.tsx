// soul-forge-electron/src/components/layout/SmartLibrarySidebar.tsx
import React, { useState, useEffect } from 'react';
import {
  LuLibrary,
  LuSparkles,
  LuClock,
  LuHeart,
  LuDownload,
  LuPlus,
  LuSettings,
  LuTrash2,
  LuChevronDown,
  LuChevronRight
} from 'react-icons/lu';

interface SmartLibrary {
  id: string;
  name: string;
  rules: any;
  icon?: string;
  sort_order: number;
}

interface SmartLibrarySidebarProps {
  onLibrarySelect: (library: SmartLibrary | null) => void;
  activeLibraryId?: string | null;
}

const SmartLibrarySidebar: React.FC<SmartLibrarySidebarProps> = ({
  onLibrarySelect,
  activeLibraryId
}) => {
  const [smartLibraries, setSmartLibraries] = useState<SmartLibrary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCustomLibrariesExpanded, setIsCustomLibrariesExpanded] = useState(true);

  // 默认的智能片库
  const defaultLibraries: SmartLibrary[] = [
    {
      id: 'all-movies',
      name: '全部影片',
      rules: {},
      icon: 'LuLibrary',
      sort_order: 0
    },
    {
      id: 'recent-added',
      name: '最新入库',
      rules: { sortField: 'db_id', sortOrder: 'desc' },
      icon: 'LuClock',
      sort_order: 1
    },
    {
      id: 'favorites',
      name: '我的收藏',
      rules: { favorited: true },
      icon: 'LuHeart',
      sort_order: 2
    },
    {
      id: 'virtual-assets',
      name: '待获取资产',
      rules: { asset_status: 'VIRTUAL' },
      icon: 'LuDownload',
      sort_order: 3
    }
  ];

  // 获取图标组件
  const getIconComponent = (iconName?: string) => {
    switch (iconName) {
      case 'LuLibrary': return LuLibrary;
      case 'LuSparkles': return LuSparkles;
      case 'LuClock': return LuClock;
      case 'LuHeart': return LuHeart;
      case 'LuDownload': return LuDownload;
      default: return LuSparkles;
    }
  };

  // 加载智能片库
  const loadSmartLibraries = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await window.sfeElectronAPI.getSmartLibraries();
      
      if (result.success) {
        setSmartLibraries(result.libraries || []);
      } else {
        setError(result.error || '加载智能片库失败');
      }
    } catch (err: any) {
      setError(err.message || '加载智能片库时发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadSmartLibraries();
  }, []);

  // 处理片库选择
  const handleLibraryClick = (library: SmartLibrary) => {
    onLibrarySelect(library);
  };

  // 渲染片库项
  const renderLibraryItem = (library: SmartLibrary, isDefault = false) => {
    const IconComponent = getIconComponent(library.icon);
    const isActive = activeLibraryId === library.id;
    
    return (
      <button
        key={library.id}
        onClick={() => handleLibraryClick(library)}
        className={`
          w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors
          ${isActive 
            ? 'bg-sky-600/80 text-white' 
            : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-white'
          }
        `}
        title={library.name}
      >
        <IconComponent className="w-4 h-4 mr-2 flex-shrink-0" />
        <span className="truncate">{library.name}</span>
      </button>
    );
  };

  return (
    <div className="w-64 bg-[#1a1a1a] border-r border-neutral-700 flex flex-col h-full">
      {/* 侧边栏标题 */}
      <div className="p-4 border-b border-neutral-700">
        <h2 className="text-lg font-semibold text-white flex items-center">
          <LuSparkles className="w-5 h-5 mr-2 text-sky-400" />
          智能片库
        </h2>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-3 space-y-4">
        {/* 默认智能片库 */}
        <div>
          <h3 className="text-xs font-medium text-neutral-400 uppercase tracking-wider mb-2 px-1">
            默认片库
          </h3>
          <div className="space-y-1">
            {defaultLibraries.map(library => renderLibraryItem(library, true))}
          </div>
        </div>

        {/* 自定义智能片库 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <button
              onClick={() => setIsCustomLibrariesExpanded(!isCustomLibrariesExpanded)}
              className="flex items-center text-xs font-medium text-neutral-400 uppercase tracking-wider hover:text-neutral-300 transition-colors px-1"
            >
              {isCustomLibrariesExpanded ? (
                <LuChevronDown className="w-3 h-3 mr-1" />
              ) : (
                <LuChevronRight className="w-3 h-3 mr-1" />
              )}
              自定义片库
            </button>
            <button
              onClick={() => {/* TODO: 打开创建智能片库的模态框 */}}
              className="p-1 text-neutral-400 hover:text-sky-400 transition-colors"
              title="创建智能片库"
            >
              <LuPlus className="w-3 h-3" />
            </button>
          </div>
          
          {isCustomLibrariesExpanded && (
            <div className="space-y-1">
              {isLoading ? (
                <div className="text-neutral-500 text-sm px-3 py-2">
                  加载中...
                </div>
              ) : error ? (
                <div className="text-red-400 text-sm px-3 py-2">
                  {error}
                </div>
              ) : smartLibraries.length === 0 ? (
                <div className="text-neutral-500 text-sm px-3 py-2">
                  暂无自定义片库
                </div>
              ) : (
                smartLibraries.map(library => renderLibraryItem(library, false))
              )}
            </div>
          )}
        </div>
      </div>

      {/* 底部操作区域 */}
      <div className="p-3 border-t border-neutral-700">
        <button
          onClick={() => {/* TODO: 打开智能片库管理页面 */}}
          className="w-full flex items-center justify-center px-3 py-2 text-sm text-neutral-400 hover:text-white hover:bg-neutral-700/50 rounded-md transition-colors"
        >
          <LuSettings className="w-4 h-4 mr-2" />
          管理片库
        </button>
      </div>
    </div>
  );
};

export default SmartLibrarySidebar;
