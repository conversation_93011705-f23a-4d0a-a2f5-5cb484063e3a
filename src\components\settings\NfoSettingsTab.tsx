// soul-forge-electron/src/components/settings/NfoSettingsTab.tsx
import React, { useState } from 'react';
import { AppSettings, FilenameSuffixRule } from '../../types';

interface NfoSettingsTabProps {
  settings: Partial<AppSettings>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  setSettings: React.Dispatch<React.SetStateAction<Partial<AppSettings>>>;
  getUniqueId: () => string;
}

const DEFAULT_FILENAME_RENAME_PLACEHOLDERS = [
  "{original_basename}: 原文件名 (不含扩展名)",
  "{nfoId}: NFO ID/番号",
  "{year}: 年份",
  "{title}: 影片标题",
  "{originalTitle}: 影片原标题",
  "{studio}: 制作商",
  "{series}: 系列",
  "{status_marker}: 状态标记 (例如 _首选)",
  "{version_categories_joined}: 版本分类 (下划线连接)",
  "{custom_tags_joined}: 自定义标签 (下划线连接)",
  "{auto_tags_joined}: 自动识别标签 (下划线连接)",
  "{resolution}: 分辨率 (例如 1920x1080)",
  "{extension}: 原文件扩展名 (例如 .mp4)",
  "{fps}: 帧率",
  "{videoBitrate}: 视频码率",
  "{audioBitrate}: 音频码率",
];


const NfoSettingsTab: React.FC<NfoSettingsTabProps> = ({
  settings,
  handleInputChange,
  setSettings,
  getUniqueId,
}) => {
  const [currentSuffixRule, setCurrentSuffixRule] = useState<{ id: string; suffix: string; tags: string }>({ id: '', suffix: '', tags: '' });
  const [editingSuffixRuleId, setEditingSuffixRuleId] = useState<string | null>(null);
  const [newPresetCategory, setNewPresetCategory] = useState('');

  const handleSuffixRuleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCurrentSuffixRule(prev => ({ ...prev, [name]: value }));
  };

  const handleAddOrUpdateSuffixRule = () => {
    if (!currentSuffixRule.suffix.trim() || !currentSuffixRule.tags.trim()) {
      alert("后缀和标签不能为空。"); return;
    }
    const tagsArray = currentSuffixRule.tags.split(',').map(t => t.trim()).filter(Boolean);
    if (tagsArray.length === 0) {
        alert("至少需要一个有效标签。"); return;
    }
    setSettings(prev => {
      const rules = prev.filenameSuffixRules || [];
      if (editingSuffixRuleId) { 
        return { ...prev, filenameSuffixRules: rules.map(r => r.id === editingSuffixRuleId ? { ...currentSuffixRule, id: String(currentSuffixRule.id), tags: tagsArray } : r) };
      } else { 
        if (rules.some(r => r.suffix === currentSuffixRule.suffix.trim())) {
            alert("该后缀规则已存在。");
            return prev;
        }
        return { ...prev, filenameSuffixRules: [...rules, { ...currentSuffixRule, id: getUniqueId(), suffix: currentSuffixRule.suffix.trim(), tags: tagsArray }] };
      }
    });
    setCurrentSuffixRule({ id: '', suffix: '', tags: '' });
    setEditingSuffixRuleId(null);
  };

  const handleEditSuffixRule = (rule: FilenameSuffixRule) => {
    setEditingSuffixRuleId(rule.id);
    setCurrentSuffixRule({ ...rule, id: String(rule.id), tags: rule.tags.join(', ') });
  };

  const handleRemoveSuffixRule = (ruleId: string) => {
    setSettings(prev => ({ ...prev, filenameSuffixRules: (prev.filenameSuffixRules || []).filter(r => r.id !== ruleId) }));
    if (editingSuffixRuleId === ruleId) {
        setCurrentSuffixRule({ id: '', suffix: '', tags: '' });
        setEditingSuffixRuleId(null);
    }
  };
  
  const handleCancelEditSuffixRule = () => {
    setCurrentSuffixRule({ id: '', suffix: '', tags: '' });
    setEditingSuffixRuleId(null);
  };

  const handleAddPresetCategory = () => {
    if (newPresetCategory.trim() && !(settings.presetVersionCategories || []).includes(newPresetCategory.trim())) {
      setSettings(prev => ({ ...prev, presetVersionCategories: [...(prev.presetVersionCategories || []), newPresetCategory.trim()] }));
      setNewPresetCategory('');
    } else if ((settings.presetVersionCategories || []).includes(newPresetCategory.trim())) {
        alert("该预设分类已存在。");
    }
  };

  const handleRemovePresetCategory = (categoryToRemove: string) => {
    setSettings(prev => ({ ...prev, presetVersionCategories: (prev.presetVersionCategories || []).filter(c => c !== categoryToRemove) }));
  };

  return (
    <div className="settings-group-content space-y-6">
      <div>
        <h4 className="settings-label font-semibold mb-2">NFO 文件处理</h4>
        <div className="space-y-2">
          <div className="flex items-center">
            <input type="checkbox" id="autoUpdateNfoWatchedRating" name="autoUpdateNfoWatchedRating" checked={!!settings.autoUpdateNfoWatchedRating} onChange={handleInputChange} className="form-checkbox-app"/>
            <label htmlFor="autoUpdateNfoWatchedRating" className="ml-2 text-sm text-neutral-100">当标记“已观看”或修改“个人评分”时，自动更新NFO文件。</label>
          </div>
          <div className="flex items-center">
            <input type="checkbox" id="autoCreateNfoOnSave" name="autoCreateNfoOnSave" checked={!!settings.autoCreateNfoOnSave} onChange={handleInputChange} className="form-checkbox-app"/>
            <label htmlFor="autoCreateNfoOnSave" className="ml-2 text-sm text-neutral-100">编辑无NFO的影片并保存时，自动创建新的NFO文件。</label>
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="settings-label font-semibold mb-2">文件名后缀自动识别规则</h4>
        <div className="mb-3 p-3 bg-[#2d2d2d] border border-[#4f4f4f] rounded-md">
          <h5 className="text-sm font-semibold text-neutral-100 mb-1.5">{editingSuffixRuleId ? "编辑规则" : "添加新规则"}</h5>
          <div className="flex flex-col sm:flex-row gap-2 items-end">
            <div className="flex-grow"><label htmlFor="suffixRuleSuffix" className="block text-xs text-neutral-300 mb-0.5">后缀 (如 -C, .HD)</label><input type="text" id="suffixRuleSuffix" name="suffix" value={currentSuffixRule.suffix} onChange={handleSuffixRuleInputChange} placeholder="例如: -C" className="form-input-app text-sm"/></div>
            <div className="flex-grow"><label htmlFor="suffixRuleTags" className="block text-xs text-neutral-300 mb-0.5">赋予的标签 (逗号分隔)</label><input type="text" id="suffixRuleTags" name="tags" value={currentSuffixRule.tags} onChange={handleSuffixRuleInputChange} placeholder="例如: 中文字幕版, 高清" className="form-input-app text-sm"/></div>
            <div className="flex gap-2">
              <button onClick={handleAddOrUpdateSuffixRule} className="button-primary-app px-3 py-1.5 text-sm whitespace-nowrap">{editingSuffixRuleId ? "更新规则" : "添加规则"}</button>
              {editingSuffixRuleId && <button onClick={handleCancelEditSuffixRule} className="button-secondary-app px-3 py-1.5 text-sm whitespace-nowrap">取消</button>}
            </div>
          </div>
        </div>
        <div className="max-h-32 overflow-y-auto space-y-1 pr-1 settings-scroll-container">
          {(settings.filenameSuffixRules || []).map(rule => (
            <div key={rule.id} className="flex items-center justify-between bg-[#2d2d2d] p-1.5 rounded border border-[#4f4f4f] text-xs">
              <span className="text-neutral-100"><strong className="text-sky-300">{rule.suffix}</strong> &rarr; {rule.tags.join(', ')}</span>
              <div className="space-x-1">
                <button onClick={() => handleEditSuffixRule(rule)} className="text-amber-400 hover:text-amber-300 font-semibold px-1">编辑</button>
                <button onClick={() => handleRemoveSuffixRule(String(rule.id))} className="text-red-400 hover:text-red-300 font-semibold px-1">移除</button>
              </div>
            </div>
          ))}
            {(!settings.filenameSuffixRules || settings.filenameSuffixRules.length === 0) && <p className="text-xs text-neutral-500 italic">暂无后缀规则。</p>}
        </div>
      </div>

      <div>
        <h4 className="settings-label font-semibold mb-2">预设版本分类</h4>
        <div className="max-h-32 overflow-y-auto space-y-1 pr-1 mb-2 settings-scroll-container">
          {(settings.presetVersionCategories || []).map(category => (
            <div key={category} className="flex items-center justify-between bg-[#2d2d2d] p-1.5 rounded border border-[#4f4f4f] text-xs">
              <span className="text-neutral-100">{category}</span>
              <button onClick={() => handleRemovePresetCategory(category)} className="text-red-400 hover:text-red-300 font-semibold px-1">移除</button>
            </div>
          ))}
          {(!settings.presetVersionCategories || settings.presetVersionCategories.length === 0) && <p className="text-xs text-neutral-500 italic">暂无预设版本分类。</p>}
        </div>
        <div className="flex items-center gap-2">
          <input type="text" value={newPresetCategory} onChange={(e) => setNewPresetCategory(e.target.value)} placeholder="添加新的预设分类..." className="form-input-app flex-grow text-sm"/>
          <button onClick={handleAddPresetCategory} className="button-primary-app px-3 py-1.5 text-sm" disabled={!newPresetCategory.trim()}>添加分类</button>
        </div>
      </div>
      
      <div>
        <label htmlFor="filenameRenameTemplate" className="settings-label font-semibold mb-1">文件名重命名模板</label>
        <textarea id="filenameRenameTemplate" name="filenameRenameTemplate" value={settings.filenameRenameTemplate || ''} onChange={handleInputChange} rows={3} className="form-textarea-app" placeholder="例如: {nfoId} {title} [{year}] {tags_joined}.{extension}"></textarea>
        <details className="text-xs text-neutral-400 mt-1">
          <summary className="cursor-pointer hover:text-amber-400">查看可用占位符</summary>
          <ul className="list-disc list-inside pl-2 mt-1 bg-[#2d2d2d] p-2 rounded border border-[#4f4f4f]">
            {DEFAULT_FILENAME_RENAME_PLACEHOLDERS.map(ph => <li key={ph}>{ph}</li>)}
          </ul>
        </details>
      </div>
    </div>
  );
};

export default NfoSettingsTab;
