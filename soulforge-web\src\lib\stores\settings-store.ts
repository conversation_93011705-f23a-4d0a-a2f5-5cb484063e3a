import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AppSettings } from '../types';

interface SettingsState {
  settings: AppSettings;
  loading: boolean;
  error: string | null;
  
  // Actions
  setSettings: (settings: Partial<AppSettings>) => void;
  updateSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => void;
  resetSettings: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Specific setting actions
  setAIProvider: (provider: AppSettings['aiProvider']) => void;
  setPrivacyMode: (enabled: boolean, password?: string) => void;
  setSnapshotQuality: (quality: AppSettings['snapshotQuality']) => void;
}

const defaultSettings: AppSettings = {
  // AI Settings
  aiProvider: 'gemini',
  customGptEndpoint: '',
  customGptApiKey: '',
  customGptModel: 'gpt-3.5-turbo',
  grokApiKey: '',
  
  // Media Settings
  snapshotQuality: 'hd_640p',
  snapshotCachePath: '',
  
  // Privacy Settings
  privacyModeEnabled: false,
  privacyModePassword: '',
  
  // Avatar Settings
  avatarDataSourceType: 'local',
  localFileTreePath: '',
  remoteGfriendsFiletreeUrl: '',
  actorAvatarLibraryPath: '',
  
  // File Settings
  filenameRenameTemplate: '{title} ({year})',
};

export const useSettingsStore = create<SettingsState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        settings: defaultSettings,
        loading: false,
        error: null,
        
        // Basic Actions
        setSettings: (newSettings) =>
          set((state) => ({
            settings: { ...state.settings, ...newSettings }
          })),
        
        updateSetting: (key, value) =>
          set((state) => ({
            settings: { ...state.settings, [key]: value }
          })),
        
        resetSettings: () => set({ settings: defaultSettings }),
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
        
        // Specific Actions
        setAIProvider: (provider) =>
          set((state) => ({
            settings: { ...state.settings, aiProvider: provider }
          })),
        
        setPrivacyMode: (enabled, password) =>
          set((state) => ({
            settings: {
              ...state.settings,
              privacyModeEnabled: enabled,
              privacyModePassword: password || state.settings.privacyModePassword
            }
          })),
        
        setSnapshotQuality: (quality) =>
          set((state) => ({
            settings: { ...state.settings, snapshotQuality: quality }
          })),
      }),
      {
        name: 'settings-store',
        partialize: (state) => ({ settings: state.settings }),
      }
    ),
    {
      name: 'settings-store',
    }
  )
);
