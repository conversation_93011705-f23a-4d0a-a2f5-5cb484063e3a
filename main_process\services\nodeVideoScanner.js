// Node.js 视频扫描器 - 替代 Python 脚本
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const NodeNfoParser = require('./nodeNfoParser');

class NodeVideoScanner {
  static VIDEO_EXTENSIONS = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg', '.m4v', '.ts', '.iso', '.strm'];
  static IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp'];
  static SUBTITLE_EXTENSIONS = ['.srt', '.ass', '.ssa', '.sub', '.idx', '.vtt'];

  static async scanPath(targetPath, options = {}) {
    const {
      ffprobePath = 'ffprobe',
      suffixRules = [],
      includeSubtitles = true,
      includeTechnicalInfo = true
    } = options;

    try {
      const stats = await fs.stat(targetPath);
      let filesToProcess = [];

      if (stats.isFile()) {
        if (this.isVideoFile(targetPath)) {
          filesToProcess.push(targetPath);
        }
      } else if (stats.isDirectory()) {
        filesToProcess = await this.findVideoFiles(targetPath);
      }

      const movies = [];
      for (const filePath of filesToProcess) {
        try {
          const movieData = await this.processVideoFile(filePath, {
            ffprobePath,
            suffixRules,
            includeSubtitles,
            includeTechnicalInfo
          });
          if (movieData) {
            movies.push(movieData);
          }
        } catch (error) {
          console.error(`处理视频文件失败: ${filePath}`, error);
        }
      }

      return { success: true, movies };
    } catch (error) {
      return { success: false, error: error.message, movies: [] };
    }
  }

  static async findVideoFiles(dirPath) {
    const videoFiles = [];

    async function walkDir(currentPath) {
      try {
        const entries = await fs.readdir(currentPath, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);
          
          if (entry.isDirectory()) {
            await walkDir(fullPath);
          } else if (entry.isFile() && NodeVideoScanner.isVideoFile(fullPath)) {
            videoFiles.push(fullPath);
          }
        }
      } catch (error) {
        console.error(`读取目录失败: ${currentPath}`, error);
      }
    }

    await walkDir(dirPath);
    return videoFiles;
  }

  static isVideoFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return this.VIDEO_EXTENSIONS.includes(ext);
  }

  static async processVideoFile(filePath, options) {
    const fileName = path.basename(filePath);
    const fileDir = path.dirname(filePath);
    const baseName = path.basename(fileName, path.extname(fileName));

    // 基本文件信息
    const movieData = {
      filePath,
      fileName,
      title: baseName, // 默认标题
      fileSize: null,
      lastScanned: new Date().toISOString(),
      nfoId: null,
      localCoverPath: null,
      hasExternalSubtitles: false,
      cdPartInfo: null,
      autoDetectedFileNameTags: [],
    };

    try {
      // 获取文件大小
      const stats = await fs.stat(filePath);
      movieData.fileSize = stats.size;
    } catch (error) {
      console.error(`获取文件大小失败: ${filePath}`, error);
    }

    // 处理 NFO 文件
    const nfoPath = path.join(fileDir, `${baseName}.nfo`);
    try {
      await fs.access(nfoPath);
      const nfoResult = await NodeNfoParser.parseNfoFile(nfoPath);
      
      if (nfoResult.success && nfoResult.data) {
        // 合并 NFO 数据
        Object.assign(movieData, {
          title: nfoResult.data.title || movieData.title,
          originalTitle: nfoResult.data.originalTitle,
          year: nfoResult.data.year,
          releaseDate: nfoResult.data.releaseDate,
          runtime: nfoResult.data.runtime,
          plot: nfoResult.data.plot,
          studio: nfoResult.data.studio,
          director: nfoResult.data.director,
          trailerUrl: nfoResult.data.trailerUrl,
          posterUrl: nfoResult.data.posterUrl,
          coverUrl: nfoResult.data.coverUrl,
          rating: nfoResult.data.rating,
          votes: nfoResult.data.votes,
          actors: nfoResult.data.actors ? JSON.stringify(nfoResult.data.actors) : null,
          genres: nfoResult.data.genres ? JSON.stringify(nfoResult.data.genres) : null,
          tags: nfoResult.data.tags ? JSON.stringify(nfoResult.data.tags) : null,
        });

        // 设置 nfoId
        if (nfoResult.data.id) {
          movieData.nfoId = nfoResult.data.id;
        }

        // 获取 NFO 文件修改时间
        try {
          const nfoStats = await fs.stat(nfoPath);
          movieData.nfoLastModified = nfoStats.mtime;
        } catch (error) {
          console.error(`获取 NFO 文件修改时间失败: ${nfoPath}`, error);
        }
      }
    } catch (error) {
      // NFO 文件不存在或解析失败，继续处理
    }

    // 如果没有从 NFO 中获取到 nfoId，尝试从文件名提取
    if (!movieData.nfoId) {
      movieData.nfoId = NodeNfoParser.extractJavIdFromFilename(fileName);
    }

    // 查找本地封面
    movieData.localCoverPath = await this.findLocalCover(fileDir, baseName);

    // 检查外部字幕
    if (options.includeSubtitles) {
      movieData.hasExternalSubtitles = await this.checkExternalSubtitles(fileDir, baseName);
    }

    // 提取 CD 分集信息
    movieData.cdPartInfo = this.extractCdPartInfo(fileName);

    // 应用文件名后缀规则
    if (options.suffixRules && options.suffixRules.length > 0) {
      movieData.autoDetectedFileNameTags = this.applyFilenameSuffixRules(fileName, options.suffixRules);
    }

    // 获取技术信息
    if (options.includeTechnicalInfo && !filePath.toLowerCase().endsWith('.strm')) {
      const techInfo = await this.getTechnicalInfo(filePath, options.ffprobePath);
      Object.assign(movieData, techInfo);
    }

    return movieData;
  }

  static async findLocalCover(videoDir, baseName) {
    // 优先查找特定名称的封面
    const specificNames = [baseName, `${baseName}-poster`, `${baseName}-cover`];
    
    for (const name of specificNames) {
      for (const ext of this.IMAGE_EXTENSIONS) {
        const coverPath = path.join(videoDir, `${name}${ext}`);
        try {
          await fs.access(coverPath);
          return coverPath;
        } catch {
          // 继续查找下一个
        }
      }
    }

    // 查找通用名称的封面
    const genericNames = ['poster', 'folder', 'cover', 'thumb', 'movie'];
    
    for (const name of genericNames) {
      for (const ext of this.IMAGE_EXTENSIONS) {
        const coverPath = path.join(videoDir, `${name}${ext}`);
        try {
          await fs.access(coverPath);
          return coverPath;
        } catch {
          // 继续查找下一个
        }
      }
    }

    return null;
  }

  static async checkExternalSubtitles(videoDir, baseName) {
    for (const ext of this.SUBTITLE_EXTENSIONS) {
      const subtitlePath = path.join(videoDir, `${baseName}${ext}`);
      try {
        await fs.access(subtitlePath);
        return true;
      } catch {
        // 继续检查下一个
      }
    }
    return false;
  }

  static extractCdPartInfo(fileName) {
    const patterns = [
      /(?:CD|DISK|DISC|PART|PT)[\s._-]*([A-D1-9])\b/i,
      /CD_(\d+)/i,
      /DISC(\d)/i,
      /FILE[\s._-]*(\d+)/i,
      /(\d+)[\s._-]*OF[\s._-]*(\d+)/i,
      /[\s._-]([A-D])$/i,
      /[\s._-](\d)$/i,
    ];

    const baseName = fileName.replace(/\.[^.]+$/, '').toUpperCase();

    for (const pattern of patterns) {
      const match = baseName.match(pattern);
      if (match) {
        if (pattern.source.includes('OF')) {
          return `Part${match[1]}_Total${match[2]}`;
        } else {
          return `Part${match[1]}`;
        }
      }
    }

    return null;
  }

  static applyFilenameSuffixRules(fileName, suffixRules) {
    const tags = [];
    const lowerFileName = fileName.toLowerCase();

    for (const rule of suffixRules) {
      if (rule.suffix && rule.tag) {
        if (lowerFileName.includes(rule.suffix.toLowerCase())) {
          tags.push(rule.tag);
        }
      }
    }

    return tags;
  }

  static async getTechnicalInfo(filePath, ffprobePath) {
    return new Promise((resolve) => {
      const command = [
        ffprobePath,
        '-v', 'error',
        '-print_format', 'json',
        '-show_format',
        '-show_streams',
        filePath
      ];

      const ffprobe = spawn(command[0], command.slice(1));
      let stdout = '';
      let stderr = '';

      ffprobe.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      ffprobe.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffprobe.on('close', (code) => {
        if (code !== 0 || !stdout.trim()) {
          resolve({
            resolution: null,
            videoHeight: null,
            videoCodec: null,
            audioCodec: null,
            fps: null,
            videoBitrate: null,
            audioChannelsDesc: null,
            audioSampleRate: null,
            audioBitrate: null,
          });
          return;
        }

        try {
          const data = JSON.parse(stdout);
          const videoStream = data.streams?.find(s => s.codec_type === 'video');
          const audioStream = data.streams?.find(s => s.codec_type === 'audio');

          resolve({
            resolution: videoStream ? `${videoStream.width}x${videoStream.height}` : null,
            videoHeight: videoStream?.height || null,
            videoCodec: videoStream?.codec_name || null,
            audioCodec: audioStream?.codec_name || null,
            fps: videoStream?.r_frame_rate ? eval(videoStream.r_frame_rate) : null,
            videoBitrate: videoStream?.bit_rate ? parseInt(videoStream.bit_rate) : null,
            audioChannelsDesc: audioStream?.channels ? `${audioStream.channels} channels` : null,
            audioSampleRate: audioStream?.sample_rate ? parseInt(audioStream.sample_rate) : null,
            audioBitrate: audioStream?.bit_rate ? parseInt(audioStream.bit_rate) : null,
          });
        } catch (error) {
          resolve({
            resolution: null,
            videoHeight: null,
            videoCodec: null,
            audioCodec: null,
            fps: null,
            videoBitrate: null,
            audioChannelsDesc: null,
            audioSampleRate: null,
            audioBitrate: null,
          });
        }
      });
    });
  }
}

module.exports = NodeVideoScanner;
