
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* 打包器模式 */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* 代码检查 */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "esModuleInterop": true,

    /* 路径别名 (可选 - 如果需要，取消注释并调整) */
    // "baseUrl": ".",
    // "paths": {
    //   "@/*": ["./src/*"]
    // }
  },
  "include": ["src", "vite.config.ts", "tailwind.config.js", "postcss.config.js"],
  "references": [{ "path": "./tsconfig.node.json" }]
}