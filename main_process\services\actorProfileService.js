// main_process/services/actorProfileService.js
// 档案管理处 - 统一管理人物档案的获取、缓存和更新

const log = require('electron-log');
const path = require('path');
const fs = require('fs').promises;
const { scrapeActorProfile } = require('./scrapers/actorWikiProvider');
const { scrapeActorFilmography } = require('./scrapers/dmmActorProvider');
const avWikiActorProvider = require('./scrapers/avWikiActorProvider');

/**
 * 人物档案管理服务
 * 负责档案的获取、缓存、更新和头像下载
 */
class ActorProfileService {
  constructor() {
    this.name = '档案管理处';
    this.version = '1.0.0';
    this.cacheValidityDays = 30; // 缓存有效期30天
    this.db = null; // 将在初始化时设置
    this.avatarCacheDir = null; // 头像缓存目录
  }

  /**
   * 初始化服务
   * @param {Object} database - 数据库实例
   * @param {string} userDataPath - 用户数据路径
   */
  initialize(database, userDataPath) {
    this.db = database;
    this.avatarCacheDir = path.join(userDataPath, 'actor_avatars');
    
    // 确保头像缓存目录存在
    this.ensureAvatarCacheDir();
    
    log.info(`[${this.name}] 服务初始化完成`);
  }

  /**
   * 确保头像缓存目录存在
   */
  async ensureAvatarCacheDir() {
    try {
      await fs.mkdir(this.avatarCacheDir, { recursive: true });
    } catch (error) {
      log.error(`[${this.name}] 创建头像缓存目录失败: ${error.message}`);
    }
  }

  /**
   * 获取演员档案
   * @param {string} actorName - 演员姓名
   * @returns {Promise<Object>} 档案结果对象
   */
  async getActorProfile(actorName) {
    log.info(`[${this.name}] 开始获取演员档案: ${actorName}`);

    try {
      // 查询本地缓存
      const cachedProfile = await this.getCachedProfile(actorName);
      
      // 判断缓存有效性
      if (cachedProfile && this.isCacheValid(cachedProfile.last_scraped_at)) {
        log.info(`[${this.name}] 使用缓存档案: ${actorName}`);
        return {
          success: true,
          data: cachedProfile,
          source: 'cache',
          message: '从缓存获取档案成功'
        };
      }

      // 调用"考古特工"
      log.info(`[${this.name}] 缓存无效或不存在，开始刮削: ${actorName}`);
      const scrapedProfile = await scrapeActorProfile(actorName);
      
      if (!scrapedProfile) {
        return {
          success: false,
          data: null,
          source: 'scraper',
          message: '未找到该演员的档案信息'
        };
      }

      // 更新档案与缓存头像
      const savedProfile = await this.saveProfile(scrapedProfile);
      
      if (savedProfile) {
        // 异步下载头像
        this.downloadAvatarAsync(savedProfile);
        
        log.info(`[${this.name}] 档案刮削和保存成功: ${actorName}`);
        return {
          success: true,
          data: savedProfile,
          source: 'scraper',
          message: '档案刮削成功'
        };
      } else {
        return {
          success: false,
          data: null,
          source: 'scraper',
          message: '档案保存失败'
        };
      }

    } catch (error) {
      log.error(`[${this.name}] 获取演员档案失败: ${error.message}`);
      return {
        success: false,
        data: null,
        source: 'error',
        message: `获取档案时发生错误: ${error.message}`
      };
    }
  }

  /**
   * 查询本地缓存档案
   * @param {string} actorName - 演员姓名
   * @returns {Promise<Object|null>} 缓存的档案对象
   */
  async getCachedProfile(actorName) {
    try {
      const stmt = this.db.prepare('SELECT * FROM actor_profiles WHERE name = ?');
      const profile = stmt.get(actorName);
      
      if (profile) {
        // 解析JSON字段
        profile.aliases = JSON.parse(profile.aliases || '[]');
        profile.stats = JSON.parse(profile.stats || '{}');
        profile.tags = JSON.parse(profile.tags || '[]');
        profile.external_ids = JSON.parse(profile.external_ids || '{}');
      }
      
      return profile;
    } catch (error) {
      log.error(`[${this.name}] 查询缓存档案失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 判断缓存是否有效
   * @param {string} lastScrapedAt - 最后刮削时间
   * @returns {boolean} 是否有效
   */
  isCacheValid(lastScrapedAt) {
    if (!lastScrapedAt) return false;
    
    try {
      const lastScraped = new Date(lastScrapedAt);
      const now = new Date();
      const diffDays = (now - lastScraped) / (1000 * 60 * 60 * 24);
      
      return diffDays < this.cacheValidityDays;
    } catch (error) {
      log.error(`[${this.name}] 判断缓存有效性失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 保存档案到数据库
   * @param {Object} profile - 档案对象
   * @returns {Promise<Object|null>} 保存后的档案对象
   */
  async saveProfile(profile) {
    try {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO actor_profiles 
        (name, aliases, avatar_remote_url, avatar_local_path, stats, bio, tags, source_url, external_ids, last_scraped_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      const result = stmt.run(
        profile.name,
        profile.aliases,
        profile.avatar_remote_url,
        profile.avatar_local_path,
        profile.stats,
        profile.bio,
        profile.tags,
        profile.source_url,
        profile.external_ids,
        profile.last_scraped_at
      );

      if (result.changes > 0) {
        // 返回保存后的完整档案
        return await this.getCachedProfile(profile.name);
      }
      
      return null;
    } catch (error) {
      log.error(`[${this.name}] 保存档案失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 异步下载头像
   * @param {Object} profile - 档案对象
   */
  async downloadAvatarAsync(profile) {
    if (!profile.avatar_remote_url || !this.avatarCacheDir) {
      return;
    }

    try {
      log.debug(`[${this.name}] 开始下载头像: ${profile.name}`);
      
      // 生成本地文件名
      const fileExtension = this.getImageExtension(profile.avatar_remote_url);
      const fileName = `${this.sanitizeFileName(profile.name)}_${Date.now()}${fileExtension}`;
      const localPath = path.join(this.avatarCacheDir, fileName);

      // 下载图片
      const response = await fetch(profile.avatar_remote_url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const buffer = await response.arrayBuffer();
      await fs.writeFile(localPath, Buffer.from(buffer));

      // 更新数据库中的本地路径
      const updateStmt = this.db.prepare('UPDATE actor_profiles SET avatar_local_path = ? WHERE name = ?');
      updateStmt.run(localPath, profile.name);

      log.debug(`[${this.name}] 头像下载成功: ${profile.name} -> ${localPath}`);

    } catch (error) {
      log.error(`[${this.name}] 下载头像失败: ${profile.name}, 错误: ${error.message}`);
    }
  }

  /**
   * 获取图片扩展名
   * @param {string} url - 图片URL
   * @returns {string} 扩展名
   */
  getImageExtension(url) {
    const match = url.match(/\.(jpg|jpeg|png|gif|webp)(\?|$)/i);
    return match ? '.' + match[1].toLowerCase() : '.jpg';
  }

  /**
   * 清理文件名中的非法字符
   * @param {string} fileName - 原始文件名
   * @returns {string} 清理后的文件名
   */
  sanitizeFileName(fileName) {
    return fileName.replace(/[<>:"/\\|?*]/g, '_').replace(/\s+/g, '_');
  }

  /**
   * 获取演员完整作品列表（包括本地没有的）
   * @param {string} actorName - 演员姓名
   * @returns {Promise<Object>} 完整作品列表结果
   */
  async getActorCompleteFilmography(actorName) {
    log.info(`[${this.name}] 开始获取演员完整作品列表: ${actorName}`);

    try {
      // 并行获取本地作品和远程作品（多数据源）
      const [localMovies, dmmResult, avWikiResult] = await Promise.all([
        this.getLocalMoviesByActor(actorName),
        scrapeActorFilmography(actorName),
        avWikiActorProvider.scrapeActorFilmography(actorName)
      ]);

      // 合并远程数据源的结果
      let remoteMovies = [];
      let remoteDataSource = '';

      if (dmmResult.success && dmmResult.data && dmmResult.data.length > 0) {
        remoteMovies = dmmResult.data.map(movie => ({ ...movie, source: 'dmm' }));
        remoteDataSource = 'DMM';
        log.info(`[${this.name}] DMM刮削成功: ${remoteMovies.length} 部作品`);
      } else if (avWikiResult.success && avWikiResult.data && avWikiResult.data.length > 0) {
        remoteMovies = avWikiResult.data.map(movie => ({ ...movie, source: 'av-wiki' }));
        remoteDataSource = 'AV-Wiki';
        log.info(`[${this.name}] AV-Wiki刮削成功: ${remoteMovies.length} 部作品`);
      } else {
        log.warn(`[${this.name}] 所有远程数据源刮削失败 - DMM: ${dmmResult.error}, AV-Wiki: ${avWikiResult.error}`);
        return {
          success: true,
          data: {
            localMovies: localMovies,
            remoteMovies: [],
            totalCount: localMovies.length,
            localCount: localMovies.length,
            remoteCount: 0
          },
          message: `仅找到本地作品 ${localMovies.length} 部，远程刮削失败`
        };
      }

      // 合并和标记作品
      const mergedResult = this.mergeLocalAndRemoteMovies(localMovies, remoteMovies);

      log.info(`[${this.name}] 完整作品列表获取成功: 本地${mergedResult.localCount}部, 远程${mergedResult.remoteCount}部 (来源: ${remoteDataSource}), 总计${mergedResult.totalCount}部`);

      return {
        success: true,
        data: mergedResult,
        message: `找到完整作品列表: 本地${mergedResult.localCount}部, 远程${mergedResult.remoteCount}部 (来源: ${remoteDataSource})`
      };

    } catch (error) {
      log.error(`[${this.name}] 获取完整作品列表失败: ${error.message}`);
      return {
        success: false,
        data: null,
        message: `获取完整作品列表失败: ${error.message}`
      };
    }
  }

  /**
   * 获取本地演员作品
   * @param {string} actorName - 演员姓名
   * @returns {Promise<Array>} 本地作品列表
   */
  async getLocalMoviesByActor(actorName) {
    try {
      const databaseService = require('./databaseService');
      return databaseService.getMoviesByActor(actorName);
    } catch (error) {
      log.error(`[${this.name}] 获取本地作品失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 合并本地和远程作品
   * @param {Array} localMovies - 本地作品
   * @param {Array} remoteMovies - 远程作品
   * @returns {Object} 合并结果
   */
  mergeLocalAndRemoteMovies(localMovies, remoteMovies) {
    // 创建本地作品的番号映射
    const localNfoIds = new Set();
    const localTitles = new Set();

    localMovies.forEach(movie => {
      if (movie.nfoId) localNfoIds.add(movie.nfoId.toUpperCase());
      if (movie.title) localTitles.add(movie.title);
    });

    // 标记本地作品
    const markedLocalMovies = localMovies.map(movie => ({
      ...movie,
      isLocal: true,
      isRemote: false,
      source: 'local'
    }));

    // 过滤和标记远程作品（排除本地已有的）
    const markedRemoteMovies = remoteMovies
      .filter(movie => {
        const nfoId = movie.nfoId ? movie.nfoId.toUpperCase() : '';
        const title = movie.title || '';

        // 如果本地已有相同番号或标题，则排除
        return !localNfoIds.has(nfoId) && !localTitles.has(title);
      })
      .map(movie => ({
        ...movie,
        isLocal: false,
        isRemote: true
        // source字段保持原有值（dmm 或 av-wiki）
      }));

    // 合并结果
    const allMovies = [...markedLocalMovies, ...markedRemoteMovies];

    return {
      localMovies: markedLocalMovies,
      remoteMovies: markedRemoteMovies,
      allMovies: allMovies,
      totalCount: allMovies.length,
      localCount: markedLocalMovies.length,
      remoteCount: markedRemoteMovies.length
    };
  }

  /**
   * 获取所有档案列表
   * @param {number} limit - 限制数量
   * @param {number} offset - 偏移量
   * @returns {Promise<Array>} 档案列表
   */
  async getAllProfiles(limit = 50, offset = 0) {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM actor_profiles
        ORDER BY last_scraped_at DESC
        LIMIT ? OFFSET ?
      `);

      const profiles = stmt.all(limit, offset);

      // 解析JSON字段
      return profiles.map(profile => {
        profile.aliases = JSON.parse(profile.aliases || '[]');
        profile.stats = JSON.parse(profile.stats || '{}');
        profile.tags = JSON.parse(profile.tags || '[]');
        profile.external_ids = JSON.parse(profile.external_ids || '{}');
        return profile;
      });
    } catch (error) {
      log.error(`[${this.name}] 获取档案列表失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 删除档案
   * @param {string} actorName - 演员姓名
   * @returns {Promise<boolean>} 是否成功
   */
  async deleteProfile(actorName) {
    try {
      // 获取档案信息以删除头像文件
      const profile = await this.getCachedProfile(actorName);
      
      // 删除数据库记录
      const stmt = this.db.prepare('DELETE FROM actor_profiles WHERE name = ?');
      const result = stmt.run(actorName);

      // 删除头像文件
      if (profile && profile.avatar_local_path) {
        try {
          await fs.unlink(profile.avatar_local_path);
        } catch (fileError) {
          log.warn(`[${this.name}] 删除头像文件失败: ${fileError.message}`);
        }
      }

      log.info(`[${this.name}] 档案删除${result.changes > 0 ? '成功' : '失败'}: ${actorName}`);
      return result.changes > 0;
    } catch (error) {
      log.error(`[${this.name}] 删除档案失败: ${error.message}`);
      return false;
    }
  }
}

// 创建单例实例
const actorProfileService = new ActorProfileService();

module.exports = {
  actorProfileService,
  ActorProfileService
};
