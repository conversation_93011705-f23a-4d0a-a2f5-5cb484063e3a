// Node.js NFO 写入器 - 替代 Python 脚本
const fs = require('fs').promises;
const path = require('path');
const { parseString, Builder } = require('xml2js');

class NodeNfoWriter {
  static async createOrUpdateNfo(videoFilePath, movieData) {
    try {
      const baseName = path.basename(videoFilePath, path.extname(videoFilePath));
      const nfoFilePath = path.join(path.dirname(videoFilePath), `${baseName}.nfo`);

      let root = null;
      let existingXml = null;

      // 尝试加载现有的 NFO 文件
      try {
        const existingContent = await fs.readFile(nfoFilePath, 'utf-8');
        const parseResult = await this.parseExistingNfo(existingContent);
        if (parseResult.success) {
          existingXml = parseResult.data;
          root = existingXml.movie || {};
        }
      } catch (error) {
        // NFO 文件不存在或解析失败，创建新的
        console.log(`创建新的 NFO 文件: ${nfoFilePath}`);
      }

      // 如果没有现有的根元素，创建新的
      if (!root) {
        root = {};
      }

      // 更新电影数据
      this.updateMovieElement(root, movieData);

      // 构建 XML
      const builder = new Builder({
        rootName: 'movie',
        xmldec: { version: '1.0', encoding: 'UTF-8' },
        renderOpts: { pretty: true, indent: '  ', newline: '\n' }
      });

      const xml = builder.buildObject(root);

      // 写入文件
      await fs.writeFile(nfoFilePath, xml, 'utf-8');

      return {
        success: true,
        nfoFilePath,
        message: `NFO 文件已${existingXml ? '更新' : '创建'}: ${nfoFilePath}`
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  static async parseExistingNfo(content) {
    return new Promise((resolve) => {
      // 清理内容
      const cleanContent = content
        .replace(/^\uFEFF/, '') // 移除 BOM
        .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;'); // 修复未转义的 &

      parseString(cleanContent, {
        explicitArray: false,
        ignoreAttrs: false,
        trim: true,
        mergeAttrs: true
      }, (err, result) => {
        if (err) {
          resolve({ success: false, error: err.message });
        } else {
          resolve({ success: true, data: result });
        }
      });
    });
  }

  static updateMovieElement(root, movieData) {
    // 基本信息
    if (movieData.title) root.title = movieData.title;
    if (movieData.originalTitle) root.originaltitle = movieData.originalTitle;
    if (movieData.year) root.year = movieData.year;
    if (movieData.releaseDate) root.premiered = movieData.releaseDate;
    if (movieData.runtime) root.runtime = movieData.runtime;
    if (movieData.plot) root.plot = movieData.plot;
    if (movieData.studio) root.studio = movieData.studio;
    if (movieData.director) root.director = movieData.director;
    if (movieData.trailerUrl) root.trailer = movieData.trailerUrl;

    // ID 信息
    if (movieData.nfoId) {
      // 更新或创建 uniqueid
      if (!root.uniqueid) {
        root.uniqueid = [];
      } else if (!Array.isArray(root.uniqueid)) {
        root.uniqueid = [root.uniqueid];
      }

      // 查找现有的主要 ID
      let primaryIdExists = false;
      for (let uid of root.uniqueid) {
        if (uid.$ && (uid.$.default === 'true' || uid.$.type === 'primary')) {
          uid._ = movieData.nfoId;
          primaryIdExists = true;
          break;
        }
      }

      // 如果没有主要 ID，添加一个
      if (!primaryIdExists) {
        root.uniqueid.unshift({
          $: { type: 'primary', default: 'true' },
          _: movieData.nfoId
        });
      }

      // 同时设置简单的 id 标签
      root.id = movieData.nfoId;
    }

    // 海报和封面
    if (movieData.posterUrl) root.thumb = movieData.posterUrl;
    if (movieData.coverUrl) root.fanart = movieData.coverUrl;

    // 评分信息
    if (movieData.rating !== undefined) root.rating = movieData.rating;
    if (movieData.votes !== undefined) root.votes = movieData.votes;
    if (movieData.personalRating !== undefined) root.userrating = movieData.personalRating;

    // 演员信息
    if (movieData.actors) {
      const actors = typeof movieData.actors === 'string' 
        ? JSON.parse(movieData.actors) 
        : movieData.actors;

      if (Array.isArray(actors) && actors.length > 0) {
        root.actor = actors.map(actor => ({
          name: actor.name,
          role: actor.role || '',
          thumb: actor.thumb || ''
        }));
      }
    }

    // 类型信息
    if (movieData.genres) {
      const genres = typeof movieData.genres === 'string' 
        ? JSON.parse(movieData.genres) 
        : movieData.genres;

      if (Array.isArray(genres) && genres.length > 0) {
        root.genre = genres;
      }
    }

    // 标签信息
    if (movieData.tags) {
      const tags = typeof movieData.tags === 'string' 
        ? JSON.parse(movieData.tags) 
        : movieData.tags;

      if (Array.isArray(tags) && tags.length > 0) {
        root.tag = tags;
      }
    }

    // 文件信息
    if (movieData.filePath) {
      root.filenameandpath = movieData.filePath;
    }

    // 添加或更新最后修改时间戳
    root.dateadded = new Date().toISOString();

    // 如果有自定义字段，也可以添加
    if (movieData.customFields) {
      Object.assign(root, movieData.customFields);
    }
  }

  static async validateNfoFile(nfoFilePath) {
    try {
      const content = await fs.readFile(nfoFilePath, 'utf-8');
      const parseResult = await this.parseExistingNfo(content);
      
      if (parseResult.success) {
        const movieData = parseResult.data.movie || parseResult.data;
        return {
          success: true,
          valid: true,
          data: movieData,
          message: 'NFO 文件格式正确'
        };
      } else {
        return {
          success: true,
          valid: false,
          error: parseResult.error,
          message: 'NFO 文件格式错误'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  static async backupNfoFile(nfoFilePath) {
    try {
      const backupPath = `${nfoFilePath}.backup.${Date.now()}`;
      await fs.copyFile(nfoFilePath, backupPath);
      return {
        success: true,
        backupPath,
        message: `NFO 文件已备份到: ${backupPath}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  static async createMinimalNfo(videoFilePath, basicInfo = {}) {
    const movieData = {
      title: basicInfo.title || path.basename(videoFilePath, path.extname(videoFilePath)),
      nfoId: basicInfo.nfoId,
      year: basicInfo.year,
      plot: basicInfo.plot || '',
      ...basicInfo
    };

    return await this.createOrUpdateNfo(videoFilePath, movieData);
  }
}

module.exports = NodeNfoWriter;
