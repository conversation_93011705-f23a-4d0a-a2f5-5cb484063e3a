/**
 * 采集器协调器 - 统一管理所有采集功能模块
 * 
 * 这个协调器负责：
 * - 协调各个功能模块的工作
 * - 管理采集流程
 * - 处理模块间的通信
 * - 提供统一的接口给外部调用
 */

const { chromium } = require('playwright');
const DownloadManager = require('./DownloadManager');
const FileRenameManager = require('./FileRenameManager');
const RecordManager = require('./RecordManager');
const TaskManager = require('./TaskManager');
const NodeNfoParser = require('./nodeNfoParser');

class CollectorOrchestrator {
  constructor(config) {
    this.log = config.log;
    this.siteProfileService = config.siteProfileService;
    this.databaseService = config.databaseService;
    
    // 初始化功能模块
    this.fileRenameManager = new FileRenameManager({ log: this.log });
    
    this.downloadManager = new DownloadManager({
      log: this.log,
      databaseService: this.databaseService,
      fileRenameManager: this.fileRenameManager,
      updateTaskStatus: this.updateTaskStatus.bind(this)
    });
    
    this.recordManager = new RecordManager({
      log: this.log,
      databaseService: this.databaseService,
      updateTaskStatus: this.updateTaskStatus.bind(this)
    });
    
    this.taskManager = new TaskManager({
      log: this.log,
      statusUpdateCallback: config.statusUpdateCallback
    });

    // 配置
    this.workspacePath = null;
    this.enableDownload = false;
    this.browser = null;
    this.context = null;
    
    // 绑定任务管理器的状态更新
    this.taskManager.on('statusUpdate', (status) => {
      this.log.info(`[CollectorOrchestrator] 任务状态: ${status.status} - ${status.message}`);
    });
  }

  /**
   * 设置状态更新回调
   */
  setStatusUpdateCallback(callback) {
    this.taskManager.setStatusUpdateCallback(callback);
  }

  /**
   * 配置下载设置
   */
  configureDownload(downloadConfig) {
    this.workspacePath = downloadConfig.workspacePath;
    this.enableDownload = downloadConfig.enableDownload;
    
    // 更新各个模块的配置
    if (this.workspacePath) {
      this.downloadManager.setWorkspacePath(this.workspacePath);
      this.recordManager.setWorkspacePath(this.workspacePath);
    }
    
    this.log.info(`[CollectorOrchestrator] 下载配置已更新: 启用=${this.enableDownload}, 路径=${this.workspacePath}`);
  }

  /**
   * 更新任务状态（代理到任务管理器）
   */
  updateTaskStatus(status, message, filePath = null) {
    this.taskManager.updateTaskStatus(status, message, filePath);
  }

  /**
   * 启动搜集任务
   */
  async startTask(siteKey, targetUrl, options = {}) {
    try {
      // 启动任务
      const startResult = this.taskManager.startTask(siteKey, targetUrl, options);
      if (!startResult.success) {
        return startResult;
      }

      // 获取站点配置
      const siteProfile = this.siteProfileService.getSiteProfile(siteKey);
      if (!siteProfile) {
        throw new Error(`未找到站点配置: ${siteKey}`);
      }

      // 执行搜集任务
      const result = await this.executeCollectionTask(siteProfile, targetUrl, options);
      
      // 完成任务
      this.taskManager.completeTask(result);
      
      return result;

    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 任务执行失败: ${error.message}`);
      this.taskManager.completeTask({ success: false, error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止搜集任务
   */
  async stopTask(force = false) {
    return this.taskManager.stopTask(force);
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return this.taskManager.getStatus();
  }

  /**
   * 执行搜集任务的核心逻辑
   */
  async executeCollectionTask(siteProfile, targetUrl, options) {
    this.log.info(`[CollectorOrchestrator] 开始执行搜集任务: ${siteProfile.name}`);
    this.updateTaskStatus('connecting', '正在连接浏览器...');

    let browser;
    try {
      // 连接到用户的Chrome浏览器
      browser = await chromium.connectOverCDP('http://localhost:9222');
      this.log.info('[CollectorOrchestrator] 成功连接到Chrome浏览器');

      // 查找目标页面
      const page = await this.findTargetPage(browser, targetUrl);
      if (!page) {
        throw new Error(`无法在已打开的页面中找到目标URL: ${targetUrl}`);
      }

      this.updateTaskStatus('scraping', '成功连接并接管页面，开始解析内容...');

      // 执行抓取逻辑
      const result = await this.executeScrapingLogic(page, siteProfile, targetUrl, options);
      
      return result;

    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 任务执行失败: ${error.message}`);
      throw error;
    }
    // 注意：不关闭浏览器，因为这是用户的浏览器
  }

  /**
   * 查找目标页面
   */
  async findTargetPage(browser, targetUrl) {
    try {
      const contexts = browser.contexts();
      for (const context of contexts) {
        const pages = context.pages();
        for (const page of pages) {
          const currentUrl = page.url();
          if (currentUrl.includes(new URL(targetUrl).hostname)) {
            this.log.info(`[CollectorOrchestrator] 找到目标页面: ${currentUrl}`);
            return page;
          }
        }
      }
      return null;
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 查找目标页面失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 执行抓取逻辑
   */
  async executeScrapingLogic(page, siteProfile, targetUrl, options) {
    const results = [];
    let currentPageNum = options.startPage || 1;
    const maxPages = options.maxPages || 5;
    let pagesProcessed = 0;
    let keepScraping = true;

    this.log.info(`[CollectorOrchestrator] 开始多页面抓取，从第 ${currentPageNum} 页开始，最多 ${maxPages} 页`);

    try {
      // 主循环，处理翻页
      while (keepScraping && pagesProcessed < maxPages && !this.taskManager.shouldStop()) {
        this.updateTaskStatus('scraping', `正在分析第 ${currentPageNum} 页...`);

        try {
          // 检查页面状态
          const pageStatus = await this.checkPageStatus(page, siteProfile);
          const handleResult = await this.taskManager.handlePageStatusError(pageStatus, page, `第 ${currentPageNum} 页`);
          
          if (handleResult === 'stop' || handleResult === 'wait_user') {
            keepScraping = false;
            break;
          }

          // 抓取当前页面的帖子
          const pageResults = await this.scrapePage(page, siteProfile, options, currentPageNum);
          results.push(...pageResults);

          // 更新进度
          this.taskManager.updateProgress({
            currentPage: currentPageNum,
            totalPages: Math.min(currentPageNum + maxPages - pagesProcessed - 1, currentPageNum + 10),
            processedPosts: results.length
          });

          pagesProcessed++;

          // 检查是否需要翻页
          if (pagesProcessed < maxPages && !this.taskManager.shouldStop()) {
            const nextPageSuccess = await this.goToNextPage(page, siteProfile, currentPageNum);
            if (!nextPageSuccess) {
              this.log.info('[CollectorOrchestrator] 已到达最后一页或翻页失败');
              break;
            }
            currentPageNum++;
          }

        } catch (pageError) {
          this.log.error(`[CollectorOrchestrator] 处理第 ${currentPageNum} 页时出错: ${pageError.message}`);
          
          // 检查是否是需要停止任务的错误
          if (pageError.message.includes('下载次数已达上限') || 
              pageError.message.includes('需要人机验证')) {
            keepScraping = false;
            break;
          }
          
          // 其他错误继续下一页
          pagesProcessed++;
          currentPageNum++;
        }
      }

      // 保存结果
      const saveResult = await this.recordManager.saveResults(results, siteProfile);
      
      // 自动导出
      const taskInfo = {
        targetUrl,
        startTime: this.taskManager.currentTask?.startTime,
        endTime: new Date(),
        pages: pagesProcessed,
        status: this.taskManager.shouldStop() ? 'stopped' : 'completed'
      };
      
      await this.recordManager.autoExportResults(results, siteProfile, taskInfo);

      // 更新最终状态
      if (this.taskManager.isForceStop()) {
        this.updateTaskStatus('force-stopped', `任务已强制停止，已保存 ${results.length} 条记录`);
      } else if (this.taskManager.shouldStop()) {
        this.updateTaskStatus('stopped', `任务已停止，已保存 ${results.length} 条记录`);
      } else {
        this.updateTaskStatus('completed', `搜集完成，共抓取 ${pagesProcessed} 页，处理 ${results.length} 条记录`);
      }

      return {
        success: true,
        results: results,
        pages: pagesProcessed,
        totalResults: results.length,
        saveResult: saveResult,
        forceStopped: this.taskManager.isForceStop()
      };

    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 抓取过程中发生错误: ${error.message}`);
      this.updateTaskStatus('failed', `抓取失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 抓取单个页面
   */
  async scrapePage(page, siteProfile, options, pageNumber) {
    const results = [];
    
    try {
      // 获取帖子链接
      const postLinks = await this.getPostLinks(page, siteProfile);
      this.log.info(`[CollectorOrchestrator] 第 ${pageNumber} 页找到 ${postLinks.length} 个帖子`);

      // 处理每个帖子
      for (let i = 0; i < postLinks.length; i++) {
        if (this.taskManager.shouldStop()) {
          this.log.info('[CollectorOrchestrator] 收到停止指令，中断当前页面抓取...');
          break;
        }

        const postUrl = postLinks[i];
        this.updateTaskStatus('scraping', `第 ${pageNumber} 页：正在处理帖子 ${i + 1}/${postLinks.length}`);

        try {
          // 解析帖子内容
          const postData = await this.parsePostContent(page, siteProfile, postUrl);
          if (!postData) {
            this.log.warn(`[CollectorOrchestrator] 跳过无效帖子: ${postUrl}`);
            continue;
          }

          // 检查日期过滤
          if (options.dateLimit && !this.checkDateLimit(postData.postDate, options.dateLimit)) {
            this.log.info(`[CollectorOrchestrator] 跳过日期超限帖子: ${postData.postTitle}`);
            continue;
          }

          // 处理下载
          if (this.enableDownload && this.workspacePath && postData.attachmentUrl) {
            try {
              const downloadResult = await this.downloadManager.downloadAttachments(page, postData, siteProfile);
              if (downloadResult.success) {
                postData.downloadStatus = 'completed';
                this.log.info(`[CollectorOrchestrator] 下载成功: ${postData.postTitle}`);
              } else {
                postData.downloadStatus = 'failed';
                postData.errorMessage = downloadResult.error || '下载失败';
              }
            } catch (downloadError) {
              // 检查是否需要停止任务的错误
              if (downloadError.message.includes('下载次数已达上限') || 
                  downloadError.message.includes('需要人机验证')) {
                throw downloadError;
              }
              
              postData.downloadStatus = 'failed';
              postData.errorMessage = downloadError.message;
              this.log.warn(`[CollectorOrchestrator] 下载失败: ${downloadError.message}`);
            }
          } else {
            postData.downloadStatus = 'skipped';
          }

          // 生成档案文件
          try {
            const archiveFilePath = await this.recordManager.generatePostArchiveFile(postData);
            if (archiveFilePath) {
              this.updateTaskStatus('scraping', `📄 档案已生成: ${postData.postTitle}`, archiveFilePath);
            }
          } catch (archiveError) {
            this.log.error(`[CollectorOrchestrator] 生成档案文件失败: ${archiveError.message}`);
          }

          results.push(postData);

          // 延迟
          if (options.delay && options.delay > 0) {
            await this.delay(options.delay);
          }

        } catch (postError) {
          this.log.warn(`[CollectorOrchestrator] 处理帖子失败 ${postUrl}: ${postError.message}`);
          
          // 检查是否是需要停止任务的错误
          if (postError.message.includes('下载次数已达上限') || 
              postError.message.includes('需要人机验证')) {
            throw postError;
          }
        }
      }

    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 抓取第 ${pageNumber} 页失败: ${error.message}`);
      throw error;
    }

    return results;
  }

  /**
   * 获取帖子链接
   */
  async getPostLinks(page, siteProfile) {
    try {
      await page.waitForSelector(siteProfile.postLinkSelector, { timeout: 10000 });
      const postElements = await page.locator(siteProfile.postLinkSelector).all();
      
      const postLinks = [];
      for (const element of postElements) {
        const href = await element.getAttribute('href');
        if (href) {
          const absoluteUrl = new URL(href, page.url()).href;
          postLinks.push(absoluteUrl);
        }
      }
      
      return postLinks;
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 获取帖子链接失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 解析帖子内容
   */
  async parsePostContent(page, siteProfile, postUrl) {
    try {
      await page.goto(postUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
      await page.waitForTimeout(1000);

      // 检查页面状态 - 添加详细调试日志
      const pageStatus = await this.checkPageStatus(page, siteProfile);
      this.log.info(`[CollectorOrchestrator] 页面状态检查结果: ${pageStatus} - ${postUrl}`);

      if (pageStatus !== 'normal') {
        this.log.warn(`[CollectorOrchestrator] 页面状态异常但继续处理: ${pageStatus} - ${postUrl}`);
        // 临时注释掉自动停止逻辑，避免误判
        // const handleResult = await this.taskManager.handlePageStatusError(pageStatus, page, postUrl);
        // if (handleResult === 'stop' || handleResult === 'wait_user') {
        //   throw new Error(`页面状态异常: ${pageStatus}`);
        // }
      }

      // 这里应该调用具体的采集器来解析内容
      // 为了简化，我们先返回基本结构
      return {
        postUrl: postUrl,
        postTitle: await this.extractPostTitle(page, siteProfile),
        nfoId: null,
        magnetLink: null,
        ed2kLink: null,
        attachmentUrl: null,
        decompressionPassword: null,
        previewImageUrl: null,
        postDate: null,
        collectionDate: new Date().toISOString(),
        downloadStatus: 'pending',
        errorMessage: null,
        downloadPath: null,
        status: 'normal'
      };

    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 解析帖子内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 提取帖子标题
   */
  async extractPostTitle(page, siteProfile) {
    try {
      await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
      const titleElement = page.locator(siteProfile.postTitleSelector).first();
      return await titleElement.textContent() || 'Untitled';
    } catch (error) {
      this.log.warn(`[CollectorOrchestrator] 提取标题失败: ${error.message}`);
      return 'Untitled';
    }
  }

  /**
   * 检查页面状态
   */
  async checkPageStatus(page, siteProfile) {
    try {
      const url = page.url();
      const title = await page.title();

      this.log.debug(`[CollectorOrchestrator] 检查页面状态 - URL: ${url}, Title: ${title}`);

      // 检查下载超限 - 使用更精确的匹配
      if (title.includes('下载次数已达上限') ||
          title.includes('您每日可下載附件總計') ||
          url.includes('download_limit')) {
        this.log.warn(`[CollectorOrchestrator] 检测到下载超限: ${title}`);
        return 'download_limit_exceeded';
      }

      // 检查人机验证 - 使用更精确的匹配，避免误判
      if (title.includes('Cloudflare') ||
          title.includes('Just a moment') ||
          title.includes('Please wait') ||
          title.includes('captcha') ||
          url.includes('challenge') ||
          url.includes('verify')) {
        this.log.warn(`[CollectorOrchestrator] 检测到人机验证: ${title}`);
        return 'human_verification_required';
      }

      // 检查服务器错误
      if (title.includes('500') || title.includes('502') || title.includes('503') ||
          title.includes('Internal Server Error') || title.includes('Service Unavailable')) {
        this.log.warn(`[CollectorOrchestrator] 检测到服务器错误: ${title}`);
        return 'server_error';
      }

      // 检查访问被拒绝
      if (title.includes('403') || title.includes('Forbidden') || title.includes('Access Denied')) {
        this.log.warn(`[CollectorOrchestrator] 检测到访问被拒绝: ${title}`);
        return 'access_denied';
      }

      return 'normal';
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 检查页面状态失败: ${error.message}`);
      return 'unknown';
    }
  }

  /**
   * 翻页到下一页
   */
  async goToNextPage(page, siteProfile, currentPageNum) {
    try {
      const nextPageButton = page.locator(siteProfile.nextPageSelector);
      if (await nextPageButton.count() === 0) {
        this.log.info('[CollectorOrchestrator] 没有找到下一页按钮');
        return false;
      }

      const urlBeforeClick = page.url();
      this.updateTaskStatus('paging', `准备翻页到第 ${currentPageNum + 1} 页...`);

      await nextPageButton.first().click();

      // 等待URL变化或超时
      try {
        await page.waitForFunction(
          (oldUrl) => window.location.href !== oldUrl,
          urlBeforeClick,
          { timeout: 10000 }
        );

        await page.waitForTimeout(2000); // 等待页面稳定

        const newUrl = page.url();
        if (newUrl === urlBeforeClick) {
          this.log.info('[CollectorOrchestrator] URL未变化，可能已到最后一页');
          return false;
        }

        this.log.info(`[CollectorOrchestrator] 成功翻页到第 ${currentPageNum + 1} 页`);
        return true;

      } catch (timeoutError) {
        this.log.warn('[CollectorOrchestrator] 翻页超时，可能已到最后一页');
        return false;
      }

    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 翻页失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查日期限制
   */
  checkDateLimit(postDate, dateLimit) {
    if (!postDate || !dateLimit) {
      return true;
    }

    try {
      const postDateTime = new Date(postDate);
      const limitDateTime = new Date(dateLimit);
      return postDateTime >= limitDateTime;
    } catch (error) {
      this.log.warn(`[CollectorOrchestrator] 日期比较失败: ${error.message}`);
      return true;
    }
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取支持的论坛列表
   */
  getForums() {
    try {
      const profiles = this.siteProfileService.getSiteProfiles();
      const forums = Object.keys(profiles)
        .filter(key => key !== 'chromeUserDataPath')
        .map(key => ({
          key: key,
          name: profiles[key].name || key,
          url: profiles[key].baseUrl || '',
          enabled: profiles[key].enabled !== false
        }));

      return { success: true, forums: forums };
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 获取论坛列表失败: ${error.message}`);
      return { success: false, error: error.message, forums: [] };
    }
  }

  /**
   * 获取搜集数据
   */
  async getData(options = {}) {
    try {
      const result = await this.databaseService.getCollectedLinks(options);
      return result;
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 获取数据失败: ${error.message}`);
      return { success: false, error: error.message, data: [] };
    }
  }

  /**
   * 删除链接
   */
  async deleteLink(id) {
    try {
      const result = await this.databaseService.deleteCollectedLink(id);
      return result;
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 删除链接失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        // 不关闭浏览器，因为这是用户的浏览器
        this.browser = null;
      }

      this.taskManager.reset();
      this.log.info('[CollectorOrchestrator] 资源清理完成');
    } catch (error) {
      this.log.error(`[CollectorOrchestrator] 清理资源失败: ${error.message}`);
    }
  }
}

module.exports = CollectorOrchestrator;
