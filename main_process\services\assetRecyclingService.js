// main_process/services/assetRecyclingService.js
const fs = require('fs').promises;
const path = require('path');
const log = require('electron-log');
const databaseService = require('./databaseService');
const settingsService = require('./settingsService');

/**
 * 资产回收服务 - 负责处理单一版本和整部影片的回收操作
 */

/**
 * 回收单一版本
 * @param {number} versionDbId - 要回收的版本的数据库 db_id
 * @returns {Promise<Object>} 回收结果
 */
async function recycleSingleVersion(versionDbId) {
  log.info(`[资产回收服务] 开始回收单一版本: ${versionDbId}`);
  
  try {
    // 1. 查询版本详细信息
    const versionInfo = await getVersionInfo(versionDbId);
    if (!versionInfo) {
      throw new Error(`未找到版本记录: ${versionDbId}`);
    }
    
    log.info(`[资产回收服务] 版本信息: ${versionInfo.nfoId} - ${versionInfo.fileName}`);
    
    // 2. 移动版本专属文件到回收站
    const moveResult = await moveVersionFilesToRecycleBin(versionInfo);
    
    // 3. 更新数据库状态
    const dbResult = await updateVersionStatus(versionDbId, 'RECYCLED', moveResult);
    
    const result = {
      success: true,
      versionDbId: versionDbId,
      nfoId: versionInfo.nfoId,
      fileName: versionInfo.fileName,
      recycledFiles: moveResult.movedFiles,
      recycleBinPath: moveResult.recycleBinPath,
      message: '单一版本回收成功'
    };
    
    log.info(`[资产回收服务] 单一版本回收成功: ${versionDbId}`);
    return result;
    
  } catch (error) {
    log.error(`[资产回收服务] 回收单一版本失败: ${versionDbId}`, error.message);
    return {
      success: false,
      error: error.message,
      versionDbId: versionDbId
    };
  }
}

/**
 * 回收整部影片
 * @param {string} nfoId - 要回收的整部影片的 nfoId
 * @returns {Promise<Object>} 回收结果
 */
async function recycleEntireMovie(nfoId) {
  log.info(`[资产回收服务] 开始回收整部影片: ${nfoId}`);
  
  try {
    // 1. 查询影片的所有版本信息
    const movieVersions = await getMovieVersions(nfoId);
    if (!movieVersions || movieVersions.length === 0) {
      throw new Error(`未找到影片记录: ${nfoId}`);
    }
    
    // 获取主记录（通常是第一个记录）
    const mainRecord = movieVersions[0];
    
    log.info(`[资产回收服务] 影片信息: ${nfoId}, 共 ${movieVersions.length} 个版本`);
    
    // 2. 移动整个影片文件夹到回收站
    const moveResult = await moveEntireMovieFolderToRecycleBin(mainRecord);
    
    // 3. 更新所有版本的数据库状态
    const dbResult = await updateMovieStatus(nfoId, 'MARKED_FOR_DELETION', moveResult);
    
    const result = {
      success: true,
      nfoId: nfoId,
      versionsCount: movieVersions.length,
      movedFolder: moveResult.movedFolder,
      recycleBinPath: moveResult.recycleBinPath,
      affectedVersions: dbResult.affectedVersions,
      message: '整部影片回收成功'
    };
    
    log.info(`[资产回收服务] 整部影片回收成功: ${nfoId}`);
    return result;
    
  } catch (error) {
    log.error(`[资产回收服务] 回收整部影片失败: ${nfoId}`, error.message);
    return {
      success: false,
      error: error.message,
      nfoId: nfoId
    };
  }
}

/**
 * 获取版本信息
 * @param {number} versionDbId - 版本数据库ID
 * @returns {Promise<Object|null>} 版本信息
 */
async function getVersionInfo(versionDbId) {
  try {
    const version = await databaseService.getMovieById(versionDbId);
    return version;
  } catch (error) {
    log.error(`[资产回收服务] 获取版本信息失败: ${versionDbId}`, error.message);
    return null;
  }
}

/**
 * 获取影片的所有版本
 * @param {string} nfoId - 影片番号
 * @returns {Promise<Array>} 版本列表
 */
async function getMovieVersions(nfoId) {
  try {
    const versions = await databaseService.getMoviesByNfoId(nfoId);
    return versions;
  } catch (error) {
    log.error(`[资产回收服务] 获取影片版本失败: ${nfoId}`, error.message);
    return [];
  }
}

/**
 * 移动版本专属文件到回收站
 * @param {Object} versionInfo - 版本信息
 * @returns {Promise<Object>} 移动结果
 */
async function moveVersionFilesToRecycleBin(versionInfo) {
  try {
    const settings = settingsService.getSettings();
    const recycleBinPath = settings.recycleBinPath;
    
    if (!recycleBinPath) {
      throw new Error('回收站路径未配置');
    }
    
    // 创建回收站子目录（按日期组织）
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const versionRecyclePath = path.join(recycleBinPath, 'versions', today, versionInfo.nfoId);
    await fs.mkdir(versionRecyclePath, { recursive: true });
    
    const movedFiles = [];
    
    // 移动主文件（.strm 或实体视频文件）
    if (versionInfo.filePath && await fileExists(versionInfo.filePath)) {
      const fileName = path.basename(versionInfo.filePath);
      const targetPath = path.join(versionRecyclePath, fileName);
      
      await fs.rename(versionInfo.filePath, targetPath);
      movedFiles.push({
        originalPath: versionInfo.filePath,
        recyclePath: targetPath,
        type: 'main_file'
      });
      
      log.info(`[资产回收服务] 已移动主文件: ${versionInfo.filePath} -> ${targetPath}`);
    }
    
    // 如果有版本专属的其他文件，也一并移动
    // 例如：字幕文件、版本特定的元数据等
    
    return {
      success: true,
      recycleBinPath: versionRecyclePath,
      movedFiles: movedFiles
    };
    
  } catch (error) {
    log.error(`[资产回收服务] 移动版本文件失败: ${versionInfo.nfoId}`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 移动整个影片文件夹到回收站
 * @param {Object} movieRecord - 影片主记录
 * @returns {Promise<Object>} 移动结果
 */
async function moveEntireMovieFolderToRecycleBin(movieRecord) {
  try {
    const settings = settingsService.getSettings();
    const recycleBinPath = settings.recycleBinPath;
    
    if (!recycleBinPath) {
      throw new Error('回收站路径未配置');
    }
    
    // 获取影片文件夹路径
    let movieFolderPath = movieRecord.asset_root_path;
    
    // 如果没有 asset_root_path，尝试从 filePath 推断
    if (!movieFolderPath && movieRecord.filePath) {
      movieFolderPath = path.dirname(movieRecord.filePath);
    }
    
    if (!movieFolderPath || !await fileExists(movieFolderPath)) {
      throw new Error(`影片文件夹不存在: ${movieFolderPath}`);
    }
    
    // 创建回收站子目录（按日期组织）
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const movieRecyclePath = path.join(recycleBinPath, 'movies', today);
    await fs.mkdir(movieRecyclePath, { recursive: true });
    
    // 移动整个文件夹
    const folderName = path.basename(movieFolderPath);
    const targetPath = path.join(movieRecyclePath, folderName);
    
    await fs.rename(movieFolderPath, targetPath);
    
    log.info(`[资产回收服务] 已移动影片文件夹: ${movieFolderPath} -> ${targetPath}`);
    
    return {
      success: true,
      recycleBinPath: movieRecyclePath,
      movedFolder: {
        originalPath: movieFolderPath,
        recyclePath: targetPath,
        folderName: folderName
      }
    };
    
  } catch (error) {
    log.error(`[资产回收服务] 移动影片文件夹失败: ${movieRecord.nfoId}`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 更新版本状态
 * @param {number} versionDbId - 版本数据库ID
 * @param {string} status - 新状态
 * @param {Object} moveResult - 移动结果
 * @returns {Promise<Object>} 更新结果
 */
async function updateVersionStatus(versionDbId, status, moveResult) {
  try {
    // 创建版本状态信息
    const versionStatus = {
      status: status,
      recycledAt: new Date().toISOString(),
      recycleBinPath: moveResult.recycleBinPath,
      movedFiles: moveResult.movedFiles
    };
    
    // 更新数据库记录
    const updateData = {
      version_status: JSON.stringify(versionStatus),
      lastScanned: new Date().toISOString()
    };
    
    const updated = await databaseService.updateMovie(versionDbId, updateData);
    
    return {
      success: updated,
      versionDbId: versionDbId,
      status: status
    };
    
  } catch (error) {
    log.error(`[资产回收服务] 更新版本状态失败: ${versionDbId}`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 更新影片状态
 * @param {string} nfoId - 影片番号
 * @param {string} status - 新状态
 * @param {Object} moveResult - 移动结果
 * @returns {Promise<Object>} 更新结果
 */
async function updateMovieStatus(nfoId, status, moveResult) {
  try {
    // 获取所有版本
    const versions = await getMovieVersions(nfoId);
    
    const affectedVersions = [];
    
    // 更新所有版本的状态
    for (const version of versions) {
      const updateData = {
        asset_status: status,
        lastScanned: new Date().toISOString()
      };
      
      const updated = await databaseService.updateMovie(version.db_id, updateData);
      
      if (updated) {
        affectedVersions.push(version.db_id);
      }
    }
    
    return {
      success: affectedVersions.length > 0,
      affectedVersions: affectedVersions,
      nfoId: nfoId,
      status: status
    };
    
  } catch (error) {
    log.error(`[资产回收服务] 更新影片状态失败: ${nfoId}`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} 文件是否存在
 */
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 生成删除清单
 * @returns {Promise<Object>} 生成结果
 */
async function generateDeletionList() {
  log.info('[资产回收服务] 开始生成删除清单...');

  try {
    const settings = settingsService.getSettings();
    const recycleBinPath = settings.recycleBinPath;

    if (!recycleBinPath) {
      throw new Error('回收站路径未配置');
    }

    // 1. 查询所有需要删除的记录
    const itemsToDelete = await getItemsMarkedForDeletion();

    if (itemsToDelete.length === 0) {
      return {
        success: true,
        message: '没有需要删除的项目',
        filePath: null,
        itemsCount: 0
      };
    }

    // 2. 提取115网盘路径
    const deletionPaths = await extractCloud115Paths(itemsToDelete);

    // 3. 生成删除清单文件
    const listFilePath = await createDeletionListFile(deletionPaths, recycleBinPath);

    const result = {
      success: true,
      message: '删除清单生成成功',
      filePath: listFilePath,
      itemsCount: itemsToDelete.length,
      pathsCount: deletionPaths.length,
      items: itemsToDelete.map(item => ({
        nfoId: item.nfoId,
        type: item.asset_status === 'MARKED_FOR_DELETION' ? 'movie' : 'version',
        fileName: item.fileName
      }))
    };

    log.info(`[资产回收服务] 删除清单生成成功: ${listFilePath}, 包含 ${deletionPaths.length} 个路径`);
    return result;

  } catch (error) {
    log.error('[资产回收服务] 生成删除清单失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 获取标记为删除的项目
 * @returns {Promise<Array>} 待删除项目列表
 */
async function getItemsMarkedForDeletion() {
  try {
    // 查询所有 asset_status 为 'MARKED_FOR_DELETION' 的记录
    const markedForDeletion = await databaseService.getMoviesFromDb({
      advancedFilters: {
        asset_status: 'MARKED_FOR_DELETION'
      },
      pageSize: 10000 // 获取所有记录
    });

    // 查询所有 version_status 包含 'RECYCLED' 的记录
    const recycledVersions = await databaseService.getMoviesFromDb({
      pageSize: 10000 // 获取所有记录
    });

    // 过滤出版本状态为 RECYCLED 的记录
    const filteredRecycledVersions = recycledVersions.movies.filter(movie => {
      if (movie.version_status) {
        try {
          const versionStatus = JSON.parse(movie.version_status);
          return versionStatus.status === 'RECYCLED';
        } catch {
          return false;
        }
      }
      return false;
    });

    // 合并两个列表
    const allItems = [...markedForDeletion.movies, ...filteredRecycledVersions];

    log.info(`[资产回收服务] 找到 ${allItems.length} 个待删除项目 (影片: ${markedForDeletion.movies.length}, 版本: ${filteredRecycledVersions.length})`);

    return allItems;

  } catch (error) {
    log.error('[资产回收服务] 获取待删除项目失败:', error.message);
    return [];
  }
}

/**
 * 提取115网盘路径
 * @param {Array} items - 待删除项目列表
 * @returns {Promise<Array>} 115网盘路径列表
 */
async function extractCloud115Paths(items) {
  const paths = [];
  const processedPaths = new Set(); // 避免重复路径

  for (const item of items) {
    try {
      // 检查是否有 filePath 且是 .strm 文件
      if (item.filePath && item.filePath.endsWith('.strm')) {
        // 读取 STRM 文件内容
        const strmContent = await fs.readFile(item.filePath, 'utf8');
        const cloud115Path = strmContent.trim();

        if (cloud115Path && !processedPaths.has(cloud115Path)) {
          paths.push({
            nfoId: item.nfoId,
            fileName: item.fileName,
            localPath: item.filePath,
            cloud115Path: cloud115Path,
            type: item.asset_status === 'MARKED_FOR_DELETION' ? 'movie' : 'version'
          });
          processedPaths.add(cloud115Path);
        }
      } else if (item.asset_status === 'MARKED_FOR_DELETION') {
        // 对于整部影片，尝试查找相关的 STRM 文件
        const moviePaths = await findMovieCloud115Paths(item);

        for (const moviePath of moviePaths) {
          if (!processedPaths.has(moviePath.cloud115Path)) {
            paths.push(moviePath);
            processedPaths.add(moviePath.cloud115Path);
          }
        }
      }
    } catch (error) {
      log.warn(`[资产回收服务] 处理项目失败: ${item.nfoId}`, error.message);
    }
  }

  log.info(`[资产回收服务] 提取到 ${paths.length} 个115网盘路径`);
  return paths;
}

/**
 * 查找影片的115网盘路径
 * @param {Object} movieItem - 影片项目
 * @returns {Promise<Array>} 115网盘路径列表
 */
async function findMovieCloud115Paths(movieItem) {
  const paths = [];

  try {
    // 如果有 asset_root_path，扫描该目录下的所有 STRM 文件
    if (movieItem.asset_root_path) {
      const strmFiles = await findStrmFilesInDirectory(movieItem.asset_root_path);

      for (const strmFile of strmFiles) {
        try {
          const strmContent = await fs.readFile(strmFile, 'utf8');
          const cloud115Path = strmContent.trim();

          if (cloud115Path) {
            paths.push({
              nfoId: movieItem.nfoId,
              fileName: path.basename(strmFile),
              localPath: strmFile,
              cloud115Path: cloud115Path,
              type: 'movie'
            });
          }
        } catch (error) {
          log.warn(`[资产回收服务] 读取STRM文件失败: ${strmFile}`, error.message);
        }
      }
    }
  } catch (error) {
    log.warn(`[资产回收服务] 查找影片115路径失败: ${movieItem.nfoId}`, error.message);
  }

  return paths;
}

/**
 * 在目录中查找STRM文件
 * @param {string} dirPath - 目录路径
 * @returns {Promise<Array>} STRM文件路径列表
 */
async function findStrmFilesInDirectory(dirPath) {
  const strmFiles = [];

  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      if (entry.isFile() && entry.name.endsWith('.strm')) {
        strmFiles.push(path.join(dirPath, entry.name));
      }
    }
  } catch (error) {
    log.warn(`[资产回收服务] 扫描目录失败: ${dirPath}`, error.message);
  }

  return strmFiles;
}

/**
 * 创建删除清单文件
 * @param {Array} deletionPaths - 删除路径列表
 * @param {string} recycleBinPath - 回收站路径
 * @returns {Promise<string>} 清单文件路径
 */
async function createDeletionListFile(deletionPaths, recycleBinPath) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = `115_deletion_list_${timestamp}.txt`;
  const filePath = path.join(recycleBinPath, fileName);

  // 生成文件内容
  const lines = [
    '# 115网盘文件删除清单',
    `# 生成时间: ${new Date().toLocaleString()}`,
    `# 总计: ${deletionPaths.length} 个文件`,
    '#',
    '# 格式: [类型] 番号 - 文件名',
    '#       115网盘路径',
    '#',
    ''
  ];

  for (const item of deletionPaths) {
    lines.push(`# [${item.type.toUpperCase()}] ${item.nfoId} - ${item.fileName}`);
    lines.push(item.cloud115Path);
    lines.push('');
  }

  // 写入文件
  await fs.writeFile(filePath, lines.join('\n'), 'utf8');

  log.info(`[资产回收服务] 删除清单文件已创建: ${filePath}`);
  return filePath;
}

/**
 * 获取回收站中的所有项目
 * @returns {Promise<Object>} 回收站项目列表
 */
async function getRecycledItems() {
  log.info('[资产回收服务] 获取回收站项目列表...');

  try {
    const items = await getItemsMarkedForDeletion();

    // 按类型和时间分组
    const groupedItems = {
      movies: [],
      versions: []
    };

    for (const item of items) {
      const recycledItem = {
        db_id: item.db_id,
        nfoId: item.nfoId,
        title: item.title,
        fileName: item.fileName,
        filePath: item.filePath,
        recycledAt: null,
        type: item.asset_status === 'MARKED_FOR_DELETION' ? 'movie' : 'version'
      };

      // 尝试获取回收时间
      if (item.version_status) {
        try {
          const versionStatus = JSON.parse(item.version_status);
          recycledItem.recycledAt = versionStatus.recycledAt;
        } catch {
          // 忽略解析错误
        }
      }

      if (recycledItem.type === 'movie') {
        groupedItems.movies.push(recycledItem);
      } else {
        groupedItems.versions.push(recycledItem);
      }
    }

    // 按回收时间排序
    const sortByRecycledAt = (a, b) => {
      const timeA = a.recycledAt ? new Date(a.recycledAt) : new Date(0);
      const timeB = b.recycledAt ? new Date(b.recycledAt) : new Date(0);
      return timeB - timeA; // 最新的在前
    };

    groupedItems.movies.sort(sortByRecycledAt);
    groupedItems.versions.sort(sortByRecycledAt);

    const result = {
      success: true,
      totalCount: items.length,
      moviesCount: groupedItems.movies.length,
      versionsCount: groupedItems.versions.length,
      items: groupedItems
    };

    log.info(`[资产回收服务] 获取回收站项目完成: 总计 ${items.length} 个 (影片: ${groupedItems.movies.length}, 版本: ${groupedItems.versions.length})`);
    return result;

  } catch (error) {
    log.error('[资产回收服务] 获取回收站项目失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  recycleSingleVersion,
  recycleEntireMovie,
  generateDeletionList,
  getRecycledItems,
  getVersionInfo,
  getMovieVersions,
  moveVersionFilesToRecycleBin,
  moveEntireMovieFolderToRecycleBin,
  updateVersionStatus,
  updateMovieStatus,
  getItemsMarkedForDeletion,
  extractCloud115Paths,
  findMovieCloud115Paths,
  createDeletionListFile
};
