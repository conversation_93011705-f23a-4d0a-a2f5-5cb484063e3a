// soul-forge-electron/src/components/settings/AiSettingsTab.tsx
import React, { useState } from 'react';
import { AppSettings } from '../../types';
import EyeIcon from '../icons/EyeIcon';
import EyeSlashIcon from '../icons/EyeSlashIcon';
import { AiCategoryManager } from '../AiCategoryManager';

interface AiSettingsTabProps {
  settings: Partial<AppSettings>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
}

const grokModelOptions = [
  { value: 'grok-3-mini-fast', label: 'Grok 3 Mini Fast (推荐)' },
  { value: 'grok-3-fast', label: 'Grok 3 Fast' },
  { value: 'grok-3-sense', label: 'Grok 3 Sense (更强)' },
];



const AiSettingsTab: React.FC<AiSettingsTabProps> = ({
  settings,
  handleInputChange,
}) => {
  const [showCustomGptKey, setShowCustomGptKey] = useState(false);
  const [showGrokKey, setShowGrokKey] = useState(false);
  const [showGeminiKey, setShowGeminiKey] = useState(false);
  const [testConnectionStatus, setTestConnectionStatus] = useState<Record<string, { message: string; success: boolean } | null>>({});

  const handleTestAiConnection = async (provider: 'googleGemini' | 'customGpt' | 'grok' | 'ollama') => {
    setTestConnectionStatus(prev => ({ ...prev, [provider]: { message: '测试中...', success: false }}));
    let providerConfig: any = {};
    if (provider === 'googleGemini') {
        providerConfig = { apiKey: settings.geminiApiKey, model: settings.geminiModel };
    } else if (provider === 'customGpt') {
        providerConfig = { endpoint: settings.customGptEndpoint, apiKey: settings.customGptApiKey, model: settings.customGptModel };
    } else if (provider === 'grok') {
        providerConfig = { apiKey: settings.grokApiKey, model: settings.grokModel || "grok-3-mini-fast" };
    } else if (provider === 'ollama') {
        providerConfig = {
          ollamaApiEndpoint: settings.ollamaApiEndpoint || 'http://localhost:11434',
          ollamaModelName: settings.ollamaModelName || 'llama3'
        };
    }

    try {
        const result = await window.sfeElectronAPI.testAiConnection(provider, providerConfig);
        setTestConnectionStatus(prev => ({ ...prev, [provider]: result }));
    } catch (e: any) {
        setTestConnectionStatus(prev => ({ ...prev, [provider]: { message: `前端测试错误: ${e.message}`, success: false }}));
    }
  };

  const renderPasswordInput = (
    id: keyof Pick<AppSettings, 'customGptApiKey' | 'grokApiKey' | 'geminiApiKey' >,
    value: string | null | undefined,
    showState: boolean,
    toggleShowState: () => void,
    placeholder: string,
    description?: React.ReactNode
  ) => (
    <div>
        <div className="relative">
          <input
            type={showState ? "text" : "password"}
            id={id}
            name={id}
            value={value || ''}
            onChange={handleInputChange}
            placeholder={placeholder}
            className="form-input-app pr-10"
          />
          <button
            type="button"
            onClick={toggleShowState}
            className="absolute inset-y-0 right-0 px-3 flex items-center text-neutral-400 hover:text-pink-400 focus:outline-none focus:ring-1 focus:ring-pink-400 rounded-r-md"
            aria-label={showState ? "隐藏" : "显示"}
          >
            {showState ? <EyeSlashIcon /> : <EyeIcon />}
          </button>
        </div>
      {description && <p className="settings-description text-xs">{description}</p>}
    </div>
  );


  return (
    <div className="settings-group-content space-y-4">
      <div>
        <label htmlFor="aiProvider" className="settings-label">选择AI供应商</label>
        <select id="aiProvider" name="aiProvider" value={settings.aiProvider || ''} onChange={handleInputChange} className="form-select-app">
          <option value="">-- 未选择 --</option>
          <option value="googleGemini">Google Gemini</option>
          <option value="customGpt">自定义GPT兼容接口</option>
          <option value="grok">Grok (xAI)</option>
          <option value="ollama">Ollama (本地运行)</option>
        </select>
      </div>

      {settings.aiProvider === 'googleGemini' && (
        <div className="p-3 border border-green-500/30 rounded-md bg-[#2d2d2d]/50 space-y-3">
          <h4 className="settings-label font-medium text-green-300">Google Gemini 设置</h4>
          <div>
            <label htmlFor="geminiApiKey" className="settings-label">Gemini API Key</label>
            {renderPasswordInput("geminiApiKey", settings.geminiApiKey, showGeminiKey, () => setShowGeminiKey(!showGeminiKey), "输入您的 Gemini API Key")}
            <p className="settings-description">
              获取API密钥：<a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-green-400 hover:text-green-300 underline">Google AI Studio</a>
            </p>
          </div>
          <div>
            <label htmlFor="geminiModel" className="settings-label">模型名称</label>
            <input
              type="text"
              id="geminiModel"
              name="geminiModel"
              value={settings.geminiModel || ''}
              onChange={handleInputChange}
              placeholder="例如: gemini-2.5-flash, gemini-1.5-pro"
              className="form-input-app"
            />
            <p className="settings-description">
              请输入要使用的 Gemini 模型名称。推荐：gemini-2.5-flash（速度快且效果好）、gemini-1.5-pro（更强大）。
            </p>
          </div>
          <button onClick={() => handleTestAiConnection('googleGemini')} className="button-secondary-app text-xs px-3 py-1.5">测试连接</button>
          {testConnectionStatus.googleGemini && <p className={`text-xs mt-1.5 p-1.5 rounded ${testConnectionStatus.googleGemini.success ? 'bg-green-700/30 text-green-300' : 'bg-red-700/30 text-red-300'}`}>{testConnectionStatus.googleGemini.message}</p>}
        </div>
      )}

      {settings.aiProvider === 'customGpt' && (
        <div className="p-3 border border-sky-500/30 rounded-md bg-[#2d2d2d]/50 space-y-3">
          <h4 className="settings-label font-medium text-sky-300">自定义 GPT 设置</h4>
          <div>
            <label htmlFor="customGptEndpoint" className="settings-label">API Endpoint URL</label>
            <input type="url" id="customGptEndpoint" name="customGptEndpoint" value={settings.customGptEndpoint || ''} onChange={handleInputChange} placeholder="例如: https://api.openai.com/v1/chat/completions" className="form-input-app"/>
          </div>
          <div>
            <label htmlFor="customGptApiKey" className="settings-label">API Key (可选)</label>
            {renderPasswordInput("customGptApiKey", settings.customGptApiKey, showCustomGptKey, () => setShowCustomGptKey(!showCustomGptKey), "输入您的 API Key")}
          </div>
          <div>
            <label htmlFor="customGptModel" className="settings-label">模型名称 (可选)</label>
            <input type="text" id="customGptModel" name="customGptModel" value={settings.customGptModel || ''} onChange={handleInputChange} placeholder="例如: gpt-3.5-turbo" className="form-input-app"/>
            <p className="settings-description">如果留空，将尝试使用通用默认模型。</p>
          </div>
          <button onClick={() => handleTestAiConnection('customGpt')} className="button-secondary-app text-xs px-3 py-1.5">测试连接</button>
          {testConnectionStatus.customGpt && <p className={`text-xs mt-1.5 p-1.5 rounded ${testConnectionStatus.customGpt.success ? 'bg-green-700/30 text-green-300' : 'bg-red-700/30 text-red-300'}`}>{testConnectionStatus.customGpt.message}</p>}
        </div>
      )}

      {settings.aiProvider === 'grok' && (
        <div className="p-3 border border-purple-500/30 rounded-md bg-[#2d2d2d]/50 space-y-3">
          <h4 className="settings-label font-medium text-purple-300">Grok (xAI) 设置</h4>
          <div>
            <label htmlFor="grokApiKey" className="settings-label">Grok API Key</label>
            {renderPasswordInput("grokApiKey", settings.grokApiKey, showGrokKey, () => setShowGrokKey(!showGrokKey), "输入您的 Grok API Key")}
          </div>
          <div>
            <label htmlFor="grokModel" className="settings-label">Grok 模型</label>
            <select id="grokModel" name="grokModel" value={settings.grokModel || 'grok-3-mini-fast'} onChange={handleInputChange} className="form-select-app">
              {grokModelOptions.map(opt => (
                <option key={opt.value} value={opt.value}>{opt.label}</option>
              ))}
            </select>
            <p className="settings-description">林珞召唤阵暂不支持流式响应。推荐使用 Mini Fast。</p>
          </div>
          <button onClick={() => handleTestAiConnection('grok')} className="button-secondary-app text-xs px-3 py-1.5">测试连接</button>
          {testConnectionStatus.grok && <p className={`text-xs mt-1.5 p-1.5 rounded ${testConnectionStatus.grok.success ? 'bg-green-700/30 text-green-300' : 'bg-red-700/30 text-red-300'}`}>{testConnectionStatus.grok.message}</p>}
        </div>
      )}

      {settings.aiProvider === 'ollama' && (
        <div className="p-3 border border-orange-500/30 rounded-md bg-[#2d2d2d]/50 space-y-3">
          <h4 className="settings-label font-medium text-orange-300">Ollama 本地AI 设置</h4>

          <div className="bg-blue-900/20 border border-blue-500/30 rounded-md p-3 mb-3">
            <h5 className="text-blue-300 font-medium mb-2">💡 使用提示</h5>
            <ul className="text-xs text-blue-200 space-y-1">
              <li>• 需要预先安装 Ollama: <a href="https://ollama.ai/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">https://ollama.ai/</a></li>
              <li>• 启动服务: <code className="bg-black/30 px-1 rounded">ollama serve</code></li>
              <li>• 下载模型: <code className="bg-black/30 px-1 rounded">ollama pull llama3</code></li>
              <li>• 数据100%不出本地，完全私密</li>
            </ul>
          </div>

          <div>
            <label htmlFor="ollamaApiEndpoint" className="settings-label">Ollama API 地址</label>
            <input
              type="url"
              id="ollamaApiEndpoint"
              name="ollamaApiEndpoint"
              value={settings.ollamaApiEndpoint || 'http://localhost:11434'}
              onChange={handleInputChange}
              placeholder="http://localhost:11434"
              className="form-input-app"
            />
            <p className="settings-description">Ollama服务的API地址，通常为本地地址</p>
          </div>

          <div>
            <label htmlFor="ollamaModelName" className="settings-label">模型名称</label>
            <input
              type="text"
              id="ollamaModelName"
              name="ollamaModelName"
              value={settings.ollamaModelName || 'llama3'}
              onChange={handleInputChange}
              placeholder="llama3"
              className="form-input-app"
            />
            <p className="settings-description">
              使用的模型名称。推荐模型：
              <span className="text-orange-300"> llama3</span> (通用),
              <span className="text-orange-300"> qwen:7b</span> (中文优化),
              <span className="text-orange-300"> codellama</span> (代码)
            </p>
          </div>

          <button onClick={() => handleTestAiConnection('ollama')} className="button-secondary-app text-xs px-3 py-1.5">测试连接</button>
          {testConnectionStatus.ollama && (
            <p className={`text-xs mt-1.5 p-1.5 rounded ${testConnectionStatus.ollama.success ? 'bg-green-700/30 text-green-300' : 'bg-red-700/30 text-red-300'}`}>
              {testConnectionStatus.ollama.message}
            </p>
          )}
        </div>
      )}

      {/* AI分类体系管理 */}
      <div className="mt-8">
        <AiCategoryManager />
      </div>
    </div>
  );
};

export default AiSettingsTab;