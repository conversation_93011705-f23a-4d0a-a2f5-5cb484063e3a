// main_process/services/collectorService.js
// 搜集服务调度器 - 重构后的纯调度器版本

const siteProfileService = require('./siteProfileService');
const ForumACollector = require('../collectors/ForumACollector');
const ForumBCollector = require('../collectors/ForumBCollector');
const dateParser = require('../utils/dateParser');
const fileSystemHelper = require('../utils/fileSystemHelper');
const pageStatusChecker = require('../utils/pageStatusChecker');

let log = null;
let isInitialized = false;

// 日期解析功能已移至 utils/dateParser.js

class CollectorService {
  constructor(mainWindow = null) {
    // 主窗口引用，用于发送事件
    this.mainWindow = mainWindow;

    // 任务状态管理
    this.isRunning = false;
    this.isStopping = false;
    this.currentTask = null;
    this.taskHistory = [];
    this.statusUpdateCallback = null;
    this.userConfirmationCallback = null;
    this.userConfirmationPromise = null;
    this.userConfirmationResolve = null;
    this.forceStop = false;
    this.currentStatus = 'idle';
    
    // 配置
    this.workspacePath = null;
    this.enableDownload = false;
    
    // 当前采集器实例
    this.currentCollector = null;
  }

  /**
   * 初始化搜集服务调度器
   */
  static initializeCollectorService(logger, projectRoot) {
    if (isInitialized) {
      return;
    }

    log = logger;
    log.info('[Collector服务] 正在初始化调度器...');

    // 初始化工具模块
    dateParser.initializeDateParser(logger);
    fileSystemHelper.initializeFileSystemHelper(logger);
    pageStatusChecker.initializePageStatusChecker(logger);

    // 初始化站点配置服务
    siteProfileService.initializeSiteProfileService(logger, projectRoot);

    // 加载站点配置
    const loadResult = siteProfileService.loadSiteProfiles();
    if (loadResult.success) {
      // 排除 chromeUserDataPath，只计算论坛配置数量
      const forumKeys = Object.keys(loadResult.profiles).filter(key => key !== 'chromeUserDataPath');
      const forumCount = forumKeys.length;
      log.info(`[Collector服务] 站点配置加载成功，支持 ${forumCount} 个论坛`);
    } else {
      log.warn(`[Collector服务] 站点配置加载失败: ${loadResult.error}`);
    }

    isInitialized = true;
    log.info('[Collector服务] 调度器初始化完成');
  }

  /**
   * 设置状态更新回调
   */
  setStatusUpdateCallback(callback) {
    this.statusUpdateCallback = callback;
  }

  /**
   * 设置用户确认回调
   */
  setUserConfirmationCallback(callback) {
    this.userConfirmationCallback = callback;
  }

  /**
   * 设置工作区路径
   */
  setWorkspacePath(path) {
    this.workspacePath = path;
  }

  /**
   * 设置是否启用下载
   */
  setEnableDownload(enable) {
    this.enableDownload = enable;
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(status, message) {
    this.currentStatus = status;
    if (this.statusUpdateCallback) {
      this.statusUpdateCallback({
        status: status,
        message: message,
        timestamp: new Date()
      });
    }
    log && log.info(`[CollectorService] 状态更新: ${status} - ${message}`);
  }

  /**
   * 获取当前任务状态
   */
  getCurrentTaskStatus() {
    return {
      isRunning: this.isRunning,
      currentStatus: this.currentStatus,
      currentTask: this.currentTask,
      taskHistory: this.taskHistory
    };
  }

  /**
   * 启动搜集任务 - 调度器模式
   */
  async startTask(siteKey, targetUrl, options = {}) {
    if (this.isRunning) {
      return { success: false, error: '已有任务正在运行' };
    }

    if (!isInitialized) {
      return { success: false, error: '服务未初始化' };
    }

    try {
      // 设置任务状态
      this.isRunning = true;
      this.isStopping = false;
      this.currentTask = {
        siteKey,
        targetUrl,
        options,
        startTime: new Date(),
        status: 'running'
      };

      log.info(`[CollectorService] 调度器启动搜集任务: ${siteKey} -> ${targetUrl}`);
      this.updateTaskStatus('initializing', '正在初始化采集器...');

      // 获取站点配置
      const siteProfile = siteProfileService.getSiteProfile(siteKey);
      if (!siteProfile) {
        throw new Error(`未找到站点配置: ${siteKey}`);
      }

      // 根据站点选择对应的采集器
      const CollectorClass = this.getCollectorClass(siteKey);
      if (!CollectorClass) {
        throw new Error(`未找到站点 ${siteKey} 对应的采集器`);
      }

      // 创建采集器实例
      this.currentCollector = new CollectorClass({
        siteProfile,
        workspacePath: this.workspacePath,
        log,
        databaseService: require('./databaseService'),
        updateTaskStatus: this.updateTaskStatus.bind(this)
      });

      // 委托给采集器执行任务
      const result = await this.currentCollector.executeTask(targetUrl, options);

      // 任务完成
      this.currentTask.status = 'completed';
      this.currentTask.endTime = new Date();
      this.currentTask.result = result;
      this.taskHistory.push({ ...this.currentTask });

      log.info(`[CollectorService] 搜集任务完成`);

      // 发送完成事件给前端
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('collector-task-completed', {
          taskId: this.currentTask.id,
          result: result
        });
      }

      // 直接返回采集器的结果，避免嵌套
      return result;

    } catch (error) {
      log.error(`[CollectorService] 搜集任务失败: ${error.message}`);
      
      // 更新任务状态
      if (this.currentTask) {
        this.currentTask.status = 'failed';
        this.currentTask.endTime = new Date();
        this.currentTask.error = error.message;
        this.taskHistory.push({ ...this.currentTask });
      }

      this.updateTaskStatus('failed', `任务失败: ${error.message}`);
      return { success: false, error: error.message };
    } finally {
      // 重置状态
      this.isRunning = false;
      this.currentTask = null;
      this.currentCollector = null;
      this.currentStatus = 'idle';
    }
  }

  /**
   * 根据站点键选择对应的采集器类
   * @param {string} siteKey - 站点键
   * @returns {Class|null} 采集器类
   */
  getCollectorClass(siteKey) {
    switch (siteKey) {
      case 'forumA':
        return ForumACollector;
      case 'forumB':
        return ForumBCollector;
      default:
        log.warn(`[CollectorService] 未知的站点键: ${siteKey}`);
        return null;
    }
  }

  /**
   * 停止当前搜集任务
   */
  async stopTask() {
    if (!this.isRunning) {
      return { success: false, error: '当前没有运行中的任务' };
    }

    log.info('[CollectorService] 正在停止搜集任务...');
    this.isStopping = true;

    try {
      // 如果有当前采集器，停止它
      if (this.currentCollector && typeof this.currentCollector.stopCollection === 'function') {
        await this.currentCollector.stopCollection();
      }

      this.updateTaskStatus('stopped', '任务已停止');
      
      // 更新任务状态
      if (this.currentTask) {
        this.currentTask.status = 'stopped';
        this.currentTask.endTime = new Date();
        this.taskHistory.push({ ...this.currentTask });
      }

      return { success: true, message: '任务已停止' };
    } catch (error) {
      log.error(`[CollectorService] 停止任务失败: ${error.message}`);
      return { success: false, error: error.message };
    } finally {
      // 重置状态
      this.isRunning = false;
      this.currentTask = null;
      this.currentCollector = null;
      this.currentStatus = 'idle';
    }
  }

  /**
   * 强制停止搜集任务
   */
  forceStopCollectionTask() {
    log.info('[CollectorService] 强制停止搜集任务');
    this.forceStop = true;
    this.isStopping = true;

    if (this.currentCollector && typeof this.currentCollector.forceStop === 'function') {
      this.currentCollector.forceStop();
    }
  }

  /**
   * 获取支持的论坛列表
   */
  getSupportedForums() {
    try {
      const profiles = siteProfileService.getAllSiteProfiles();
      if (log) {
        log.info(`[CollectorService] getSupportedForums - profiles:`, profiles);
      }

      if (!profiles) {
        if (log) {
          log.warn(`[CollectorService] getSupportedForums - 没有获取到站点配置`);
        }
        return [];
      }

      // 排除 chromeUserDataPath，只返回论坛配置
      const forums = Object.keys(profiles)
        .filter(key => key !== 'chromeUserDataPath')
        .map(key => ({
          key,
          name: profiles[key].name || key,
          url: profiles[key].baseUrl || ''
        }));

      if (log) {
        log.info(`[CollectorService] getSupportedForums - 返回论坛列表:`, forums);
      }

      return forums;
    } catch (error) {
      if (log) {
        log.error(`[CollectorService] getSupportedForums - 错误:`, error);
      }
      return [];
    }
  }

  /**
   * 获取任务状态 (兼容旧接口)
   */
  getTaskStatus() {
    return this.getCurrentTaskStatus();
  }

  /**
   * 配置下载设置
   * @param {Object} config - 下载配置
   * @param {boolean} config.enableDownload - 是否启用下载
   * @param {string} config.workspacePath - 工作区路径
   * @returns {Object} 配置结果
   */
  configureDownload(config) {
    try {
      this.setWorkspacePath(config.workspacePath);
      this.setEnableDownload(config.enableDownload);

      if (log) {
        log.info(`[CollectorService] 下载配置已更新: ${config.enableDownload ? '启用' : '禁用'}, 路径: ${config.workspacePath}`);
      }

      return { success: true, message: '下载配置已保存' };
    } catch (error) {
      if (log) {
        log.error(`[CollectorService] 配置下载设置失败: ${error.message}`);
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 设置主窗口引用
   * @param {BrowserWindow} mainWindow - 主窗口实例
   */
  setMainWindow(mainWindow) {
    this.mainWindow = mainWindow;
    if (log) {
      log.info('[CollectorService] 主窗口引用已设置');
    }
  }

  // 页面状态检查功能已移至 utils/pageStatusChecker.js
  // executeCollectionTask 方法已移至具体的采集器类中
  // executeScrapingLogic 方法已移至具体的采集器类中
  // parsePostContent 方法已移至具体的采集器类中
  // downloadAttachments 方法已移至具体的采集器类中
  // 其他业务逻辑方法已移至具体的采集器类中
}

// 创建实例
const collectorServiceInstance = new CollectorService();

// 添加静态方法到实例上，保持向后兼容
collectorServiceInstance.initializeCollectorService = CollectorService.initializeCollectorService;

module.exports = collectorServiceInstance;
