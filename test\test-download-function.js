/**
 * 测试下载功能
 * 检查附件下载和重命名机制是否正常工作
 */

const path = require('path');

// 模拟日志对象
const mockLog = {
  info: (msg) => console.log(`[INFO] ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${msg}`),
  error: (msg) => console.error(`[ERROR] ${msg}`)
};

// 模拟数据库服务
const mockDatabaseService = {
  updateDownloadStatus: (url, status, path, error) => {
    console.log(`[DB] 更新下载状态: ${url} -> ${status} ${path ? `(${path})` : ''} ${error ? `(错误: ${error})` : ''}`);
  }
};

async function testCollectorServiceImport() {
  console.log('\n=== 测试 CollectorService 导入 ===');
  
  try {
    // 测试导入
    const collectorService = require('./main_process/services/collectorService');
    console.log('✅ CollectorService 导入成功');
    
    // 测试初始化
    collectorService.initializeCollectorService(mockLog, process.cwd());
    console.log('✅ CollectorService 初始化成功');
    
    // 测试关键方法存在性
    const methods = [
      'downloadAttachments',
      'generateStandardFileName',
      'parsePostContent'
    ];
    
    for (const method of methods) {
      if (typeof collectorService[method] === 'function') {
        console.log(`✅ ${method} 方法存在`);
      } else {
        console.log(`❌ ${method} 方法缺失`);
      }
    }
    
    return collectorService;
    
  } catch (error) {
    console.error('❌ CollectorService 导入失败:', error.message);
    return null;
  }
}

async function testFileNameBuilder() {
  console.log('\n=== 测试 FileNameBuilder ===');
  
  try {
    const fileNameBuilder = require('./main_process/utils/fileNameBuilder');
    console.log('✅ FileNameBuilder 导入成功');
    
    // 测试文件名生成
    const testPostData = {
      nfoId: 'TEST-001',
      postTitle: '[测试论坛] TEST-001 测试标题 [1080p]',
      decompressionPassword: 'test123',
      boardInfo: {
        boardName: '测试论坛'
      }
    };
    
    const fileName = fileNameBuilder.buildStandardFileName(testPostData);
    console.log(`✅ 生成的文件名: ${fileName}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ FileNameBuilder 测试失败:', error.message);
    return false;
  }
}

async function testGenerateStandardFileName(collectorService) {
  console.log('\n=== 测试 generateStandardFileName 方法 ===');
  
  try {
    const testPostData = {
      nfoId: 'TEST-002',
      postTitle: '[98堂] TEST-002 另一个测试标题 [720p]',
      decompressionPassword: 'pass456',
      boardInfo: {
        boardName: '98堂'
      }
    };
    
    const result = collectorService.generateStandardFileName(testPostData, 'rar');
    
    if (result.success) {
      console.log('✅ generateStandardFileName 成功');
      console.log(`   - 生成的文件名: ${result.fileName}`);
      console.log(`   - 原始标题: ${result.originalTitle}`);
      console.log(`   - NFO ID: ${result.nfoId}`);
    } else {
      console.log('❌ generateStandardFileName 失败:', result.error);
    }
    
    return result.success;
    
  } catch (error) {
    console.error('❌ generateStandardFileName 测试失败:', error.message);
    return false;
  }
}

async function testDownloadAttachmentsMethod(collectorService) {
  console.log('\n=== 测试 downloadAttachments 方法结构 ===');
  
  try {
    // 检查方法是否存在
    if (typeof collectorService.downloadAttachments !== 'function') {
      console.log('❌ downloadAttachments 方法不存在');
      return false;
    }
    
    console.log('✅ downloadAttachments 方法存在');
    
    // 检查方法的参数数量
    const methodString = collectorService.downloadAttachments.toString();
    const paramMatch = methodString.match(/async\s+downloadAttachments\s*\(([^)]*)\)/);
    
    if (paramMatch) {
      const params = paramMatch[1].split(',').map(p => p.trim()).filter(p => p);
      console.log(`✅ 方法参数: ${params.join(', ')}`);
      
      if (params.length >= 3) {
        console.log('✅ 参数数量正确 (page, postData, siteProfile)');
      } else {
        console.log('⚠️ 参数数量可能不正确');
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ downloadAttachments 方法检查失败:', error.message);
    return false;
  }
}

async function testSiteProfiles() {
  console.log('\n=== 测试站点配置 ===');
  
  try {
    const fs = require('fs');
    const siteProfiles = JSON.parse(fs.readFileSync('./site-profiles.json', 'utf8'));
    
    console.log('✅ site-profiles.json 加载成功');
    
    // 检查论坛配置
    const forums = ['forumA', 'forumB'];
    for (const forum of forums) {
      if (siteProfiles[forum]) {
        const config = siteProfiles[forum].config;
        console.log(`✅ ${forum} 配置存在`);
        console.log(`   - 名称: ${siteProfiles[forum].name}`);
        console.log(`   - 附件选择器: ${config.attachmentUrlSelector}`);
        
        // 检查附件选择器是否包含文件扩展名过滤
        if (config.attachmentUrlSelector.includes('.rar') || 
            config.attachmentUrlSelector.includes('.zip')) {
          console.log('✅ 附件选择器包含文件扩展名过滤');
        } else {
          console.log('⚠️ 附件选择器可能缺少文件扩展名过滤');
        }
      } else {
        console.log(`❌ ${forum} 配置缺失`);
      }
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 站点配置测试失败:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 开始测试下载功能\n');
  
  const results = [];
  
  // 1. 测试 CollectorService 导入
  const collectorService = await testCollectorServiceImport();
  results.push(collectorService !== null);
  
  if (!collectorService) {
    console.log('\n❌ CollectorService 导入失败，无法继续测试');
    return false;
  }
  
  // 2. 测试 FileNameBuilder
  results.push(await testFileNameBuilder());
  
  // 3. 测试 generateStandardFileName
  results.push(await testGenerateStandardFileName(collectorService));
  
  // 4. 测试 downloadAttachments 方法
  results.push(await testDownloadAttachmentsMethod(collectorService));
  
  // 5. 测试站点配置
  results.push(await testSiteProfiles());
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('\n=== 测试结果汇总 ===');
  console.log(`通过测试: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！下载功能基础结构正常');
    console.log('\n📋 建议检查项目:');
    console.log('   1. 确保 Chrome 浏览器正在运行并开启调试端口');
    console.log('   2. 确保目标页面已经打开并登录');
    console.log('   3. 检查工作区路径设置是否正确');
    console.log('   4. 检查附件链接是否存在于页面中');
  } else {
    console.log('❌ 部分测试失败，需要修复基础问题');
  }
  
  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testCollectorServiceImport,
  testFileNameBuilder,
  testGenerateStandardFileName,
  testDownloadAttachmentsMethod,
  testSiteProfiles
};
