import React, { useEffect, useState } from 'react';
import { useAiCategoryStore, AiCategory } from '../hooks/useAiCategoryStore';
import { Trash2, Edit, Plus, Check, X } from 'lucide-react';

export function AiCategoryManager() {
  const { 
    categories, 
    isLoading, 
    error, 
    fetchCategories, 
    addCategory, 
    deleteCategory, 
    updateCategory,
    clearError 
  } = useAiCategoryStore();

  const [newCategoryName, setNewCategoryName] = useState('');
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingName, setEditingName] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleAdd = async () => {
    if (!newCategoryName.trim()) return;
    
    setIsAdding(true);
    const success = await addCategory(newCategoryName.trim());
    if (success) {
      setNewCategoryName('');
    }
    setIsAdding(false);
  };

  const handleDelete = async (id: number, name: string) => {
    if (window.confirm(`确定要删除分类标签 "${name}" 吗？`)) {
      await deleteCategory(id);
    }
  };

  const handleStartEdit = (category: AiCategory) => {
    setEditingId(category.id);
    setEditingName(category.name);
    clearError();
  };

  const handleSaveEdit = async () => {
    if (editingId && editingName.trim()) {
      const success = await updateCategory(editingId, editingName.trim());
      if (success) {
        setEditingId(null);
        setEditingName('');
      }
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditingName('');
    clearError();
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: 'add' | 'edit') => {
    if (e.key === 'Enter') {
      if (action === 'add') {
        handleAdd();
      } else {
        handleSaveEdit();
      }
    } else if (e.key === 'Escape' && action === 'edit') {
      handleCancelEdit();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold text-white mb-2">AI 分类体系管理</h2>
        <p className="text-gray-400 text-sm">
          管理AI智能分析使用的分类标签。这些标签将用于自动分类采集的帖子内容。
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button
              onClick={clearError}
              className="text-red-300 hover:text-red-100"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* 添加新分类 */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-3">添加新分类</h3>
        <div className="flex gap-2">
          <input
            type="text"
            value={newCategoryName}
            onChange={(e) => setNewCategoryName(e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, 'add')}
            placeholder="输入新的分类名称"
            className="form-input-app flex-1"
            disabled={isAdding}
          />
          <button
            onClick={handleAdd}
            disabled={!newCategoryName.trim() || isAdding}
            className="button-secondary-app flex items-center"
          >
            <Plus className="h-4 w-4 mr-1" />
            {isAdding ? '添加中...' : '添加'}
          </button>
        </div>
      </div>

      {/* 分类列表 */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-3">
          现有分类 ({categories.length})
        </h3>
        
        {isLoading ? (
          <div className="text-center py-8 text-gray-400">
            加载中...
          </div>
        ) : categories.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            暂无分类标签
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {categories.map((category) => (
              <div
                key={category.id}
                className="flex items-center justify-between p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
              >
                {editingId === category.id ? (
                  // 编辑模式
                  <div className="flex items-center gap-2 flex-1">
                    <input
                      type="text"
                      value={editingName}
                      onChange={(e) => setEditingName(e.target.value)}
                      onKeyPress={(e) => handleKeyPress(e, 'edit')}
                      className="form-input-app flex-1"
                      autoFocus
                    />
                    <button
                      onClick={handleSaveEdit}
                      className="button-secondary-app bg-green-600 hover:bg-green-700 text-white px-2 py-1"
                    >
                      <Check className="h-4 w-4" />
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="button-secondary-app border border-gray-500 text-gray-300 hover:bg-gray-600 px-2 py-1"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  // 显示模式
                  <>
                    <div className="flex-1">
                      <span className="text-white font-medium">{category.name}</span>
                      <div className="text-xs text-gray-400 mt-1">
                        创建时间: {new Date(category.created_at).toLocaleString()}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleStartEdit(category)}
                        className="icon-button-app text-gray-300 hover:text-white hover:bg-gray-600"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(category.id, category.name)}
                        className="icon-button-app text-red-400 hover:text-red-300 hover:bg-red-900/50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-900/30 border border-blue-500/50 p-4 rounded-lg">
        <h4 className="text-blue-200 font-semibold mb-2">使用说明</h4>
        <ul className="text-blue-100 text-sm space-y-1">
          <li>• 这些分类标签将用于AI智能分析功能</li>
          <li>• 在历史档案中点击"🤖 AI分析"时，AI会根据这些标签对帖子内容进行分类</li>
          <li>• 建议添加与您的内容相关的具体分类，如"国产精品"、"4K"、"VR"等</li>
          <li>• 分类名称不能重复，建议使用简洁明确的词汇</li>
        </ul>
      </div>
    </div>
  );
}
