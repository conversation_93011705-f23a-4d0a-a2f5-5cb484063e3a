import React, { useState, useEffect } from 'react';
import { useArchiveStore } from '../hooks/useArchiveStore';
import { useAiCategoryStore } from '../hooks/useAiCategoryStore';
import { Search, Filter, X, Calendar, RefreshCw, FileText, Brain, Zap } from 'lucide-react';

export function ArchiveFilterBar() {
  const {
    filters,
    setFilters,
    resetFilters,
    loading,
    openLinkModal,
    selectedRecordIds,
    isBatchAnalyzing,
    batchProgress,
    toggleSelection,
    selectAll,
    clearSelection,
    setBatchStatus,
    results
  } = useArchiveStore();
  const { categories, fetchCategories } = useAiCategoryStore();

  // 本地状态管理
  const [localKeyword, setLocalKeyword] = useState(filters.keyword || '');
  const [selectedAiTags, setSelectedAiTags] = useState<string[]>(filters.aiTags || []);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [localDateRange, setLocalDateRange] = useState({
    startDate: filters.dateRange?.startDate || '',
    endDate: filters.dateRange?.endDate || ''
  });
  const [isCopying, setIsCopying] = useState(false);

  // 顶级分类选项
  const topCategories = [
    '全部',
    '国产精品',
    '人物合集',
    'VR',
    '4K',
    '动画',
    '无码',
    '有码',
    '欧美',
    '日韩',
    '亚洲'
  ];

  // 来源论坛选项
  const sourceForums = [
    '全部',
    '98堂',
    'x1080x',
    '其他'
  ];

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // 处理关键词搜索
  const handleKeywordSearch = () => {
    setFilters({ keyword: localKeyword.trim() });
  };

  // 处理关键词输入框回车
  const handleKeywordKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleKeywordSearch();
    }
  };

  // 处理AI标签选择
  const handleAiTagToggle = (tag: string) => {
    const newTags = selectedAiTags.includes(tag)
      ? selectedAiTags.filter(t => t !== tag)
      : [...selectedAiTags, tag];
    
    setSelectedAiTags(newTags);
    setFilters({ aiTags: newTags });
  };

  // 处理日期范围应用
  const handleDateRangeApply = () => {
    if (localDateRange.startDate && localDateRange.endDate) {
      setFilters({
        dateRange: {
          startDate: localDateRange.startDate,
          endDate: localDateRange.endDate
        }
      });
    } else {
      setFilters({ dateRange: undefined });
    }
  };

  // 清除所有筛选
  const handleClearAll = () => {
    setLocalKeyword('');
    setSelectedAiTags([]);
    setLocalDateRange({ startDate: '', endDate: '' });
    resetFilters();
  };

  // 提取所有符合条件的链接
  const handleExtractLinks = async () => {
    setIsCopying(true);
    try {
      console.log('🔗 开始提取链接，当前筛选条件:', filters);

      // 使用当前的筛选条件调用后端
      const response = await window.sfeElectronAPI.archiveGetAllLinksForQuery(filters);

      if (response.success && response.links && response.links.length > 0) {
        // 打开预览弹窗而不是直接复制
        openLinkModal(response.links);
        console.log('✅ 链接提取成功:', { count: response.links.length, links: response.links });
      } else {
        alert('没有找到符合条件的下载链接。\n\n请尝试调整筛选条件或检查数据中是否包含下载链接。');
        console.log('ℹ️ 没有找到链接');
      }
    } catch (error) {
      console.error('❌ 提取链接失败:', error);
      alert(`提取失败！\n\n错误信息：${error.message || '未知错误'}\n\n请查看控制台日志获取更多信息。`);
    } finally {
      setIsCopying(false);
    }
  };

  // 批量 AI 分析选中项
  const handleBatchAnalyzeSelected = async () => {
    if (selectedRecordIds.size === 0) {
      alert('请先选择要分析的记录');
      return;
    }

    const recordIds = Array.from(selectedRecordIds);
    console.log('🧠 开始批量AI分析选中项:', recordIds);

    try {
      const response = await window.sfeElectronAPI.archiveBatchAnalyzeAI(recordIds);
      if (response.success) {
        console.log('✅ 批量AI分析任务已启动:', response.message);
      } else {
        alert(`启动批量分析失败：${response.error}`);
      }
    } catch (error) {
      console.error('❌ 启动批量AI分析失败:', error);
      alert(`启动失败：${error.message || '未知错误'}`);
    }
  };

  // 批量 AI 分析全部待处理
  const handleBatchAnalyzeAll = async () => {
    try {
      console.log('🧠 开始获取全部待处理记录...');

      // 构建筛选条件：当前筛选 + ai_tags_json 为空
      const filtersForUnprocessed = {
        ...filters,
        hasAiTags: false // 添加一个标记表示只要没有AI标签的
      };

      // 获取所有符合条件且未处理的记录
      const response = await window.sfeElectronAPI.archiveComplexQuery(filtersForUnprocessed);

      if (response.success && response.data && response.data.length > 0) {
        // 过滤出没有AI标签的记录
        const unprocessedRecords = response.data.filter(record =>
          !record.ai_tags_json || record.ai_tags_json.trim() === ''
        );

        if (unprocessedRecords.length === 0) {
          alert('没有找到待处理的记录。\n\n所有符合筛选条件的记录都已经分析过了。');
          return;
        }

        const recordIds = unprocessedRecords.map(record => record.id);
        console.log('🧠 开始批量AI分析全部待处理:', { total: recordIds.length, ids: recordIds });

        const batchResponse = await window.sfeElectronAPI.archiveBatchAnalyzeAI(recordIds);
        if (batchResponse.success) {
          console.log('✅ 批量AI分析任务已启动:', batchResponse.message);
        } else {
          alert(`启动批量分析失败：${batchResponse.error}`);
        }
      } else {
        alert('没有找到符合条件的记录。\n\n请尝试调整筛选条件。');
      }
    } catch (error) {
      console.error('❌ 启动批量AI分析失败:', error);
      alert(`启动失败：${error.message || '未知错误'}`);
    }
  };

  return (
    <div className="bg-gray-800 p-4 rounded-lg space-y-4">
      {/* 第一行：顶级分类标签页 */}
      <div>
        <div className="flex flex-wrap gap-2">
          {topCategories.map((category) => (
            <button
              key={category}
              onClick={() => setFilters({ topCategory: category })}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                filters.topCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* 第二行：关键词搜索和高级筛选切换 */}
      <div className="flex gap-3 items-center">
        <div className="flex-1 flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              value={localKeyword}
              onChange={(e) => setLocalKeyword(e.target.value)}
              onKeyPress={handleKeywordKeyPress}
              placeholder="搜索帖子标题..."
              className="form-input-app pl-10 w-full"
            />
          </div>
          <button
            onClick={handleKeywordSearch}
            disabled={loading}
            className="button-secondary-app px-4"
          >
            搜索
          </button>
        </div>

        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`button-secondary-app flex items-center gap-2 ${
            showAdvanced ? 'bg-blue-600 text-white' : ''
          }`}
        >
          <Filter className="h-4 w-4" />
          高级筛选
        </button>

        <button
          onClick={handleClearAll}
          className="button-secondary-app flex items-center gap-2 text-red-400 hover:text-red-300"
        >
          <X className="h-4 w-4" />
          清除
        </button>

        <button
          onClick={() => setFilters({})}
          disabled={loading}
          className="button-secondary-app flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          刷新
        </button>

        <button
          onClick={handleExtractLinks}
          disabled={isCopying || loading}
          className="button-secondary-app flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white disabled:bg-green-800"
          title="提取所有符合筛选条件的下载链接进行预览编辑"
        >
          <FileText className={`h-4 w-4 ${isCopying ? 'animate-pulse' : ''}`} />
          {isCopying ? '提取中...' : '提取链接'}
        </button>

        <button
          onClick={handleBatchAnalyzeSelected}
          disabled={selectedRecordIds.size === 0 || isBatchAnalyzing || loading}
          className="button-secondary-app flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white disabled:bg-purple-800"
          title="对选中的记录进行批量AI分析"
        >
          <Brain className={`h-4 w-4 ${isBatchAnalyzing ? 'animate-pulse' : ''}`} />
          分析选中项 ({selectedRecordIds.size})
        </button>

        <button
          onClick={handleBatchAnalyzeAll}
          disabled={isBatchAnalyzing || loading}
          className="button-secondary-app flex items-center gap-2 bg-orange-600 hover:bg-orange-700 text-white disabled:bg-orange-800"
          title="对所有符合筛选条件且未处理的记录进行批量AI分析"
        >
          <Zap className={`h-4 w-4 ${isBatchAnalyzing ? 'animate-pulse' : ''}`} />
          分析全部待处理
        </button>
      </div>

      {/* 高级筛选面板 */}
      {showAdvanced && (
        <div className="bg-gray-700 p-4 rounded-lg space-y-4">
          {/* AI标签多选 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              AI标签筛选
            </label>
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleAiTagToggle(category.name)}
                  className={`px-2 py-1 rounded text-xs transition-colors ${
                    selectedAiTags.includes(category.name)
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
            {selectedAiTags.length > 0 && (
              <div className="mt-2 text-xs text-gray-400">
                已选择: {selectedAiTags.join(', ')}
              </div>
            )}
          </div>

          {/* 来源论坛选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              来源论坛
            </label>
            <select
              value={filters.sourceForum || '全部'}
              onChange={(e) => setFilters({ sourceForum: e.target.value })}
              className="form-select-app w-48"
            >
              {sourceForums.map((forum) => (
                <option key={forum} value={forum}>
                  {forum}
                </option>
              ))}
            </select>
          </div>

          {/* 日期范围选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              采集日期范围
            </label>
            <div className="flex gap-2 items-center">
              <input
                type="date"
                value={localDateRange.startDate}
                onChange={(e) => setLocalDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                className="form-input-app w-40"
              />
              <span className="text-gray-400">至</span>
              <input
                type="date"
                value={localDateRange.endDate}
                onChange={(e) => setLocalDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                className="form-input-app w-40"
              />
              <button
                onClick={handleDateRangeApply}
                className="button-secondary-app flex items-center gap-1"
              >
                <Calendar className="h-4 w-4" />
                应用
              </button>
            </div>
            {filters.dateRange && (
              <div className="mt-1 text-xs text-gray-400">
                当前范围: {filters.dateRange.startDate} 至 {filters.dateRange.endDate}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
