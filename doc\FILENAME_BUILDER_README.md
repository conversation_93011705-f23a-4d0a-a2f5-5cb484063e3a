# 文件名构建器 (FileNameBuilder) 说明文档

## 概述

文件名构建器是SoulForge项目中的一个核心组件，用于为生成的.md档案文件和下载的附件实施标准化重命名规则。

## 命名格式

标准化文件名格式：`[番号] - [标题] [标签]`

### 示例

- `[ADN-621] - ADN-621 美女写真集 [4K]`
- `[SSIS-123] - SSIS-123-C 中文字幕版本 1080p [1080p] [内嵌字幕-C]`
- `[JUL-789] - JUL-789-UC 4K 中文字幕破解版 [4K] [破解版-UC]`
- `美女写真合集 1080p 中文字幕 [1080p] [内嵌字幕-C]` (无番号情况)

## 功能特性

### 1. 番号处理
- **提取基础番号**：从`ADN-621-C`提取出`ADN-621`
- **后缀转标签**：将`-C`、`-U`、`-UC`等后缀转换为对应标签
- **格式标准化**：统一转换为大写格式

### 2. 标题清理
- 移除论坛后缀（如`- x1080x.com - Powered by Discuz!`）
- 移除标题开头的重复标签（如`[4K]`）
- 清理特殊字符和多余空格

### 3. 标签系统

#### 支持的标签类型

**品质标签**（最高优先级）
- `4K` - 4K/2160p分辨率
- `1080p` - 1080p/FHD分辨率  
- `720p` - 720p/HD分辨率
- `480p` - 480p/SD分辨率

**字幕标签**
- `内嵌字幕-C` - 内嵌中文字幕
- `外挂字幕` - 外挂字幕文件

**版本标签**
- `破解版-U` - 无码破解版
- `破解版-UC` - 内嵌中文字幕的破解版
- `流出版` - 流出版本

**特殊格式标签**
- `VR` - 虚拟现实
- `3D` - 3D格式
- `HDR` - HDR格式

#### 标签优先级和冲突处理

1. **优先级排序**：品质 > 字幕 > 版本 > 特殊格式
2. **冲突避免**：
   - 如果有`-UC`后缀，不会同时添加`-U`和`-C`标签
   - 高优先级标签（从番号提取）会覆盖低优先级标签（从标题提取）

### 4. 文件名安全处理
- 移除Windows不允许的字符：`<>:"/\|?*`
- 合并多个空格
- 限制文件名长度（最大200字符，为扩展名留空间）

## 集成方式

### 在CollectorService中的使用

```javascript
// 1. 导入文件名构建器
const fileNameBuilder = require('../utils/fileNameBuilder');

// 2. 生成.md档案文件名
const standardFileName = fileNameBuilder.buildStandardFileName(postData);
const fileName = `${standardFileName}.md`;

// 3. 生成附件文件名
const result = this.generateStandardFileName(postData, fileExtension);
// 内部调用fileNameBuilder.buildStandardFileName()
```

### API接口

#### `buildStandardFileName(postData)`

**参数**：
- `postData.nfoId` - 番号（可选）
- `postData.postTitle` - 帖子标题（必需）
- `postData.tags` - 标签数组（可选）
- `postData.decompressionPassword` - 解压密码（可选）

**返回值**：
- `string` - 标准化的文件名（不含扩展名）

## 实现细节

### 核心方法

1. `_extractCleanNfoId()` - 提取并清理番号
2. `_cleanTitle()` - 清理标题
3. `_extractAndStandardizeTags()` - 提取并标准化标签
4. `_extractTagsFromNfoId()` - 从番号后缀提取标签
5. `_extractTagsFromText()` - 从文本中提取标签
6. `_isConflictingTag()` - 检查标签冲突
7. `_sortTagsByPriority()` - 按优先级排序标签
8. `_sanitizeFileName()` - 文件名安全处理

### 配置文件位置

- 主文件：`main_process/utils/fileNameBuilder.js`
- 集成点：`main_process/services/collectorService.js`

## 验收标准

✅ 所有新生成的.md文件和下载的附件都遵循`[番号] - [标题] [标签]`格式  
✅ 文件名中的标签正确反映帖子的元数据  
✅ 同一番号的不同版本文件能够自然排序，便于管理  
✅ 支持无番号情况的处理  
✅ 标签冲突得到正确处理  

## 更新日志

- **2025-07-26**: 初始版本实现
  - 实现标准化文件名构建器
  - 集成到CollectorService
  - 支持番号提取、标题清理、标签标准化
  - 实现标签优先级和冲突处理机制
