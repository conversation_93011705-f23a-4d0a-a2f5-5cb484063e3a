// 最终的 CollectorService 验证脚本
// 在 Electron 应用的开发者控制台中运行

async function finalCollectorVerification() {
  console.log('🎯 开始最终的 CollectorService 验证...\n');
  
  try {
    // 1. 验证 API 可用性
    console.log('1️⃣ 验证 API 可用性');
    
    const apiMethods = [
      'collectorStartTask',
      'collectorStopTask', 
      'collectorGetStatus',
      'collectorGetForums'
    ];
    
    let allApisAvailable = true;
    apiMethods.forEach(method => {
      if (typeof window.sfeElectronAPI[method] === 'function') {
        console.log(`  ✅ ${method} - 可用`);
      } else {
        console.log(`  ❌ ${method} - 不可用`);
        allApisAvailable = false;
      }
    });
    
    if (!allApisAvailable) {
      console.error('❌ 部分 API 不可用，请检查 preload.js 配置');
      return false;
    }
    
    // 2. 验证站点配置加载
    console.log('\n2️⃣ 验证站点配置加载');
    
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (forumsResult.success) {
      console.log('✅ 站点配置加载成功');
      console.log(`支持的论坛数量: ${forumsResult.forums.length}`);
      
      forumsResult.forums.forEach((forum, index) => {
        console.log(`  ${index + 1}. ${forum.key}: ${forum.name}`);
      });
      
      if (forumsResult.forums.length !== 2) {
        console.warn('⚠️ 期望有2个论坛配置，实际数量不符');
      }
    } else {
      console.error('❌ 站点配置加载失败:', forumsResult.error);
      return false;
    }
    
    // 3. 验证服务状态
    console.log('\n3️⃣ 验证服务状态');
    
    const statusResult = await window.sfeElectronAPI.collectorGetStatus();
    
    if (statusResult.success) {
      console.log('✅ 服务状态获取成功');
      console.log(`运行状态: ${statusResult.status.isRunning ? '运行中' : '空闲'}`);
      console.log(`当前任务: ${statusResult.status.currentTask ? '有' : '无'}`);
      console.log(`历史任务数: ${statusResult.status.taskHistory.length}`);
    } else {
      console.error('❌ 服务状态获取失败:', statusResult.error);
      return false;
    }
    
    // 4. 验证任务启动功能
    console.log('\n4️⃣ 验证任务启动功能');
    
    if (forumsResult.forums.length > 0) {
      const testSiteKey = forumsResult.forums[0].key;
      const testTargetUrl = 'https://example.com/forum/test';
      const testOptions = { maxPages: 2, delay: 500 };
      
      console.log(`测试参数:`);
      console.log(`  站点: ${testSiteKey}`);
      console.log(`  URL: ${testTargetUrl}`);
      console.log(`  选项: ${JSON.stringify(testOptions)}`);
      
      const taskResult = await window.sfeElectronAPI.collectorStartTask(testSiteKey, testTargetUrl, testOptions);
      
      if (taskResult.success) {
        console.log('✅ 任务启动成功');
        console.log(`任务ID: ${taskResult.taskId}`);
        console.log(`搜集结果: 找到 ${taskResult.result.collectedCount} 个链接`);
        
        // 验证返回的数据结构
        if (taskResult.result.links && Array.isArray(taskResult.result.links)) {
          console.log('✅ 返回数据结构正确');
          
          if (taskResult.result.links.length > 0) {
            const link = taskResult.result.links[0];
            console.log('示例链接数据:');
            console.log(`  标题: ${link.postTitle}`);
            console.log(`  URL: ${link.postUrl}`);
            console.log(`  NFO ID: ${link.nfoId}`);
            console.log(`  磁力链接: ${link.magnetLink}`);
          }
        } else {
          console.warn('⚠️ 返回数据结构异常');
        }
      } else {
        console.error('❌ 任务启动失败:', taskResult.error);
        return false;
      }
    }
    
    // 5. 验证错误处理
    console.log('\n5️⃣ 验证错误处理');
    
    // 测试无效站点
    const invalidSiteResult = await window.sfeElectronAPI.collectorStartTask('invalidSite', 'https://example.com', {});
    
    if (!invalidSiteResult.success) {
      console.log('✅ 无效站点错误处理正确');
      console.log(`错误信息: ${invalidSiteResult.error}`);
    } else {
      console.warn('⚠️ 无效站点错误处理异常');
    }
    
    // 测试无效URL
    if (forumsResult.forums.length > 0) {
      const invalidUrlResult = await window.sfeElectronAPI.collectorStartTask(forumsResult.forums[0].key, 'invalid-url', {});
      
      if (!invalidUrlResult.success) {
        console.log('✅ 无效URL错误处理正确');
        console.log(`错误信息: ${invalidUrlResult.error}`);
      } else {
        console.warn('⚠️ 无效URL错误处理异常');
      }
    }
    
    // 6. 验证停止功能
    console.log('\n6️⃣ 验证停止功能');
    
    const stopResult = await window.sfeElectronAPI.collectorStopTask();
    
    if (stopResult.success || stopResult.message) {
      console.log('✅ 停止功能正常');
      console.log(`消息: ${stopResult.message}`);
    } else {
      console.warn('⚠️ 停止功能异常');
    }
    
    // 7. 最终状态检查
    console.log('\n7️⃣ 最终状态检查');
    
    const finalStatus = await window.sfeElectronAPI.collectorGetStatus();
    
    if (finalStatus.success) {
      console.log('✅ 最终状态检查成功');
      console.log(`运行状态: ${finalStatus.status.isRunning ? '运行中' : '空闲'}`);
      console.log(`历史任务数: ${finalStatus.status.taskHistory.length}`);
      
      if (finalStatus.status.taskHistory.length > 0) {
        console.log('\n最近任务历史:');
        finalStatus.status.taskHistory.slice(-2).forEach((task, index) => {
          console.log(`  ${index + 1}. 站点: ${task.siteKey}, 状态: ${task.status}`);
          console.log(`     开始: ${new Date(task.startTime).toLocaleTimeString()}`);
          if (task.endTime) {
            console.log(`     结束: ${new Date(task.endTime).toLocaleTimeString()}`);
          }
        });
      }
    }
    
    console.log('\n🎉 CollectorService 最终验证完成！');
    console.log('✅ 所有核心功能正常');
    console.log('✅ 站点配置集成成功');
    console.log('✅ 错误处理机制完善');
    console.log('✅ 任务状态管理正常');
    console.log('✅ IPC 通信正常');
    
    console.log('\n📋 验收标准确认:');
    console.log('✅ collectorService.js 文件已创建');
    console.log('✅ startTask 方法可正常调用');
    console.log('✅ siteProfileService 集成成功');
    console.log('✅ 有效 siteKey 可获取配置信息');
    console.log('✅ 无效 siteKey 正确抛出错误');
    
    return true;
    
  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
    return false;
  }
}

// 简化版验证
async function quickVerification() {
  console.log('⚡ 快速验证 CollectorService...\n');
  
  try {
    // 获取论坛列表
    const forums = await window.sfeElectronAPI.collectorGetForums();
    console.log(`论坛配置: ${forums.success ? '✅' : '❌'} (${forums.forums?.length || 0} 个)`);
    
    // 获取状态
    const status = await window.sfeElectronAPI.collectorGetStatus();
    console.log(`服务状态: ${status.success ? '✅' : '❌'} (${status.status?.isRunning ? '运行中' : '空闲'})`);
    
    // 测试任务启动
    if (forums.success && forums.forums.length > 0) {
      const task = await window.sfeElectronAPI.collectorStartTask(forums.forums[0].key, 'https://test.com', {});
      console.log(`任务启动: ${task.success ? '✅' : '❌'} (${task.success ? '成功' : task.error})`);
    }
    
    console.log('\n⚡ 快速验证完成！');
    
  } catch (error) {
    console.error('❌ 快速验证失败:', error);
  }
}

// 导出函数
window.finalCollectorVerification = finalCollectorVerification;
window.quickVerification = quickVerification;

console.log(`
🛠️ CollectorService 最终验证工具已加载！

使用方法:
1. finalCollectorVerification() - 完整验证
2. quickVerification() - 快速验证

推荐使用: finalCollectorVerification()
`);

// 自动运行验证
finalCollectorVerification();
