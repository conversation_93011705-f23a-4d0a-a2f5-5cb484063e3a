import React from 'react';
import { LuX, LuFile, LuHardDrive, LuImage, LuExternalLink } from 'react-icons/lu';

interface WhatsLinkFile {
  name: string;
  size: number;
  path?: string;
}

interface WhatsLinkData {
  name?: string;
  files?: WhatsLinkFile[];
  total_size?: number;
  file_count?: number;
  screenshots?: string[];
  magnet?: string;
  hash?: string;
  trackers?: string[];
}

interface WhatsLinkPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: WhatsLinkData | null;
  isLoading?: boolean;
  error?: string | null;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const WhatsLinkPreviewModal: React.FC<WhatsLinkPreviewModalProps> = ({
  isOpen,
  onClose,
  data,
  isLoading = false,
  error = null
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-[#2c2c2c] rounded-lg border border-[#444444] shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-[#444444]">
          <h2 className="text-xl font-semibold text-neutral-100 flex items-center gap-2">
            <LuFile className="text-blue-400" />
            磁力链接内容预览
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-[#404040] rounded-lg transition-colors"
            aria-label="关闭"
          >
            <LuX size={20} className="text-neutral-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
              <span className="ml-3 text-neutral-300">正在获取预览信息...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-900/30 border border-red-600/50 rounded-lg p-4 text-red-300">
              <p className="font-medium">获取预览信息失败</p>
              <p className="text-sm mt-1 text-red-400">{error}</p>
            </div>
          )}

          {!isLoading && !error && data && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="bg-[#1e1e1e] rounded-lg p-4 border border-[#404040]">
                <h3 className="text-lg font-medium text-neutral-100 mb-3">基本信息</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {data.name && (
                    <div>
                      <span className="text-neutral-400 text-sm">名称:</span>
                      <p className="text-neutral-200 font-medium break-all">{data.name}</p>
                    </div>
                  )}
                  {data.file_count !== undefined && (
                    <div>
                      <span className="text-neutral-400 text-sm">文件数量:</span>
                      <p className="text-neutral-200 font-medium">{data.file_count} 个文件</p>
                    </div>
                  )}
                  {data.total_size !== undefined && (
                    <div>
                      <span className="text-neutral-400 text-sm">总大小:</span>
                      <p className="text-neutral-200 font-medium flex items-center gap-1">
                        <LuHardDrive size={16} className="text-green-400" />
                        {formatFileSize(data.total_size)}
                      </p>
                    </div>
                  )}
                  {data.hash && (
                    <div>
                      <span className="text-neutral-400 text-sm">哈希值:</span>
                      <p className="text-neutral-200 font-mono text-xs break-all">{data.hash}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Files List */}
              {data.files && data.files.length > 0 && (
                <div className="bg-[#1e1e1e] rounded-lg p-4 border border-[#404040]">
                  <h3 className="text-lg font-medium text-neutral-100 mb-3">文件列表</h3>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {data.files.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-[#2c2c2c] rounded border border-[#404040]">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <LuFile size={16} className="text-blue-400 flex-shrink-0" />
                          <span className="text-neutral-200 text-sm truncate" title={file.name}>
                            {file.name}
                          </span>
                        </div>
                        <span className="text-neutral-400 text-xs ml-2 flex-shrink-0">
                          {formatFileSize(file.size)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Screenshots */}
              {data.screenshots && data.screenshots.length > 0 && (
                <div className="bg-[#1e1e1e] rounded-lg p-4 border border-[#404040]">
                  <h3 className="text-lg font-medium text-neutral-100 mb-3 flex items-center gap-2">
                    <LuImage className="text-purple-400" />
                    截图预览
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {data.screenshots.map((screenshot, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={screenshot}
                          alt={`截图 ${index + 1}`}
                          className="w-full h-32 object-cover rounded border border-[#404040] group-hover:border-blue-400 transition-colors"
                          loading="lazy"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors rounded flex items-center justify-center">
                          <LuExternalLink className="text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {!isLoading && !error && !data && (
            <div className="text-center py-8 text-neutral-400">
              <LuFile size={48} className="mx-auto mb-4 opacity-50" />
              <p>暂无预览信息</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-[#444444] p-4 bg-[#1e1e1e]">
          <div className="flex items-center justify-between">
            <p className="text-xs text-neutral-500">
              预览信息由{' '}
              <a 
                href="https://whatslink.info" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 underline"
              >
                whatslink.info
              </a>
              {' '}提供
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-[#404040] hover:bg-[#505050] text-neutral-200 rounded-lg transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsLinkPreviewModal;
