const fs = require('fs');

const config = {
  "chromeUserDataPath": "C:\\temp\\chrome-debug",
  "forumA": {
    "name": "x1080x",
    "loginUrl": "https://ccgga.me/forum.php",
    "config": {
      "postContainerSelector": "tbody[id^='normalthread_']",
      "postLinkSelector": "tr > th > a.s.xst",
      "postDateSelector": "tr > td.by:nth-child(3) > em",
      "postTitleSelectorOnPage": "#thread_subject",
      "previewImageSelector": "img[onload*='thumbImg']",
      "magnetLinkSelector": "a[href^='magnet:']",
      "ed2kLinkSelector": "a[href^='ed2k://']",
      "attachmentUrlSelector": "a[id^='aid']",
      "passwordSelector": "TODO",
      "nextPageSelector": "a.nxt",
      "downloadLimitIndicatorText": "limit",
      "humanVerificationIndicatorSelector": "#challenge-running",
      "serverErrorIndicatorSelector": "body:has-text('500 Internal Server Error')"
    },
    "boards": {
      "202": { "name": "4K", "tags": ["JAV", "4K", "Original", "RAR"] },
      "75": { "name": "HD", "tags": ["JAV", "1080p", "Original", "RAR"] },
      "164": { "name": "S-cute", "tags": ["JAV", "S-cute", "Original", "RAR"] }
    }
  },
  "forumB": {
    "name": "98",
    "loginUrl": "https://example.com/login",
    "config": {},
    "boards": {
      "37": { "name": "test", "tags": ["JAV"] }
    }
  }
};

fs.writeFileSync('site-profiles.json', JSON.stringify(config, null, 2), 'utf8');
console.log('site-profiles.json created successfully');
