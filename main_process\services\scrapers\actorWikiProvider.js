// main_process/services/scrapers/actorWikiProvider.js
// "二级制导"考古特工 - 专门负责人物档案的精确获取

const { chromium } = require('playwright');
const cheerio = require('cheerio');
const log = require('electron-log');

/**
 * 二级制导考古特工
 * 采用两阶段模式：Wikidata API精准制导 + Wikipedia页面深度挖掘
 */
class ActorWikiProvider {
  constructor() {
    this.name = '二级制导考古特工';
    this.version = '1.0.0';
    this.timeout = 30000; // 30秒超时
    
    // AV女优相关关键词
    this.actressKeywords = [
      'AV女优', 'AV女優', 'Japanese AV idol', 'Japanese pornographic actress',
      'pornographic actress', 'Japanese actress', 'gravure idol', 'Japanese idol',
      'グラビアアイドル', 'アダルトビデオ', 'AV監督', 'Japanese pornographic film director'
    ];
  }

  /**
   * 刮削演员档案信息
   * @param {string} actorName - 演员姓名
   * @returns {Promise<Object|null>} 演员档案对象
   */
  async scrapeActorProfile(actorName) {
    log.info(`[${this.name}] 开始刮削演员档案: ${actorName}`);
    
    try {
      // 阶段一：Wikidata API 精准制导
      const wikidataResult = await this.stageOneWikidataSearch(actorName);
      if (!wikidataResult) {
        log.warn(`[${this.name}] 阶段一失败，未找到目标人物: ${actorName}`);
        return null;
      }

      // 阶段二：Wikipedia 页面深度挖掘
      const wikipediaResult = await this.stageTwoWikipediaExtraction(wikidataResult);
      if (!wikipediaResult) {
        log.warn(`[${this.name}] 阶段二失败，无法提取页面内容: ${actorName}`);
        return null;
      }

      // 数据整合
      const profile = this.integrateProfileData(wikidataResult, wikipediaResult, actorName);
      
      log.info(`[${this.name}] 成功刮削演员档案: ${actorName}`);
      return profile;

    } catch (error) {
      log.error(`[${this.name}] 刮削过程发生错误: ${error.message}`);
      log.error(`[${this.name}] 错误堆栈: ${error.stack}`);
      return null;
    }
  }

  /**
   * 阶段一：Wikidata API 精准制导
   * @param {string} actorName - 演员姓名
   * @returns {Promise<Object|null>} Wikidata结果对象
   */
  async stageOneWikidataSearch(actorName) {
    log.debug(`[${this.name}] 阶段一：开始Wikidata API搜索...`);

    try {
      // 预处理：语言转换以提升搜索命中率
      const searchNames = this.preprocessActorName(actorName);
      
      for (const searchName of searchNames) {
        log.debug(`[${this.name}] 搜索名称: ${searchName}`);
        
        // API搜索
        const searchUrl = `https://www.wikidata.org/w/api.php?action=wbsearchentities&search=${encodeURIComponent(searchName)}&language=zh&format=json`;
        const searchResponse = await this.makeHttpRequest(searchUrl);
        
        if (!searchResponse || !searchResponse.search) {
          continue;
        }

        // 目标甄别
        const targetEntity = this.identifyTargetEntity(searchResponse.search);
        if (!targetEntity) {
          continue;
        }

        // 获取实体数据
        const entityData = await this.fetchEntityData(targetEntity.id);
        if (!entityData) {
          continue;
        }

        // 提取结构化情报
        const structuredData = this.extractStructuredData(entityData, targetEntity);
        if (structuredData) {
          log.debug(`[${this.name}] 阶段一成功，找到目标实体: ${targetEntity.id}`);
          return structuredData;
        }
      }

      return null;

    } catch (error) {
      log.error(`[${this.name}] 阶段一发生错误: ${error.message}`);
      return null;
    }
  }

  /**
   * 预处理演员姓名，生成多个搜索候选
   * @param {string} actorName - 原始演员姓名
   * @returns {Array<string>} 搜索候选名称数组
   */
  preprocessActorName(actorName) {
    const candidates = [actorName];
    
    // 简体转繁体（参考源码逻辑）
    try {
      // 这里可以集成zhconv库进行转换，暂时使用简单的替换
      const traditionalName = actorName
        .replace(/优/g, '優')
        .replace(/导/g, '導')
        .replace(/监/g, '監')
        .replace(/业/g, '業');
      
      if (traditionalName !== actorName) {
        candidates.push(traditionalName);
      }
    } catch (error) {
      log.warn(`[${this.name}] 语言转换失败: ${error.message}`);
    }

    return candidates;
  }

  /**
   * 目标甄别：通过描述关键词筛选正确的目标人物
   * @param {Array} searchResults - 搜索结果数组
   * @returns {Object|null} 目标实体对象
   */
  identifyTargetEntity(searchResults) {
    for (const result of searchResults) {
      const description = result.description || '';
      
      // 检查是否匹配AV女优相关关键词
      for (const keyword of this.actressKeywords) {
        if (description.toLowerCase().includes(keyword.toLowerCase())) {
          log.debug(`[${this.name}] 找到匹配实体: ${result.id}, 描述: ${description}`);
          return result;
        }
      }
    }
    
    return null;
  }

  /**
   * 获取实体数据
   * @param {string} entityId - 实体ID
   * @returns {Promise<Object|null>} 实体数据
   */
  async fetchEntityData(entityId) {
    try {
      const entityUrl = `https://m.wikidata.org/wiki/Special:EntityData/${entityId}.json`;
      const response = await this.makeHttpRequest(entityUrl);
      
      if (response && response.entities && response.entities[entityId]) {
        return response.entities[entityId];
      }
      
      return null;
    } catch (error) {
      log.error(`[${this.name}] 获取实体数据失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 提取结构化情报
   * @param {Object} entityData - 实体数据
   * @param {Object} targetEntity - 目标实体
   * @returns {Object|null} 结构化数据对象
   */
  extractStructuredData(entityData, targetEntity) {
    try {
      const result = {
        entityId: targetEntity.id,
        description: targetEntity.description || '',
        externalIds: {},
        aliases: [],
        wikipediaUrls: {}
      };

      // 提取外部ID
      const claims = entityData.claims || {};
      const externalIdMappings = {
        'P4985': 'tmdb',      // TheMovieDb
        'P345': 'imdb',       // IMDb
        'P2002': 'twitter',   // Twitter
        'P2003': 'instagram', // Instagram
        'P9781': 'fanza',     // Fanza
        'P8720': 'xhamster'   // xHamster
      };

      for (const [property, key] of Object.entries(externalIdMappings)) {
        if (claims[property] && claims[property][0]) {
          const value = claims[property][0].mainsnak?.datavalue?.value;
          if (value) {
            result.externalIds[key] = value;
          }
        }
      }

      // 提取别名
      const aliases = entityData.aliases || {};
      for (const lang of ['zh', 'ja', 'en']) {
        if (aliases[lang]) {
          for (const alias of aliases[lang]) {
            if (alias.value && !result.aliases.includes(alias.value)) {
              result.aliases.push(alias.value);
            }
          }
        }
      }

      // 提取维基链接
      const sitelinks = entityData.sitelinks || {};
      if (sitelinks.zhwiki) {
        result.wikipediaUrls.zh = sitelinks.zhwiki.url;
      }
      if (sitelinks.jawiki) {
        result.wikipediaUrls.ja = sitelinks.jawiki.url;
      }

      return result;

    } catch (error) {
      log.error(`[${this.name}] 提取结构化数据失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 阶段二：Wikipedia 页面深度挖掘
   * @param {Object} wikidataResult - 阶段一的结果
   * @returns {Promise<Object|null>} Wikipedia提取结果
   */
  async stageTwoWikipediaExtraction(wikidataResult) {
    log.debug(`[${this.name}] 阶段二：开始Wikipedia页面深度挖掘...`);

    try {
      // 选择目标URL：优先中文，其次日文
      const targetUrl = this.selectTargetWikipediaUrl(wikidataResult.wikipediaUrls);
      if (!targetUrl) {
        log.warn(`[${this.name}] 没有可用的Wikipedia URL`);
        return null;
      }

      log.debug(`[${this.name}] 目标URL: ${targetUrl}`);

      // 启动爬虫
      const pageContent = await this.fetchWikipediaPage(targetUrl);
      if (!pageContent) {
        return null;
      }

      // 提取半结构化情报
      const extractedData = this.extractWikipediaContent(pageContent, targetUrl);

      return extractedData;

    } catch (error) {
      log.error(`[${this.name}] 阶段二发生错误: ${error.message}`);
      return null;
    }
  }

  /**
   * 选择目标Wikipedia URL
   * @param {Object} wikipediaUrls - Wikipedia URL对象
   * @returns {string|null} 选中的URL
   */
  selectTargetWikipediaUrl(wikipediaUrls) {
    // 优先选择中文维基
    if (wikipediaUrls.zh) {
      // 转换为移动版URL以提升解析成功率
      return wikipediaUrls.zh.replace('zh.wikipedia.org/wiki/', 'zh.m.wikipedia.org/zh-cn/');
    }

    // 其次选择日文维基
    if (wikipediaUrls.ja) {
      return wikipediaUrls.ja.replace('ja.wikipedia.org/wiki/', 'ja.m.wikipedia.org/wiki/');
    }

    return null;
  }

  /**
   * 获取Wikipedia页面内容
   * @param {string} url - Wikipedia页面URL
   * @returns {Promise<string|null>} 页面HTML内容
   */
  async fetchWikipediaPage(url) {
    let browser = null;
    let context = null;
    let page = null;

    try {
      // 启动浏览器
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      context = await browser.newContext({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1920, height: 1080 }
      });

      page = await context.newPage();

      // 访问页面
      await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: this.timeout
      });

      // 等待内容加载
      await page.waitForTimeout(2000);

      // 获取页面内容
      const content = await page.content();

      return content;

    } catch (error) {
      log.error(`[${this.name}] 获取Wikipedia页面失败: ${error.message}`);
      return null;
    } finally {
      // 资源清理
      try {
        if (page) await page.close();
        if (context) await context.close();
        if (browser) await browser.close();
      } catch (cleanupError) {
        log.error(`[${this.name}] 清理浏览器资源失败: ${cleanupError.message}`);
      }
    }
  }

  /**
   * 提取Wikipedia页面内容
   * @param {string} html - 页面HTML
   * @param {string} sourceUrl - 源URL
   * @returns {Object} 提取的内容对象
   */
  extractWikipediaContent(html, sourceUrl) {
    try {
      // 预处理HTML：移除引用标注
      const cleanHtml = html.replace(/<a href="#cite_note.*?<\/a>/g, '');
      const $ = cheerio.load(cleanHtml);

      const result = {
        sourceUrl: sourceUrl,
        stats: {},
        bio: '',
        avatarUrl: ''
      };

      // 提取个人资料表格 (Infobox)
      this.extractInfobox($, result);

      // 提取简介与生平
      this.extractBiography($, result);

      // 提取头像图片
      this.extractAvatar($, result);

      return result;

    } catch (error) {
      log.error(`[${this.name}] 提取Wikipedia内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 提取个人资料表格 (Infobox)
   * @param {Object} $ - Cheerio对象
   * @param {Object} result - 结果对象
   */
  extractInfobox($, result) {
    try {
      const infobox = $('.infobox, .infobox.vcard.plainlist').first();
      if (!infobox.length) {
        log.debug(`[${this.name}] 未找到个人资料表格`);
        return;
      }

      const rows = infobox.find('tr');
      rows.each((i, row) => {
        const $row = $(row);
        const label = $row.find('th, td[scope="row"]').first().text().trim();
        const value = $row.find('td').not('[scope="row"]').first().text().trim();

        if (label && value) {
          // 处理生日信息
          if (label.includes('出生') || label.includes('生年')) {
            const birthMatch = value.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
            if (birthMatch) {
              const year = birthMatch[1];
              const month = birthMatch[2].padStart(2, '0');
              const day = birthMatch[3].padStart(2, '0');
              result.stats.birthday = `${year}-${month}-${day}`;
              result.stats.year = year;
            }
          }

          // 处理身高信息
          if (label.includes('身高')) {
            const heightMatch = value.match(/(\d+)\s*cm/);
            if (heightMatch) {
              result.stats.height = heightMatch[1] + 'cm';
            }
          }

          // 处理三围信息
          if (label.includes('三围') || label.includes('スリーサイズ')) {
            result.stats.measurements = value;
          }

          // 处理出身地信息
          if (label.includes('出身地') || label.includes('出身')) {
            result.stats.birthplace = value;
          }

          // 处理血型信息
          if (label.includes('血型')) {
            result.stats.bloodType = value;
          }

          // 处理活动期间
          if (label.includes('活动期间') || label.includes('活動期間') || label.includes('活動時期')) {
            result.stats.activeYears = value;
          }
        }
      });

      log.debug(`[${this.name}] 提取个人资料完成，字段数: ${Object.keys(result.stats).length}`);

    } catch (error) {
      log.error(`[${this.name}] 提取个人资料失败: ${error.message}`);
    }
  }

  /**
   * 提取简介与生平
   * @param {Object} $ - Cheerio对象
   * @param {Object} result - 结果对象
   */
  extractBiography($, result) {
    try {
      let bio = '';

      // 提取开头简介段落
      const introSection = $('#mf-section-0, .mw-parser-output').first();
      if (introSection.length) {
        const introParagraphs = introSection.find('p').slice(0, 3); // 取前3段
        introParagraphs.each((i, p) => {
          const text = $(p).text().trim();
          if (text && text.length > 10) { // 过滤太短的段落
            bio += text + '\n\n';
          }
        });
      }

      // 提取"人物"章节
      const personSection = this.findSectionByTitle($, ['人物', '人物介绍', '人物紹介']);
      if (personSection) {
        bio += '=== 人物介绍 ===\n';
        bio += this.extractSectionText($, personSection) + '\n\n';
      }

      // 提取"简历"或"生平"章节
      const careerSection = this.findSectionByTitle($, [
        '简历', '簡歷', '个人简历', '個人簡歷', '略歴', '経歴', '来歴',
        '生平', '生平与职业生涯', '略歴・人物'
      ]);
      if (careerSection) {
        bio += '=== 个人经历 ===\n';
        bio += this.extractSectionText($, careerSection) + '\n\n';
      }

      result.bio = bio.trim();
      log.debug(`[${this.name}] 提取简介完成，长度: ${result.bio.length}`);

    } catch (error) {
      log.error(`[${this.name}] 提取简介失败: ${error.message}`);
    }
  }

  /**
   * 提取头像图片
   * @param {Object} $ - Cheerio对象
   * @param {Object} result - 结果对象
   */
  extractAvatar($, result) {
    try {
      // 查找个人资料表格中的图片
      const infoboxImg = $('.infobox img, .infobox-image img').first();
      if (infoboxImg.length) {
        let src = infoboxImg.attr('src');
        if (src) {
          // 处理相对URL
          if (src.startsWith('//')) {
            src = 'https:' + src;
          } else if (src.startsWith('/')) {
            src = 'https://zh.wikipedia.org' + src;
          }
          result.avatarUrl = src;
          log.debug(`[${this.name}] 找到头像图片: ${src}`);
        }
      }
    } catch (error) {
      log.error(`[${this.name}] 提取头像失败: ${error.message}`);
    }
  }

  /**
   * 根据标题查找章节
   * @param {Object} $ - Cheerio对象
   * @param {Array} titles - 标题数组
   * @returns {Object|null} 章节元素
   */
  findSectionByTitle($, titles) {
    for (const title of titles) {
      const section = $(`.toctext:contains("${title}")`).first();
      if (section.length) {
        return section;
      }
    }
    return null;
  }

  /**
   * 提取章节文本内容
   * @param {Object} $ - Cheerio对象
   * @param {Object} section - 章节元素
   * @returns {string} 章节文本
   */
  extractSectionText($, section) {
    try {
      const sectionNumber = section.prev().text();
      if (!sectionNumber) return '';

      const sectionContent = $(`#mf-section-${sectionNumber}`);
      if (!sectionContent.length) return '';

      let text = '';
      sectionContent.find('p, li').each((i, elem) => {
        const elemText = $(elem).text().trim();
        if (elemText) {
          text += elemText + '\n';
        }
      });

      return text.trim();
    } catch (error) {
      log.error(`[${this.name}] 提取章节文本失败: ${error.message}`);
      return '';
    }
  }

  /**
   * 数据整合：将两个阶段的数据整合成最终档案对象
   * @param {Object} wikidataResult - Wikidata结果
   * @param {Object} wikipediaResult - Wikipedia结果
   * @param {string} actorName - 演员姓名
   * @returns {Object} 最终档案对象
   */
  integrateProfileData(wikidataResult, wikipediaResult, actorName) {
    const now = new Date().toISOString();

    return {
      name: actorName,
      aliases: JSON.stringify(wikidataResult.aliases || []),
      avatar_remote_url: wikipediaResult.avatarUrl || null,
      avatar_local_path: null, // 将在服务层处理
      stats: JSON.stringify(wikipediaResult.stats || {}),
      bio: wikipediaResult.bio || '',
      tags: JSON.stringify([wikidataResult.description || '']),
      source_url: wikipediaResult.sourceUrl || '',
      external_ids: JSON.stringify(wikidataResult.externalIds || {}),
      last_scraped_at: now
    };
  }

  /**
   * HTTP请求辅助函数
   * @param {string} url - 请求URL
   * @returns {Promise<Object|null>} 响应数据
   */
  async makeHttpRequest(url) {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        },
        timeout: this.timeout
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      log.error(`[${this.name}] HTTP请求失败: ${url}, 错误: ${error.message}`);
      return null;
    }
  }
}

// 创建单例实例
const actorWikiProvider = new ActorWikiProvider();

/**
 * 导出主要函数
 * @param {string} actorName - 演员姓名
 * @returns {Promise<Object|null>} 演员档案对象
 */
async function scrapeActorProfile(actorName) {
  return await actorWikiProvider.scrapeActorProfile(actorName);
}

module.exports = {
  scrapeActorProfile,
  ActorWikiProvider
};
