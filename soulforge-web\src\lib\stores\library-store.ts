import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { MovieLibrary } from '@/lib/types';
import { apiClient } from '@/lib/api/client';

interface LibraryState {
  // Data
  libraries: MovieLibrary[];
  selectedLibrary: MovieLibrary | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  setLibraries: (libraries: MovieLibrary[]) => void;
  setSelectedLibrary: (library: MovieLibrary | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Library Actions
  addLibrary: (library: MovieLibrary) => void;
  updateLibrary: (id: string, updates: Partial<MovieLibrary>) => void;
  removeLibrary: (id: string) => void;
  
  // Async Actions
  fetchLibraries: () => Promise<void>;
  fetchLibrary: (id: string) => Promise<MovieLibrary | null>;
  saveLibrary: (library: Partial<MovieLibrary>) => Promise<MovieLibrary | null>;
  deleteLibraryAsync: (id: string) => Promise<void>;
  scanLibrary: (id: string) => Promise<void>;
}

export const useLibraryStore = create<LibraryState>()(
  devtools(
    (set, get) => ({
      // Initial State
      libraries: [],
      selectedLibrary: null,
      loading: false,
      error: null,
      
      // Basic Actions
      setLibraries: (libraries) => set({ libraries }),
      setSelectedLibrary: (library) => set({ selectedLibrary: library }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      
      // Library Actions
      addLibrary: (library) =>
        set((state) => ({
          libraries: [...state.libraries, library]
        })),
      
      updateLibrary: (id, updates) =>
        set((state) => ({
          libraries: state.libraries.map((library) =>
            library.id === id ? { ...library, ...updates } : library
          ),
          selectedLibrary: state.selectedLibrary?.id === id 
            ? { ...state.selectedLibrary, ...updates }
            : state.selectedLibrary
        })),
      
      removeLibrary: (id) =>
        set((state) => ({
          libraries: state.libraries.filter((library) => library.id !== id),
          selectedLibrary: state.selectedLibrary?.id === id ? null : state.selectedLibrary
        })),
      
      // Async Actions
      fetchLibraries: async () => {
        set({ loading: true, error: null });
        
        try {
          const response = await apiClient.getLibraries();
          
          if (response.success && response.data) {
            set({ 
              libraries: response.data,
              loading: false 
            });
          } else {
            throw new Error(response.error || 'Failed to fetch libraries');
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false 
          });
        }
      },

      fetchLibrary: async (id: string) => {
        try {
          // For now, find from existing libraries
          const library = get().libraries.find(lib => lib.id === id);
          if (library) {
            return library;
          }
          
          // TODO: Implement API call when library detail endpoint is available
          throw new Error('Library not found');
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return null;
        }
      },

      saveLibrary: async (library: Partial<MovieLibrary>) => {
        try {
          const response = library.id 
            ? await apiClient.updateLibrary(library.id, library)
            : await apiClient.createLibrary(library);
            
          if (response.success && response.data) {
            if (library.id) {
              // Update existing library
              set((state) => ({
                libraries: state.libraries.map((lib) =>
                  lib.id === library.id ? response.data! : lib
                )
              }));
            } else {
              // Add new library
              set((state) => ({
                libraries: [...state.libraries, response.data!]
              }));
            }
            return response.data;
          }
          throw new Error(response.error || 'Failed to save library');
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return null;
        }
      },

      deleteLibraryAsync: async (id: string) => {
        try {
          const response = await apiClient.deleteLibrary(id);
          if (response.success) {
            set((state) => ({
              libraries: state.libraries.filter((library) => library.id !== id),
              selectedLibrary: state.selectedLibrary?.id === id ? null : state.selectedLibrary
            }));
          } else {
            throw new Error(response.error || 'Failed to delete library');
          }
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
      },

      scanLibrary: async (id: string) => {
        const library = get().libraries.find(lib => lib.id === id);
        if (!library) {
          set({ error: 'Library not found' });
          return;
        }

        set({ loading: true, error: null });
        
        try {
          // Parse paths from JSON string
          const paths = typeof library.paths === 'string' 
            ? JSON.parse(library.paths) 
            : library.paths;
            
          const response = await apiClient.scanMovies(paths, id);
          if (response.success) {
            set({ loading: false });
            // Optionally trigger a refresh of movies
          } else {
            throw new Error(response.error || 'Failed to scan library');
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false 
          });
        }
      },
    }),
    {
      name: 'library-store',
    }
  )
);
