/**
 * 页面状态检查工具 - 从collectorService.js提取
 * 
 * 提供统一的页面状态检查和错误处理功能
 */

let log = null;

/**
 * 初始化页面状态检查器
 * @param {Object} logger - 日志对象
 */
function initializePageStatusChecker(logger) {
  log = logger;
}

/**
 * 检查页面状态 - 多级风控机制
 * @param {Object} page - Playwright 页面对象
 * @param {Object} siteProfile - 站点配置
 * @returns {Promise<string>} 页面状态: 'ok', 'download_limit_exceeded', 'human_verification_required', 'server_error'
 */
async function checkPageStatus(page, siteProfile) {
  try {
    // 1. 检查是否需要人工干预 (Cloudflare验证等)
    if (siteProfile.humanVerificationIndicatorSelector) {
      const humanVerification = page.locator(siteProfile.humanVerificationIndicatorSelector);
      if (await humanVerification.count() > 0) {
        return 'human_verification_required';
      }
    }

    // 2. 检查下载限制
    if (siteProfile.downloadLimitIndicatorSelector) {
      const downloadLimit = page.locator(siteProfile.downloadLimitIndicatorSelector);
      if (await downloadLimit.count() > 0) {
        return 'download_limit_exceeded';
      }
    }

    // 3. 检查服务器错误
    if (siteProfile.serverErrorIndicatorSelector) {
      const serverError = page.locator(siteProfile.serverErrorIndicatorSelector);
      if (await serverError.count() > 0) {
        return 'server_error';
      }
    }

    // 4. 检查页面标题是否包含错误信息
    const title = await page.title();
    if (title.includes('错误') || title.includes('Error') || title.includes('403') || title.includes('404')) {
      return 'server_error';
    }

    return 'ok';
  } catch (error) {
    if (log) {
      log.warn(`[PageStatusChecker] 检查页面状态时发生错误: ${error.message}`);
    }
    return 'server_error';
  }
}

/**
 * 处理页面状态错误
 * @param {string} status - 页面状态
 * @param {Object} page - Playwright 页面对象
 * @param {string} context - 上下文信息
 * @param {Function} updateTaskStatus - 状态更新回调
 * @returns {Promise<string>} 处理结果: 'continue', 'stop', 'retry', 'wait_user'
 */
async function handlePageStatusError(status, page, context = '', updateTaskStatus = null) {
  switch (status) {
    case 'download_limit_exceeded':
      if (updateTaskStatus) {
        updateTaskStatus('stopped', `检测到下载次数已达上限，任务自动停止。${context}`);
      }
      if (log) {
        log.info('[PageStatusChecker] 下载超限，任务停止');
      }
      return 'stop';

    case 'human_verification_required':
      if (updateTaskStatus) {
        updateTaskStatus('waiting_user', `检测到需要人机验证，请手动完成验证后继续。${context}`);
      }
      if (log) {
        log.info('[PageStatusChecker] 需要人机验证，等待用户操作');
      }
      return 'wait_user';

    case 'server_error':
      if (log) {
        log.warn(`[PageStatusChecker] 检测到服务器错误，${context}`);
      }
      return 'retry';

    default:
      return 'continue';
  }
}

/**
 * 等待页面状态恢复正常
 * @param {Object} page - Playwright 页面对象
 * @param {Object} siteProfile - 站点配置
 * @param {number} maxWaitTime - 最大等待时间（毫秒）
 * @param {number} checkInterval - 检查间隔（毫秒）
 * @returns {Promise<boolean>} 是否恢复正常
 */
async function waitForPageStatusOk(page, siteProfile, maxWaitTime = 30000, checkInterval = 2000) {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    const status = await checkPageStatus(page, siteProfile);
    if (status === 'ok') {
      return true;
    }

    if (log) {
      log.info(`[PageStatusChecker] 页面状态: ${status}，等待恢复...`);
    }

    await new Promise(resolve => setTimeout(resolve, checkInterval));
  }

  return false;
}

module.exports = {
  initializePageStatusChecker,
  checkPageStatus,
  handlePageStatusError,
  waitForPageStatusOk
};
