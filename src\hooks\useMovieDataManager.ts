
// soul-forge-electron/src/hooks/useMovieDataManager.ts
import { useState, useCallback, useEffect } from 'react';
import { Movie, MovieFetchParams, AdvancedFilterOptions, SortableMovieField, AppSettings, PrivacyModeState } from '../types';

interface UseMovieDataManagementProps {
  initialPageSize: number;
  initialSortField: SortableMovieField;
  initialSortOrder: 'asc' | 'desc';
  privacyState: PrivacyModeState;
  isPrivacyUnlockedThisSession: boolean;
  appSettings: AppSettings;
  activeLibraryId?: string | null; 
}

export function useMovieDataManager({
  initialPageSize,
  initialSortField,
  initialSortOrder,
  privacyState,
  isPrivacyUnlockedThisSession,
  appSettings,
  activeLibraryId, 
}: UseMovieDataManagementProps) {
  const [movies, setMovies] = useState<Movie[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilterOptions | null>(null);
  const [sortField, setSortField] = useState<SortableMovieField>(initialSortField);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(initialSortOrder);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const fetchMovies = useCallback(async (
    pageToFetch = 1,
    currentSearchTerm = searchTerm,
    currentAdvancedFilters = advancedFilters,
    currentSortField = sortField,
    currentSortOrder = sortOrder,
    currentPageSize = pageSize,
    libraryIdForFetch = activeLibraryId
  ) => {
    // 移除了阻止全局查询的逻辑 - 现在支持 libraryId 为 null 的全局影片墙查询

    setIsLoading(true);
    setError(null);
    console.log('[useMovieDataManager] Fetching movies with params:', {
        pageToFetch, currentSearchTerm, currentAdvancedFilters, currentSortField, currentSortOrder, currentPageSize,
        privacyHideTags: (privacyState.isEnabled && !isPrivacyUnlockedThisSession && appSettings.privacyHideTags) || null,
        libraryId: libraryIdForFetch 
    });
    try {
      const params: MovieFetchParams = {
        sortField: currentSortField,
        sortOrder: currentSortOrder,
        filterText: currentSearchTerm,
        advancedFilters: currentAdvancedFilters || undefined,
        pageNumber: pageToFetch,
        pageSize: currentPageSize,
        privacySettings: (privacyState.isEnabled && !isPrivacyUnlockedThisSession && appSettings.privacyHideTags && appSettings.privacyHideTags.length > 0)
          ? { hideTags: appSettings.privacyHideTags }
          : null,
        libraryId: libraryIdForFetch, 
      };
      const result = await window.sfeElectronAPI.getMovies(params);
      if (result.success) {
        setMovies(result.movies);
        setTotalPages(Math.ceil(result.totalMovies / currentPageSize) || 1);
        setCurrentPage(pageToFetch); 
      } else {
        setError(result.error || '获取影片列表失败');
        setMovies([]);
        setTotalPages(1);
      }
    } catch (e: any) {
      setError(`获取影片时发生错误: ${e.message}`);
      setMovies([]);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  }, [searchTerm, advancedFilters, sortField, sortOrder, pageSize, privacyState.isEnabled, isPrivacyUnlockedThisSession, appSettings.privacyHideTags, activeLibraryId]); 


  useEffect(() => {
    // 修改初始加载逻辑：默认执行全局查询（libraryId 为 null）
    // 这样应用启动后就直接显示全局影片墙，而不需要先选择片库
    fetchMovies(1, searchTerm, advancedFilters, sortField, sortOrder, pageSize, activeLibraryId);
  }, [activeLibraryId, searchTerm, advancedFilters, sortField, sortOrder, pageSize, privacyState, isPrivacyUnlockedThisSession, appSettings.privacyHideTags, fetchMovies]);


  const updateSearchTerm = useCallback((newSearchTerm: string) => {
    setSearchTerm(newSearchTerm);
    // Fetch will be triggered by useEffect due to searchTerm change
  }, []);

  const applyAdvancedFilters = useCallback((newFilters: AdvancedFilterOptions | null) => {
    setAdvancedFilters(newFilters);
    // Fetch will be triggered by useEffect due to advancedFilters change
  }, []);

  const updateSort = useCallback((newSortField: SortableMovieField, newSortOrder: 'asc' | 'desc') => {
    setSortField(newSortField);
    setSortOrder(newSortOrder);
    // Fetch will be triggered by useEffect due to sortField/sortOrder change
  }, []);
  
  const updatePageSize = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    // Fetch will be triggered by useEffect due to pageSize change (currentPage will reset to 1 effectively via fetchMovies call)
  }, []);

  const goToPage = useCallback((pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      // Call fetchMovies directly as this is a specific pagination action not covered by the main useEffect dependencies for reset.
      fetchMovies(pageNumber, searchTerm, advancedFilters, sortField, sortOrder, pageSize, activeLibraryId);
    }
  }, [totalPages, searchTerm, advancedFilters, sortField, sortOrder, pageSize, fetchMovies, activeLibraryId]);
  
  const refreshCurrentPage = useCallback(() => {
    fetchMovies(currentPage, searchTerm, advancedFilters, sortField, sortOrder, pageSize, activeLibraryId);
  }, [currentPage, searchTerm, advancedFilters, sortField, sortOrder, pageSize, fetchMovies, activeLibraryId]);

  const updateMovieInList = useCallback((updatedMovie: Movie) => {
    setMovies(prevMovies => prevMovies.map(m => m.db_id === updatedMovie.db_id ? updatedMovie : m));
  }, []);

  const resetFiltersAndFetch = useCallback(() => {
    setSearchTerm('');
    setAdvancedFilters(null);
    // Fetch will be triggered by useEffect due to searchTerm/advancedFilters change
  }, []);

  return {
    movies,
    isLoading,
    error,
    currentPage,
    totalPages,
    searchTerm,
    advancedFilters,
    sortField,
    sortOrder,
    pageSize,
    fetchMovies,
    updateSearchTerm,
    applyAdvancedFilters,
    setAdvancedFilters, 
    setSearchTerm, 
    updateSort,
    updatePageSize,
    goToPage,
    refreshCurrentPage,
    updateMovieInList,
    setMovies, 
    setCurrentPage, 
    resetFiltersAndFetch,
  };
}
