@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义滚动条，营造更“奢华”的感觉 */
/* 适用于 WebKit 内核浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: #2c2c2c; /* 暗色轨道 */
}
::-webkit-scrollbar-thumb {
  background-color: #B8860B; /* 暗金色滑块 */
  border-radius: 4px;
  border: 2px solid #2c2c2c; /* 滑块周围的内边距 */
}
::-webkit-scrollbar-thumb:hover {
  background-color: #daa520; /* 悬停时更亮的金色 */
}

/* 适用于 Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #B8860B #2c2c2c; /* 滑块 轨道 */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  background-color: #1a1a1a; /*整个应用的基础背景色 */
  color: #e0e0e0; /* 默认浅色文本颜色，以在深色背景上获得更好的对比度 */
}

/* V1.32: 全局表单元素样式，增强对比度和一致性 */
@layer components {
  .form-input-app, .form-textarea-app {
    @apply w-full px-3 py-2 bg-[#2d2d2d] border border-[#4f4f4f] rounded-md shadow-sm
           text-neutral-100 placeholder-neutral-500 
           focus:ring-2 focus:ring-[#B8860B] focus:border-[#B8860B] focus:outline-none 
           transition-colors duration-150 ease-in-out disabled:opacity-60 disabled:bg-[#383838] disabled:placeholder-neutral-600 disabled:text-neutral-400;
  }

  .form-select-app {
    @apply w-full pl-3 pr-10 py-2 bg-[#2d2d2d] border border-[#4f4f4f] rounded-md shadow-sm
           text-neutral-100 
           focus:ring-2 focus:ring-[#B8860B] focus:border-[#B8860B] focus:outline-none 
           appearance-none cursor-pointer disabled:opacity-60 disabled:bg-[#383838] disabled:text-neutral-400
           transition-colors duration-150 ease-in-out;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
  }
  
  .form-checkbox-app {
    @apply h-4 w-4 text-[#B8860B] bg-neutral-700 border-neutral-500 rounded 
           focus:ring-2 focus:ring-offset-0 focus:ring-[#B8860B] 
           disabled:opacity-60 cursor-pointer;
  }

  .button-base-app {
    @apply font-semibold py-2 px-4 rounded-md shadow-md transition-all duration-200 ease-in-out
           disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-neutral-600 disabled:text-neutral-400
           focus:outline-none focus:ring-2 focus:ring-opacity-75 focus:ring-offset-2 focus:ring-offset-[#232323];
  }

  .button-primary-app {
    @apply button-base-app bg-[#B8860B] hover:bg-[#daa520] text-white focus:ring-[#B8860B];
  }
  .button-secondary-app {
    @apply button-base-app bg-sky-700 hover:bg-sky-600 text-white focus:ring-sky-600;
  }
  .button-danger-app {
    @apply button-base-app bg-red-700 hover:bg-red-600 text-white focus:ring-red-600;
  }
   .button-success-app {
    @apply button-base-app bg-emerald-700 hover:bg-emerald-600 text-white focus:ring-emerald-600;
  }
   .button-warning-app {
    @apply button-base-app bg-orange-600 hover:bg-orange-500 text-white focus:ring-orange-500;
  }
   .button-neutral-app {
    @apply button-base-app bg-neutral-700 hover:bg-neutral-600 text-neutral-100 focus:ring-neutral-500;
   }

  /* Header navigation buttons */
  .header-nav-button-app {
    @apply px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150 ease-in-out flex items-center;
    /* Base: text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400 */
    /* Active: text-amber-400 bg-amber-900/30 (or other distinct style) */
  }

  .icon-button-app {
    @apply p-1.5 sm:p-2 rounded-md text-neutral-300 hover:bg-neutral-700/60 hover:text-white focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 transition-all;
  }

  /* Pagination buttons */
  .pagination-button {
    @apply p-2 rounded-md text-neutral-300 hover:bg-neutral-700/60 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
  }

  /* View mode and sort control bar buttons */
  .control-bar-button {
    @apply p-1.5 rounded text-neutral-400 hover:bg-neutral-700 hover:text-neutral-100 transition-colors;
  }
  .control-bar-button-active {
    @apply bg-amber-500 text-black;
  }


  /* 设置页面特定分组样式 (旧，如果还在用，则保留) */
  .settings-group {
    @apply bg-[#272727] border border-[#4a4a4a] rounded-lg shadow-md;
  }
  .settings-group summary {
    @apply py-3 px-4 text-base font-semibold text-amber-400 cursor-pointer list-none 
           flex justify-between items-center hover:bg-[#2f2f2f]/70 rounded-t-lg transition-colors;
  }
  .settings-group[open] summary {
    @apply bg-[#2a2a2a] border-b border-[#4a4a4a];
  }
  .settings-group summary::-webkit-details-marker { display: none; }
  .settings-group summary::after { 
    content: '▼'; font-size: 0.8em; transition: transform 0.2s; color: #b8860b;
  }
  .settings-group[open] summary::after { transform: rotate(-180deg); }
  
  /* 设置页面选项卡内容区域的统一样式 */
  .settings-group-content { 
    @apply p-0 space-y-4; /* 移除内边距，因为外部div现在有p-6 */
  }
  .settings-tab-content-item { /* 应用于每个设置项的容器div */
     @apply p-3 mb-3 bg-[#282828]/40 border border-transparent rounded-md; /* 为每个设置项增加轻微背景和圆角 */
  }
  .settings-tab-content-item:last-child {
    @apply mb-0;
  }

  .settings-label {
    @apply block text-sm font-medium text-neutral-200 mb-1.5;
  }
  .settings-description {
    @apply text-xs text-neutral-400 mt-1;
  }

  /* 设置中特定可滚动容器的自定义滚动条 */
  .settings-scroll-container::-webkit-scrollbar { width: 6px; height: 6px; }
  .settings-scroll-container::-webkit-scrollbar-track { background: #232323; border-radius:3px; }
  .settings-scroll-container::-webkit-scrollbar-thumb { background-color: #886f36; border-radius: 3px; border: 1px solid #232323; }
  .settings-scroll-container { scrollbar-width: thin; scrollbar-color: #886f36 #232323; }

  /* V1.33: 林珞召唤阵模态框内组件的特定样式 */
  .form-textarea-linluo {
    @apply w-full p-3 text-sm bg-[#2a2229]/90 border border-pink-500/50 rounded-lg 
           focus:ring-2 focus:ring-pink-400 focus:border-pink-400 
           placeholder-neutral-400 resize-none shadow-inner text-neutral-100
           transition-all duration-150 ease-in-out;
  }
  .button-primary-linluo {
    @apply w-full py-2.5 text-sm font-semibold 
           bg-gradient-to-r from-pink-600 to-purple-600 
           hover:from-pink-500 hover:to-purple-500 
           text-white rounded-lg shadow-md hover:shadow-lg 
           transition-all duration-300 transform hover:scale-105 active:scale-95
           focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-opacity-75;
  }

  /* LinLuo Summoning Modal Chat Styles */
  .chat-message-container {
    @apply flex flex-col space-y-4 p-6; /* Increased padding */
  }
  .chat-message {
    @apply py-3.5 px-5 rounded-xl max-w-[85%] text-sm leading-relaxed shadow-lg; /* Increased padding, subtle shadow */
  }
  .user-message {
    @apply bg-pink-600 text-white self-end rounded-br-none; /* User messages: pink, right-aligned */
  }
  .ai-message {
    @apply bg-purple-700 text-white self-start rounded-bl-none; /* AI messages: purple, left-aligned */
  }
  .chat-message.error-message {
      @apply bg-red-700 text-white self-start rounded-bl-none; /* Error messages: red, left-aligned */
  }
  .chat-message.loading-message {
      @apply bg-neutral-600 text-neutral-300 self-start rounded-bl-none italic; /* Loading messages: neutral, left-aligned, italic */
  }

  /* For Waterfall/Masonry view */
  .masonry-grid { /* DEPRECATED - use masonry-wall */
    column-gap: 1rem; /* Corresponds to gap-4 */
  }
  .break-inside-avoid { /* DEPRECATED - use built-in break-inside-avoid-column if needed */
    break-inside: avoid;
  }
  
  .grid-cols-dynamic-card { /* This class is for general card grid, not specific to virtualization */
    @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6;
  }

}

/* 动画 */
@keyframes pulse-slow {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.05); opacity: 0.7; }
}
.animate-pulse-slow {
  animation: pulse-slow 4s infinite ease-in-out;
}

/* 针对特定区域的更细致的滚动条 (例如林珞模态框内容区) */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent; /* 使轨道透明，这样背景色可以透出来 */
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(236, 72, 153, 0.5); /* Tailwind pink-500 with 50% opacity */
  border-radius: 3px;
}
.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(236, 72, 153, 0.7); /* Brighter pink on hover */
}

/* Firefox scrollbar styling for .scrollbar-thin */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(236, 72, 153, 0.5) transparent; /* thumb track */
}

/* 确保SVG图标可以通过 text-color 控制填充色 */
.fill-current {
  fill: currentColor;
}

/* Masonry layout for Waterfall View */
.masonry-wall {
  column-gap: 1rem; /* Corresponds to Tailwind's gap-4 */
}
.masonry-wall > * {
  break-inside: avoid-column;
  margin-bottom: 1rem; /* Corresponds to Tailwind's mb-4, creating vertical gap */
}

/* Responsive column counts for masonry-wall are now handled by Tailwind classes in MovieGridDisplay.tsx */
/* Media queries previously here are removed */


/* Badge style for version/CD count etc on cards, used in MovieCard.tsx */
.badge-app {
    @apply text-xs font-bold px-2 py-0.5 rounded shadow-lg flex items-center;
}

/* Utility for text shadow on movie cards */
@layer utilities {
  .text-shadow-md {
    text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.85), 0px 1px 1px rgba(0,0,0,0.75);
  }
}

/* 分页按钮样式 */
@layer components {
  .pagination-button {
    @apply px-3 py-1.5 mx-0.5 text-sm rounded-md border border-gray-600
           text-gray-300 bg-gray-700 hover:bg-gray-600 hover:text-white
           disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-700 disabled:hover:text-gray-300
           transition-colors duration-150 ease-in-out;
  }
}