import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const library = await prisma.movieLibrary.findUnique({
      where: { id: params.id },
      include: {
        movieLinks: {
          include: {
            movie: true,
          },
        },
      },
    });

    if (!library) {
      return NextResponse.json(
        {
          success: false,
          error: 'Library not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: library,
    });
  } catch (error) {
    console.error('Error fetching library:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch library',
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, paths } = body;

    const library = await prisma.movieLibrary.update({
      where: { id: params.id },
      data: {
        name,
        paths: typeof paths === 'string' ? paths : JSON.stringify(paths),
      },
    });

    return NextResponse.json({
      success: true,
      data: library,
    });
  } catch (error) {
    console.error('Error updating library:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update library',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.movieLibrary.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: 'Library deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting library:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete library',
      },
      { status: 500 }
    );
  }
}
