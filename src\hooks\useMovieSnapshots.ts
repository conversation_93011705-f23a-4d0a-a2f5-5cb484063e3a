
// soul-forge-electron/src/hooks/useMovieSnapshots.ts
import { useState, useCallback } from 'react';
import { SnapshotInfo, AppSettings, Movie } from '../types';

export function useMovieSnapshots(appSettings: AppSettings) {
  const [movieSnapshots, setMovieSnapshots] = useState<SnapshotInfo[]>([]);
  const [isLoadingSnapshots, setIsLoadingSnapshots] = useState(false);

  const fetchSnapshotsForMovie = useCallback(async (movie: Movie | null, forceGenerate: boolean = false) => {
    // 只在强制生成时输出日志，避免刷屏
    if (forceGenerate) {
      console.log("🎬 [useMovieSnapshots] fetchSnapshotsForMovie 被调用", {
        movieTitle: movie?.title,
        movieDbId: movie?.db_id,
        filePath: movie?.filePath,
        forceGenerate
      });
    }

    if (!movie || movie.db_id === undefined) {
      if (forceGenerate) {
        console.warn("🎬 [useMovieSnapshots] 影片信息无效，清空快照列表");
      }
      setMovieSnapshots([]);
      return;
    }
    const movieDbId = movie.db_id;

    setIsLoadingSnapshots(true);
    try {
      if (forceGenerate) {
        console.log("🎬 [useMovieSnapshots] 开始强制生成快照流程");
        const params = {
          videoDbId: movieDbId,
          videoFilePath: movie.filePath,
          snapshotQuality: appSettings.snapshotQuality || 'hd_640p',
        };

        console.log("🎬 [useMovieSnapshots] 快照生成参数:", params);

        if (!params.videoFilePath) {
          console.error("🎬 [useMovieSnapshots] ❌ 视频文件路径为空，无法生成快照", { movieDbId, movieTitle: movie.title });
          alert(`无法生成快照：视频文件路径为空\n影片：${movie.title}\nID：${movieDbId}`);
          setIsLoadingSnapshots(false);
          return;
        }

        if (window.sfeElectronAPI && params.videoFilePath) {
          console.log("🎬 [useMovieSnapshots] ✅ 开始调用主进程生成快照...");
          const generateResult = await window.sfeElectronAPI.generateThumbnails(params);
          console.log("🎬 [useMovieSnapshots] 快照生成结果:", generateResult);

          if (!generateResult.success) {
            console.error("🎬 [useMovieSnapshots] ❌ 快照生成失败:", generateResult.error);
            alert(`快照生成失败: ${generateResult.error}`);
            setIsLoadingSnapshots(false);
            return;
          } else {
            console.log("🎬 [useMovieSnapshots] ✅ 快照生成成功！");
          }
        } else {
          console.error("🎬 [useMovieSnapshots] ❌ sfeElectronAPI 不可用或文件路径为空");
        }
      }
      if (window.sfeElectronAPI) {
        const result = await window.sfeElectronAPI.getExistingSnapshots({ movieDbId });
        if (result.success && result.snapshots) {
          setMovieSnapshots(result.snapshots);
        } else {
          if (forceGenerate) {
            console.error("Failed to fetch snapshots:", result.error);
          }
          setMovieSnapshots([]);
        }
      }
    } catch (e: any) {
      console.error("Error in fetchSnapshotsForMovie:", e);
      setMovieSnapshots([]);
    } finally {
      setIsLoadingSnapshots(false);
    }
  }, [appSettings.snapshotQuality]);

  const clearSnapshots = useCallback(() => {
    setMovieSnapshots([]);
  }, []);

  return {
    movieSnapshots,
    isLoadingSnapshots,
    fetchSnapshotsForMovie,
    clearSnapshots,
  };
}
