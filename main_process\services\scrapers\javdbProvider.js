// main_process/services/scrapers/javdbProvider.js
const { chromium } = require('playwright');
const cheerio = require('cheerio');
const log = require('electron-log');
const settingsService = require('../settingsService');

const PROVIDER_NAME = 'javdb';
const PROVIDER_VERSION = '1.1.0'; // 升级版本，使用 CDP 连接方式

/**
 * JavDB 刮削器 Provider
 * 专门负责从 JavDB 网站刮削影片的详细元数据，包括磁力链接、用户评分等
 */

/**
 * 【优化】查找详情页 URL - 基于对标软件 get_real_url 逻辑优化
 * @param {Object} page - Playwright 页面对象
 * @param {string} nfoId - 影片 ID
 * @returns {Promise<string|null>} 详情页 URL
 */
async function findDetailPageUrl(page, nfoId) {
    try {
        log.info(`[JavDB Provider] 开始查找详情页 URL: ${nfoId}`);

        // 获取搜索结果页面的 HTML
        const htmlContent = await page.content();
        const $ = cheerio.load(htmlContent);

        // 【优化】使用对标软件的精确选择器
        const resultItems = [];
        $('a.box').each((index, element) => {
            const $element = $(element);
            const href = $element.attr('href') || '';
            const title = $element.find('div.video-title strong').text().trim();
            const meta = $element.find('div.meta').text().trim();

            if (href && title) {
                resultItems.push({ href, title, meta });
            }
        });

        log.info(`[JavDB Provider] 找到 ${resultItems.length} 个搜索结果`);

        // 【优化】基于对标软件的日期格式处理
        let processedNumber = nfoId;
        if (nfoId.includes('.')) {
            const oldDateMatch = nfoId.match(/\D+(\d{2}\.\d{2}\.\d{2})$/);
            if (oldDateMatch) {
                const oldDate = oldDateMatch[1];
                const newDate = '20' + oldDate;
                processedNumber = nfoId.replace(oldDate, newDate);
            }
        }

        // 【优化】基于对标软件的精确匹配逻辑
        // 先从所有结果里精确匹配，避免gs067模糊匹配问题
        for (const item of resultItems) {
            if (processedNumber.toUpperCase() === item.title.toUpperCase()) {
                log.info(`[JavDB Provider] 完全精确匹配找到: ${item.href}`);
                return item.href;
            }
        }

        // 然后进行包含匹配
        for (const item of resultItems) {
            if (item.title.toUpperCase().includes(processedNumber.toUpperCase())) {
                log.info(`[JavDB Provider] 包含匹配找到: ${item.href}`);
                return item.href;
            }
        }

        // 【优化】基于对标软件的模糊匹配逻辑
        // 再从所有结果模糊匹配
        const normalizedNumber = processedNumber.toUpperCase().replace(/[.\-\s]/g, '');
        for (const item of resultItems) {
            const normalizedContent = (item.title + item.meta).toUpperCase().replace(/[.\-\s]/g, '');
            if (normalizedContent.includes(normalizedNumber)) {
                log.info(`[JavDB Provider] 模糊匹配找到: ${item.href}`);
                return item.href;
            }
        }

        log.warn(`[JavDB Provider] 未找到匹配的详情页 URL`);
        return null;

    } catch (error) {
        log.error(`[JavDB Provider] 查找详情页 URL 失败: ${error.message}`);
        return null;
    }
}

/**
 * 【优化】获取标题 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @param {string} nfoId - 番号，用于清理标题
 * @returns {string} 标题
 */
function getTitle($, nfoId) {
    // 【优化】使用对标软件的精确选择器
    const title = $('h2.title.is-4 strong.current-title').text().trim();
    const originaltitle = $('h2.title.is-4 span.origin-title').text().trim();

    // 【优化】基于对标软件的标题清理逻辑
    let cleanTitle = title;

    // 移除番号前缀（如果存在）
    if (nfoId && cleanTitle.toUpperCase().startsWith(nfoId.toUpperCase())) {
        cleanTitle = cleanTitle.substring(nfoId.length).trim();
    }

    // 清理特殊标记 - 参考对标软件的清理规则
    cleanTitle = cleanTitle
        .replace(/中文字幕/g, '')
        .replace(/無碼/g, '')
        .replace(/无码/g, '')
        .replace(/\\n/g, '')
        .replace(/_/g, '-')
        .replace(/--/g, '-')
        .replace(/^\s*-\s*/, '')  // 移除开头的短横线
        .trim();

    return cleanTitle || originaltitle || '';
}

/**
 * 【优化】获取演员列表 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 演员数组
 */
function getActors($) {
    const actors = [];

    // 【优化】使用对标软件的精确选择器
    // 对标软件XPath: //strong[contains(text(),"演員")]/../span/a/text()
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();

        // 【优化】支持更多语言变体
        if (strongText.includes('演員:') ||
            strongText.includes('Actor(s):') ||
            strongText.includes('演员:') ||
            strongText.includes('出演者:')) {

            $element.find('.value a').each((i, actorElement) => {
                const $actor = $(actorElement);
                const actorName = $actor.text().trim();
                const actorUrl = $actor.attr('href');

                if (actorName && actorName !== '♀') {  // 过滤无效演员名
                    actors.push({
                        name: actorName,
                        url: actorUrl ? (actorUrl.startsWith('http') ? actorUrl : `https://javdb.com${actorUrl}`) : ''
                    });
                }
            });
        }
    });

    // 【优化】去重处理
    const uniqueActors = [];
    const seenNames = new Set();

    for (const actor of actors) {
        if (!seenNames.has(actor.name)) {
            seenNames.add(actor.name);
            uniqueActors.push(actor);
        }
    }

    return uniqueActors;
}

/**
 * 【优化】获取封面图片 URL - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 封面 URL
 */
function getCover($) {
    // 【优化】使用对标软件的精确选择器
    // 对标软件XPath: //img[@class="video-cover"]/@src
    let coverUrl = $('img.video-cover').attr('src') || '';

    // 【优化】处理相对路径和协议
    if (coverUrl) {
        if (coverUrl.startsWith('//')) {
            coverUrl = 'https:' + coverUrl;
        } else if (coverUrl.startsWith('/')) {
            coverUrl = 'https://javdb.com' + coverUrl;
        }
    }

    return coverUrl;
}

/**
 * 获取预览图片列表
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 预览图片 URL 数组
 */
function getPreviewImages($) {
    const previewImages = [];
    
    $('.tile-images.preview-images .tile-item').each((index, element) => {
        const href = $(element).attr('href');
        if (href) {
            previewImages.push(href);
        }
    });
    
    return previewImages;
}

/**
 * 获取发行日期
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 发行日期
 */
function getReleaseDate($) {
    let releaseDate = '';
    
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();
        
        if (strongText.includes('日期:') || strongText.includes('Released Date:')) {
            releaseDate = $element.find('.value').text().trim();
        }
    });
    
    return releaseDate;
}

/**
 * 获取用户评分
 * @param {Object} $ - Cheerio 对象
 * @returns {string} 评分
 */
function getRating($) {
    const ratingText = $('.score-stars').parent().text();
    const ratingMatch = ratingText.match(/(\d{1}\..+)分/);
    return ratingMatch ? ratingMatch[1] : '';
}

/**
 * 获取其他基础信息的辅助函数
 */
function getStudio($) {
    let studio = '';
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();
        
        if (strongText.includes('片商:') || strongText.includes('Maker:')) {
            studio = $element.find('.value a').text().trim();
        }
    });
    return studio;
}

function getSeries($) {
    let series = '';
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();
        
        if (strongText.includes('系列:') || strongText.includes('Series:')) {
            series = $element.find('.value a').text().trim();
        }
    });
    return series;
}

function getDirector($) {
    let director = '';
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();
        
        if (strongText.includes('導演:') || strongText.includes('Director:')) {
            director = $element.find('.value a').text().trim();
        }
    });
    return director;
}

function getRuntime($) {
    let runtime = '';
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();
        
        if (strongText.includes('時長') || strongText.includes('Duration:')) {
            runtime = $element.find('.value').text().trim()
                .replace(' 分鍾', '')
                .replace(' minute(s)', '');
        }
    });
    return runtime;
}

/**
 * 【优化】获取标签 - 基于对标软件优化
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 标签数组
 */
function getTags($) {
    const tags = [];

    // 【优化】使用对标软件的精确选择器
    // 对标软件XPath: //strong[contains(text(),"類別")]/../span/a/text()
    $('.panel-block').each((index, element) => {
        const $element = $(element);
        const strongText = $element.find('strong').text();

        // 【优化】支持更多语言变体
        if (strongText.includes('類別:') ||
            strongText.includes('Tags:') ||
            strongText.includes('类别:') ||
            strongText.includes('ジャンル:')) {

            $element.find('.value a').each((i, tagElement) => {
                const $tag = $(tagElement);
                const tagName = $tag.text().trim();
                const tagUrl = $tag.attr('href');

                if (tagName) {
                    tags.push({
                        name: tagName,
                        url: tagUrl ? (tagUrl.startsWith('http') ? tagUrl : `https://javdb.com${tagUrl}`) : ''
                    });
                }
            });
        }
    });

    // 【优化】去重处理
    const uniqueTags = [];
    const seenNames = new Set();

    for (const tag of tags) {
        if (!seenNames.has(tag.name)) {
            seenNames.add(tag.name);
            uniqueTags.push(tag);
        }
    }

    return uniqueTags;
}

/**
 * 【优化】获取磁力链接 - 基于对标软件优化
 * @param {Object} page - Playwright 页面对象
 * @returns {Promise<Array>} 磁力链接数组
 */
async function getMagnetLinksWithSafeClick(page) {
    try {
        log.info(`[JavDB Provider] 开始获取磁力链接...`);

        // 【优化】使用对标软件的精确选择器
        const buttonSelectors = [
            'a:has-text("磁力鏈接")',
            'a:has-text("Magnet Links")',
            'a:has-text("磁力链接")',
            'a.button.is-info',  // 对标软件使用的选择器
            '.magnet-button',
            '[data-target*="magnet"]',
            'button:has-text("磁力")',
            'a[href*="magnet"]'
        ];

        let magnetButton = null;
        for (const selector of buttonSelectors) {
            const button = page.locator(selector).first();
            if (await button.count() > 0) {
                magnetButton = button;
                log.info(`[JavDB Provider] 找到磁力链接按钮: ${selector}`);
                break;
            }
        }

        if (!magnetButton) {
            log.warn(`[JavDB Provider] 未找到磁力链接按钮，尝试直接解析页面`);
            return await extractMagnetLinksFromPage(page);
        }

        // 【优化】使用对标软件的安全点击方式
        log.info(`[JavDB Provider] 准备点击磁力链接按钮...`);

        try {
            // 确保按钮可见
            await magnetButton.scrollIntoViewIfNeeded();
            await page.waitForTimeout(1000); // 等待滚动完成

            // 模拟真实用户点击
            await magnetButton.click({ force: true });
            log.info(`[JavDB Provider] 磁力链接按钮已点击`);

            // 【优化】等待内容加载，使用对标软件的等待策略
            await Promise.race([
                page.waitForSelector('.magnet-table, .table, .magnet-links', { timeout: 10000 }),
                page.waitForSelector('a[href^="magnet:"]', { timeout: 10000 }),
                page.waitForTimeout(5000) // 最多等待5秒
            ]);

            log.info(`[JavDB Provider] 磁力链接内容已加载`);

        } catch (clickError) {
            log.warn(`[JavDB Provider] 点击磁力链接按钮失败: ${clickError.message}`);
            // 即使点击失败，也尝试解析页面
        }

        // 等待一段时间确保内容完全加载
        await page.waitForTimeout(2000);

        // 解析磁力链接
        return await extractMagnetLinksFromPage(page);

    } catch (error) {
        log.error(`[JavDB Provider] 获取磁力链接失败: ${error.message}`);
        return [];
    }
}

/**
 * 从页面中提取磁力链接
 * @param {Object} page - Playwright 页面对象
 * @returns {Promise<Array>} 磁力链接数组
 */
async function extractMagnetLinksFromPage(page) {
    try {
        // 获取页面内容
        const htmlContent = await page.content();
        const $ = cheerio.load(htmlContent);

        const magnetLinks = [];

        // 多种选择器策略解析磁力链接
        const tableSelectors = [
            '.table tbody tr',
            '.magnet-table tbody tr',
            '.magnet-links tr',
            'tr:has(a[href^="magnet:"])',
            '.magnet-item'
        ];

        let foundLinks = false;

        for (const selector of tableSelectors) {
            $(selector).each((index, element) => {
                const $row = $(element);

                // 提取磁力链接
                const magnetLink = $row.find('a[href^="magnet:"]').attr('href');

                if (magnetLink) {
                    foundLinks = true;

                    // 提取文件大小
                    const sizeText = $row.find('td').eq(1).text().trim() ||
                                   $row.find('.size').text().trim() ||
                                   $row.text().match(/(\d+\.?\d*\s*[KMGT]B)/i)?.[1] || '';

                    // 检查是否包含字幕
                    const rowText = $row.text().toLowerCase();
                    const hasSubtitles = rowText.includes('字幕') ||
                                       rowText.includes('sub') ||
                                       rowText.includes('中文') ||
                                       rowText.includes('chinese');

                    // 提取文件名
                    const fileName = $row.find('a[href^="magnet:"]').text().trim() ||
                                   $row.find('.name').text().trim() ||
                                   magnetLink.match(/dn=([^&]+)/)?.[1] || '';

                    magnetLinks.push({
                        link: magnetLink,
                        size: sizeText,
                        has_subtitles: hasSubtitles,
                        name: decodeURIComponent(fileName)
                    });

                    log.info(`[JavDB Provider] 提取磁力链接: ${fileName || '未知文件'} (${sizeText})`);
                }
            });

            if (foundLinks) break; // 如果找到了链接，就不再尝试其他选择器
        }

        // 如果表格方式没找到，尝试直接搜索页面中的磁力链接
        if (magnetLinks.length === 0) {
            const allMagnetLinks = $('a[href^="magnet:"]');
            allMagnetLinks.each((index, element) => {
                const $link = $(element);
                const magnetLink = $link.attr('href');
                const fileName = $link.text().trim() || `磁力链接${index + 1}`;

                magnetLinks.push({
                    link: magnetLink,
                    size: '',
                    has_subtitles: false,
                    name: fileName
                });
            });
        }

        log.info(`[JavDB Provider] 成功提取 ${magnetLinks.length} 个磁力链接`);
        return magnetLinks;

    } catch (error) {
        log.error(`[JavDB Provider] 解析磁力链接失败: ${error.message}`);
        return [];
    }
}

/**
 * 检查用户是否已登录 JavDB
 * @param {Object} page - Playwright 页面对象
 * @returns {Promise<boolean>} 是否已登录
 */
async function checkUserLogin(page) {
    try {
        // 检查是否存在登录相关的元素
        const loginElements = await page.locator('a[href*="login"], .login-btn, .user-menu, .avatar').count();
        const loggedInElements = await page.locator('.user-menu, .avatar, a[href*="logout"]').count();

        return loggedInElements > 0;
    } catch (error) {
        log.warn(`[JavDB Provider] 检查登录状态失败: ${error.message}`);
        return false;
    }
}

/**
 * 提醒用户登录 JavDB
 * @param {string} baseUrl - JavDB 基础 URL
 */
function promptUserLogin(baseUrl) {
    const message = `
🔐 JavDB 访问提醒

由于 JavDB 网站的风控机制较为严格，建议您：

1. 手动启动 Chrome 浏览器（使用调试模式）：
   chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\\temp\\chrome-debug"

2. 在浏览器中访问 ${baseUrl} 并完成登录

3. 确保能正常访问内容后，再使用刮削功能

这样可以避免被反爬机制拦截，提高刮削成功率。
    `;

    log.warn(`[JavDB Provider] ${message}`);
}

/**
 * 连接到用户的 Chrome 浏览器
 * @returns {Promise<{browser: Object, page: Object}>} 浏览器和页面对象
 */
async function connectToUserChrome() {
    try {
        log.info(`[JavDB Provider] 尝试连接到用户的 Chrome 浏览器 (端口: 9222)...`);

        // 连接到用户手动打开的Chrome
        const browser = await chromium.connectOverCDP('http://localhost:9222');
        const context = browser.contexts()[0]; // 获取默认的浏览器上下文

        log.info(`[JavDB Provider] 已连接到 Chrome 实例`);

        // 查找或创建 JavDB 页面
        const pages = context.pages();
        let page = pages.find(p => p.url().includes('javdb.com'));

        if (!page) {
            // 如果没有找到 JavDB 页面，创建新页面
            page = await context.newPage();
            log.info(`[JavDB Provider] 创建新页面用于 JavDB 访问`);
        } else {
            log.info(`[JavDB Provider] 找到现有 JavDB 页面，接管进行刮削`);
        }

        return { browser, page };

    } catch (error) {
        log.error(`[JavDB Provider] 无法连接到 Chrome 实例: ${error.message}`);
        throw new Error(`请先启动 Chrome 浏览器并使用 --remote-debugging-port=9222 参数。\n详细说明：${error.message}`);
    }
}

/**
 * 从 JavDB 刮削数据 - 主函数（使用 CDP 连接方式）
 * @param {string} nfoId
 * @returns {Promise<Object>} 刮削到的原始数据
 */
async function scrape(nfoId) {
    const settings = settingsService.getSettings();
    const baseUrl = settings.javdbBaseUrl || 'https://javdb.com';

    let browser = null;
    let page = null;

    try {
        log.info(`[JavDB Provider] 开始刮削 ${nfoId}...`);

        // 1. 连接到用户的 Chrome 浏览器
        const connection = await connectToUserChrome();
        browser = connection.browser;
        page = connection.page;

        // 2. 检查用户登录状态
        const currentUrl = page.url();
        if (!currentUrl.includes('javdb.com')) {
            // 如果当前页面不是 JavDB，先导航到首页
            log.info(`[JavDB Provider] 导航到 JavDB 首页...`);
            await page.goto(baseUrl, { waitUntil: 'domcontentloaded' });
            await page.waitForTimeout(2000);
        }

        // 检查登录状态
        const isLoggedIn = await checkUserLogin(page);
        if (!isLoggedIn) {
            promptUserLogin(baseUrl);
            log.warn(`[JavDB Provider] 建议用户先登录 JavDB 以提高刮削成功率`);
        }

        // 3. 搜索阶段
        const searchUrl = `${baseUrl}/search?q=${encodeURIComponent(nfoId.trim())}&locale=zh`;
        log.info(`[JavDB Provider] 搜索 URL: ${searchUrl}`);

        await page.goto(searchUrl, { waitUntil: 'domcontentloaded' });
        await page.waitForTimeout(2000); // 等待页面稳定

        // 检查是否被拦截
        const pageContent = await page.content();
        if (pageContent.includes('ray-id') || pageContent.includes('Cloudflare')) {
            throw new Error('被 Cloudflare 5 秒盾拦截！建议手动在浏览器中完成验证后重试。');
        }

        if (pageContent.includes('The owner of this website has banned your access')) {
            throw new Error('由于请求过多，JavDB 网站暂时禁止了当前 IP 的访问！建议稍后重试或更换网络。');
        }

        if (pageContent.includes('Due to copyright restrictions')) {
            throw new Error('由于版权限制，JavDB 网站禁止了日本 IP 的访问！请更换日本以外代理。');
        }

        // 4. 查找详情页 URL
        const detailUrl = await findDetailPageUrl(page, nfoId);
        if (!detailUrl) {
            throw new Error('搜索结果: 未匹配到番号！可能该番号在 JavDB 中不存在。');
        }

        // 5. 访问详情页
        const fullDetailUrl = detailUrl.startsWith('http') ? detailUrl : `${baseUrl}${detailUrl}?locale=zh`;
        log.info(`[JavDB Provider] 详情页 URL: ${fullDetailUrl}`);

        await page.goto(fullDetailUrl, { waitUntil: 'domcontentloaded' });
        await page.waitForTimeout(2000); // 等待页面稳定

        // 检查详情页访问权限
        const detailContent = await page.content();
        if (detailContent.includes('/plans/sfpay_order') || detailContent.includes('payment-methods')) {
            throw new Error('需要 VIP 权限才能访问此内容！建议升级 VIP 或尝试其他番号。');
        }

        if (detailContent.includes('/password_resets')) {
            throw new Error('此內容需要登入才能查看！请在浏览器中手动登录 JavDB 后重试。');
        }

        // 【现代化改造】深度数据采集 - 应采尽采模式
        log.info(`[JavDB Provider] 🚀 开始深度数据采集 (应采尽采模式)...`);

        const htmlContent = await page.content();
        const $ = cheerio.load(htmlContent);

        // === 基础信息采集 ===
        const title = getTitle($, nfoId);
        if (!title) {
            throw new Error('数据获取失败: 未获取到标题！页面可能加载不完整。');
        }

        const actors = getActors($);
        const coverUrl = getCover($);
        const previewImages = getPreviewImages($);
        const releaseDate = getReleaseDate($);
        const rating = getRating($);
        const studio = getStudio($);
        const series = getSeries($);
        const director = getDirector($);
        const runtime = getRuntime($);
        const tags = getTags($);

        // === 新增：用户评论采集 ===
        const userComments = await getUserComments($, page);

        // === 新增：完整标签体系采集 ===
        const allTags = getAllTags($);
        const tagCategories = getTagCategories($);

        // === 新增：社区数据采集 ===
        const communityData = getCommunityData($);
        const userStats = getUserStats($);

        // === 新增：完整磁力链接采集 ===
        const magnetLinks = await getAllMagnetLinksDetailed(page);

        // === 新增：相关影片采集 ===
        const relatedMovies = getRelatedMovies($, baseUrl);

        // === 新增：技术信息采集 ===
        const technicalInfo = getTechnicalInfo($);
        const metaInfo = getMetaInfo($);

        // 【优化】组装兼容对标软件的返回数据 - 应采尽采
        const scrapedData = {
            // === 基础信息 ===
            nfoId: nfoId,
            number: nfoId,  // 对标软件字段
            title: title,
            originaltitle: title,  // 对标软件字段
            plot: '', // JavDB 通常没有详细剧情
            outline: '',  // 对标软件字段
            originalplot: '',  // 对标软件字段
            releaseDate: releaseDate,
            release: releaseDate,  // 对标软件字段
            year: releaseDate ? releaseDate.substring(0, 4) : null,  // 对标软件字段
            runtime: runtime,

            // === 人员信息 ===
            actors: actors,
            actor: Array.isArray(actors) ? actors.map(a => typeof a === 'string' ? a : a.name).join(',') : '',  // 对标软件字段
            actor_photo: getActorPhoto(actors),  // 对标软件字段
            director: director,
            studio: studio,
            publisher: studio,  // 对标软件字段
            series: series,

            // === 标签信息 (兼容旧格式 + 新详细格式) ===
            tags: tags, // 保持向后兼容
            tag: Array.isArray(tags) ? tags.map(t => typeof t === 'string' ? t : t.name).join(',') : '',  // 对标软件字段
            allTags: allTags, // 新增完整标签
            tagCategories: tagCategories, // 新增标签分类

            // === 图像信息 ===
            coverUrl: coverUrl,
            thumb: coverUrl,  // 对标软件字段
            poster: coverUrl,  // 对标软件字段
            previewImages: previewImages,
            extrafanart: previewImages,  // 对标软件字段

            // === 评分和社区信息 ===
            rating: rating,
            score: rating || '',  // 对标软件字段
            userComments: userComments, // 新增用户评论
            communityData: communityData, // 新增社区数据
            userStats: userStats, // 新增用户统计

            // === 磁力链接 (兼容旧格式 + 新详细格式) ===
            magnet_links: magnetLinks, // 保持向后兼容
            magnetLinksDetailed: magnetLinks, // 新增详细磁力信息

            // === 相关作品 ===
            relatedMovies: relatedMovies,

            // === 技术和元数据信息 ===
            technicalInfo: technicalInfo,
            metaInfo: metaInfo,

            // === 对标软件标准字段 ===
            trailer: '',
            image_download: !!coverUrl,
            image_cut: 'center',
            mosaic: '有码',  // JavDB主要是有码内容
            wanted: '',

            // === 来源信息 ===
            sourceUrl: fullDetailUrl.replace('?locale=zh', ''),
            website: fullDetailUrl.replace('?locale=zh', ''),  // 对标软件字段
            scrapedAt: new Date().toISOString(),
            provider: PROVIDER_NAME,
            version: PROVIDER_VERSION,

            // === 统计信息 ===
            dataRichness: {
                actorsCount: actors.length,
                tagsCount: tags.length,
                allTagsCount: allTags.length,
                userCommentsCount: userComments.length,
                magnetLinksCount: magnetLinks.length,
                relatedMoviesCount: relatedMovies.length,
                previewImagesCount: previewImages.length,
                tagCategoriesCount: Object.keys(tagCategories).length,
                communityDataCount: Object.keys(communityData).length,
                userStatsCount: Object.keys(userStats).length,
                technicalInfoCount: Object.keys(technicalInfo).length,
                hasRating: !!rating,
                hasCommunityData: Object.keys(communityData).length > 0,
                hasUserComments: userComments.length > 0
            }
        };

        log.info(`[JavDB Provider] 🎉 深度刮削完成 ${nfoId}！数据丰富度: 演员${actors.length}个, 标签${allTags.length}个, 评论${userComments.length}条, 磁力${magnetLinks.length}个, 相关作品${relatedMovies.length}部`);
        return scrapedData;

    } catch (error) {
        log.error(`[JavDB Provider] 刮削 ${nfoId} 失败: ${error.message}`);
        throw error;
    } finally {
        // 注意：使用 CDP 连接时，不要关闭浏览器，让用户自己决定
        if (page) {
            log.info(`[JavDB Provider] 保持浏览器页面打开，用户可继续使用`);
        }
    }
}

/**
 * 【新增】获取用户评论 - 完整采集
 * @param {Object} $ - Cheerio 对象
 * @param {Object} page - Playwright 页面对象
 * @returns {Promise<Array>} 用户评论列表
 */
async function getUserComments($, page) {
    const comments = [];

    try {
        // 查找评论区域
        $('.comment, .review, .user-comment, [class*="comment"]').each((i, el) => {
            const commentText = $(el).find('.comment-text, .review-text, .content').text().trim();
            const author = $(el).find('.author, .user-name, .username').text().trim();
            const date = $(el).find('.date, .time, .created-at').text().trim();
            const rating = $(el).find('.rating, .score, .star').text().trim();

            if (commentText && commentText.length > 5) {
                comments.push({
                    text: commentText,
                    author: author || '匿名用户',
                    date: date || '',
                    rating: rating || '',
                    source: 'javdb'
                });
            }
        });

        // 如果页面有评论分页，尝试加载更多
        const hasMoreComments = $('.pagination, .load-more, .next-page').length > 0;
        if (hasMoreComments && comments.length < 10) {
            // 可以在这里添加分页加载逻辑
            log.info(`[JavDB Provider] 发现评论分页，当前已获取 ${comments.length} 条评论`);
        }

    } catch (error) {
        log.warn(`[JavDB Provider] 获取用户评论时出错: ${error.message}`);
    }

    return comments;
}

/**
 * 【新增】获取所有标签 - JavDB 标签体系
 * @param {Object} $ - Cheerio 对象
 * @returns {Array} 所有标签
 */
function getAllTags($) {
    const allTags = [];

    // JavDB 的标签选择器
    const tagSelectors = [
        '.tags a',
        '.genre a',
        '.category a',
        'a[href*="/tags/"]',
        'a[href*="/genres/"]',
        '.tag-list a'
    ];

    tagSelectors.forEach(selector => {
        $(selector).each((i, el) => {
            const tagName = $(el).text().trim();
            const tagUrl = $(el).attr('href');

            if (tagName && !allTags.find(tag => tag.name === tagName)) {
                allTags.push({
                    name: tagName,
                    url: tagUrl ? (tagUrl.startsWith('http') ? tagUrl : `https://javdb.com${tagUrl}`) : ''
                });
            }
        });
    });

    return allTags;
}

/**
 * 【新增】获取标签分类
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 标签分类
 */
function getTagCategories($) {
    const categories = {};

    // 查找标签分类区域
    $('.tag-category, .genre-category').each((i, el) => {
        const categoryName = $(el).find('.category-title, .title').text().trim();
        const categoryTags = [];

        $(el).find('a').each((j, tagEl) => {
            const tagName = $(tagEl).text().trim();
            const tagUrl = $(tagEl).attr('href');

            if (tagName) {
                categoryTags.push({
                    name: tagName,
                    url: tagUrl ? (tagUrl.startsWith('http') ? tagUrl : `https://javdb.com${tagUrl}`) : ''
                });
            }
        });

        if (categoryName && categoryTags.length > 0) {
            categories[categoryName] = categoryTags;
        }
    });

    return categories;
}

/**
 * 【新增】获取社区数据
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 社区数据
 */
function getCommunityData($) {
    const communityData = {};

    // 查找社区相关信息
    const communitySelectors = {
        wantToWatch: '.want-to-watch, .wishlist',
        watched: '.watched, .seen',
        favorites: '.favorites, .liked',
        collections: '.collections, .bookmarks'
    };

    Object.entries(communitySelectors).forEach(([key, selector]) => {
        const element = $(selector).first();
        if (element.length > 0) {
            const text = element.text().trim();
            const numberMatch = text.match(/(\d+)/);

            communityData[key] = {
                text: text,
                count: numberMatch ? parseInt(numberMatch[1]) : 0
            };
        }
    });

    return communityData;
}

/**
 * 【新增】获取用户统计
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 用户统计信息
 */
function getUserStats($) {
    const userStats = {};

    // 查找用户统计信息
    $('.user-stats, .stats, .movie-stats').each((i, el) => {
        $(el).find('.stat-item, .stat').each((j, statEl) => {
            const label = $(statEl).find('.label, .title').text().trim();
            const value = $(statEl).find('.value, .count').text().trim();

            if (label && value) {
                const numberMatch = value.match(/(\d+)/);
                userStats[label] = {
                    text: value,
                    number: numberMatch ? parseInt(numberMatch[1]) : 0
                };
            }
        });
    });

    return userStats;
}

/**
 * 【新增】获取完整磁力链接 - 详细信息
 * @param {Object} page - Playwright 页面对象
 * @returns {Promise<Array>} 详细磁力链接列表
 */
async function getAllMagnetLinksDetailed(page) {
    const magnetLinks = [];

    try {
        // 查找磁力链接按钮
        const magnetButtons = await page.$$('.magnet-link, .download-link, [href^="magnet:"], .copy-magnet');

        for (const button of magnetButtons) {
            try {
                // 获取磁力链接信息
                const magnetInfo = await button.evaluate(el => {
                    const href = el.getAttribute('href') || el.getAttribute('data-magnet') || '';
                    const title = el.getAttribute('title') || el.textContent.trim();
                    const size = el.closest('tr, .item, .magnet-item')?.querySelector('.size, .file-size')?.textContent.trim() || '';
                    const date = el.closest('tr, .item, .magnet-item')?.querySelector('.date, .upload-date')?.textContent.trim() || '';
                    const quality = el.closest('tr, .item, .magnet-item')?.querySelector('.quality, .resolution')?.textContent.trim() || '';
                    const hasSubtitles = el.closest('tr, .item, .magnet-item')?.querySelector('.subtitle, .sub')?.textContent.includes('字幕') || false;

                    return {
                        link: href,
                        title: title,
                        size: size,
                        date: date,
                        quality: quality,
                        hasSubtitles: hasSubtitles
                    };
                });

                if (magnetInfo.link && magnetInfo.link.startsWith('magnet:')) {
                    magnetLinks.push({
                        ...magnetInfo,
                        source: 'javdb',
                        scrapedAt: new Date().toISOString()
                    });
                }

            } catch (error) {
                log.warn(`[JavDB Provider] 处理磁力链接时出错: ${error.message}`);
            }
        }

        log.info(`[JavDB Provider] 获取到 ${magnetLinks.length} 个详细磁力链接`);

    } catch (error) {
        log.error(`[JavDB Provider] 获取磁力链接时出错: ${error.message}`);
    }

    return magnetLinks;
}

/**
 * 【新增】获取相关影片
 * @param {Object} $ - Cheerio 对象
 * @param {string} baseUrl - 基础URL
 * @returns {Array} 相关影片列表
 */
function getRelatedMovies($, baseUrl) {
    const relatedMovies = [];

    // 查找相关影片区域
    $('.related-movies, .similar-movies, .recommend').find('a[href*="/v/"]').each((i, el) => {
        const link = $(el).attr('href');
        const title = $(el).find('img').attr('alt') || $(el).attr('title') || $(el).text().trim();
        const image = $(el).find('img').attr('src') || $(el).find('img').attr('data-src');
        const code = $(el).find('.code, .movie-code').text().trim();

        if (link && title) {
            relatedMovies.push({
                title: title,
                code: code,
                url: link.startsWith('http') ? link : `${baseUrl}${link}`,
                image: image ? (image.startsWith('http') ? image : `${baseUrl}${image}`) : ''
            });
        }
    });

    return relatedMovies.slice(0, 20); // 限制数量
}

/**
 * 【新增】获取技术信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 技术信息
 */
function getTechnicalInfo($) {
    const technicalInfo = {};

    // 查找技术信息表格
    $('.info-table tr, .movie-info tr, .details tr').each((i, el) => {
        const label = $(el).find('td:first-child, .label').text().trim().replace(':', '');
        const value = $(el).find('td:last-child, .value').text().trim();

        if (label && value && label !== value) {
            technicalInfo[label] = value;
        }
    });

    return technicalInfo;
}

/**
 * 【新增】获取元数据信息
 * @param {Object} $ - Cheerio 对象
 * @returns {Object} 元数据信息
 */
function getMetaInfo($) {
    const metaInfo = {};

    // 获取页面元数据
    metaInfo.title = $('title').text().trim();
    metaInfo.description = $('meta[name="description"]').attr('content') || '';
    metaInfo.keywords = $('meta[name="keywords"]').attr('content') || '';
    metaInfo.ogTitle = $('meta[property="og:title"]').attr('content') || '';
    metaInfo.ogDescription = $('meta[property="og:description"]').attr('content') || '';
    metaInfo.ogImage = $('meta[property="og:image"]').attr('content') || '';

    // JavDB 特有的元数据
    metaInfo.movieCode = $('.movie-code, .code').text().trim();
    metaInfo.movieId = $('.movie-id, .id').text().trim();

    return metaInfo;
}

/**
 * 【新增】获取演员头像映射 - 参考对标软件
 * @param {Array} actors - 演员数组
 * @returns {Object} 演员头像映射
 */
function getActorPhoto(actors) {
    const actorPhoto = {};

    if (Array.isArray(actors)) {
        actors.forEach(actor => {
            const actorName = typeof actor === 'string' ? actor : actor.name;
            if (actorName) {
                actorPhoto[actorName] = (typeof actor === 'object' && actor.image) ? actor.image : '';
            }
        });
    }

    return actorPhoto;
}

module.exports = {
    name: PROVIDER_NAME,
    scrape,
    version: PROVIDER_VERSION
};
