@echo off
echo ========================================
echo    SoulForge Next.js 版本启动脚本
echo ========================================
echo.

echo 正在启动 Next.js 版本的 SoulForge...
echo 这个版本完全使用 Node.js + TypeScript，不依赖 Python
echo.

cd soulforge-web

echo 检查依赖...
if not exist node_modules (
    echo 安装依赖中...
    npm install
)

echo.
echo 启动开发服务器...
echo 应用将在 http://localhost:3000 启动
echo.
echo 功能特性:
echo - ✅ 完全 TypeScript/Node.js 实现
echo - ✅ 多版本电影合并
echo - ✅ NFO ID 自动提取和修复
echo - ✅ 媒体库扫描 (无需 Python)
echo - ✅ 现代化 UI 界面
echo.

npm run dev
