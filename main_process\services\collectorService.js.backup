// SoulForge Collector 服务
// 负责执行论坛链接搜集的核心自动化任务

const { chromium } = require('playwright');
const axios = require('axios');
const path = require('path');
const siteProfileService = require('./siteProfileService');
const NodeNfoParser = require('./nodeNfoParser');
const databaseService = require('./databaseService');
const CookieService = require('./cookieService');

let log;
let isInitialized = false;

/**
 * Collector 服务类
 * 提供论坛链接搜集的核心功能
 */
class CollectorService {
  constructor() {
    this.isRunning = false;
    this.isStopping = false; // 任务中断标志
    this.currentTask = null;
    this.taskHistory = [];
    this.browser = null;
    this.context = null;
    this.statusUpdateCallback = null;
    this.downloadPath = null;
    this.enableDownload = false;
    this.cookieService = null; // CookieService 实例
  }

  /**
   * 初始化 Collector 服务
   * @param {Object} logger - 日志服务实例
   * @param {string} projectRoot - 项目根目录路径
   */
  initialize(logger, projectRoot) {
    if (isInitialized) {
      return;
    }

    log = logger;
    log.info('[Collector服务] 正在初始化...');

    // 初始化站点配置服务
    siteProfileService.initializeSiteProfileService(logger, projectRoot);

    // 初始化 Cookie 服务
    this.cookieService = new CookieService(projectRoot);
    log.info('[Collector服务] CookieService 初始化完成');

    // 加载站点配置
    const loadResult = siteProfileService.loadSiteProfiles();
    if (loadResult.success) {
      const forumCount = Object.keys(loadResult.profiles).length;
      log.info(`[Collector服务] 站点配置加载成功，支持 ${forumCount} 个论坛`);
    } else {
      log.warn(`[Collector服务] 站点配置加载失败: ${loadResult.error}`);
    }

    isInitialized = true;
    log.info('[Collector服务] 初始化完成');
  }

  /**
   * 配置下载设置
   * @param {Object} downloadConfig - 下载配置
   */
  configureDownload(downloadConfig = {}) {
    const {
      enableDownload = false,
      downloadPath = null
    } = downloadConfig;

    this.enableDownload = enableDownload;
    this.downloadPath = downloadPath;

    if (this.enableDownload && this.downloadPath) {
      log.info(`[Collector] 下载功能已启用，下载路径: ${this.downloadPath}`);
    } else {
      log.info(`[Collector] 下载功能已禁用`);
    }
  }

  /**
   * 启动一个搜集任务
   * @param {string} siteKey - 站点配置的键 (例如: 'forumA')
   * @param {string} targetUrl - 目标板块的URL
   * @param {Object} options - 搜集选项
   * @returns {Promise<Object>} 任务执行结果
   */
  async startTask(siteKey, targetUrl, options = {}) {
    log.info(`[Collector] 任务启动: 站点='${siteKey}', URL='${targetUrl}'`);

    // 检查服务是否已初始化
    if (!isInitialized) {
      const error = 'Collector服务未初始化，请先调用 initialize() 方法';
      log.error(`[Collector] ${error}`);
      throw new Error(error);
    }

    // 检查是否已有任务在运行
    if (this.isRunning) {
      const error = '已有搜集任务在运行中，请等待当前任务完成';
      log.warn(`[Collector] ${error}`);
      throw new Error(error);
    }

    try {
      // 设置任务状态
      this.isRunning = true;
      this.isStopping = false; // 重置中断标志
      this.currentTask = {
        siteKey,
        targetUrl,
        options,
        startTime: new Date(),
        status: 'running'
      };

      // 步骤1: 加载站点配置
      log.info(`[Collector] 正在加载站点配置: ${siteKey}`);
      const siteProfile = siteProfileService.getSiteProfile(siteKey);
      
      if (!siteProfile) {
        throw new Error(`Site profile for key '${siteKey}' not found.`);
      }

      // 步骤2: 验证配置并打印日志
      log.info(`[Collector] 成功加载站点配置: ${siteProfile.name}`);
      log.info(`[Collector] 登录页面: ${siteProfile.loginUrl}`);
      log.info(`[Collector] 帖子链接选择器: ${siteProfile.postLinkSelector}`);
      log.info(`[Collector] 磁力链接选择器: ${siteProfile.magnetLinkSelector}`);

      // 步骤3: 验证目标URL
      if (!targetUrl || !this.isValidUrl(targetUrl)) {
        throw new Error(`Invalid target URL: ${targetUrl}`);
      }

      // 步骤4: 准备搜集选项
      const taskOptions = {
        maxPages: options.maxPages || 5,
        delay: options.delay || 1000,
        timeout: options.timeout || 30000,
        ...options
      };

      log.info(`[Collector] 搜集选项: 最大页数=${taskOptions.maxPages}, 延迟=${taskOptions.delay}ms`);

      // 步骤5: 执行搜集任务（当前为模拟）
      const result = await this.executeCollectionTask(siteProfile, targetUrl, taskOptions);

      // 步骤6: 更新任务状态
      this.currentTask.status = 'completed';
      this.currentTask.endTime = new Date();
      this.currentTask.result = result;

      // 添加到历史记录
      this.taskHistory.push({ ...this.currentTask });

      log.info(`[Collector] 任务完成: 搜集到 ${result.collectedCount} 个链接`);

      return {
        success: true,
        taskId: this.currentTask.startTime.getTime(),
        result
      };

    } catch (error) {
      log.error(`[Collector] 任务执行失败: ${error.message}`);
      
      // 更新任务状态
      if (this.currentTask) {
        this.currentTask.status = 'failed';
        this.currentTask.endTime = new Date();
        this.currentTask.error = error.message;
        this.taskHistory.push({ ...this.currentTask });
      }

      // 向上抛出错误
      throw error;

    } finally {
      // 重置运行状态
      this.isRunning = false;
      this.currentTask = null;
    }
  }

  /**
   * 执行实际的搜集任务（使用 Playwright 实现）
   * @param {Object} siteProfile - 站点配置
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 搜集选项
   * @returns {Promise<Object>} 搜集结果
   */
  async executeCollectionTask(siteProfile, targetUrl, options) {
    log.info(`[Collector] 使用混合方案: Chrome用户配置文件 + 手动验证 + 自动抓取`);

    // 直接使用混合方案
    return await this.executeWithChromeHybridMode(siteProfile, targetUrl, options);
  }

  /**
   * 混合方案：Chrome 用户配置文件 + 手动验证 + 自动抓取
   */
  async executeWithChromeHybridMode(siteProfile, targetUrl, options) {
    log.info('[Collector] 启动 Chrome 混合方案');
    this.updateTaskStatus('chrome-hybrid', '正在启动 Chrome 用户配置文件...');

    let browser, context, page;
    const results = [];

    try {
      // 步骤1: 获取Chrome用户配置文件路径
      const userDataDir = siteProfileService.getChromeUserDataPath();
      if (!userDataDir || userDataDir.includes('TODO')) {
        throw new Error('Chrome用户配置文件路径未在site-profiles.json中正确设置！');
      }

      log.info(`[Collector] 使用Chrome用户配置文件: ${userDataDir}`);

      // 步骤2: 使用 launchPersistentContext 启动一个带用户配置的浏览器会话
      this.updateTaskStatus('launching-chrome', '正在启动真实 Chrome 浏览器...');
      this.context = await chromium.launchPersistentContext(userDataDir, {
        headless: false, // 必须是可见的，用于手动验证
        args: [
          '--disable-blink-features=AutomationControlled',
          '--start-maximized',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ],
        viewport: null // 使用完整窗口大小
      });

      page = await this.context.newPage();
      log.info(`[Collector] Chrome 浏览器启动成功，已加载用户配置`);

      // 步骤3: 导航到目标页面，让用户手动处理验证
      this.updateTaskStatus('manual-verification', '请在浏览器中手动完成登录和验证...');
      await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });

      // 等待页面加载
      await page.waitForTimeout(3000);

      // 步骤4: 检测是否需要手动验证
      const needsVerification = await this.checkIfNeedsVerification(page);

      if (needsVerification) {
        log.info('[Collector] 检测到需要手动验证');
        this.updateTaskStatus('waiting-verification', '⚠️ 请在浏览器中完成登录和验证，然后点击"继续"按钮');

        // 等待用户手动完成验证
        await this.waitForUserVerification(page);
      }

      // 步骤5: 验证完成后，开始自动抓取
      this.updateTaskStatus('auto-scraping', '验证完成，开始自动抓取内容...');

      // 重新导航确保页面是最新的
      await page.goto(targetUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
      await page.waitForTimeout(2000);

      // 验证登录状态
      const isLoggedIn = await this.verifyLoginStatus(page);
      if (!isLoggedIn) {
        log.warn('[Collector] 警告：仍未检测到登录状态');
        this.updateTaskStatus('login-warning', '⚠️ 未检测到登录状态，但继续尝试抓取...');
      } else {
        log.info('[Collector] 登录状态验证成功');
        this.updateTaskStatus('login-verified', '✅ 登录状态验证成功');
      }

      // 步骤6: 提取帖子链接
      this.updateTaskStatus('parsing-links', '正在解析帖子链接...');
      const allPostLinks = await page.evaluate((selector) => {
        return Array.from(document.querySelectorAll(selector)).map(a => a.href);
      }, siteProfile.postLinkSelector);

      // 去重并过滤
      const uniquePostLinks = [...new Set(allPostLinks)];

      // 进一步过滤，排除公告类帖子
      const postLinks = uniquePostLinks.filter(url => {
        const excludePatterns = [
          'tid=148288', // 最新网址公告
          'tid=730414', // 手机端使用公告
          'tid=271670', // VIP入会公告
          'redirect',   // 重定向链接
          'lastpost'    // 最后回复链接
        ];

        return !excludePatterns.some(pattern => url.includes(pattern));
      });

      log.info(`[Collector] 发现 ${allPostLinks.length} 个链接，去重后 ${uniquePostLinks.length} 个，过滤后 ${postLinks.length} 个有效帖子`);

      if (postLinks.length === 0) {
        this.updateTaskStatus('no-posts', '未找到有效帖子，可能需要调整选择器或检查登录状态');
        return {
          collectedCount: 0,
          pages: 1,
          links: [],
          message: '未找到有效帖子'
        };
      }

      // 步骤7: 处理帖子
      for (let i = 0; i < postLinks.length; i++) {
        if (this.isStopping) {
          log.info('[Collector] 收到停止指令，正在中断任务...');
          this.updateTaskStatus('stopped', '任务已被用户手动停止。');
          break;
        }

        const postUrl = postLinks[i];
        this.updateTaskStatus('scraping', `正在处理帖子 ${i + 1}/${postLinks.length}: ${postUrl}`);

        try {
          await page.goto(postUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
          await page.waitForTimeout(1000);

          // 解析帖子内容
          const postData = await this.parsePostContent(page, siteProfile, postUrl);
          if (postData) {
            results.push(postData);
            log.info(`[Collector] 已抓取: ${postData.postTitle}`);
          }

        } catch (error) {
          log.warn(`[Collector] 跳过帖子 ${postUrl}，原因: ${error.message}`);
        }

        if (options.delay && options.delay > 0) {
          await this.delay(options.delay);
        }
      }

      // 保存数据
      const finalResult = await this.saveResults(results, siteProfile);
      this.updateTaskStatus('completed', `搜集完成，共处理 ${results.length} 条记录。`);

      return finalResult;

    } finally {
      // 注意：不要关闭浏览器，让用户自己决定
      log.info('[Collector] 任务完成，浏览器保持打开状态');
    }

      log.info(`[Collector] FlareSolverr成功获取板块页面`);

      // 步骤3: 启动浏览器解析页面内容
      this.updateTaskStatus('parsing-links', '正在解析帖子链接...');
      browser = await chromium.launch({ headless: false });
      context = await browser.newContext();
      page = await context.newPage();

      // 加载页面内容
      await page.setContent(boardPageResponse.data.solution.response);

      // 提取帖子链接
      const allPostLinks = await page.evaluate((selector) => {
        return Array.from(document.querySelectorAll(selector)).map(a => a.href);
      }, siteProfile.postLinkSelector);

      // 去重并过滤
      const uniquePostLinks = [...new Set(allPostLinks)];

      // 进一步过滤，排除公告类帖子
      const postLinks = uniquePostLinks.filter(url => {
        const excludePatterns = [
          'tid=148288', // 最新网址公告
          'tid=730414', // 手机端使用公告
          'tid=271670', // VIP入会公告
          'redirect',   // 重定向链接
          'lastpost'    // 最后回复链接
        ];

        return !excludePatterns.some(pattern => url.includes(pattern));
      });

      log.info(`[Collector] 发现 ${allPostLinks.length} 个链接，去重后 ${uniquePostLinks.length} 个，过滤后 ${postLinks.length} 个有效帖子`);

      // 步骤4: 遍历每个帖子链接，逐一获取内容
      for (let i = 0; i < postLinks.length; i++) {
        // *** 任务中断检查点 ***
        if (this.isStopping) {
          log.info('[Collector] 收到停止指令，正在中断任务...');
          this.updateTaskStatus('stopped', '任务已被用户手动停止。');
          break;
        }

        const postUrl = postLinks[i];
        this.updateTaskStatus('scraping', `正在处理帖子 ${i + 1}/${postLinks.length}: ${postUrl}`);

        try {
          log.info(`[Collector] 正在通过FlareSolverr请求帖子: ${postUrl}`);

          const postPageResponse = await axios.post('http://127.0.0.1:8191/v1', {
            cmd: 'request.get',
            url: postUrl,
            cookies: flareSolverrCookies,
            maxTimeout: 300000, // 延长超时到5分钟
            session: 'ccgga_session', // 使用相同会话
            proxy: {
              url: null // 不使用代理
            }
          });

          if (postPageResponse.data.status !== 'ok') {
            log.warn(`[Collector] 跳过帖子 ${postUrl}，原因: ${postPageResponse.data.message}`);
            continue; // 跳过失败的帖子，继续下一个
          }

          log.info(`[Collector] FlareSolverr成功获取帖子内容: ${postUrl}`);

          // 加载帖子内容到页面进行解析
          await page.setContent(postPageResponse.data.solution.response);

          // 等待页面加载
          await page.waitForTimeout(1000);
          // 抓取帖子标题
          let postTitle = '';
          try {
            await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
            postTitle = await page.locator(siteProfile.postTitleSelector).first().textContent();
            postTitle = postTitle ? postTitle.trim() : '';
          } catch (error) {
            log.warn(`[Collector] 无法获取帖子标题: ${error.message}`);
            // 尝试从页面标题获取
            postTitle = await page.title() || `帖子 ${i + 1}`;
          }

          // 提取NFO ID
          const nfoId = NodeNfoParser.extractJavIdFromFilename(postTitle);

          // 抓取磁力链接
          let magnetLinks = [];
          try {
            const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
            for (const element of magnetElements) {
              const text = await element.textContent();
              if (text && text.includes('magnet:')) {
                magnetLinks.push(text.trim());
              }
            }
          } catch (error) {
            log.warn(`[Collector] 无法获取磁力链接: ${error.message}`);
          }

          // 抓取ed2k链接
          let ed2kLinks = [];
          try {
            const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
            for (const element of ed2kElements) {
              const text = await element.textContent();
              if (text && text.includes('ed2k:')) {
                ed2kLinks.push(text.trim());
              }
            }
          } catch (error) {
            log.warn(`[Collector] 无法获取ed2k链接: ${error.message}`);
          }

          // 抓取附件链接
          let attachmentUrls = [];
          try {
            const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
            for (const element of attachmentElements) {
              const href = await element.getAttribute('href');
              if (href) {
                attachmentUrls.push(href);
              }
            }
          } catch (error) {
            log.warn(`[Collector] 无法获取附件链接: ${error.message}`);
          }

          // 抓取解压密码
          let decompressionPassword = '';
          try {
            const passwordElement = await page.locator(siteProfile.passwordSelector).first();
            decompressionPassword = await passwordElement.textContent() || '';
            decompressionPassword = decompressionPassword.trim();
          } catch (error) {
            log.warn(`[Collector] 无法获取解压密码: ${error.message}`);
          }

          // 组织数据
          const postData = {
            postUrl,
            postTitle,
            nfoId,
            magnetLink: magnetLinks.join('\n'),
            ed2kLink: ed2kLinks.join('\n'),
            attachmentUrl: attachmentUrls.join('\n'),
            decompressionPassword,
            collectionDate: new Date().toISOString()
          };

          results.push(postData);
          log.info(`[Collector] 已抓取: ${postTitle} ${nfoId ? `(${nfoId})` : ''}`);

        } catch (error) {
          log.warn(`[Collector] 跳过帖子 ${postUrl}，原因: ${error.message}`);
        }

        // 添加延迟避免过快请求
        if (options.delay && options.delay > 0) {
          await this.delay(options.delay);
        }
      }

      // 保存数据到数据库
      let dbResult = null;
      if (results.length > 0) {
        this.updateTaskStatus('saving', '正在保存数据到数据库...');

        try {
          dbResult = databaseService.insertCollectedLinks(results, siteProfile.key);

          if (dbResult.success) {
            log.info(`[Collector] 数据库保存成功: ${dbResult.message}`);
          } else {
            log.warn(`[Collector] 数据库保存失败: ${dbResult.error}`);
          }
        } catch (error) {
          log.error(`[Collector] 数据库保存异常: ${error.message}`);
        }
      }

      const finalResult = {
        collectedCount: results.length,
        pages: 1,
        links: results,
        message: `成功搜集 ${results.length} 个帖子信息`,
        dbResult: dbResult
      };

      this.updateTaskStatus('completed', `搜集完成，共处理 ${results.length} 条记录。`);
      log.info(`[Collector] 搜集任务完成: 找到 ${results.length} 个有效帖子`);

      return finalResult;

    } catch (error) {
      log.error(`[Collector] 搜集任务执行失败: ${error.message}`);
      this.updateTaskStatus('failed', `搜集失败: ${error.message}`);
      throw error;
    } finally {
      // 清理浏览器资源
      if (browser) {
        await browser.close();
        log.info('[Collector] 浏览器已关闭');
      }
    }
  }

  /**
   * 备用方案：直接使用 Playwright + Cookies
   */
  async executeWithPlaywrightFallback(siteProfile, targetUrl, options, cookies) {
    log.info('[Collector] 启动 Playwright 备用方案');
    this.updateTaskStatus('fallback-mode', '正在使用 Playwright 备用方案...');

    let browser, context, page;
    const results = [];

    try {
      // 启动浏览器
      browser = await chromium.launch({
        headless: false,
        args: [
          '--disable-blink-features=AutomationControlled',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      });

      context = await browser.newContext({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      });

      // 添加 cookies
      const playwrightCookies = cookies.map(cookie => {
        const playwrightCookie = {
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain.startsWith('.') ? cookie.domain.substring(1) : cookie.domain,
          path: cookie.path,
          secure: cookie.secure,
          httpOnly: cookie.httpOnly,
          sameSite: cookie.sameSite || 'Lax'
        };

        // 处理过期时间
        if (cookie.expires && cookie.expires > 0) {
          playwrightCookie.expires = cookie.expires;
        }

        return playwrightCookie;
      });

      log.info(`[Collector] 正在添加 ${playwrightCookies.length} 个 cookies 到浏览器`);
      await context.addCookies(playwrightCookies);

      // 验证关键 cookies
      const authCookies = playwrightCookies.filter(c =>
        c.name.includes('auth') || c.name.includes('GvEt_2132')
      );
      log.info(`[Collector] 添加了 ${authCookies.length} 个认证相关 cookies`);
      authCookies.forEach(cookie => {
        log.info(`[Collector] 认证 Cookie: ${cookie.name} = ${cookie.value.substring(0, 20)}...`);
      });

      page = await context.newPage();

      // 导航到目标页面
      this.updateTaskStatus('navigating', '正在导航到目标页面...');
      await page.goto(targetUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      });

      // 等待页面加载
      await page.waitForTimeout(3000);

      // 验证登录状态
      this.updateTaskStatus('verifying-login', '正在验证登录状态...');
      const isLoggedIn = await this.verifyLoginStatus(page);
      if (!isLoggedIn) {
        log.warn('[Collector] 警告：未检测到登录状态，可能需要更新 cookies');
      } else {
        log.info('[Collector] 登录状态验证成功');
      }

      // 检查是否需要处理 Cloudflare
      const cfChallenge = await page.$('.lds-ring, .cf-browser-verification');
      if (cfChallenge) {
        log.info('[Collector] 检测到 Cloudflare 挑战，等待解决...');
        this.updateTaskStatus('solving-challenge', '正在解决 Cloudflare 挑战...');

        // 等待挑战解决
        await page.waitForSelector('.lds-ring', { state: 'detached', timeout: 120000 });
        await page.waitForTimeout(2000);
      }

      // 提取帖子链接
      this.updateTaskStatus('parsing-links', '正在解析帖子链接...');
      const postLinks = await page.evaluate((selector) => {
        return Array.from(document.querySelectorAll(selector)).map(a => a.href);
      }, siteProfile.postLinkSelector);

      log.info(`[Collector] 发现 ${postLinks.length} 个帖子`);

      // 处理帖子
      for (let i = 0; i < postLinks.length; i++) {
        if (this.isStopping) {
          log.info('[Collector] 收到停止指令，正在中断任务...');
          this.updateTaskStatus('stopped', '任务已被用户手动停止。');
          break;
        }

        const postUrl = postLinks[i];
        this.updateTaskStatus('scraping', `正在处理帖子 ${i + 1}/${postLinks.length}: ${postUrl}`);

        try {
          await page.goto(postUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
          await page.waitForTimeout(1000);

          // 解析帖子内容
          const postData = await this.parsePostContent(page, siteProfile, postUrl);
          if (postData) {
            results.push(postData);
            log.info(`[Collector] 已抓取: ${postData.postTitle}`);
          }

        } catch (error) {
          log.warn(`[Collector] 跳过帖子 ${postUrl}，原因: ${error.message}`);
        }

        if (options.delay && options.delay > 0) {
          await this.delay(options.delay);
        }
      }

      // 保存数据
      const finalResult = await this.saveResults(results, siteProfile);
      this.updateTaskStatus('completed', `搜集完成，共处理 ${results.length} 条记录。`);

      return finalResult;

    } finally {
      if (browser) {
        await browser.close();
        log.info('[Collector] 浏览器已关闭');
      }
    }
  }

  /**
   * 检测是否需要手动验证
   */
  async checkIfNeedsVerification(page) {
    try {
      // 检查常见的验证指示器
      const verificationIndicators = [
        '.lds-ring', // Cloudflare 加载环
        '.cf-browser-verification', // Cloudflare 验证页面
        '[class*="captcha"]', // 验证码
        '[class*="verification"]', // 验证页面
        'input[type="password"]', // 密码输入框
        '.login-form', // 登录表单
        '#login', // 登录区域
        'a[href*="login"]' // 登录链接
      ];

      for (const selector of verificationIndicators) {
        try {
          const element = await page.$(selector);
          if (element) {
            const isVisible = await element.isVisible();
            if (isVisible) {
              log.info(`[Collector] 检测到验证指示器: ${selector}`);
              return true;
            }
          }
        } catch (error) {
          // 继续检查下一个选择器
        }
      }

      // 检查页面标题是否包含验证相关文本
      const title = await page.title();
      const verificationTitles = ['验证', '登录', 'verification', 'login', 'challenge'];

      for (const text of verificationTitles) {
        if (title.toLowerCase().includes(text.toLowerCase())) {
          log.info(`[Collector] 页面标题包含验证相关文本: "${title}"`);
          return true;
        }
      }

      // 检查页面内容
      const pageContent = await page.content();
      if (pageContent.includes('Just a moment') ||
          pageContent.includes('Checking your browser') ||
          pageContent.includes('验证中') ||
          pageContent.includes('请稍候')) {
        log.info('[Collector] 页面内容显示正在验证');
        return true;
      }

      return false;

    } catch (error) {
      log.error(`[Collector] 检测验证状态时出错: ${error.message}`);
      return false;
    }
  }

  /**
   * 等待用户手动完成验证
   */
  async waitForUserVerification(page) {
    log.info('[Collector] 等待用户手动完成验证...');

    // 创建一个用户确认对话框
    const userConfirmed = await this.showUserConfirmationDialog();

    if (!userConfirmed) {
      throw new Error('用户取消了验证过程');
    }

    // 等待验证完成的指示器消失
    try {
      await page.waitForSelector('.lds-ring', { state: 'detached', timeout: 30000 });
      log.info('[Collector] Cloudflare 验证完成');
    } catch (error) {
      log.info('[Collector] 未检测到 Cloudflare 验证，可能已完成或不需要');
    }

    // 额外等待确保页面稳定
    await page.waitForTimeout(2000);

    log.info('[Collector] 用户验证过程完成');
  }

  /**
   * 显示用户确认对话框
   */
  async showUserConfirmationDialog() {
    return new Promise((resolve) => {
      // 通过状态更新机制通知前端显示确认对话框
      this.updateTaskStatus('user-confirmation', JSON.stringify({
        type: 'confirmation',
        title: '手动验证确认',
        message: '请在浏览器中完成登录和验证，完成后点击"继续"按钮',
        confirmText: '继续',
        cancelText: '取消'
      }));

      // 设置一个临时的确认回调
      this.userConfirmationCallback = resolve;

      // 30秒后自动确认（避免无限等待）
      setTimeout(() => {
        if (this.userConfirmationCallback) {
          log.info('[Collector] 30秒后自动继续');
          this.userConfirmationCallback(true);
          this.userConfirmationCallback = null;
        }
      }, 30000);
    });
  }

  /**
   * 用户确认回调（由前端调用）
   */
  handleUserConfirmation(confirmed) {
    if (this.userConfirmationCallback) {
      this.userConfirmationCallback(confirmed);
      this.userConfirmationCallback = null;
    }
  }

  /**
   * 验证登录状态
   */
  async verifyLoginStatus(page) {
    try {
      // 检查常见的登录指示器
      const loginIndicators = [
        'a[href*="logout"]', // 登出链接
        '.username', // 用户名显示
        '[class*="user"]', // 用户相关元素
        'a[href*="profile"]', // 个人资料链接
        'a[href*="member"]' // 会员链接
      ];

      for (const selector of loginIndicators) {
        try {
          const element = await page.$(selector);
          if (element) {
            const text = await element.textContent();
            log.info(`[Collector] 找到登录指示器: ${selector} = "${text}"`);
            return true;
          }
        } catch (error) {
          // 继续检查下一个选择器
        }
      }

      // 检查页面内容是否包含登录相关文本
      const pageContent = await page.content();
      const loginTexts = ['退出', '登出', '个人资料', '用户名', 'logout', 'profile'];

      for (const text of loginTexts) {
        if (pageContent.includes(text)) {
          log.info(`[Collector] 页面包含登录相关文本: "${text}"`);
          return true;
        }
      }

      log.warn('[Collector] 未找到明确的登录状态指示器');
      return false;

    } catch (error) {
      log.error(`[Collector] 验证登录状态时出错: ${error.message}`);
      return false;
    }
  }

  /**
   * 解析帖子内容
   */
  async parsePostContent(page, siteProfile, postUrl) {
    try {
      // 抓取帖子标题
      let postTitle = '';
      try {
        await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
        postTitle = await page.locator(siteProfile.postTitleSelector).first().textContent();
        postTitle = postTitle ? postTitle.trim() : '';
      } catch (error) {
        postTitle = await page.title() || `帖子`;
      }

      // 提取NFO ID
      const nfoId = NodeNfoParser.extractJavIdFromFilename(postTitle);

      // 抓取磁力链接
      let magnetLinks = [];
      try {
        const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
        for (const element of magnetElements) {
          const text = await element.textContent();
          if (text && text.includes('magnet:')) {
            magnetLinks.push(text.trim());
          }
        }
      } catch (error) {
        log.warn(`[Collector] 无法获取磁力链接: ${error.message}`);
      }

      // 抓取ed2k链接
      let ed2kLinks = [];
      try {
        const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
        for (const element of ed2kElements) {
          const text = await element.textContent();
          if (text && text.includes('ed2k:')) {
            ed2kLinks.push(text.trim());
          }
        }
      } catch (error) {
        log.warn(`[Collector] 无法获取ed2k链接: ${error.message}`);
      }

      // 抓取附件链接
      let attachmentUrls = [];
      try {
        const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
        for (const element of attachmentElements) {
          const href = await element.getAttribute('href');
          if (href) {
            attachmentUrls.push(href);
          }
        }
      } catch (error) {
        log.warn(`[Collector] 无法获取附件链接: ${error.message}`);
      }

      // 抓取解压密码
      let decompressionPassword = '';
      try {
        const passwordElement = await page.locator(siteProfile.passwordSelector).first();
        decompressionPassword = await passwordElement.textContent() || '';
        decompressionPassword = decompressionPassword.trim();
      } catch (error) {
        log.warn(`[Collector] 无法获取解压密码: ${error.message}`);
      }

      return {
        postUrl,
        postTitle,
        nfoId,
        magnetLink: magnetLinks.join('\n'),
        ed2kLink: ed2kLinks.join('\n'),
        attachmentUrl: attachmentUrls.join('\n'),
        decompressionPassword,
        collectionDate: new Date().toISOString()
      };

    } catch (error) {
      log.error(`[Collector] 解析帖子内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 保存结果到数据库
   */
  async saveResults(results, siteProfile) {
    let dbResult = null;
    if (results.length > 0) {
      this.updateTaskStatus('saving', '正在保存数据到数据库...');

      try {
        dbResult = databaseService.insertCollectedLinks(results, siteProfile.key);

        if (dbResult.success) {
          log.info(`[Collector] 数据库保存成功: ${dbResult.message}`);
        } else {
          log.warn(`[Collector] 数据库保存失败: ${dbResult.error}`);
        }
      } catch (error) {
        log.error(`[Collector] 数据库保存异常: ${error.message}`);
      }
    }

    return {
      collectedCount: results.length,
      pages: 1,
      links: results,
      message: `成功搜集 ${results.length} 个帖子信息`,
      dbResult: dbResult
    };
  }

  /**
   * 停止当前搜集任务
   * @returns {Promise<Object>} 停止结果
   */
  async stopTask() {
    if (!this.isRunning) {
      log.warn('[Collector] 没有正在运行的任务');
      return { success: false, message: '没有正在运行的任务' };
    }

    log.info('[Collector] 正在停止搜集任务...');

    // 设置中断标志，让正在执行的任务优雅停止
    this.isStopping = true;

    try {
      // 关闭浏览器
      await this.closeBrowser();

      this.isRunning = false;
      if (this.currentTask) {
        this.currentTask.status = 'stopped';
        this.currentTask.endTime = new Date();
        this.taskHistory.push({ ...this.currentTask });
        this.currentTask = null;
      }

      this.updateTaskStatus('stopped', '搜集任务已被用户停止');
      log.info('[Collector] 搜集任务已停止');
      return { success: true, message: '搜集任务已停止' };

    } catch (error) {
      log.error(`[Collector] 停止任务时出错: ${error.message}`);
      return { success: false, message: `停止任务失败: ${error.message}` };
    }
  }

  /**
   * 关闭浏览器
   * @returns {Promise<void>}
   */
  async closeBrowser() {
    try {
      if (this.context) {
        await this.context.close();
        this.context = null;
        log.info('[Collector] 已关闭浏览器会话');
      }
    } catch (error) {
      log.error(`[Collector] 关闭浏览器时出错: ${error.message}`);
    }
  }

  /**
   * 获取当前任务状态
   * @returns {Object} 任务状态信息
   */
  getTaskStatus() {
    return {
      isRunning: this.isRunning,
      currentTask: this.currentTask,
      taskHistory: this.taskHistory.slice(-10) // 返回最近10个任务
    };
  }

  /**
   * 获取支持的论坛列表
   * @returns {Array} 论坛列表
   */
  getSupportedForums() {
    try {
      return siteProfileService.getAvailableForums();
    } catch (error) {
      log.error(`[Collector] 获取论坛列表失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 验证URL格式
   * @param {string} url - 要验证的URL
   * @returns {boolean} 是否为有效URL
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 更新任务状态
   * @param {string} status - 任务状态
   * @param {string} message - 状态消息
   */
  updateTaskStatus(status, message) {
    if (this.currentTask) {
      this.currentTask.status = status;
      this.currentTask.statusMessage = message;
      this.currentTask.lastUpdate = new Date();
    }

    log.info(`[Collector] 状态更新: ${status} - ${message}`);

    // 如果有状态更新回调，调用它
    if (this.statusUpdateCallback) {
      this.statusUpdateCallback({ status, message, timestamp: new Date() });
    }
  }

  /**
   * 设置状态更新回调
   * @param {Function} callback - 回调函数
   */
  setStatusUpdateCallback(callback) {
    this.statusUpdateCallback = callback;
  }

  /**
   * 下载附件
   * @param {Object} page - Playwright 页面对象
   * @param {Object} postData - 帖子数据
   * @param {Object} siteProfile - 站点配置
   * @returns {Promise<void>}
   */
  async downloadAttachments(page, postData, siteProfile) {
    const { postTitle, decompressionPassword, attachmentUrl } = postData;

    if (!attachmentUrl) {
      log.info(`[Collector] 跳过下载: 没有附件链接`);
      return;
    }

    const attachmentUrls = attachmentUrl.split('\n').filter(url => url.trim());

    for (let i = 0; i < attachmentUrls.length; i++) {
      const url = attachmentUrls[i].trim();

      try {
        this.updateTaskStatus('downloading', `正在下载附件 ${i + 1}/${attachmentUrls.length}: ${postTitle}`);

        // 更新数据库状态为下载中
        databaseService.updateDownloadStatus(postData.postUrl, 'downloading');

        // 设置下载监听器
        const downloadPromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('下载超时'));
          }, 300000); // 5分钟超时

          page.once('download', async (download) => {
            clearTimeout(timeout);

            try {
              // 获取原始文件名
              const originalFileName = await download.suggestedFilename();
              const fileExtension = originalFileName.split('.').pop() || 'rar';

              // 构建新文件名: [帖子标题]-[解压密码].[扩展名]
              let newFileName = this.sanitizeFileName(postTitle);

              if (decompressionPassword) {
                newFileName += `-${this.sanitizeFileName(decompressionPassword)}`;
              }

              newFileName += `.${fileExtension}`;

              // 构建完整路径
              const path = require('path');
              const fullPath = path.join(this.downloadPath, newFileName);

              log.info(`[Collector] 开始保存文件: ${originalFileName} -> ${newFileName}`);

              // 保存文件
              await download.saveAs(fullPath);

              log.info(`[Collector] 附件下载完成: ${newFileName}`);
              this.updateTaskStatus('download-completed', `附件下载完成: ${newFileName}`);

              // 更新数据库状态为已完成
              databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);

              resolve(fullPath);

            } catch (error) {
              log.error(`[Collector] 保存文件失败: ${error.message}`);
              databaseService.updateDownloadStatus(postData.postUrl, 'failed');
              reject(error);
            }
          });
        });


        // 查找并点击下载链接
        try {
          // 尝试使用配置的选择器
          const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();

          if (attachmentElements.length > i) {
            log.info(`[Collector] 点击下载链接: ${url}`);

            // 点击下载链接
            await attachmentElements[i].click();

            // 等待下载开始
            const downloadPath = await downloadPromise;
            log.info(`[Collector] 下载成功: ${downloadPath}`);

          } else {
            log.warn(`[Collector] 未找到下载链接元素: ${url}`);
            databaseService.updateDownloadStatus(postData.postUrl, 'failed');
          }

        } catch (clickError) {
          log.warn(`[Collector] 点击下载链接失败: ${clickError.message}`);

          // 尝试直接导航到下载链接
          if (url.startsWith('http')) {
            log.info(`[Collector] 尝试直接导航到下载链接: ${url}`);
            await page.goto(url);

            // 等待下载开始
            const downloadPath = await downloadPromise;
            log.info(`[Collector] 直接下载成功: ${downloadPath}`);
          } else {
            throw clickError;
          }
        }

        // 添加延迟避免过快请求
        await this.delay(2000);

      } catch (error) {
        log.error(`[Collector] 下载附件失败 ${url}: ${error.message}`);
        this.updateTaskStatus('download-failed', `下载失败: ${error.message}`);
        databaseService.updateDownloadStatus(postData.postUrl, 'failed');

        // 如果是人机验证，给用户更多时间
        if (error.message.includes('timeout') || error.message.includes('验证')) {
          log.info(`[Collector] 可能遇到人机验证，等待用户操作...`);
          this.updateTaskStatus('waiting-verification', '等待用户完成人机验证...');
          await this.delay(30000); // 等待30秒
        }
      }
    }
  }

  /**
   * 清理文件名中的非法字符
   * @param {string} fileName - 原始文件名
   * @returns {string} 清理后的文件名
   */
  sanitizeFileName(fileName) {
    if (!fileName) return 'untitled';

    // 移除或替换非法字符
    return fileName
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换非法字符为下划线
      .replace(/\s+/g, '_')           // 替换空格为下划线
      .replace(/_{2,}/g, '_')         // 合并多个下划线
      .replace(/^_+|_+$/g, '')        // 移除开头和结尾的下划线
      .substring(0, 100);             // 限制长度
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出单例
module.exports = new CollectorService();
