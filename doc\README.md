# 🔥 SoulForge - 灵魂熔炉

一个强大的自动化搜集和归档系统，专为高效管理和组织数字内容而设计。

## ✨ 主要特性

### 🚀 核心功能
- **智能搜集**：自动化的内容搜集和数据提取
- **静默下载**：完全无干扰的后台下载体验
- **原子化归档**：每个帖子独立的档案文件管理
- **智能重命名**：统一的文件命名格式
- **多站点支持**：可配置的站点适配器

### 🛡️ 技术特性
- **一键启动**：无需手动管理多个服务
- **错误恢复**：健壮的异常处理机制
- **状态管理**：完整的任务状态追踪
- **数据完整性**：可靠的数据存储和备份

## 🚀 快速开始

### 📋 系统要求
- Node.js 16+ 
- npm 或 yarn
- Windows 10/11 (推荐)

### 📦 安装依赖
```bash
npm install
```

### 🎯 一键启动（推荐）
```bash
npm run app
```
这个命令会自动启动前端服务器和Electron应用，无需手动操作多个终端！

### 🔧 传统开发模式（如果需要）
1. 启动前端开发服务器：
```bash
npm run dev
```

2. 在新的终端窗口中启动 Electron：
```bash
npm run electron:dev
```

## 📁 项目结构

```
SoulForge/
├── main.js                    # Electron 主进程
├── src/                       # 前端源码 (React + Vite)
├── main_process/              # 后端服务
│   ├── services/             # 核心服务
│   └── utils/                # 工具函数
├── site-profiles.json        # 站点配置
└── package.json              # 项目配置
```

## 🎯 使用指南

### 1. 首次设置
1. 启动应用：`npm run app`
2. 设置工作区路径
3. 配置站点登录信息

### 2. 开始搜集
1. 选择目标站点
2. 设置搜集参数
3. 点击开始搜集
4. 系统将自动处理所有内容

### 3. 查看结果
- **下载文件**：在工作区的 `attachments` 文件夹
- **档案文件**：在工作区的 `archives` 文件夹
- **数据库记录**：在应用内查看和管理

## 🔧 高级配置

### 站点配置
编辑 `site-profiles.json` 文件来添加新站点或修改现有配置。

### 工作区结构
```
工作区/
├── attachments/              # 下载的附件文件
├── archives/                # 原子化档案文件
│   └── [论坛名]/
│       └── [板块ID]/
│           ├── 帖子1.txt
│           └── 帖子2.txt
└── logs/                    # 搜集日志
```

## 🛠️ 开发

### 构建生产版本
```bash
npm run build
npm run dist
```

### 调试模式
```bash
npm run app
```
应用会自动打开开发者工具。

## 📊 系统特性

### ✅ 启动体验
- 🎯 **一键启动**：无需手动管理多个服务
- 🚀 **快速启动**：从启动到可用仅需几秒
- 🛡️ **智能错误处理**：友好的错误提示和恢复机制

### ✅ 搜集功能
- 📊 **高效搜集**：智能的内容识别和提取
- 🎯 **精确过滤**：避免重复内容，处理新内容
- 📝 **完整记录**：所有信息准确保存

### ✅ 下载系统
- 🔇 **完全静默**：Chrome静音模式，无干扰体验
- 📁 **智能重命名**：统一的文件命名格式
- 🛡️ **错误处理**：完善的下载失败处理机制

### ✅ 档案管理
- 📄 **原子化归档**：每个帖子独立的档案文件
- 🔗 **完整信息**：包含所有相关链接和状态
- 📊 **状态追踪**：清晰的处理状态记录

## 🏆 版本历史

### v1.6.0 (最新)
- ✅ 实现一键启动功能
- ✅ 完善原子化归档系统
- ✅ 修复重命名问题
- ✅ 优化用户体验

### v1.5.x
- ✅ 静默下载功能
- ✅ 智能重命名系统
- ✅ 多站点支持

## 📞 支持

如果您遇到问题或有建议，请：
1. 检查日志文件获取详细错误信息
2. 确保所有依赖正确安装
3. 验证系统要求是否满足

## 📄 许可证

本项目仅供学习和研究使用。

---

**🎉 恭喜！您现在拥有了一个功能完整、稳定可靠的专业自动化搜集和归档系统！**
