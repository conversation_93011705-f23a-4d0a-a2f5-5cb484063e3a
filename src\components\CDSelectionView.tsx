// soul-forge-electron/src/components/CDSelectionView.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { Movie, CdPartInfo, AppSettings, SnapshotInfo as SnapshotInfoType, CdPartsFetchResult, PlayVideoParams } from '../types';
import ImageWithFallback from './ImageWithFallback';
import ImagePreviewModal from './ImagePreviewModal';
import LazyLoadWrapper from './LazyLoadWrapper';
import { LuPlay, LuCamera, LuInfo, LuDatabase, LuFileVideo, LuScanText, LuClapperboard, LuAudioWaveform, LuTv, LuDisc3, LuFilm } from 'react-icons/lu'; // Added LuFilm

interface CDSelectionViewProps {
  isOpen: boolean;
  representativeMovie: Movie | null;
  nfoId: string | null;
  onClose: (refreshList?: boolean) => void;
  onSelectCdPart: (cdPart: Movie) => void;
  appSettings: AppSettings;
}

const formatFileSize = (bytes?: number | null): string => { 
    if (bytes === null || bytes === undefined || isNaN(bytes) || bytes < 0) return '未知';
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
const formatRuntime = (runtimeMinutes?: number | null): string => { 
    if (runtimeMinutes === null || runtimeMinutes === undefined || isNaN(runtimeMinutes) || runtimeMinutes <= 0) return '未知';
    if (runtimeMinutes < 60) return `${runtimeMinutes}分钟`;
    const hours = Math.floor(runtimeMinutes / 60);
    const minutes = runtimeMinutes % 60;
    return `${hours}小时${minutes > 0 ? ` ${minutes}分钟` : ''}`.trim();
};
const formatVideoHeightForDisplay = (height?: number | string | null): string => { 
    if (height === null || height === undefined || String(height).trim() === '') return '未知';
    return `${height}p`;
};

const DetailItem: React.FC<{ label: string; value?: string | number | null; unit?: string; title?: string, className?: string, icon?: React.ReactNode }> = ({ label, value, unit, title, className = '', icon }) => {
  if (value === null || value === undefined || String(value).trim() === '') return null;
  const displayValue = `${value}${unit || ''}`;
  return (
    <span title={title || `${label}: ${displayValue}`} className={`text-xs flex items-center ${className}`}>
      {icon && <span className="mr-1 opacity-70">{icon}</span>}
      <span className="text-neutral-400">{label}: </span>
      <span className="text-sky-300">{displayValue}</span>
    </span>
  );
};

const getTagStyle = (tagText: string): string => { 
  const lowerTag = tagText.toLowerCase();
  if (lowerTag.includes('4k')) return 'bg-amber-500 text-black';
  if (lowerTag.includes('vr')) return 'bg-purple-600 text-white';
  if (lowerTag.includes('bd原盘')) return 'bg-sky-700 text-white';
  if (lowerTag.includes('中文字幕')) return 'bg-red-600 text-white';
  if (lowerTag.includes('破解版')) return 'bg-orange-500 text-black';
  if (lowerTag.includes('外挂字幕')) return 'bg-green-600 text-white';
  if (lowerTag.includes('流出版')) return 'bg-neutral-500 text-white';
  return 'bg-slate-600 text-white';
};


const CDPartCard: React.FC<{
  cdPart: CdPartInfo;
  appSettings: AppSettings;
  onSelectCdPart: (cdPart: Movie) => void;
  anyVersionLoading: boolean; 
}> = ({ cdPart, appSettings, onSelectCdPart, anyVersionLoading }) => {
  
  const handlePlayCdPart = () => {
    const params: PlayVideoParams = {
        filePath: cdPart.filePath,
        title: cdPart.title || cdPart.fileName,
        strmUrl: cdPart.strmUrl
    };
    window.sfeElectronAPI.playVideo(params);
  };

  return (
    <div className={`relative bg-[#272727] rounded-lg border border-[#4a4a4a] shadow-lg p-3 space-y-2.5 transition-all hover:shadow-xl`}>
      {cdPart.specialTags && cdPart.specialTags.length > 0 && (
        <div className="absolute top-1.5 right-1.5 flex flex-col items-end space-y-0.5 pointer-events-none z-10">
          {cdPart.specialTags.slice(0, 2).map(tag => (
            <span 
              key={tag} 
              className={`text-[8px] font-semibold px-1 py-0 rounded-sm shadow-sm ${getTagStyle(tag)}`}
              title={tag}
            >
              {tag}
            </span>
          ))}
        </div>
      )}
       {cdPart.cdPartInfo && (
          <div
            className="absolute top-1.5 left-1.5 bg-teal-600 text-white text-[9px] font-bold px-1.5 py-0.5 rounded shadow-md flex items-center pointer-events-none z-10"
            title={cdPart.cdPartInfo}
          >
            <LuDisc3 size={9} className="mr-0.5" />
            {cdPart.cdPartInfo}
          </div>
        )}

      <div className="flex gap-3">
        <div className="w-20 h-28 flex-shrink-0 rounded overflow-hidden border border-[#4f4f4f] bg-[#2c2c2c] cursor-pointer group relative" onClick={() => onSelectCdPart(cdPart)}>
            <ImageWithFallback
              primarySrc={cdPart.coverDataUrl} 
              secondarySrc={cdPart.posterUrl}
              appDefaultCoverDataUrl={appSettings?.customDefaultCoverDataUrl}
              alt={`封面: ${cdPart.fileName}`}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              placeholder={<div className="w-full h-full bg-[#333333] flex items-center justify-center text-neutral-600 text-xs">无封面</div>}
            />
        </div>
        <div className="flex-grow min-w-0">
          <h3 
            onClick={() => onSelectCdPart(cdPart)} 
            className="font-semibold text-sm text-neutral-100 hover:text-amber-400 hover:underline cursor-pointer break-all line-clamp-2 mb-1"
            title={cdPart.fileName}
          >
            {cdPart.fileName} ({cdPart.cdPartInfo || '分集'})
          </h3>
          <p className="text-[10px] text-neutral-500 break-all line-clamp-2" title={cdPart.filePath}>{cdPart.filePath}</p>
          
          <div className="mt-1.5 text-xs space-y-0.5">
            <div className="flex flex-wrap gap-x-3 gap-y-0.5 items-center">
              <DetailItem label="视频编码" value={cdPart.videoCodec} icon={<LuFilm size={11}/>} className="text-teal-300"/>
              <DetailItem label="分辨率" value={cdPart.resolution || formatVideoHeightForDisplay(cdPart.videoHeight)} icon={<LuTv size={11}/>} className="text-sky-300"/>
            </div>
            <div className="flex flex-wrap gap-x-3 gap-y-0.5 items-center">
              <DetailItem label="大小" value={formatFileSize(cdPart.fileSize)} icon={<LuDatabase size={11}/>} />
              <DetailItem label="时长" value={formatRuntime(cdPart.runtime)} icon={<LuFileVideo size={11}/>}/>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-start gap-1.5 mt-2">
        <button onClick={handlePlayCdPart} title="播放此分集" className="button-primary-app !p-1.5 text-xs" disabled={anyVersionLoading}><LuPlay className="text-sm mr-1 inline"/>播放此集</button>
        <button onClick={() => onSelectCdPart(cdPart)} title="查看此分集详情" className="button-secondary-app !p-1.5 text-xs" disabled={anyVersionLoading}><LuInfo className="text-sm mr-1 inline"/>查看详情</button>
      </div>
    </div>
  );
};


const CDSelectionView: React.FC<CDSelectionViewProps> = ({
  isOpen,
  representativeMovie,
  nfoId,
  onClose,
  onSelectCdPart,
  appSettings,
}) => {
  const [cdParts, setCdParts] = useState<CdPartInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCdParts = useCallback(async (currentNfoId: string) => {
    setIsLoading(true);
    setError(null);
    setCdParts([]);
    try {
      const result: CdPartsFetchResult = await window.sfeElectronAPI.getMovieCdParts(currentNfoId);
      if (result.success) {
        setCdParts(result.cdParts);
      } else {
        setError(result.error || "未能加载CD分集信息。");
      }
    } catch (err: any) {
      console.error("Error fetching CD parts:", err);
      setError(`获取CD分集信息时发生前端错误: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (isOpen && nfoId) {
      fetchCdParts(nfoId);
    } else if (!isOpen) {
        setCdParts([]);
        setError(null);
    }
  }, [isOpen, nfoId, fetchCdParts]);


  if (!isOpen || !representativeMovie) return null;

  return (
    <>
      <div 
        className="fixed inset-0 z-[60] flex items-center justify-center p-2 sm:p-4 bg-black/85 backdrop-blur-md"
        aria-modal="true" role="dialog"
      >
        <div 
          className="bg-[#1e1e1e] text-neutral-100 rounded-xl shadow-2xl w-full max-w-xl max-h-[90vh] flex flex-col border border-[#4f4f4f]"
          onClick={(e) => e.stopPropagation()}
        >
          <header className="flex items-center justify-between p-3 sm:p-4 border-b border-[#3a3a3a] bg-[#2a2a2a] flex-shrink-0">
            <h2 className="text-lg sm:text-xl font-bold text-[#B8860B] truncate" title={representativeMovie.title || representativeMovie.fileName}>
              CD分集选择: {representativeMovie.title || representativeMovie.fileName} <span className="text-sm text-neutral-400">({cdParts.length} 集)</span>
            </h2>
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => onClose()} 
                className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-[#3a3a3a]" 
                aria-label="关闭CD分集选择"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
              </button>
            </div>
          </header>
          
          {isLoading && <div className="flex-grow flex items-center justify-center"><p className="text-neutral-400 text-center py-8">正在加载CD分集列表...</p></div>}
          {error && <div className="flex-grow flex items-center justify-center"><p className="text-red-400 text-center py-8 bg-red-900/20 rounded p-4">{error}</p></div>}
          
          {!isLoading && !error && cdParts.length === 0 && (
            <div className="flex-grow flex items-center justify-center"><p className="text-neutral-400 text-center py-8">未找到该作品的CD分集信息。</p></div>
          )}

          {!isLoading && cdParts.length > 0 && (
            <div className="flex-grow overflow-y-auto settings-scroll-container p-3 space-y-3">
              {cdParts.map(cdPart => (
                <CDPartCard
                  key={cdPart.db_id}
                  cdPart={cdPart}
                  appSettings={appSettings}
                  onSelectCdPart={onSelectCdPart}
                  anyVersionLoading={false} 
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};
export default CDSelectionView;
