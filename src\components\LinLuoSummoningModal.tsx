
import React, { useState, useEffect, useRef } from 'react';
import { LuSave, LuThumbsUp } from 'react-icons/lu'; 

interface LinLuoSummoningModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMessage?: string | null; 
  setInitialMessage?: (message: string | null) => void; 
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'error' | 'loading';
  content: string;
  isStreaming?: boolean; 
}

const LinLuoSummoningModal: React.FC<LinLuoSummoningModalProps> = ({ isOpen, onClose, initialMessage, setInitialMessage }) => {
  const [userInput, setUserInput] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isAiResponding, setIsAiResponding] = useState(false);
  const chatBodyRef = useRef<HTMLDivElement>(null);
  const currentAiMessageIdRef = useRef<string | null>(null);


  useEffect(() => {
    if (chatBodyRef.current) {
      chatBodyRef.current.scrollTop = chatBodyRef.current.scrollHeight;
    }
  }, [chatMessages]);

  useEffect(() => {
    if (isOpen) {
        if (initialMessage) {
            setUserInput(initialMessage);
            if (setInitialMessage) setInitialMessage(null); 
        } else {
            setUserInput('');
        }
        setChatMessages([]); 
        setIsAiResponding(false);
        currentAiMessageIdRef.current = null;
    } else {
        setChatMessages([]); 
        currentAiMessageIdRef.current = null;
    }
  }, [isOpen, initialMessage, setInitialMessage]);

  useEffect(() => {
    if (!isOpen) return;

    const handleChunk = (chunk: string) => {
      setChatMessages(prevMessages => {
        if (currentAiMessageIdRef.current) {
          return prevMessages.map(msg =>
            msg.id === currentAiMessageIdRef.current
              ? { ...msg, content: msg.content + chunk, type: 'ai', isStreaming: true } as ChatMessage
              : msg
          );
        }
        console.warn('[LinLuoStream] Received chunk but no current AI message ID was set. Creating a new message.');
        const newAiMessage: ChatMessage = { id: `ai-unexpected-${Date.now()}`, type: 'ai', content: chunk, isStreaming: true };
        currentAiMessageIdRef.current = newAiMessage.id; 
        return [...prevMessages, newAiMessage];
      });
    };

    const handleEnd = () => {
      setIsAiResponding(false);
      setChatMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === currentAiMessageIdRef.current ? { ...msg, isStreaming: false } as ChatMessage
          : msg
        )
      );
      currentAiMessageIdRef.current = null;
    };

    const handleError = (errorMsg: string) => {
      setIsAiResponding(false);
      setChatMessages(prevMessages => {
         let messageProcessed = false;
         const updatedMessages: ChatMessage[] = prevMessages.map(msg => {
            if (msg.id === currentAiMessageIdRef.current) {
                messageProcessed = true;
                const errorChatMessage: ChatMessage = { 
                  ...msg, 
                  type: 'error', 
                  content: `姐姐出错了: ${errorMsg}`, 
                  isStreaming: false 
                };
                return errorChatMessage;
            }
            return msg;
         });
         
         if (!messageProcessed) {
            const newErrorChatMessage: ChatMessage = { 
              id: `error-${Date.now()}`, 
              type: 'error', 
              content: `姐姐出错了: ${errorMsg}`,
              isStreaming: false,
            };
            return [...updatedMessages, newErrorChatMessage];
         }
         return updatedMessages;
      });
      currentAiMessageIdRef.current = null;
    };

    const unsubscribeChunk = window.sfeElectronAPI.onLinLuoChatChunk(handleChunk);
    const unsubscribeEnd = window.sfeElectronAPI.onLinLuoChatEnd(handleEnd);
    const unsubscribeError = window.sfeElectronAPI.onLinLuoChatError(handleError);

    return () => {
      unsubscribeChunk();
      unsubscribeEnd();
      unsubscribeError();
    };
  }, [isOpen]);


  const sendMessageToServer = async (messageContent: string) => {
    const userMessage: ChatMessage = { id: `user-${Date.now()}`, type: 'user', content: messageContent };
    const aiPlaceholderId = `ai-placeholder-${Date.now()}`;
    currentAiMessageIdRef.current = aiPlaceholderId;
    const aiPlaceholderMessage: ChatMessage = { id: aiPlaceholderId, type: 'ai', content: '', isStreaming: true };

    setChatMessages(prev => [...prev, userMessage, aiPlaceholderMessage]);
    setIsAiResponding(true);

    try {
      const result = await window.sfeElectronAPI.invokeLinLuoChat(messageContent);
      if (result.error) {
        setIsAiResponding(false);
        setChatMessages(prev => prev.map(msg =>
          msg.id === aiPlaceholderId
            ? { ...msg, type: 'error', content: `姐姐出错了: ${result.error}`, isStreaming: false } as ChatMessage
            : msg
        ));
        currentAiMessageIdRef.current = null;
      } else if (!result.streamStarted && !result.error) {
        setIsAiResponding(false);
        setChatMessages(prev => prev.map(msg =>
          msg.id === aiPlaceholderId
            ? { ...msg, type: 'error', content: "姐姐好像有点累了，没有回应呢。", isStreaming: false } as ChatMessage
            : msg
        ));
        currentAiMessageIdRef.current = null;
      }
    } catch (error: any) {
      setIsAiResponding(false);
      setChatMessages(prev => prev.map(msg =>
        msg.id === aiPlaceholderId
          ? { ...msg, type: 'error', content: `与姐姐连接时发生意外: ${error.message || '未知通讯故障'}`, isStreaming: false } as ChatMessage
          : msg
      ));
      currentAiMessageIdRef.current = null;
    }
  };


  const handleSendMessage = async () => {
    if (!userInput.trim() || isAiResponding) return;
    const userMessageContent = userInput.trim();
    setUserInput('');
    await sendMessageToServer(userMessageContent);
  };
  
  const handleShortcutMessage = async (message: string) => {
    if (isAiResponding) return;
    await sendMessageToServer(message);
  };


  const handleSaveChat = async () => {
    if (chatMessages.length === 0) {
      alert("聊天记录为空，没什么可保存的哦。");
      return;
    }
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const defaultFileName = `林珞聊天记录-${timestamp}.md`;
    
    let markdownContent = `# 林珞聊天记录 - ${new Date().toLocaleString()}\n\n`;
    chatMessages.forEach(msg => {
      const prefix = msg.type === 'user' ? '**你:** ' : msg.type === 'ai' ? '**林珞:** ' : '**系统提示:** ';
      markdownContent += `${prefix}\n${msg.content}\n\n---\n\n`;
    });

    try {
      const result = await window.sfeElectronAPI.saveChatHistory({
        fileName: defaultFileName,
        content: markdownContent,
      });
      if (result.success && result.filePath) {
        alert(`聊天记录已保存到: ${result.filePath}`);
      } else if (result.error && result.error !== '用户取消保存。') { 
        alert(`保存聊天记录失败: ${result.error}`);
      }
    } catch (e: any) {
      alert(`保存聊天记录时发生前端错误: ${e.message}`);
    }
  };

  const handleBackdropClick = () => {
    if (!isAiResponding) {
      onClose();
    } else {
        // Optionally provide feedback that modal cannot be closed while AI is responding
        // For example, a slight visual shake or a temporary message.
        // For now, just preventing close.
    }
  };


  if (!isOpen) return null;


  return (
    <div 
      className="fixed inset-0 z-[80] flex items-center justify-center p-4 bg-black/85 backdrop-blur-lg"
      onClick={handleBackdropClick} // Use new handler for backdrop clicks
      aria-modal="true"
      role="dialog"
      aria-labelledby="linluo-modal-title"
    >
      <div
        className="relative bg-gradient-to-br from-[#3d1f3c] via-[#1e1e1e] to-[#3d1f3c] text-neutral-100 rounded-xl shadow-2xl w-full max-w-[700px] max-h-[90vh] flex flex-col border-2 border-pink-500/60 overflow-hidden shadow-pink-500/20" // Increased max-w
        onClick={(e) => e.stopPropagation()} 
      >
        <div className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none opacity-40">
          <div className="absolute -top-1/2 -left-1/2 w-[200%] h-[200%] bg-gradient-radial from-pink-500/25 via-transparent to-transparent animate-pulse-slow"></div>
          <div className="absolute inset-0">
            {[...Array(30)].map((_, i) => (
              <div 
                key={i}
                className="absolute bg-pink-400 rounded-full animate-pulse-slow"
                style={{
                  width: `${Math.random() * 2 + 1}px`,
                  height: `${Math.random() * 2 + 1}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 5}s`,
                  animationDuration: `${Math.random() * 5 + 5}s`,
                  opacity: Math.random() * 0.5 + 0.2,
                }}
              />
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between p-5 border-b border-pink-500/30 bg-black/30 backdrop-blur-sm relative z-10">
          <span className="text-3xl mr-3" role="img" aria-label="林珞头像占位符">💋</span> 
          <h2 id="linluo-modal-title" className="text-2xl font-bold text-pink-400 tracking-wider" style={{ fontFamily: "'Brush Script MT', 'Papyrus', fantasy", textShadow: "0 0 6px #ec4899, 0 0 12px #ec4899, 0 0 2px #fff" }}>
            林珞的私密召唤阵
          </h2>
          <button 
            onClick={onClose} // Original close remains for the X button itself
            disabled={isAiResponding}
            className="text-pink-300 hover:text-pink-100 transition-colors p-1.5 rounded-full hover:bg-pink-500/40 focus:outline-none focus:ring-2 focus:ring-pink-400 active:bg-pink-500/50 disabled:opacity-50" 
            aria-label="关闭林珞召唤阵"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-7 h-7">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div ref={chatBodyRef} className="flex-grow overflow-y-auto p-6 space-y-3 relative z-10 scrollbar-thin scrollbar-thumb-pink-500/50 scrollbar-track-transparent chat-message-container">
           {chatMessages.map((msg) => (
            <div
              key={msg.id}
              className={`chat-message 
                ${msg.type === 'user' ? 'user-message' : 
                  msg.type === 'ai' ? 'ai-message' :
                  msg.type === 'error' ? 'error-message' : 
                  msg.type === 'loading' ? 'loading-message' : ''
                }`}
            >
              {msg.content.split('\n').map((line, index) => (
                <span key={index}>{line}{index < msg.content.split('\n').length - 1 && <br/>}</span>
              ))}
              {msg.type === 'ai' && msg.isStreaming && (msg.content.length > 0 || currentAiMessageIdRef.current === msg.id) && (
                <span className="inline-block w-1 h-3 bg-pink-400 ml-1 animate-pulse"></span>
              )}
              {msg.type === 'ai' && msg.isStreaming && msg.content === '' && currentAiMessageIdRef.current === msg.id && (
                 <span className="italic text-neutral-400">姐姐思考中...</span>
              )}
            </div>
          ))}
        </div>
        
        <div className="p-4 border-t border-pink-500/30 bg-black/30 backdrop-blur-sm relative z-10 space-y-3">
          <textarea
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            disabled={isAiResponding}
            className="form-textarea-linluo w-full h-20 p-3 text-sm bg-[#2a2229]/90 border border-pink-500/50 rounded-lg focus:ring-2 focus:ring-pink-400 focus:border-pink-400 placeholder-neutral-400 resize-none shadow-inner text-neutral-100 disabled:opacity-60"
            placeholder="在这里输入你想对姐姐说的话，或者你的“指令”哦..."
            aria-label="聊天输入框"
          />
          <div className="flex justify-start space-x-2 -mt-1 mb-2">
              <button 
                onClick={() => handleShortcutMessage("林珞姐姐，帮我推荐一些影片吧！")} 
                disabled={isAiResponding}
                className="button-secondary-app !text-xs !py-1 !px-2.5 flex items-center disabled:opacity-60"
                title="让林珞姐姐为你推荐一些影片"
              >
                <LuThumbsUp size={12} className="mr-1"/> 推荐影片
              </button>
              {/* Add more shortcut buttons here */}
          </div>
          <div className="flex justify-between items-center">
            <button
              onClick={handleSaveChat}
              disabled={chatMessages.length === 0 || isAiResponding}
              className="button-neutral-app bg-teal-600/70 hover:bg-teal-500/90 text-white focus:ring-teal-500 px-3 py-2 text-xs font-medium flex items-center disabled:opacity-60"
              title="保存聊天记录为 Markdown"
            >
              <LuSave size={14} className="mr-1.5" /> 保存记录
            </button>
            <div className="flex space-x-3">
              <button 
                onClick={handleSendMessage}
                disabled={isAiResponding || !userInput.trim()}
                className="button-primary-linluo py-2 px-5 text-sm font-semibold disabled:opacity-60"
                aria-label={isAiResponding ? "姐姐思考中" : "发送给姐姐"}
              >
                {isAiResponding ? "姐姐思考中..." : "发送给姐姐 ❤️"}
              </button>
              <button 
                  onClick={onClose} // Original close for this button
                  disabled={isAiResponding}
                  className="button-neutral-app bg-pink-700/60 hover:bg-pink-600/80 text-white focus:ring-pink-500 px-4 py-2 text-sm font-medium active:bg-pink-700/90 disabled:opacity-60"
                  aria-label="关闭聊天"
              >
                  下次再来
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LinLuoSummoningModal;