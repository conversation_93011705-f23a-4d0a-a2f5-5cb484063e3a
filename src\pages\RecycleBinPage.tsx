import React, { useEffect, useState } from 'react';
import { LuTrash2, LuRotateCcw, LuTrash, LuCircleAlert } from 'react-icons/lu';

interface DeletedItem {
  id: string;
  title: string;
  nfoId: string;
  deletedDate: string;
  originalPath: string;
  fileSize: number;
  coverUrl?: string;
  reason: string;
}

const RecycleBinPage: React.FC = () => {
  const [deletedItems, setDeletedItems] = useState<DeletedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchDeletedItems = async () => {
      try {
        setIsLoading(true);
        console.log('开始获取回收站数据...');

        const result = await window.sfeElectronAPI.getRecycledItems();
        console.log('回收站数据获取结果:', result);

        if (result.success && result.data) {
          // 合并电影和版本数据
          const allItems: DeletedItem[] = [];

          // 处理回收的电影
          if (result.data.movies) {
            result.data.movies.forEach((movie: any, index: number) => {
              allItems.push({
                id: `movie-${index + 1}`,
                title: movie.title || `影片 ${movie.nfoId}`,
                nfoId: movie.nfoId,
                deletedDate: movie.recycledAt || new Date().toLocaleString('zh-CN'),
                originalPath: movie.filePath || '未知路径',
                fileSize: 0, // 电影级别删除通常不显示单个文件大小
                reason: '整部影片回收'
              });
            });
          }

          // 处理回收的版本
          if (result.data.versions) {
            result.data.versions.forEach((version: any, index: number) => {
              allItems.push({
                id: `version-${index + 1}`,
                title: version.title || `版本 ${version.fileName}`,
                nfoId: version.nfoId,
                deletedDate: version.recycledAt || new Date().toLocaleString('zh-CN'),
                originalPath: version.filePath || '未知路径',
                fileSize: 0, // 版本信息中可能没有文件大小
                reason: '单一版本回收'
              });
            });
          }

          setDeletedItems(allItems);
          console.log(`成功加载 ${allItems.length} 个回收站项目`);
        } else {
          console.warn('回收站数据为空或获取失败:', result.error);
          setDeletedItems([]);
        }
      } catch (error) {
        console.error('获取回收站数据失败:', error);
        setDeletedItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeletedItems();
  }, []);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSelectItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedItems.size === deletedItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(deletedItems.map(item => item.id)));
    }
  };

  const handleRestore = async (itemIds: string[]) => {
    // TODO: 实现恢复逻辑
    console.log('恢复项目:', itemIds);
  };

  const handlePermanentDelete = async (itemIds: string[]) => {
    if (confirm('确定要永久删除这些项目吗？此操作无法撤销！')) {
      // TODO: 实现永久删除逻辑
      console.log('永久删除项目:', itemIds);
    }
  };

  const handleEmptyRecycleBin = async () => {
    if (confirm('确定要清空回收站吗？此操作将永久删除所有项目，无法撤销！')) {
      // TODO: 实现清空回收站逻辑
      console.log('清空回收站');
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
            <LuTrash2 className="h-8 w-8 text-red-500" />
            回收站
          </h1>
          <p className="text-gray-400">管理已删除的影片，可以恢复或永久删除</p>
        </div>

        {/* 操作栏 */}
        {deletedItems.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <label className="flex items-center gap-2 text-white">
                  <input
                    type="checkbox"
                    checked={selectedItems.size === deletedItems.length}
                    onChange={handleSelectAll}
                    className="rounded"
                  />
                  全选 ({selectedItems.size}/{deletedItems.length})
                </label>
              </div>
              
              <div className="flex items-center gap-2">
                {selectedItems.size > 0 && (
                  <>
                    <button
                      onClick={() => handleRestore(Array.from(selectedItems))}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                    >
                      <LuRotateCcw className="h-4 w-4" />
                      恢复选中
                    </button>
                    <button
                      onClick={() => handlePermanentDelete(Array.from(selectedItems))}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                    >
                      <LuTrash className="h-4 w-4" />
                      永久删除
                    </button>
                  </>
                )}
                
                <button
                  onClick={handleEmptyRecycleBin}
                  className="px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors flex items-center gap-2"
                >
                  <LuTrash2 className="h-4 w-4" />
                  清空回收站
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 项目列表 */}
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gray-700 rounded"></div>
                  <div className="flex-1">
                    <div className="h-6 bg-gray-700 rounded w-1/3 mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded w-1/4 mb-1"></div>
                    <div className="h-4 bg-gray-700 rounded w-1/6"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : deletedItems.length > 0 ? (
          <div className="space-y-4">
            {deletedItems.map((item) => (
              <div
                key={item.id}
                className={`bg-gray-800 rounded-lg p-6 border transition-colors ${
                  selectedItems.has(item.id) 
                    ? 'border-[#B8860B] bg-gray-800/80' 
                    : 'border-gray-700 hover:border-gray-600'
                }`}
              >
                <div className="flex items-center gap-4">
                  <input
                    type="checkbox"
                    checked={selectedItems.has(item.id)}
                    onChange={() => handleSelectItem(item.id)}
                    className="rounded"
                  />
                  
                  <div className="w-16 h-16 bg-gray-700 rounded flex items-center justify-center">
                    {item.coverUrl ? (
                      <img
                        src={item.coverUrl}
                        alt={item.title}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <LuTrash2 className="h-8 w-8 text-gray-500" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="text-white font-semibold text-lg">{item.title}</h3>
                    <p className="text-gray-400 text-sm">番号: {item.nfoId}</p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-400">
                      <span>删除时间: {item.deletedDate}</span>
                      <span>文件大小: {formatFileSize(item.fileSize)}</span>
                      <span>删除原因: {item.reason}</span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">原路径: {item.originalPath}</p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleRestore([item.id])}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                    >
                      <LuRotateCcw className="h-4 w-4" />
                      恢复
                    </button>
                    <button
                      onClick={() => handlePermanentDelete([item.id])}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                    >
                      <LuTrash className="h-4 w-4" />
                      永久删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <LuTrash2 className="h-16 w-16 mx-auto mb-4 text-gray-600" />
            <h3 className="text-xl font-semibold text-white mb-2">回收站为空</h3>
            <p className="text-gray-400">暂无已删除的影片</p>
          </div>
        )}

        {/* 警告信息 */}
        {deletedItems.length > 0 && (
          <div className="mt-8 bg-yellow-900/30 border border-yellow-600/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <LuCircleAlert className="h-5 w-5 text-yellow-400" />
              <div>
                <p className="text-yellow-400 font-medium">注意事项</p>
                <p className="text-yellow-300 text-sm mt-1">
                  • 回收站中的项目会占用磁盘空间，建议定期清理<br/>
                  • 永久删除的项目无法恢复，请谨慎操作<br/>
                  • 恢复的项目将回到原始位置
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecycleBinPage;
