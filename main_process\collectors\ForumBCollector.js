/**
 * 论坛B (98堂) 采集器 - 完全继承ForumACollector
 *
 * 继承自ForumACollector，专门针对98堂的特殊需求：
 * 1. ED2K链接处理 - 从div.blockcode ol li中提取
 * 2. 磁力链接处理 - 从div.blockcode ol li中提取
 * 3. 解压密码提取 - 支持多种格式的密码匹配
 * 4. 网盘链接提取 - 支持链接+提取码组合
 * 5. 使用和论坛A一样的下载和重命名机制
 */

const ForumACollector = require('./ForumACollector');
const path = require('path');
const fs = require('fs');

class ForumBCollector extends ForumACollector {
  constructor(config) {
    super(config);
    this.forumName = '98堂';
  }

  /**
   * 🔧 重写：提取磁力链接 - 98堂专用逻辑
   * 从div.blockcode ol li中提取磁力链接，清理HTML标签
   */
  async extractMagnetLinks(page, siteProfile) {
    const magnetLinks = [];

    try {
      const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
      this.log.info(`[${this.forumName}] 🔍 找到 ${magnetElements.length} 个磁力链接候选元素`);

      for (const element of magnetElements) {
        try {
          // 获取li元素的HTML内容
          const innerHTML = await element.innerHTML();

          // 使用正则表达式提取磁力链接
          const magnetMatches = innerHTML.match(/magnet:\?[^<\s"']+/gi);
          if (magnetMatches) {
            magnetMatches.forEach(match => {
              const cleanLink = match.trim();
              if (cleanLink.startsWith('magnet:?') && cleanLink.length > 20) {
                magnetLinks.push(cleanLink);
                this.log.info(`[${this.forumName}] 🧲 提取磁力链接: ${cleanLink.substring(0, 50)}...`);
              }
            });
          }
        } catch (error) {
          this.log.warn(`[${this.forumName}] 提取磁力链接失败: ${error.message}`);
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 无法获取磁力链接: ${error.message}`);
    }

    return magnetLinks;
  }

  /**
   * 🔧 重写：提取ED2K链接 - 98堂专用逻辑
   * 从div.blockcode ol li中提取ED2K链接，支持换行和空格处理
   */
  async extractEd2kLinks(page, siteProfile) {
    const ed2kLinks = [];

    try {
      const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
      this.log.info(`[${this.forumName}] 🔍 找到 ${ed2kElements.length} 个ED2K链接候选元素`);

      for (const element of ed2kElements) {
        try {
          // 获取li元素的HTML内容
          const innerHTML = await element.innerHTML();

          // 🔧 增强的正则表达式：支持换行符和更复杂的格式
          const ed2kMatches = innerHTML.match(/ed2k:\/\/\|file\|[^|]*\|[^|]*\|[^|]*\|/gs);
          if (ed2kMatches) {
            ed2kMatches.forEach(match => {
              // 移除HTML标签和多余的空白字符
              const cleanLink = match.replace(/<[^>]*>/g, '').replace(/\s+/g, '').trim();
              if (cleanLink.length > 30) { // ED2K链接基本长度验证
                ed2kLinks.push(cleanLink);
                this.log.info(`[${this.forumName}] 🔗 提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
              }
            });
          }
        } catch (error) {
          this.log.warn(`[${this.forumName}] 提取ED2K链接失败: ${error.message}`);
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 无法获取ED2K链接: ${error.message}`);
    }

    return ed2kLinks;
  }

  /**
   * 🔧 重写：提取解压密码 - 98堂专用逻辑
   * 支持多种格式的密码匹配
   */
  extractDecompressionPassword(postBodyText) {
    if (!postBodyText) return null;

    // 98堂的密码提取规则
    const passwordPatterns = [
      /【解压密码】[：:]\s*([^\s\n\r]+)/i,
      /解压密码[：:]\s*([^\s\n\r]+)/i,
      /RAR解压密码为[：:]\s*([^\s\n\r]+)/i,
      /密码[：:]\s*([^\s\n\r]+)/i,
      /password[：:]\s*([^\s\n\r]+)/i
    ];

    for (const pattern of passwordPatterns) {
      const match = postBodyText.match(pattern);
      if (match && match[1]) {
        const password = match[1].trim();
        this.log.info(`[${this.forumName}] 🔑 提取解压密码: ${password}`);
        return password;
      }
    }

    return null;
  }

  /**
   * 🔧 重写：提取网盘链接 - 98堂专用逻辑
   * 支持链接+提取码组合
   */
  extractCloudLinksWithCodes(postBodyText) {
    const cloudLinks = [];

    if (!postBodyText) return cloudLinks;

    try {
      // 按照手册要求：正则表达式 链接\d*：(https?:\/\/[^\s]+)\s*提取码：(\w+)
      const cloudLinkPattern = /链接\d*[：:](https?:\/\/[^\s]+)\s*提取码[：:](\w+)/gi;
      let match;

      while ((match = cloudLinkPattern.exec(postBodyText)) !== null) {
        cloudLinks.push({
          url: match[1].trim(),
          code: match[2].trim()
        });
        this.log.info(`[${this.forumName}] 🌐 提取网盘链接: ${match[1]} (提取码: ${match[2]})`);
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 提取网盘链接失败: ${error.message}`);
    }

    return cloudLinks;
  }

  /**
   * 🔧 重写：提取元数据 - 98堂专用逻辑
   * 增强文件大小和其他元数据的提取
   */
  extractMetadataFromText(postBodyText) {
    const metadata = super.extractMetadataFromText(postBodyText);

    if (!postBodyText) return metadata;

    // 98堂特有的文件大小格式
    const fileSizePatterns = [
      /【影片容量】[：:]\s*(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|G|M))/i,
      /文件大小[：:]\s*(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|G|M))/i,
      /容量[：:]\s*(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|G|M))/i,
      /大小[：:]\s*(\d+\.?\d*\s*(?:GiB|MiB|GB|MB|G|M))/i
    ];

    for (const pattern of fileSizePatterns) {
      const match = postBodyText.match(pattern);
      if (match && match[1]) {
        metadata.fileSize = match[1].trim();
        this.log.info(`[${this.forumName}] 📊 提取文件大小: ${metadata.fileSize}`);
        break;
      }
    }

    return metadata;
  }

  /**
   * 🔧 重写：过滤帖子链接 - 98堂专用逻辑
   * 跳过置顶公告帖子，只处理普通帖子
   */
  async extractPostLinks(page, siteProfile) {
    const postLinks = await super.extractPostLinks(page, siteProfile);

    // 过滤掉置顶帖子，只保留普通帖子
    const filteredLinks = [];

    for (const link of postLinks) {
      try {
        // 查找对应的tbody元素来检查是否为置顶帖
        const linkElement = await page.locator(`a[href="${link.url}"]`).first();
        const tbodyElement = await linkElement.locator('xpath=ancestor::tbody[1]').first();
        const tbodyId = await tbodyElement.getAttribute('id');

        if (tbodyId && tbodyId.startsWith('stickthread_')) {
          this.log.info(`[${this.forumName}] 🚫 跳过置顶帖子: ${link.title} (${tbodyId})`);
          continue;
        }

        if (tbodyId && tbodyId.startsWith('normalthread_')) {
          this.log.info(`[${this.forumName}] ✅ 处理普通帖子: ${link.title} (${tbodyId})`);
          filteredLinks.push(link);
        } else {
          this.log.warn(`[${this.forumName}] ⚠️ 未知帖子类型: ${link.title} (${tbodyId})`);
        }
      } catch (error) {
        this.log.warn(`[${this.forumName}] 检查帖子类型失败: ${link.title} - ${error.message}`);
        // 如果检查失败，默认包含该帖子
        filteredLinks.push(link);
      }
    }

    this.log.info(`[${this.forumName}] 📊 过滤结果: 原始${postLinks.length}个帖子，过滤后${filteredLinks.length}个普通帖子`);

    return filteredLinks;
  }

  /**
   * 🔧 98堂专用：获取有效的附件元素（区分图片和附件）
   */
  async getValidAttachmentElements(page, siteProfile) {
    try {
      this.log.info(`[${this.forumName}] 🔸 查找有效附件元素，使用选择器 ${siteProfile.attachmentUrlSelector}`);

      // 查找所有包含mod=attachment的链接
      const allElements = await page.locator(siteProfile.attachmentUrlSelector).all();
      this.log.info(`[${this.forumName}] 🔸 找到 ${allElements.length} 个可能的附件元素`);

      const validAttachmentElements = [];

      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];

        try {
          // 获取链接的href属性
          const href = await element.getAttribute('href');

          if (!href || !href.includes('mod=attachment')) {
            continue;
          }

          // 🔧 关键：排除图片链接（包含&nothumb=yes的通常是图片）
          if (href.includes('&nothumb=yes')) {
            this.log.info(`[${this.forumName}] 🚫 跳过图片链接: ${href}`);
            continue;
          }

          // 检查是否是真正的附件链接（不是图片）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          // 只处理<a>标签，排除<img>标签
          if (tagName !== 'a') {
            this.log.info(`[${this.forumName}] 🚫 跳过非链接元素: ${tagName}`);
            continue;
          }

          // 检查链接是否有文本内容（真正的附件链接通常有文件名文本）
          const linkText = await element.textContent();

          if (!linkText || linkText.trim().length === 0) {
            this.log.info(`[${this.forumName}] 🚫 跳过无文本内容的链接`);
            continue;
          }

          // 🔧 排除"下载附件"这样的通用文本，只要包含文件扩展名的
          const trimmedText = linkText.trim();
          if (trimmedText === '下载附件' || trimmedText === 'Download Attachment') {
            this.log.info(`[${this.forumName}] 🚫 跳过通用下载链接: ${trimmedText}`);
            continue;
          }

          // 🔧 检查是否包含文件扩展名（真正的附件通常有文件名）
          const hasFileExtension = /\.(txt|rar|zip|7z|pdf|doc|docx|xls|xlsx|mp4|avi|mkv|torrent)$/i.test(trimmedText);
          if (!hasFileExtension) {
            this.log.info(`[${this.forumName}] 🚫 跳过无文件扩展名的链接: ${trimmedText}`);
            continue;
          }

          // 检查是否包含target="_blank"属性（附件链接通常有这个属性）
          const target = await element.getAttribute('target');

          if (target !== '_blank') {
            this.log.info(`[${this.forumName}] 🚫 跳过非_blank目标的链接: ${trimmedText}`);
            continue;
          }

          // 🔧 检查元素是否可见
          const isVisible = await element.isVisible();
          if (!isVisible) {
            this.log.info(`[${this.forumName}] 🚫 跳过不可见的链接: ${trimmedText}`);
            continue;
          }

          this.log.info(`[${this.forumName}] ✅ 找到有效附件: ${trimmedText}`);
          validAttachmentElements.push(element);

        } catch (error) {
          this.log.warn(`[${this.forumName}] 处理附件元素 ${i} 时出错: ${error.message}`);
        }
      }

      this.log.info(`[${this.forumName}] 🔸 总共找到 ${validAttachmentElements.length} 个有效附件元素`);

      return validAttachmentElements;

    } catch (error) {
      this.log.error(`[${this.forumName}] 获取有效附件元素失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 🔧 重写：提取附件链接 - 98堂专用，区分图片和附件
   */
  async extractAttachmentUrls(page, siteProfile) {
    try {
      // 使用我们的专用方法获取有效附件元素
      const validAttachmentElements = await this.getValidAttachmentElements(page, siteProfile);

      const attachmentUrls = [];

      for (let i = 0; i < validAttachmentElements.length; i++) {
        const element = validAttachmentElements[i];

        try {
          // 获取链接的href属性
          const href = await element.getAttribute('href');
          const linkText = await element.textContent();

          // 构建完整的URL
          const fullUrl = href.startsWith('http') ? href : `https://bs7v.k29rn.net/${href}`;

          this.log.info(`[${this.forumName}] 🔸 提取附件链接: ${fullUrl}`);
          this.log.info(`[${this.forumName}] 🔸 附件文件名: ${linkText.trim()}`);

          attachmentUrls.push(fullUrl);

        } catch (error) {
          this.log.warn(`[${this.forumName}] 处理附件元素 ${i} 时出错: ${error.message}`);
        }
      }

      this.log.info(`[${this.forumName}] 🔸 总共提取到 ${attachmentUrls.length} 个有效附件链接`);

      return attachmentUrls.length > 0 ? attachmentUrls.join('\n') : null;

    } catch (error) {
      this.log.error(`[${this.forumName}] 提取附件链接失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 🔧 重写：下载附件 - 98堂专用模拟手动点击下载
   * 1. 模拟手动点击下载
   * 2. 弹窗不推到前台
   * 3. 使用帖子标题重命名
   */
  async downloadAttachments(page, postData, siteProfile) {
    const { postTitle, decompressionPassword, attachmentUrl } = postData;

    if (!attachmentUrl) {
      this.log.info(`[${this.forumName}] 跳过下载: 没有附件链接`);
      return { success: false, message: '没有附件链接' };
    }

    // 🔧 修复：直接使用用户指定的工作区路径，不创建子目录
    const attachmentsDir = this.workspacePath;
    this.log.info(`[${this.forumName}] 📁 目标下载目录: ${attachmentsDir}`);

    // 确保目录存在
    if (!fs.existsSync(attachmentsDir)) {
      fs.mkdirSync(attachmentsDir, { recursive: true });
    }

    let downloadSuccess = false;

    try {
      // 🔧 关键修复：只处理一次，不要循环处理URL
      // 因为我们已经在当前帖子页面，直接处理页面上的附件元素

      // 使用我们专用的附件提取方法，区分图片和附件
      const attachmentElements = await this.getValidAttachmentElements(page, siteProfile);

      if (attachmentElements.length === 0) {
        this.log.warn(`[${this.forumName}] 未找到有效附件元素，跳过下载`);
        return { success: false, message: '未找到有效附件' };
      }

      this.log.info(`[${this.forumName}] 找到 ${attachmentElements.length} 个有效附件`);

      // 遍历所有附件元素进行下载
      for (let attachmentIndex = 0; attachmentIndex < attachmentElements.length; attachmentIndex++) {
        this.log.info(`[${this.forumName}] 准备下载附件 ${attachmentIndex + 1}/${attachmentElements.length}`);

        this.updateTaskStatus('downloading', `正在下载附件 ${attachmentIndex + 1}/${attachmentElements.length}: ${postTitle}`);

        try {
          // 🔧 关键修复：创建postData的深拷贝，避免异步竞态条件导致的张冠李戴
          const postDataCopy = JSON.parse(JSON.stringify(postData));
          this.log.info(`[${this.forumName}] 🔒 为附件 ${attachmentIndex + 1} 创建独立的帖子数据副本: ${postDataCopy.postTitle}`);

          const success = await this.downloadSingleAttachmentForumB(
            page,
            attachmentElements[attachmentIndex],
            postDataCopy, // 使用副本而不是原始引用
            attachmentsDir,
            attachmentIndex
          );

          if (success) {
            downloadSuccess = true;
          }
        } catch (singleDownloadError) {
          this.log.warn(`[${this.forumName}] 下载附件 ${attachmentIndex + 1} 失败: ${singleDownloadError.message}`);
          // 继续下载其他附件，不要因为一个附件失败就停止整个流程
        }

        // 多附件下载间隔
        if (attachmentIndex < attachmentElements.length - 1) {
          await page.waitForTimeout(2000);
        }
      }

    } catch (error) {
      this.log.error(`[${this.forumName}] 下载附件失败: ${error.message}`);

      // 只有在特定错误时才抛出异常，其他错误不影响主流程
      if (error.message.includes('下载次数已达上限') ||
          error.message.includes('需要人机验证')) {
        throw error;
      }

      // 其他错误记录但不抛出，确保主流程继续
      return { success: false, message: `下载失败: ${error.message}` };
    }

    this.log.info(`[${this.forumName}] 📥 下载完成，成功: ${downloadSuccess}`);
    return { success: downloadSuccess, message: downloadSuccess ? '下载成功' : '下载失败' };
  }

  /**
   * 🔧 98堂专用：下载单个附件 - 使用页面下载API
   * 1. 设置下载监听器
   * 2. 点击附件链接触发下载
   * 3. 自动重命名并移动到指定位置
   */
  async downloadSingleAttachmentForumB(page, attachmentElement, postData, attachmentsDir, attachmentIndex) {
    const fs = require('fs');
    const path = require('path');

    try {
      this.log.info(`[${this.forumName}] 🖱️ 开始下载附件 ${attachmentIndex + 1}`);

      // 获取附件URL用于日志
      const attachmentUrl = await attachmentElement.getAttribute('href');
      this.log.info(`[${this.forumName}] 📎 附件URL: ${attachmentUrl}`);

      // 🔧 修复：确保文件保存到attachments子目录
      const attachmentsSubDir = path.join(attachmentsDir, 'attachments');
      if (!fs.existsSync(attachmentsSubDir)) {
        fs.mkdirSync(attachmentsSubDir, { recursive: true });
        this.log.info(`[${this.forumName}] 📁 创建附件目录: ${attachmentsSubDir}`);
      }

      // 🎯 创建唯一的下载标识符，避免张冠李戴
      const downloadId = `${Date.now()}_${attachmentIndex}_${Math.random().toString(36).substr(2, 9)}`;
      this.log.info(`[${this.forumName}] 🆔 创建唯一下载标识符: ${downloadId}`);

      // 🔧 关键修复：将帖子数据与下载ID绑定，避免竞态条件
      const downloadContext = {
        id: downloadId,
        postData: JSON.parse(JSON.stringify(postData)), // 深拷贝帖子数据
        attachmentIndex: attachmentIndex,
        attachmentUrl: attachmentUrl,
        attachmentsSubDir: attachmentsSubDir,
        timestamp: Date.now()
      };

      this.log.info(`[${this.forumName}] 🔒 下载上下文已创建: 帖子="${downloadContext.postData.postTitle}", 索引=${downloadContext.attachmentIndex}`);

      // 🎯 设置下载监听器
      let downloadStarted = false;
      let downloadPath = null;
      let originalFileName = null;

      const downloadPromise = new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          if (!downloadStarted) {
            this.log.warn(`[${this.forumName}] ⏰ 下载超时，但点击已完成 (ID: ${downloadId})`);
            resolve(true); // 按照用户要求：点击了就算下载了
          }
        }, 30000); // 30秒超时

        // 🔧 关键修复：使用下载上下文，确保每个下载使用正确的帖子数据
        const downloadHandler = async (download) => {
          try {
            // 🔧 关键：检查下载文件名是否匹配预期的附件
            const downloadFileName = download.suggestedFilename();
            this.log.info(`[${this.forumName}] 📥 检测到下载事件: ${downloadFileName} (ID: ${downloadId})`);

            // 🔧 防止重复处理：检查是否已经开始下载
            if (downloadStarted) {
              this.log.warn(`[${this.forumName}] ⚠️ 下载已开始，跳过重复处理 (ID: ${downloadId})`);
              return;
            }

            downloadStarted = true;
            clearTimeout(timeout);

            // 🔧 立即移除监听器，避免被其他下载触发
            page.off('download', downloadHandler);

            originalFileName = downloadFileName;
            this.log.info(`[${this.forumName}] 📥 下载开始: ${originalFileName} (ID: ${downloadId})`);
            this.log.info(`[${this.forumName}] 🎯 使用下载上下文: 帖子="${downloadContext.postData.postTitle}", 索引=${downloadContext.attachmentIndex}`);

            // 🔧 98堂专用：使用下载上下文中的帖子数据生成文件名
            const newFileName = this.buildForumBFileName(downloadContext.postData, originalFileName, downloadContext.attachmentIndex);
            const targetPath = path.join(downloadContext.attachmentsSubDir, newFileName);

            this.log.info(`[${this.forumName}] 📝 文件重命名: ${originalFileName} -> ${newFileName} (ID: ${downloadId})`);
            this.log.info(`[${this.forumName}] 📁 目标路径: ${targetPath}`);

            // 保存到指定位置
            await download.saveAs(targetPath);

            this.log.info(`[${this.forumName}] ✅ 下载完成: ${newFileName} (ID: ${downloadId})`);
            this.log.info(`[${this.forumName}] ✅ 文件已保存到: ${targetPath}`);

            // 🔧 修复：使用下载上下文中的正确帖子URL更新数据库状态
            if (this.databaseService) {
              this.log.info(`[${this.forumName}] 📊 更新数据库状态: ${downloadContext.postData.postUrl} (ID: ${downloadId})`);
              this.databaseService.updateDownloadStatus(downloadContext.postData.postUrl, 'completed', targetPath);
            }

            resolve(true);
          } catch (downloadError) {
            this.log.error(`[${this.forumName}] ❌ 下载处理失败: ${downloadError.message} (ID: ${downloadId})`);
            resolve(true); // 即使处理失败，也算下载成功
          }
        };

        // 🔧 绑定下载监听器
        page.on('download', downloadHandler);
        this.log.info(`[${this.forumName}] 🎧 已绑定下载监听器 (ID: ${downloadId})`);
      });

      // 🖱️ 点击附件链接触发下载
      this.log.info(`[${this.forumName}] 🖱️ 点击附件链接触发下载...`);

      try {
        await attachmentElement.click();
        this.log.info(`[${this.forumName}] ✅ 附件链接已点击`);
      } catch (clickError) {
        this.log.error(`[${this.forumName}] ❌ 点击失败: ${clickError.message}`);
        return false;
      }

      // 等待下载完成或超时
      const result = await downloadPromise;
      return result;

    } catch (error) {
      this.log.error(`[${this.forumName}] ❌ 下载附件失败: ${error.message}`);

      // 按照用户要求：点击了就算下载了
      if (error.message.includes('已点击') || error.message.includes('点击')) {
        return true;
      }

      return false;
    }
  }






  /**
   * 🔧 获取默认下载路径
   */
  getDefaultDownloadPath() {
    const os = require('os');
    const path = require('path');
    return path.join(os.homedir(), 'Downloads');
  }

  /**
   * 🔧 获取下载文件夹中的文件列表
   */
  getDownloadFolderFiles() {
    const fs = require('fs');
    const downloadPath = this.getDefaultDownloadPath();

    try {
      if (!fs.existsSync(downloadPath)) {
        return [];
      }

      return fs.readdirSync(downloadPath).filter(file => {
        const filePath = require('path').join(downloadPath, file);
        return fs.statSync(filePath).isFile();
      });
    } catch (error) {
      this.log.warn(`[${this.forumName}] 获取下载文件夹文件列表失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 🔧 98堂专用：构建文件名 - 使用帖子标题+附件名称
   */
  buildForumBFileName(postData, originalFileName, attachmentIndex = 0) {
    const path = require('path');

    // 🔧 解决中文文件名乱码问题
    let decodedFileName = originalFileName;
    try {
      // 尝试解码URL编码的文件名
      if (originalFileName.includes('%')) {
        decodedFileName = decodeURIComponent(originalFileName);
      }

      // 尝试处理可能的编码问题
      if (decodedFileName.includes('\\u')) {
        decodedFileName = JSON.parse(`"${decodedFileName}"`);
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 文件名解码失败，使用原始文件名: ${error.message}`);
      decodedFileName = originalFileName;
    }

    // 获取原始文件扩展名
    const ext = path.extname(decodedFileName);

    // 🔧 清理帖子标题，移除论坛信息和特殊字符
    let cleanTitle = postData.postTitle
      // 移除论坛相关信息
      .replace(/\s*-\s*98堂.*$/i, '') // 移除 "- 98堂[原色花堂] - Powered by..." 等
      .replace(/\s*-\s*网友原创区.*$/i, '') // 移除 "- 网友原创区- 98堂..." 等
      .replace(/\s*-\s*Powered\s+by.*$/i, '') // 移除 "- Powered by Discuz!" 等
      .replace(/\s*\[原色花堂\].*$/i, '') // 移除 "[原色花堂]" 等
      // 移除Windows不允许的字符
      .replace(/[<>:"/\\|?*]/g, '')
      // 合并多个空格和连字符
      .replace(/\s+/g, ' ')
      .replace(/-+/g, '-')
      .replace(/\s*-\s*$/, '') // 移除末尾的连字符
      .trim();

    // 限制帖子标题长度
    if (cleanTitle.length > 80) {
      cleanTitle = cleanTitle.substring(0, 80).trim();
    }

    // 🔧 清理原始文件名，处理中文乱码和特殊字符
    let cleanOriginalName = decodedFileName
      .replace(ext, '') // 移除扩展名
      // 移除www.98T.la@前缀
      .replace(/^www\.98T\.la@/, '')
      // 移除其他网站标识
      .replace(/^[a-zA-Z0-9]+\.[a-zA-Z]+@/, '')
      // 移除Windows不允许的字符
      .replace(/[<>:"/\\|?*]/g, '')
      // 合并多个空格
      .replace(/\s+/g, ' ')
      .trim();

    // 限制附件名称长度
    if (cleanOriginalName.length > 50) {
      cleanOriginalName = cleanOriginalName.substring(0, 50).trim();
    }

    this.log.info(`[${this.forumName}] 📝 文件名处理: 原始="${originalFileName}" -> 解码="${decodedFileName}" -> 清理="${cleanOriginalName}"`);

    // 如果标题为空，使用清理后的原始文件名
    if (!cleanTitle) {
      return cleanOriginalName ? `${cleanOriginalName}${ext}` : decodedFileName;
    }

    // 如果附件名称为空，只使用帖子标题
    if (!cleanOriginalName) {
      return `${cleanTitle}${ext}`;
    }

    // 使用"帖子名称 - 附件名称"的格式
    return `${cleanTitle} - ${cleanOriginalName}${ext}`;
  }

  /**
   * 🔧 重写：检查并补充排序参数
   */
  async scrapeMultiplePages(page, siteProfile, options = {}) {
    // 检查URL是否包含排序参数
    const currentUrl = page.url();
    const requiredParams = '&filter=author&orderby=dateline';

    if (!currentUrl.includes(requiredParams)) {
      this.log.info(`[${this.forumName}] 🔧 检测到URL缺少排序参数，正在补充...`);

      // 构建新的URL
      const separator = currentUrl.includes('?') ? '&' : '?';
      const newUrl = currentUrl + separator + 'filter=author&orderby=dateline';

      this.log.info(`[${this.forumName}] 🔧 重定向到: ${newUrl}`);

      // 导航到新URL
      await page.goto(newUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
      await page.waitForTimeout(2000);
    }

    // 调用父类方法
    return await super.scrapeMultiplePages(page, siteProfile, options);
  }
}

module.exports = ForumBCollector;