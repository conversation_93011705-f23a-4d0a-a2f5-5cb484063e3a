
import 'react'; // 导入 React 以进行模块增强

// 原始的 '/// <reference types="vite/client" />' 在第1行导致了错误:
// "Cannot find type definition file for 'vite/client'." (找不到 'vite/client' 的类型定义文件。)
// 此引用已被移除/注释以解决报告的错误。
// 如果使用了 Vite 客户端特性 (如 import.meta.env)，它们的 TypeScript 类型可能不可用。
// 这可能表明 Vite 安装或 TypeScript 配置 (例如 tsconfig.json `types` 数组) 存在问题。

// 添加 styled-jsx 的类型定义
// 这允许使用 <style jsx>{`...`}</style> 和 <style jsx global>{`...`}</style>
// 修正: 从全局 JSX 命名空间增强更改为 React 模块增强
// 这通常是扩展 React 自身类型定义的一种更稳健的方式。
declare module 'react' {
  interface StyleHTMLAttributes<T> extends React.HTMLAttributes<T> {
    jsx?: boolean;
    global?: boolean;
  }
}

// 添加 'export {};' 以确保此文件被视为一个模块，从而允许全局增强。
export {};