import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation, useNavigate } from 'react-router-dom';

// Pages
import DashboardPage from './pages/DashboardPage';
import LibraryPage from './pages/LibraryPage';
import FavoritesPage from './pages/FavoritesPage';
import StagingAreaPage from './pages/StagingAreaPage';
import RecycleBinPage from './pages/RecycleBinPage';
import ActorsPage from './pages/ActorsPage';
import IntelligenceCenterPage from './components/IntelligenceCenterPage';
import CollectorPage from './components/CollectorPage';
import { SettingsPage } from './components/SettingsPage';

// Layout Components
import AppHeader from './components/layout/AppHeader';

// Hooks
import { useAppSettings } from './hooks/useAppSettings';
import { usePrivacyManager } from './hooks/usePrivacyManager';
import { useScanStatus } from './hooks/useScanStatus';

// Modals
import PrivacyUnlockModal from './components/modals/PrivacyUnlockModal';
import AdvancedFilterModal from './components/AdvancedFilterModal';
import LinLuoSummoningModal from './components/LinLuoSummoningModal';
import RecommendationsModal from './components/modals/RecommendationsModal';

// Tools
import NfoPlotPolisherTool from './components/tools/NfoPlotPolisherTool';
import ScraperTestTool from './components/tools/ScraperTestTool';

// Types
import { AppView } from './hooks/useAppView';
import { Movie } from './types';

const AppRouter: React.FC = () => {
  return (
    <Router>
      <AppContent />
    </Router>
  );
};

const AppContent: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // 应用设置
  const { appSettings, isLoading: isSettingsLoading, saveSettings: updateSettings } = useAppSettings();

  // 隐私模式管理
  const { isPrivacyLocked, handlePrivacyUnlock, privacyState, isPrivacyUnlockedThisSession, handleTogglePrivacyLockIcon } = usePrivacyManager(appSettings);

  // 扫描管理
  const { isScanning } = useScanStatus({
    onScanCompleteCallback: (data) => {
      console.log('扫描完成:', data);
    }
  });

  // 简化的库管理状态
  const [activeLibrary, setActiveLibrary] = useState<any>(null);

  // 模态框状态
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isLinLuoModalOpen, setIsLinLuoModalOpen] = useState(false);
  const [isRecommendationsModalOpen, setIsRecommendationsModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [settingsInitialTab, setSettingsInitialTab] = useState<string>('general');

  // 搜索状态
  const [searchTerm, setSearchTerm] = useState('');

  // 推荐相关状态
  const [recommendedMovies, setRecommendedMovies] = useState<Movie[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const [linLuoFormattedRecommendation, setLinLuoFormattedRecommendation] = useState<string | null>(null);
  const [isFormattingRecommendation, setIsFormattingRecommendation] = useState(false);

  // 筛选器相关状态
  const [currentFilters, setCurrentFilters] = useState<any>({});
  const [availableGenres, setAvailableGenres] = useState<string[]>([]);
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  // 工具箱相关状态
  const [isNfoPlotPolisherOpen, setIsNfoPlotPolisherOpen] = useState(false);
  const [isScraperTestOpen, setIsScraperTestOpen] = useState(false);

  // 导航处理函数
  const handleNavigation = (path: string) => {
    navigate(path);
  };

  // 扫描处理函数
  const handleTriggerScan = async () => {
    try {
      if (activeLibrary) {
        // 触发特定库的扫描
        const result = await window.sfeElectronAPI.scanLibrary(activeLibrary.id);
        if (!result.success) {
          console.error('扫描失败:', result.error);
        }
      } else {
        // 触发全局扫描
        const result = await window.sfeElectronAPI.scanAllLibraries();
        if (!result.success) {
          console.error('扫描失败:', result.error);
        }
      }
    } catch (error) {
      console.error('扫描触发失败:', error);
    }
  };

  // 推荐处理函数
  const handleFetchRecommendations = async () => {
    setIsLoadingRecommendations(true);
    setLinLuoFormattedRecommendation(null);
    try {
      const params = { limitPerCategory: 5, maxTotal: 20 };
      const result = await window.sfeElectronAPI.getInitialRecommendations(params);
      if (result.success && result.recommendedMovies) {
        setRecommendedMovies(result.recommendedMovies);
      } else {
        setRecommendedMovies([]);
        console.error('获取推荐失败:', result.error);
      }
    } catch (error) {
      console.error('获取推荐失败:', error);
      setRecommendedMovies([]);
    }
    setIsLoadingRecommendations(false);
  };

  const handleFormatRecommendationForLinLuo = async () => {
    if (!recommendedMovies || recommendedMovies.length === 0) return;
    setIsFormattingRecommendation(true);
    try {
      const result = await window.sfeElectronAPI.formatRecommendationsAsAiMessage({movies: recommendedMovies});
      if(result.success && result.formattedMessage){
        setLinLuoFormattedRecommendation(result.formattedMessage);
      } else {
        console.error('格式化推荐语失败:', result.error);
        setLinLuoFormattedRecommendation(null);
      }
    } catch (error) {
      console.error('格式化推荐语失败:', error);
      setLinLuoFormattedRecommendation(null);
    }
    setIsFormattingRecommendation(false);
  };

  // 简单的影片卡片点击处理
  const handleCardClick = (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => {
    console.log('影片卡片点击:', movie.title);
    // TODO: 实现影片详情显示逻辑
  };

  // 筛选器处理函数
  const handleApplyFilters = (filters: any) => {
    setCurrentFilters(filters);
    setIsFilterModalOpen(false);
    console.log('应用筛选器:', filters);
    // TODO: 实现筛选逻辑
  };

  // 工具箱功能处理函数
  const handleLaunchNfoPlotPolisher = () => {
    console.log('启动 NFO 剧情简介 AI 润色工具...');
    setIsNfoPlotPolisherOpen(true);
  };

  const handleLaunchScraperTest = () => {
    console.log('启动刮削器测试工具...');
    setIsScraperTestOpen(true);
  };

  // 图片可见性切换
  const handleToggleImageVisibility = async () => {
    const newSettings = {
      ...appSettings,
      imagesGloballyVisible: !appSettings.imagesGloballyVisible
    };

    const result = await updateSettings(newSettings);
    if (!result.success) {
      console.error('更新图片可见性设置失败:', result.error);
    }
  };

  // 将路径转换为AppView（为了兼容现有的AppHeader）
  const getAppViewFromPath = (pathname: string): AppView => {
    switch (pathname) {
      case '/':
        return 'dashboard' as AppView; // 主页使用dashboard视图
      case '/library':
        return 'mainWall';
      case '/intelligence':
        return 'intelligenceCenter';
      case '/collector':
        return 'collectorView';
      case '/settings':
        return 'dashboard' as AppView; // 设置页面使用dashboard视图
      default:
        return 'dashboard' as AppView;
    }
  };

  const currentView = getAppViewFromPath(location.pathname);

  // 如果设置还在加载，显示加载界面
  if (isSettingsLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <p>正在加载应用设置...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* 隐私模式解锁模态框 */}
      <PrivacyUnlockModal
        isOpen={isPrivacyLocked}
        onUnlock={handlePrivacyUnlock}
      />

      {/* 主应用内容 */}
      {!isPrivacyLocked && (
        <>
          {/* 主导航栏 - 始终显示 */}
          <AppHeader
            currentAppView={currentView}
            onSetCurrentAppView={(view) => {
              // 根据AppView导航到对应路径
              switch (view) {
                case 'mainWall':
                  handleNavigation('/library');
                  break;
                case 'intelligenceCenter':
                  handleNavigation('/intelligence');
                  break;
                case 'collectorView':
                  handleNavigation('/collector');
                  break;
                case 'favoritesView':
                  handleNavigation('/favorites');
                  break;
                case 'stagingArea':
                  handleNavigation('/staging');
                  break;
                case 'recycleBin':
                  handleNavigation('/recycle-bin');
                  break;
                default:
                  handleNavigation('/');
              }
            }}
            searchTerm={searchTerm}
            onSearchTermChange={setSearchTerm}
            onTriggerScan={handleTriggerScan}
            isScanning={isScanning}
            onOpenFilterModal={() => setIsFilterModalOpen(true)}
            onOpenLinLuoModal={() => setIsLinLuoModalOpen(true)}
            onOpenSettingsModal={(initialTab) => {
              setSettingsInitialTab(initialTab || 'general');
              setIsSettingsModalOpen(true);
            }}
            onOpenRecommendationsModal={() => {
              setIsRecommendationsModalOpen(true);
              handleFetchRecommendations();
            }}
            privacyState={privacyState}
            isPrivacyUnlockedThisSession={isPrivacyUnlockedThisSession}
            onTogglePrivacyLockIcon={handleTogglePrivacyLockIcon}
            appSettings={appSettings}
            onUpdateSettings={updateSettings}
            onNavigateHome={() => handleNavigation('/')}
          />

          {/* LibraryToolbar 现在由 LibraryPage 内部管理 */}

          {/* 路由内容 */}
          <Routes>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/library" element={<LibraryPage />} />
            <Route path="/favorites" element={<FavoritesPage />} />
            <Route path="/staging" element={<StagingAreaPage />} />
            <Route path="/recycle-bin" element={<RecycleBinPage />} />
            <Route path="/actors" element={<ActorsPage />} />
            <Route path="/intelligence" element={<IntelligenceCenterPage />} />
            <Route path="/collector" element={<CollectorPage onMovieDataChanged={() => {}} />} />
            <Route
              path="/settings"
              element={
                <SettingsPage
                  isOpen={true}
                  onClose={() => handleNavigation('/')}
                  currentSettings={appSettings}
                  onSettingsSaved={(newSettings) => {
                    updateSettings(newSettings);
                    handleNavigation('/');
                  }}
                  onLaunchNfoPlotPolisher={handleLaunchNfoPlotPolisher}
                  onLaunchScraperTest={handleLaunchScraperTest}
                />
              }
            />
          </Routes>

          {/* 模态框 */}
          <AdvancedFilterModal
            isOpen={isFilterModalOpen}
            onClose={() => setIsFilterModalOpen(false)}
            onApplyFilters={handleApplyFilters}
            currentFilters={currentFilters}
            availableGenres={availableGenres}
            availableTags={availableTags}
          />

          <LinLuoSummoningModal
            isOpen={isLinLuoModalOpen}
            onClose={() => setIsLinLuoModalOpen(false)}
          />

          <RecommendationsModal
            isOpen={isRecommendationsModalOpen}
            onClose={() => setIsRecommendationsModalOpen(false)}
            movies={recommendedMovies}
            isLoading={isLoadingRecommendations}
            appDefaultCover={appSettings.customDefaultCoverDataUrl}
            onMovieClick={handleCardClick}
            onFormatForLinLuo={handleFormatRecommendationForLinLuo}
            linLuoFormattedMessage={linLuoFormattedRecommendation}
            isFormatting={isFormattingRecommendation}
            onSendToLinLuo={(message) => {
              setLinLuoFormattedRecommendation(message);
              setIsRecommendationsModalOpen(false);
              setIsLinLuoModalOpen(true);
            }}
          />

          {/* 设置模态框 - 使用路由而不是模态框 */}
          {isSettingsModalOpen && (
            <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
              <div className="bg-gray-800 rounded-lg w-full max-w-4xl h-full max-h-[90vh] overflow-hidden">
                <SettingsPage
                  isOpen={true}
                  onClose={() => setIsSettingsModalOpen(false)}
                  currentSettings={appSettings}
                  onSettingsSaved={(newSettings) => {
                    updateSettings(newSettings);
                    setIsSettingsModalOpen(false);
                  }}
                  onLaunchNfoPlotPolisher={handleLaunchNfoPlotPolisher}
                  onLaunchScraperTest={handleLaunchScraperTest}
                  initialTab={settingsInitialTab}
                />
              </div>
            </div>
          )}

          {/* 工具组件 */}
          <NfoPlotPolisherTool
            isOpen={isNfoPlotPolisherOpen}
            onClose={() => setIsNfoPlotPolisherOpen(false)}
          />

          <ScraperTestTool
            isOpen={isScraperTestOpen}
            onClose={() => setIsScraperTestOpen(false)}
          />
        </>
      )}
    </div>
  );
};

export default AppRouter;
