// main_process/services/aiCategoryService.js

let log;
let db;

/**
 * 初始化AI分类服务
 * @param {Object} logger - 日志记录器
 * @param {Object} database - 数据库实例
 */
function initializeAiCategoryService(logger, database) {
  log = logger;
  db = database;
  log.info('[AI分类服务] 初始化完成');
}

/**
 * 获取所有分类标签
 * @returns {Array} 分类标签数组
 */
function getAllCategories() {
  try {
    const categories = db.prepare('SELECT * FROM ai_categories ORDER BY name ASC').all();
    log.info(`[AI分类服务] 获取所有分类标签，共 ${categories.length} 个`);
    return categories;
  } catch (error) {
    log.error(`[AI分类服务] 获取分类标签失败: ${error.message}`);
    return [];
  }
}

/**
 * 添加新的分类标签
 * @param {string} name - 分类标签名称
 * @returns {Object} 操作结果
 */
function addCategory(name) {
  try {
    if (!name || name.trim().length === 0) {
      return { success: false, error: '分类名称不能为空' };
    }

    const trimmedName = name.trim();
    const result = db.prepare('INSERT INTO ai_categories (name) VALUES (?)').run(trimmedName);
    
    log.info(`[AI分类服务] 添加分类标签成功: ${trimmedName}, ID: ${result.lastInsertRowid}`);
    return { 
      success: true, 
      id: result.lastInsertRowid,
      message: '分类标签添加成功'
    };
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      log.warn(`[AI分类服务] 分类标签已存在: ${name}`);
      return { success: false, error: '该分类标签已存在' };
    }
    
    log.error(`[AI分类服务] 添加分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 更新分类标签
 * @param {number} id - 分类标签ID
 * @param {string} newName - 新的分类标签名称
 * @returns {Object} 操作结果
 */
function updateCategory(id, newName) {
  try {
    if (!newName || newName.trim().length === 0) {
      return { success: false, error: '分类名称不能为空' };
    }

    const trimmedName = newName.trim();
    const result = db.prepare('UPDATE ai_categories SET name = ? WHERE id = ?').run(trimmedName, id);
    
    if (result.changes > 0) {
      log.info(`[AI分类服务] 更新分类标签成功: ID ${id}, 新名称: ${trimmedName}`);
      return { success: true, message: '分类标签更新成功' };
    } else {
      log.warn(`[AI分类服务] 未找到要更新的分类标签: ID ${id}`);
      return { success: false, error: '分类标签不存在' };
    }
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      log.warn(`[AI分类服务] 分类标签名称已存在: ${newName}`);
      return { success: false, error: '该分类标签名称已存在' };
    }
    
    log.error(`[AI分类服务] 更新分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 删除分类标签
 * @param {number} id - 分类标签ID
 * @returns {Object} 操作结果
 */
function deleteCategory(id) {
  try {
    const result = db.prepare('DELETE FROM ai_categories WHERE id = ?').run(id);
    
    if (result.changes > 0) {
      log.info(`[AI分类服务] 删除分类标签成功: ID ${id}`);
      return { success: true, message: '分类标签删除成功' };
    } else {
      log.warn(`[AI分类服务] 未找到要删除的分类标签: ID ${id}`);
      return { success: false, error: '分类标签不存在' };
    }
  } catch (error) {
    log.error(`[AI分类服务] 删除分类标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 获取分类标签名称数组（供AI分析使用）
 * @returns {string[]} 分类标签名称数组
 */
function getCategoryNames() {
  try {
    const categories = db.prepare('SELECT name FROM ai_categories ORDER BY name ASC').all();
    const names = categories.map(cat => cat.name);
    log.info(`[AI分类服务] 获取分类标签名称数组，共 ${names.length} 个`);
    return names;
  } catch (error) {
    log.error(`[AI分类服务] 获取分类标签名称失败: ${error.message}`);
    return [];
  }
}

/**
 * 检查分类标签是否存在
 * @param {string} name - 分类标签名称
 * @returns {boolean} 是否存在
 */
function categoryExists(name) {
  try {
    const result = db.prepare('SELECT COUNT(*) as count FROM ai_categories WHERE name = ?').get(name.trim());
    return result.count > 0;
  } catch (error) {
    log.error(`[AI分类服务] 检查分类标签是否存在失败: ${error.message}`);
    return false;
  }
}

module.exports = {
  initializeAiCategoryService,
  getAllCategories,
  addCategory,
  updateCategory,
  deleteCategory,
  getCategoryNames,
  categoryExists
};
