// main_process/services/scrapers/avhelpwikiProvider.js
const { getBrowser } = require('../browserManager');
const { extractVrTrailerUrl } = require('../dmmVrTrailerExtractor');
const log = require('electron-log');

/**
 * AV Help Wiki Provider - 从 av-help.memo.wiki 获取每日更新的番号列表
 * 这个 Provider 不遵循 IScraperProvider 接口，因为它不刮削详情，只发现目标
 */

/**
 * 从 AV Help Wiki 获取指定日期的番号列表
 * @param {Date|string} targetDate - 目标日期，Date对象或YYYY-MM-DD格式字符串
 * @returns {Promise<string[]>} 去重后的番号字符串数组
 */
async function fetchNfoIdsForDate(targetDate) {
    const browser = await getBrowser();
    const page = await browser.newPage();
    const url = 'https://av-help.memo.wiki/';

    // 处理日期参数
    let dateObj;
    if (typeof targetDate === 'string') {
        dateObj = new Date(targetDate);
    } else if (targetDate instanceof Date) {
        dateObj = targetDate;
    } else {
        throw new Error('targetDate 必须是 Date 对象或 YYYY-MM-DD 格式的字符串');
    }

    if (isNaN(dateObj.getTime())) {
        throw new Error('无效的日期格式');
    }

    const dateStr = formatDateForDisplay(dateObj);
    log.info(`[AVHelpWiki] 开始获取 ${dateStr} 的番号列表...`);

    try {
        // 设置用户代理
        await page.setExtraHTTPHeaders({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7'
        });

        await page.goto(url, {
            timeout: 30000,
            waitUntil: 'networkidle'
        });

        log.info(`[AVHelpWiki] 页面加载完成，开始查找 ${dateStr} 的更新链接...`);

        // 查找指定日期的FANZA新作链接
        const targetLinks = await findFanzaLinksForDate(page, dateObj);

        if (targetLinks.length === 0) {
            log.warn(`[AVHelpWiki] 未找到 ${dateStr} 的FANZA新作链接`);
            return [];
        }

        log.info(`[AVHelpWiki] 找到 ${targetLinks.length} 个 ${dateStr} 的更新链接`);

        const allNfoIds = new Set();

        // 访问每个目标日期的更新页面并提取番号
        for (const link of targetLinks) {
            try {
                log.info(`[AVHelpWiki] 正在访问: ${link}`);

                await page.goto(link, {
                    timeout: 30000,
                    waitUntil: 'networkidle'
                });

                // 从页面内容中提取番号
                const nfoIds = await extractNfoIdsFromPage(page);

                log.info(`[AVHelpWiki] 从 ${link} 提取到 ${nfoIds.length} 个番号`);

                // 添加到总集合中
                nfoIds.forEach(id => allNfoIds.add(id));

                // 添加延迟避免请求过快
                await page.waitForTimeout(2000);

            } catch (error) {
                log.error(`[AVHelpWiki] 访问链接失败: ${link}`, error.message);
            }
        }

        const result = Array.from(allNfoIds);
        log.info(`[AVHelpWiki] ${dateStr} 总共发现 ${result.length} 个唯一番号`);

        return result;

    } catch (error) {
        log.error(`[AVHelpWiki] 获取 ${dateStr} 番号列表失败:`, error.message);
        throw error;
    } finally {
        await page.close();
    }
}

/**
 * 从 AV Help Wiki 获取今日更新的番号列表（向后兼容接口）
 * @returns {Promise<string[]>} 去重后的番号字符串数组
 */
async function fetchDailyNfoIds() {
    log.info('[AVHelpWiki] 调用向后兼容接口，获取今日简报...');
    return await fetchNfoIdsForDate(new Date());
}

/**
 * 查找指定日期的FANZA新作链接
 * @param {Page} page - Playwright页面对象
 * @param {Date} targetDate - 目标日期对象
 * @returns {Promise<string[]>} 指定日期的更新链接数组
 */
async function findFanzaLinksForDate(page, targetDate) {
    try {
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1;
        const day = targetDate.getDate();

        // 获取星期几（日文）
        const weekdays = ['日', '月', '火', '水', '木', '金', '土'];
        const weekday = weekdays[targetDate.getDay()];

        // 构建目标日期的多种可能格式
        const datePatterns = [
            `${year}年${month}月${day}日(${weekday})`,
            `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日(${weekday})`,
            `${year}年${month}月${day.toString().padStart(2, '0')}日(${weekday})`,
            `${year}年${month.toString().padStart(2, '0')}月${day}日(${weekday})`,
            // 不带星期的格式
            `${year}年${month}月${day}日`,
            `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`,
            `${year}年${month}月${day.toString().padStart(2, '0')}日`,
            `${year}年${month.toString().padStart(2, '0')}月${day}日`
        ];

        const dateStr = formatDateForDisplay(targetDate);
        log.info(`[AVHelpWiki] 查找 ${dateStr} 的日期模式: ${datePatterns.slice(0, 4).join(', ')}`);

        // 查找包含目标日期的FANZA新作链接
        const links = await page.evaluate((patterns) => {
            const foundLinks = [];
            const allLinks = document.querySelectorAll('a');

            for (const link of allLinks) {
                const linkText = link.textContent || '';
                const href = link.href || '';

                // 检查链接文本是否包含目标日期
                for (const pattern of patterns) {
                    if (linkText.includes(pattern) &&
                        linkText.includes('FANZA') &&
                        linkText.includes('新作')) {
                        foundLinks.push(href);
                        break;
                    }
                }
            }

            return foundLinks;
        }, datePatterns);

        if (links.length > 0) {
            log.info(`[AVHelpWiki] 找到 ${links.length} 个 ${dateStr} 的精确匹配链接`);
            return links;
        }

        // 如果没有找到精确匹配，尝试模糊匹配
        log.info(`[AVHelpWiki] 未找到 ${dateStr} 的精确匹配，尝试模糊匹配...`);

        const fuzzyLinks = await page.evaluate((patterns) => {
            const foundLinks = [];
            const allLinks = document.querySelectorAll('a');

            for (const link of allLinks) {
                const linkText = link.textContent || '';
                const href = link.href || '';

                // 模糊匹配：只检查年月日，不检查星期
                for (let i = 4; i < patterns.length; i++) { // 从不带星期的格式开始
                    const pattern = patterns[i];
                    if (linkText.includes(pattern) &&
                        linkText.includes('FANZA') &&
                        linkText.includes('新作')) {
                        foundLinks.push(href);
                        break;
                    }
                }
            }

            return foundLinks;
        }, datePatterns);

        if (fuzzyLinks.length > 0) {
            log.info(`[AVHelpWiki] 找到 ${fuzzyLinks.length} 个 ${dateStr} 的模糊匹配链接`);
            return fuzzyLinks;
        }

        log.warn(`[AVHelpWiki] 未找到 ${dateStr} 的任何匹配链接`);
        return [];

    } catch (error) {
        log.error(`[AVHelpWiki] 查找指定日期链接失败:`, error.message);
        return [];
    }
}

/**
 * 从页面内容中提取番号
 * @param {Page} page - Playwright页面对象
 * @returns {Promise<string[]>} 提取到的番号数组
 */
async function extractNfoIdsFromPage(page) {
    try {
        // 等待页面内容加载
        await page.waitForTimeout(1000);
        
        // 从页面中提取所有可能的番号
        const nfoIds = await page.evaluate(() => {
            const foundIds = new Set();

            // 番号正则表达式 - 匹配常见的AV番号格式
            const nfoIdRegex = /\b[A-Z]{2,6}-?\d{3,5}\b/gi;

            // 在浏览器环境中定义验证函数
            function isValidNfoIdInBrowser(nfoId) {
                // 基本格式检查
                if (!nfoId || typeof nfoId !== 'string') return false;

                // 长度检查
                if (nfoId.length < 5 || nfoId.length > 15) return false;

                // 格式检查：字母-数字
                if (!/^[A-Z]{2,6}-\d{3,5}$/.test(nfoId)) return false;

                // 检查是否是已知的有效前缀
                const validPrefixes = [
                    'SSIS', 'SSNI', 'STARS', 'STAR', 'SOE', 'SONE',
                    'JUFE', 'JUFD', 'JULIA', 'JUL', 'JUQ', 'JUX', 'JUY',
                    'PRED', 'PGD', 'PPPD', 'PPSD',
                    'WAAA', 'WANZ', 'WAA',
                    'MIDE', 'MIAD', 'MIAE', 'MIAA', 'MIAB', 'MIDA', 'MIDV', 'MIMK', 'MIRD', 'MIZD',
                    'CAWD', 'CJOD', 'CESD',
                    'EBOD', 'EYAN', 'EKDV',
                    'FSDSS', 'FLAV',
                    'SAME', 'SHKD', 'SSPD', 'ATID', 'ADN', 'RBK',
                    'VEC', 'VENX', 'VENU',
                    'DASS', 'DASD',
                    'MVSD', 'MVBD',
                    'PJAM', 'PJAB',
                    'ROYD', 'RCTD',
                    'HUNTB', 'HUNTA',
                    'DVDMS', 'DVMM',
                    'IENF', 'IENE',
                    'SCOP', 'SCPX',
                    'NHDTB', 'NHDTA',
                    'NPJS', 'NKKD', 'NGOD', 'NDRA',
                    'TIKB', 'TIKP', 'TIKF',
                    'YMDD', 'YMDS',
                    'PARATHD',
                    'STOL', 'SAVR', 'KIWVR', 'JUVR', 'BEBL', 'AQUMAM', 'AQUCO', 'AQUBL',
                    'VOSM', 'VOD', 'VEQ',
                    'MIKR', 'MANX', 'MAGNVD', 'LULU',
                    'GAS', 'DROP', 'DOKS', 'DHLD',
                    'VAIAV', 'SVCAO', 'SEVEN', 'NSBB', 'DRPT', 'BQBB', 'AIAV',
                    'AXDVD', 'YUJ'
                ];

                const prefix = nfoId.split('-')[0];
                return validPrefixes.includes(prefix);
            }

            // 从页面文本中提取
            const pageText = document.body.textContent || '';
            const matches = pageText.match(nfoIdRegex);

            if (matches) {
                matches.forEach(match => {
                    // 清理和标准化番号格式
                    let cleanId = match.toUpperCase().trim();

                    // 确保有连字符
                    if (!/[A-Z]+-\d+/.test(cleanId)) {
                        // 在字母和数字之间添加连字符
                        cleanId = cleanId.replace(/([A-Z]+)(\d+)/, '$1-$2');
                    }

                    // 验证番号格式
                    if (isValidNfoIdInBrowser(cleanId)) {
                        foundIds.add(cleanId);
                    }
                });
            }

            // 从链接和图片alt属性中提取
            const links = document.querySelectorAll('a, img');
            links.forEach(element => {
                const text = element.textContent || element.alt || element.title || '';
                const matches = text.match(nfoIdRegex);

                if (matches) {
                    matches.forEach(match => {
                        let cleanId = match.toUpperCase().trim();
                        if (!/[A-Z]+-\d+/.test(cleanId)) {
                            cleanId = cleanId.replace(/([A-Z]+)(\d+)/, '$1-$2');
                        }
                        if (isValidNfoIdInBrowser(cleanId)) {
                            foundIds.add(cleanId);
                        }
                    });
                }
            });

            return Array.from(foundIds);
        });

        // 提取预告片链接
        const trailerUrls = await extractTrailerUrls(page);

        log.debug(`[AVHelpWiki] 页面提取到 ${nfoIds.length} 个有效番号，${trailerUrls.length} 个预告片链接`);
        
        // 在Node.js环境中进行额外过滤
        const filteredIds = nfoIds.filter(id => {
            // 过滤掉明显不是番号的内容
            if (id.length < 5 || id.length > 15) return false;
            if (!/^[A-Z]{2,6}-\d{3,5}$/.test(id)) return false;
            
            // 过滤掉一些常见的误识别
            const excludePatterns = [
                /^ID-\d+$/,      // ID-xxx 通常不是番号
                /^NO-\d+$/,      // NO-xxx 通常不是番号
                /^TV-\d+$/,      // TV-xxx 通常不是番号
                /^DVD-\d+$/,     // DVD-xxx 通常不是番号
                /^CD-\d+$/,      // CD-xxx 通常不是番号
                /^VR-\d+$/       // VR-xxx 可能不是标准番号
            ];
            
            return !excludePatterns.some(pattern => pattern.test(id));
        });
        
        log.debug(`[AVHelpWiki] 页面提取到 ${filteredIds.length} 个有效番号`);
        return filteredIds;
        
    } catch (error) {
        log.error('[AVHelpWiki] 提取番号失败:', error.message);
        return [];
    }
}

/**
 * 格式化日期为显示用的字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD)
 */
function formatDateForDisplay(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 格式化日期为日文显示格式
 * @param {Date} date - 日期对象
 * @returns {string} 日文格式的日期字符串 (YYYY年M月D日)
 */
function formatDateForJapanese(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekdays = ['日', '月', '火', '水', '木', '金', '土'];
    const weekday = weekdays[date.getDay()];
    return `${year}年${month}月${day}日(${weekday})`;
}

/**
 * 从页面中提取预告片链接
 * @param {Page} page - Playwright页面对象
 * @returns {Promise<Array>} 预告片链接数组
 */
async function extractTrailerUrls(page) {
    try {
        const trailerUrls = await page.evaluate(() => {
            const trailers = [];

            // 根据用户提供的实际HTML格式查找预告片链接
            // 格式：<a href="https://cc3001.dmm.co.jp/litevideo/freepv/y/ymd/ymds00223/ymds00223hmb.mp4" target="_blank" class="outlink" rel="nofollow">

            // 1. 查找所有包含 .mp4 的链接
            const mp4Links = document.querySelectorAll('a[href*=".mp4"]');

            for (const link of mp4Links) {
                const href = link.href;

                // 检查是否是DMM预告片链接
                if (href.includes('dmm.co.jp') &&
                    (href.includes('freepv') || href.includes('litevideo'))) {

                    // 从URL路径中提取番号
                    // 例如：/freepv/y/ymd/ymds00223/ymds00223hmb.mp4
                    const pathMatch = href.match(/\/([a-z]+)(\d+)\/\1\2/i);
                    let nfoId = '';
                    if (pathMatch) {
                        nfoId = (pathMatch[1] + '-' + pathMatch[2]).toUpperCase();
                    } else {
                        // 备用提取方法
                        const fileMatch = href.match(/([a-z]+)(\d+)[a-z]*\.mp4/i);
                        if (fileMatch) {
                            nfoId = (fileMatch[1] + '-' + fileMatch[2]).toUpperCase();
                        }
                    }

                    if (nfoId) {
                        trailers.push({
                            url: href,
                            nfoId: nfoId,
                            linkText: link.textContent?.trim() || '',
                            source: 'dmm_freepv'
                        });
                    }
                }
            }

            // 2. 查找其他可能的预告片链接格式
            const otherLinks = document.querySelectorAll('a[href*="freepv"], a[href*="preview"], a[href*="trailer"]');

            for (const link of otherLinks) {
                const href = link.href;

                if (href.includes('.mp4')) {
                    // 尝试从链接中提取番号
                    const nfoIdMatch = href.match(/([a-z]+)[-_]?(\d+)/i);
                    let nfoId = '';
                    if (nfoIdMatch) {
                        nfoId = (nfoIdMatch[1] + '-' + nfoIdMatch[2]).toUpperCase();
                    }

                    if (nfoId) {
                        trailers.push({
                            url: href,
                            nfoId: nfoId,
                            linkText: link.textContent?.trim() || '',
                            source: 'other'
                        });
                    }
                }
            }

            // 3. 检查图片的父链接（用户提供的格式）
            const images = document.querySelectorAll('img');
            for (const img of images) {
                const parent = img.parentElement;
                if (parent && parent.tagName === 'A') {
                    const href = parent.href;
                    if (href && href.includes('.mp4') &&
                        (href.includes('freepv') || href.includes('dmm.co.jp'))) {

                        // 从URL中提取番号
                        const pathMatch = href.match(/\/([a-z]+)(\d+)\/\1\2/i);
                        let nfoId = '';
                        if (pathMatch) {
                            nfoId = (pathMatch[1] + '-' + pathMatch[2]).toUpperCase();
                        } else {
                            const fileMatch = href.match(/([a-z]+)(\d+)[a-z]*\.mp4/i);
                            if (fileMatch) {
                                nfoId = (fileMatch[1] + '-' + fileMatch[2]).toUpperCase();
                            }
                        }

                        if (nfoId) {
                            trailers.push({
                                url: href,
                                nfoId: nfoId,
                                linkText: img.alt || img.title || '',
                                source: 'image_parent'
                            });
                        }
                    }
                }
            }

            // 4. 查找DMM VR播放器页面链接
            const vrPlayerLinks = document.querySelectorAll('a[href*="/vr-sample-player/"], a[href*="vr-sample"], a[href*="vr_sample"]');

            for (const link of vrPlayerLinks) {
                const href = link.href;

                // 检查是否是DMM VR播放器页面
                if (href.includes('dmm.co.jp') &&
                    (href.includes('/vr-sample-player/') || href.includes('vr-sample') || href.includes('vr_sample'))) {

                    // 尝试从URL中提取番号
                    const nfoIdMatch = href.match(/([a-z]+)[-_]?(\d+)/i);
                    let nfoId = '';
                    if (nfoIdMatch) {
                        nfoId = (nfoIdMatch[1] + '-' + nfoIdMatch[2]).toUpperCase();
                    }

                    if (nfoId) {
                        trailers.push({
                            url: href,
                            nfoId: nfoId,
                            linkText: link.textContent?.trim() || '',
                            source: 'dmm_vr_player',
                            needsExtraction: true // 标记需要破译
                        });
                    }
                }
            }

            // 去重
            const uniqueTrailers = [];
            const seenUrls = new Set();

            for (const trailer of trailers) {
                if (!seenUrls.has(trailer.url)) {
                    seenUrls.add(trailer.url);
                    uniqueTrailers.push(trailer);
                }
            }

            return uniqueTrailers;
        });

        log.debug(`[AVHelpWiki] 提取到 ${trailerUrls.length} 个预告片链接`);

        // 处理需要破译的VR播放器链接
        const processedTrailers = [];
        for (const trailer of trailerUrls) {
            if (trailer.needsExtraction && trailer.source === 'dmm_vr_player') {
                log.info(`[AVHelpWiki] 发现VR播放器页面，开始破译: ${trailer.url}`);

                try {
                    const realVideoUrl = await extractVrTrailerUrl(trailer.url);

                    if (realVideoUrl) {
                        log.info(`[AVHelpWiki] VR预告片破译成功: ${trailer.nfoId} -> ${realVideoUrl}`);
                        processedTrailers.push({
                            ...trailer,
                            url: realVideoUrl, // 使用破译后的真实视频URL
                            originalPlayerUrl: trailer.url, // 保留原始播放器URL
                            source: 'dmm_vr_extracted',
                            needsExtraction: false
                        });
                    } else {
                        log.warn(`[AVHelpWiki] VR预告片破译失败: ${trailer.url}`);
                        // 破译失败时，仍然保留原始链接，但标记为失败
                        processedTrailers.push({
                            ...trailer,
                            source: 'dmm_vr_failed',
                            needsExtraction: false
                        });
                    }
                } catch (error) {
                    log.error(`[AVHelpWiki] VR预告片破译异常: ${trailer.url} - ${error.message}`);
                    // 异常时保留原始链接
                    processedTrailers.push({
                        ...trailer,
                        source: 'dmm_vr_error',
                        needsExtraction: false
                    });
                }
            } else {
                // 非VR播放器链接直接添加
                processedTrailers.push(trailer);
            }
        }

        log.debug(`[AVHelpWiki] 处理完成，最终返回 ${processedTrailers.length} 个预告片链接`);
        return processedTrailers;

    } catch (error) {
        log.error('[AVHelpWiki] 提取预告片链接失败:', error.message);
        return [];
    }
}

/**
 * 验证番号格式是否有效
 * @param {string} nfoId - 要验证的番号
 * @returns {boolean} 是否为有效番号
 */
function isValidNfoId(nfoId) {
    // 基本格式检查
    if (!nfoId || typeof nfoId !== 'string') return false;
    
    // 长度检查
    if (nfoId.length < 5 || nfoId.length > 15) return false;
    
    // 格式检查：字母-数字
    if (!/^[A-Z]{2,6}-\d{3,5}$/.test(nfoId)) return false;
    
    // 检查是否是已知的有效前缀
    const validPrefixes = [
        'SSIS', 'SSNI', 'STARS', 'STAR', 'SOE', 'SONE',
        'JUFE', 'JUFD', 'JULIA', 'JUL', 'JUQ', 'JUX', 'JUY',
        'PRED', 'PGD', 'PPPD', 'PPSD',
        'WAAA', 'WANZ', 'WAA',
        'MIDE', 'MIAD', 'MIAE', 'MIAA', 'MIAB', 'MIDA', 'MIDV', 'MIMK', 'MIRD', 'MIZD',
        'CAWD', 'CJOD', 'CESD',
        'EBOD', 'EYAN', 'EKDV',
        'FSDSS', 'FLAV',
        'SAME', 'SHKD', 'SSPD', 'ATID', 'ADN', 'RBK',
        'VEC', 'VENX', 'VENU',
        'DASS', 'DASD',
        'MVSD', 'MVBD',
        'PJAM', 'PJAB',
        'ROYD', 'RCTD',
        'HUNTB', 'HUNTA',
        'DVDMS', 'DVMM',
        'IENF', 'IENE',
        'SCOP', 'SCPX',
        'NHDTB', 'NHDTA',
        'NPJS', 'NKKD', 'NGOD', 'NDRA',
        'TIKB', 'TIKP', 'TIKF',
        'YMDD', 'YMDS',
        'PARATHD',
        'STOL', 'SAVR', 'KIWVR', 'JUVR', 'BEBL', 'AQUMAM', 'AQUCO', 'AQUBL',
        'VOSM', 'VOD', 'VEQ',
        'MIKR', 'MANX', 'MAGNVD', 'LULU',
        'GAS', 'DROP', 'DOKS', 'DHLD',
        'VAIAV', 'SVCAO', 'SEVEN', 'NSBB', 'DRPT', 'BQBB', 'AIAV',
        'AXDVD'
    ];
    
    const prefix = nfoId.split('-')[0];
    return validPrefixes.includes(prefix);
}

module.exports = {
    fetchNfoIdsForDate,
    fetchDailyNfoIds,
    findFanzaLinksForDate,
    extractNfoIdsFromPage,
    extractTrailerUrls,
    formatDateForDisplay,
    formatDateForJapanese,
    isValidNfoId
};
