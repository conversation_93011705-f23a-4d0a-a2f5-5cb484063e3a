// soul-forge-electron/src/components/layout/AppHeader.tsx
import React, { useRef, useState, useEffect } from 'react';
import { LuSearch, LuSettings, LuScanLine, LuSparkles, LuFilter, LuLock, LuLockOpen, LuThumbsUp, LuLayoutDashboard, LuEllipsis, LuBox, LuLink2, LuFileText, LuDownload, LuTrash2, LuRadar } from 'react-icons/lu';
import HeartIcon from '../icons/HeartIcon';
import EyeIcon from '../icons/EyeIcon'; 
import EyeSlashIcon from '../icons/EyeSlashIcon'; 
import { PrivacyModeState, AppSettings, SettingsResult } from '../../types'; 
import { AppView } from '../../hooks/useAppView';
import { TabKey as SettingsTabKey } from '../SettingsPage'; 


interface AppHeaderProps {
  currentAppView: AppView;
  onSetCurrentAppView: (view: AppView) => void;
  searchTerm: string;
  onSearchTermChange: (term: string) => void;
  onTriggerScan: () => void;
  isScanning: boolean;
  onOpenFilterModal: () => void;
  onOpenLinLuoModal: () => void;
  onOpenSettingsModal: (initialTab?: SettingsTabKey) => void;
  onOpenRecommendationsModal: () => void;
  privacyState: PrivacyModeState;
  isPrivacyUnlockedThisSession: boolean;
  onTogglePrivacyLockIcon: () => void;
  appSettings: AppSettings;
  onUpdateSettings: (settings: Partial<AppSettings>) => Promise<SettingsResult>;
  onNavigateHome: () => void;
}

const AppHeader: React.FC<AppHeaderProps> = ({
  currentAppView,
  onSetCurrentAppView,
  searchTerm,
  onSearchTermChange,
  onTriggerScan,
  isScanning,
  onOpenFilterModal,
  onOpenLinLuoModal,
  onOpenSettingsModal,
  onOpenRecommendationsModal,
  privacyState,
  isPrivacyUnlockedThisSession,
  onTogglePrivacyLockIcon,
  appSettings,
  onUpdateSettings,
  onNavigateHome,
}) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);
  const moreMenuRef = useRef<HTMLDivElement>(null);

  const handleToggleImageVisibility = async () => {
    try {
      console.log('切换图片可见性:', !appSettings.imagesGloballyVisible);
      const result = await onUpdateSettings({
        imagesGloballyVisible: !appSettings.imagesGloballyVisible,
      });
      if (result.success) {
        console.log('图片可见性设置更新成功');
      } else {
        console.error('图片可见性设置更新失败:', result.error);
        alert('设置更新失败: ' + result.error);
      }
    } catch (error) {
      console.error('切换图片可见性时出错:', error);
      alert('设置更新失败，请检查控制台错误信息');
    }
  };

  const handleMoreMenuAction = (action: () => void) => {
    action();
    setIsMoreMenuOpen(false);
  };
  
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (moreMenuRef.current && !moreMenuRef.current.contains(event.target as Node)) {
        setIsMoreMenuOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [moreMenuRef]);


  return (
    <header className="bg-[#2a2a2a] shadow-lg p-3 sticky top-0 z-50 border-b border-neutral-700/50">
      <div className="container mx-auto flex items-center justify-between">
        {/* Left Aligned Items */}
        <div className="flex items-center space-x-2">
          <button
            onClick={onNavigateHome}
            title="主页"
            className={`header-nav-button-app ${currentAppView === 'dashboard' ? 'text-amber-400 bg-amber-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400'}`}
            aria-pressed={currentAppView === 'dashboard'}
          >
            <LuLayoutDashboard size={18} /> <span className="hidden sm:inline ml-1">主页</span>
          </button>

          {/* 影片墙按钮 */}
          <button
            onClick={() => onSetCurrentAppView('mainWall')}
            title="影片墙"
            className={`header-nav-button-app ${currentAppView === 'mainWall' ? 'text-amber-400 bg-amber-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400'}`}
            aria-pressed={currentAppView === 'mainWall'}
          >
            <LuFileText size={18} /> <span className="hidden sm:inline ml-1">影片墙</span>
          </button>

           {/* 移除了片库名称显示 - 现在使用智能片库侧边栏 */}
          <button
            onClick={() => onSetCurrentAppView('favoritesView')}
            title="收藏夹"
            className={`header-nav-button-app ${currentAppView === 'favoritesView' ? 'text-amber-400 bg-amber-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400'}`}
            aria-pressed={currentAppView === 'favoritesView'}
          >
            <HeartIcon filled={currentAppView === 'favoritesView'} className="w-5 h-5" /> <span className="hidden sm:inline ml-1">收藏夹</span>
          </button>
          <button
            onClick={() => onSetCurrentAppView('intelligenceCenter')}
            title="情报中心"
            className={`header-nav-button-app ${currentAppView === 'intelligenceCenter' ? 'text-amber-400 bg-amber-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400'}`}
            aria-pressed={currentAppView === 'intelligenceCenter'}
          >
            <LuRadar size={18} /> <span className="hidden sm:inline ml-1">情报中心</span>
          </button>
          <button
            onClick={() => onSetCurrentAppView('collectorView')}
            title="链接搜集器"
            className={`header-nav-button-app ${currentAppView === 'collectorView' || currentAppView === 'ingestCenter' ? 'text-amber-400 bg-amber-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400'}`}
            aria-pressed={currentAppView === 'collectorView' || currentAppView === 'ingestCenter'}
          >
            <LuLink2 size={18} /> <span className="hidden sm:inline ml-1">搜集器</span>
          </button>
          <button
            onClick={() => onSetCurrentAppView('stagingArea')}
            title="下载中转站"
            className={`header-nav-button-app ${currentAppView === 'stagingArea' ? 'text-amber-400 bg-amber-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-amber-400'}`}
            aria-pressed={currentAppView === 'stagingArea'}
          >
            <LuDownload size={18} /> <span className="hidden sm:inline ml-1">中转站</span>
          </button>
          <button
            onClick={() => onSetCurrentAppView('recycleBin')}
            title="回收站"
            className={`header-nav-button-app ${currentAppView === 'recycleBin' ? 'text-red-400 bg-red-900/30' : 'text-neutral-300 hover:bg-neutral-700/50 hover:text-red-400'}`}
            aria-pressed={currentAppView === 'recycleBin'}
          >
            <LuTrash2 size={18} /> <span className="hidden sm:inline ml-1">回收站</span>
          </button>
        </div>

        {/* Centered Search Bar & Filter Button */}
        <div className="flex-grow flex items-center max-w-md mx-2 sm:mx-4">
          <div className="relative flex-grow">
            <input
              ref={searchInputRef}
              type="search"
              placeholder="搜索影片、演员、番号..."
              value={searchTerm}
              onChange={(e) => onSearchTermChange(e.target.value)}
              className="form-input-app w-full !py-2 !pl-9 !pr-3 text-sm"
              aria-label="搜索影片"
            />
            <LuSearch className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4 pointer-events-none" />
          </div>
          <button onClick={onOpenFilterModal} title="高级筛选" className="icon-button-app ml-1.5">
            <LuFilter size={18} />
          </button>
        </div>

        {/* Right Aligned Items */}
        <div className="flex items-center space-x-1.5">
           <button 
            onClick={handleToggleImageVisibility} 
            title={appSettings.imagesGloballyVisible ? "隐藏所有图片 (SFW)" : "显示所有图片 (NSFW)"} 
            className="icon-button-app"
          >
            {appSettings.imagesGloballyVisible ? <EyeIcon className="w-5 h-5"/> : <EyeSlashIcon className="w-5 h-5 text-red-400"/>}
          </button>
          <button onClick={onOpenLinLuoModal} title="召唤林珞姐姐" className="icon-button-app">
            <LuSparkles size={20} />
          </button>
          <button onClick={onTriggerScan} disabled={isScanning} title="扫描所有源文件夹" className="icon-button-app">
             <LuScanLine size={20} className={`${isScanning ? 'animate-ping' : ''}`}/>
          </button>
          <button onClick={() => onOpenSettingsModal()} title="设置" className="icon-button-app">
            <LuSettings size={20} />
          </button>
          
          {/* More Actions Dropdown */}
          <div className="relative" ref={moreMenuRef}>
            <button 
              onClick={() => setIsMoreMenuOpen(prev => !prev)} 
              title="更多操作" 
              className="icon-button-app"
              aria-haspopup="true"
              aria-expanded={isMoreMenuOpen}
            >
              <LuEllipsis size={20} />
            </button>
            {isMoreMenuOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-[#2c2c2c] border border-[#444444] rounded-md shadow-xl py-1 z-50">
                <button onClick={() => handleMoreMenuAction(onOpenRecommendationsModal)} title="AI为你推荐" className="w-full text-left px-3 py-2 text-sm text-neutral-200 hover:bg-[#383838] flex items-center"><LuThumbsUp size={16} className="mr-2"/>AI推荐</button>
                <button onClick={() => handleMoreMenuAction(() => onOpenSettingsModal('tools'))} title="工具箱" className="w-full text-left px-3 py-2 text-sm text-neutral-200 hover:bg-[#383838] flex items-center"><LuBox size={16} className="mr-2"/>工具箱</button>
                <div className="my-1 border-t border-neutral-600"></div>
                <button onClick={() => handleMoreMenuAction(onTogglePrivacyLockIcon)} title={privacyState.isEnabled ? (isPrivacyUnlockedThisSession ? "隐私模式 (已解锁)" : "隐私模式 (已锁定)") : "隐私模式 (已禁用)"} className="w-full text-left px-3 py-2 text-sm text-neutral-200 hover:bg-[#383838] flex items-center">
                   {privacyState.isEnabled ? (isPrivacyUnlockedThisSession ? <LuLockOpen size={16} className="mr-2 text-green-400" /> : <LuLock size={16} className="mr-2 text-red-400" />) : <LuLockOpen size={16} className="mr-2" />}
                   隐私模式
                </button>
              </div>
            )}
          </div>

        </div>
      </div>
    </header>
  );
};

export default AppHeader;