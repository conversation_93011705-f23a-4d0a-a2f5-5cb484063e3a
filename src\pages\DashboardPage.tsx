import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LuActivity, LuZap, LuClock, LuPlay, LuDatabase, LuUsers, LuSettings, LuTrendingUp } from 'react-icons/lu';

interface ActivityItem {
  id: string;
  type: 'movie_added' | 'link_collected' | 'scan_completed' | 'profile_created';
  message: string;
  timestamp: string;
  icon: React.ReactNode;
}

interface TaskStatus {
  isRunning: boolean;
  taskName?: string;
  taskType?: string;
}

interface LibraryStats {
  movieCount: number;
  linkCount: number;
  actorCount: number;
}

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [taskStatus, setTaskStatus] = useState<TaskStatus>({ isRunning: false });
  const [libraryStats, setLibraryStats] = useState<LibraryStats>({ movieCount: 0, linkCount: 0, actorCount: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [isStatsLoading, setIsStatsLoading] = useState(true);

  // 获取实时动态数据
  useEffect(() => {
    const fetchActivities = async () => {
      try {
        setIsLoading(true);
        const result = await window.sfeElectronAPI.getRecentActivity();

        if (result.success) {
          setActivities(result.data);
        } else {
          console.error('获取活动数据失败:', result.error);
        }
      } catch (error) {
        console.error('获取活动数据异常:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchActivities();

    // 每30秒刷新一次数据
    const interval = setInterval(fetchActivities, 30000);

    return () => clearInterval(interval);
  }, []);

  // 获取库存统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsStatsLoading(true);
        const result = await window.sfeElectronAPI.getDashboardStats();

        if (result.success) {
          setLibraryStats(result.data);
        } else {
          console.error('获取统计数据失败:', result.error);
        }
      } catch (error) {
        console.error('获取统计数据异常:', error);
      } finally {
        setIsStatsLoading(false);
      }
    };

    fetchStats();

    // 每60秒刷新一次统计数据
    const interval = setInterval(fetchStats, 60000);

    return () => clearInterval(interval);
  }, []);

  // 快速入口配置
  const quickAccessItems = [
    {
      title: '影片墙',
      description: '浏览和管理影片库',
      icon: <LuPlay className="h-8 w-8" />,
      color: 'bg-blue-600 hover:bg-blue-700',
      path: '/library'
    },
    {
      title: '情报中心',
      description: '收集和分析影片信息',
      icon: <LuTrendingUp className="h-8 w-8" />,
      color: 'bg-purple-600 hover:bg-purple-700',
      path: '/intelligence'
    },
    {
      title: '人物档案',
      description: '演员和制作人员档案',
      icon: <LuUsers className="h-8 w-8" />,
      color: 'bg-green-600 hover:bg-green-700',
      path: '/actors'
    },
    {
      title: '设置',
      description: '应用配置和偏好设置',
      icon: <LuSettings className="h-8 w-8" />,
      color: 'bg-gray-600 hover:bg-gray-700',
      path: '/settings'
    }
  ];

  const handleQuickAccess = (path: string) => {
    navigate(path);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-white mb-3 bg-gradient-to-r from-[#B8860B] to-amber-400 bg-clip-text text-transparent">
            综合看板
          </h1>
          <p className="text-gray-300 text-lg">欢迎回到 SoulForge，这里是您的数字影片管理中心</p>
          <div className="mt-4 w-24 h-1 bg-gradient-to-r from-[#B8860B] to-amber-400 mx-auto rounded-full"></div>
        </div>

        {/* 网格布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* 实时动态挂件 */}
          <div className="lg:col-span-2">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 shadow-2xl hover:shadow-[#B8860B]/10 transition-all duration-300 hover:border-[#B8860B]/30">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-[#B8860B]/20 rounded-lg">
                  <LuActivity className="h-5 w-5 text-[#B8860B]" />
                </div>
                <h2 className="text-xl font-semibold text-white">实时动态</h2>
                <div className="ml-auto">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
              </div>
              
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse p-4 bg-gray-700/50 rounded-lg">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gray-600 rounded-lg"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-600 rounded w-1/2"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : activities.length > 0 ? (
                <div className="space-y-3">
                  {activities.map((activity, index) => (
                    <div
                      key={activity.id}
                      className="group flex items-start gap-4 p-4 bg-gray-700/50 rounded-lg hover:bg-gray-700/70 transition-all duration-200 hover:scale-[1.02] hover:shadow-lg"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="p-2 bg-[#B8860B]/20 rounded-lg group-hover:bg-[#B8860B]/30 transition-colors">
                        <LuActivity className="h-4 w-4 text-[#B8860B]" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium leading-relaxed">{activity.message}</p>
                        <p className="text-gray-400 text-xs mt-1 flex items-center gap-1">
                          <LuClock className="h-3 w-3" />
                          {activity.timestamp}
                        </p>
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="w-1 h-8 bg-[#B8860B] rounded-full"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-400">
                  <div className="p-4 bg-gray-700/30 rounded-full w-fit mx-auto mb-4">
                    <LuActivity className="h-12 w-12 opacity-50" />
                  </div>
                  <p className="text-lg font-medium mb-2">暂无最近活动</p>
                  <p className="text-sm">开始使用应用后，这里将显示最新动态</p>
                </div>
              )}
            </div>
          </div>

          {/* 右侧挂件列 */}
          <div className="space-y-8">

            {/* 任务队列状态挂件 */}
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 shadow-2xl hover:shadow-[#B8860B]/10 transition-all duration-300 hover:border-[#B8860B]/30">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-[#B8860B]/20 rounded-lg">
                  <LuClock className="h-5 w-5 text-[#B8860B]" />
                </div>
                <h2 className="text-lg font-semibold text-white">任务状态</h2>
              </div>
              
              <div className="text-center">
                {taskStatus.isRunning ? (
                  <div className="p-4 bg-orange-500/10 rounded-lg border border-orange-500/20">
                    <div className="relative mx-auto mb-4 w-12 h-12">
                      <div className="animate-spin rounded-full h-12 w-12 border-2 border-orange-500/30 border-t-orange-500"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <LuZap className="h-5 w-5 text-orange-500" />
                      </div>
                    </div>
                    <p className="text-white text-sm font-medium mb-1">{taskStatus.taskName}</p>
                    <p className="text-orange-400 text-xs">正在运行...</p>
                  </div>
                ) : (
                  <div className="p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                    <div className="relative mx-auto mb-4 w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    </div>
                    <p className="text-white text-sm font-medium mb-1">后台空闲</p>
                    <p className="text-green-400 text-xs">所有任务已完成</p>
                  </div>
                )}
              </div>
            </div>

            {/* 快速统计挂件 */}
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50 shadow-2xl hover:shadow-[#B8860B]/10 transition-all duration-300 hover:border-[#B8860B]/30">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-[#B8860B]/20 rounded-lg">
                  <LuDatabase className="h-5 w-5 text-[#B8860B]" />
                </div>
                <h2 className="text-lg font-semibold text-white">库存概览</h2>
              </div>
              
              {isStatsLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="p-3 bg-gray-700/30 rounded-lg animate-pulse">
                      <div className="flex justify-between items-center">
                        <div className="h-4 bg-gray-600 rounded w-20"></div>
                        <div className="h-6 bg-gray-600 rounded w-12"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20 hover:bg-blue-500/20 transition-colors cursor-pointer" onClick={() => handleQuickAccess('/library')}>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <LuPlay className="h-4 w-4 text-blue-400" />
                        <span className="text-gray-300 text-sm">影片总数</span>
                      </div>
                      <span className="text-white font-bold text-lg">{libraryStats.movieCount.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 hover:bg-green-500/20 transition-colors cursor-pointer" onClick={() => handleQuickAccess('/actors')}>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <LuUsers className="h-4 w-4 text-green-400" />
                        <span className="text-gray-300 text-sm">演员档案</span>
                      </div>
                      <span className="text-white font-bold text-lg">{libraryStats.actorCount.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="p-3 bg-purple-500/10 rounded-lg border border-purple-500/20 hover:bg-purple-500/20 transition-colors cursor-pointer" onClick={() => handleQuickAccess('/intelligence')}>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <LuTrendingUp className="h-4 w-4 text-purple-400" />
                        <span className="text-gray-300 text-sm">收集链接</span>
                      </div>
                      <span className="text-white font-bold text-lg">{libraryStats.linkCount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 快速入口挂件 */}
        <div className="mt-8">
          <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 shadow-2xl hover:shadow-[#B8860B]/10 transition-all duration-300">
            <div className="flex items-center gap-3 mb-8">
              <div className="p-2 bg-[#B8860B]/20 rounded-lg">
                <LuZap className="h-5 w-5 text-[#B8860B]" />
              </div>
              <h2 className="text-xl font-semibold text-white">快速入口</h2>
              <div className="ml-auto text-xs text-gray-400">点击卡片快速导航</div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {quickAccessItems.map((item, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickAccess(item.path)}
                  className={`${item.color} text-white p-6 rounded-xl transition-all duration-300 group hover:scale-105 hover:shadow-xl relative overflow-hidden`}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>

                  <div className="text-center relative z-10">
                    <div className="mb-4 group-hover:scale-110 transition-transform duration-300">
                      {item.icon}
                    </div>
                    <h3 className="font-semibold mb-2 text-lg">{item.title}</h3>
                    <p className="text-xs opacity-90 leading-relaxed">{item.description}</p>
                  </div>

                  {/* 悬浮指示器 */}
                  <div className="absolute bottom-2 right-2 w-2 h-2 bg-white/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
