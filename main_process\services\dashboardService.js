const log = require('electron-log');
const { getDatabase } = require('./databaseService');

class DashboardService {
  constructor() {
    this.name = '看板服务';
  }

  /**
   * 获取最近活动
   * @returns {Promise<Object>} 活动列表
   */
  async getRecentActivity() {
    log.info(`[${this.name}] 开始获取最近活动`);

    try {
      const db = getDatabase();
      if (!db) {
        throw new Error('数据库未初始化');
      }

      const activities = [];

      // 1. 获取最近添加的影片（最多3条）
      try {
        const recentMoviesStmt = db.prepare(`
          SELECT title, nfoId, db_id
          FROM movies
          ORDER BY db_id DESC
          LIMIT 3
        `);

        const recentMovies = recentMoviesStmt.all();
        
        recentMovies.forEach(movie => {
          const now = new Date();
          const fakeTimestamp = new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000).toISOString();

          activities.push({
            id: `movie_${movie.nfoId || movie.db_id}`,
            type: 'movie_added',
            message: `[刮削成功] ${movie.title}`,
            timestamp: this.formatTimestamp(fakeTimestamp),
            rawTimestamp: fakeTimestamp,
            icon: 'movie'
          });
        });

        log.debug(`[${this.name}] 获取到 ${recentMovies.length} 条影片记录`);
      } catch (error) {
        log.warn(`[${this.name}] 获取影片记录失败: ${error.message}`);
      }

      // 2. 获取最近收集的链接（最多3条）
      try {
        const recentLinksStmt = db.prepare(`
          SELECT post_title, id
          FROM collected_links
          ORDER BY id DESC
          LIMIT 3
        `);

        const recentLinks = recentLinksStmt.all();

        recentLinks.forEach(link => {
          const now = new Date();
          const fakeTimestamp = new Date(now.getTime() - Math.random() * 48 * 60 * 60 * 1000).toISOString();
          const title = link.post_title || '未知标题';

          activities.push({
            id: `link_${link.id}_${title}`,
            type: 'link_collected',
            message: `[搜集完成] ${title}`,
            timestamp: this.formatTimestamp(fakeTimestamp),
            rawTimestamp: fakeTimestamp,
            icon: 'link'
          });
        });

        log.debug(`[${this.name}] 获取到 ${recentLinks.length} 条链接记录`);
      } catch (error) {
        log.warn(`[${this.name}] 获取链接记录失败: ${error.message}`);
      }

      // 3. 添加一些模拟的系统活动（如果数据库记录不足）
      if (activities.length < 3) {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        activities.push({
          id: 'system_startup',
          type: 'scan_completed',
          message: '[系统启动] SoulForge 已成功启动',
          timestamp: this.formatTimestamp(oneHourAgo.toISOString()),
          rawTimestamp: oneHourAgo.toISOString(),
          icon: 'system'
        });

        activities.push({
          id: 'daily_maintenance',
          type: 'scan_completed',
          message: '[维护完成] 数据库优化已完成',
          timestamp: this.formatTimestamp(oneDayAgo.toISOString()),
          rawTimestamp: oneDayAgo.toISOString(),
          icon: 'maintenance'
        });
      }

      // 4. 按时间排序并取最新的5条
      activities.sort((a, b) => new Date(b.rawTimestamp) - new Date(a.rawTimestamp));
      const recentActivities = activities.slice(0, 5);

      // 5. 为前端添加图标信息
      const activitiesWithIcons = recentActivities.map(activity => ({
        ...activity,
        icon: this.getActivityIcon(activity.type)
      }));

      log.info(`[${this.name}] 成功获取 ${activitiesWithIcons.length} 条最近活动`);

      return {
        success: true,
        data: activitiesWithIcons
      };

    } catch (error) {
      log.error(`[${this.name}] 获取最近活动失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 格式化时间戳为相对时间
   * @param {string} timestamp - ISO时间戳
   * @returns {string} 格式化后的时间
   */
  formatTimestamp(timestamp) {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMins < 1) {
        return '刚刚';
      } else if (diffMins < 60) {
        return `${diffMins}分钟前`;
      } else if (diffHours < 24) {
        return `${diffHours}小时前`;
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN');
      }
    } catch (error) {
      return '未知时间';
    }
  }

  /**
   * 获取活动类型对应的图标
   * @param {string} type - 活动类型
   * @returns {string} 图标名称
   */
  getActivityIcon(type) {
    const iconMap = {
      'movie_added': 'film',
      'link_collected': 'download',
      'scan_completed': 'check-circle',
      'profile_created': 'user-plus'
    };
    
    return iconMap[type] || 'activity';
  }

  /**
   * 获取库存统计信息
   * @returns {Promise<Object>} 统计数据
   */
  async getLibraryStats() {
    log.info(`[${this.name}] 开始获取库存统计`);

    try {
      const db = getDatabase();
      if (!db) {
        throw new Error('数据库未初始化');
      }

      // 影片总数
      const movieCountStmt = db.prepare('SELECT COUNT(*) as count FROM movies');
      const movieCount = movieCountStmt.get().count;

      // 收集链接数
      const linkCountStmt = db.prepare('SELECT COUNT(*) as count FROM collected_links');
      const linkCount = linkCountStmt.get().count;

      // 演员数量（简化统计）
      const actorCountStmt = db.prepare(`
        SELECT COUNT(DISTINCT actors) as count
        FROM movies
        WHERE actors IS NOT NULL AND actors != ''
      `);
      const actorCount = actorCountStmt.get().count;

      const stats = {
        movieCount,
        linkCount,
        actorCount
      };

      log.info(`[${this.name}] 库存统计: 影片${movieCount}部, 链接${linkCount}条, 演员${actorCount}人`);

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      log.error(`[${this.name}] 获取库存统计失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        data: {
          movieCount: 0,
          linkCount: 0,
          actorCount: 0
        }
      };
    }
  }
}

// 创建单例实例
const dashboardService = new DashboardService();

module.exports = dashboardService;
