// 测试 CollectorService 核心服务的脚本
// 在 Electron 应用的开发者控制台中运行

async function testCollectorService() {
  console.log('🧪 开始测试 CollectorService 核心服务...\n');
  
  try {
    // 1. 测试获取支持的论坛列表
    console.log('1️⃣ 测试获取支持的论坛列表');
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (forumsResult.success) {
      console.log('✅ 成功获取论坛列表');
      console.log(`论坛数量: ${forumsResult.forums.length}`);
      forumsResult.forums.forEach((forum, index) => {
        console.log(`  ${index + 1}. ${forum.key}: ${forum.name}`);
      });
    } else {
      console.error('❌ 获取论坛列表失败:', forumsResult.error);
      return false;
    }
    
    // 2. 测试获取服务状态
    console.log('\n2️⃣ 测试获取服务状态');
    const statusResult = await window.sfeElectronAPI.collectorGetStatus();
    
    if (statusResult.success) {
      console.log('✅ 成功获取服务状态');
      console.log(`运行状态: ${statusResult.status.isRunning ? '运行中' : '空闲'}`);
      console.log(`当前任务: ${statusResult.status.currentTask ? '有' : '无'}`);
      console.log(`历史任务数: ${statusResult.status.taskHistory.length}`);
    } else {
      console.error('❌ 获取服务状态失败:', statusResult.error);
      return false;
    }
    
    // 3. 测试启动搜集任务（使用有效的站点配置）
    if (forumsResult.forums.length > 0) {
      console.log('\n3️⃣ 测试启动搜集任务');
      
      const testSiteKey = forumsResult.forums[0].key;
      const testTargetUrl = 'https://example.com/forum/board';
      const testOptions = {
        maxPages: 3,
        delay: 500
      };
      
      console.log(`使用站点: ${testSiteKey}`);
      console.log(`目标URL: ${testTargetUrl}`);
      console.log(`选项: ${JSON.stringify(testOptions)}`);
      
      try {
        const taskResult = await window.sfeElectronAPI.collectorStartTask(testSiteKey, testTargetUrl, testOptions);
        
        if (taskResult.success) {
          console.log('✅ 搜集任务启动成功');
          console.log(`任务ID: ${taskResult.taskId}`);
          console.log(`搜集结果: 找到 ${taskResult.result.collectedCount} 个链接`);
          
          // 显示搜集到的链接示例
          if (taskResult.result.links && taskResult.result.links.length > 0) {
            console.log('\n搜集到的链接示例:');
            taskResult.result.links.forEach((link, index) => {
              console.log(`  ${index + 1}. ${link.postTitle}`);
              console.log(`     URL: ${link.postUrl}`);
              console.log(`     NFO ID: ${link.nfoId}`);
              console.log(`     磁力链接: ${link.magnetLink}`);
            });
          }
        } else {
          console.error('❌ 搜集任务启动失败:', taskResult.error);
        }
      } catch (error) {
        console.error('❌ 搜集任务执行异常:', error);
      }
    }
    
    // 4. 测试无效站点配置的错误处理
    console.log('\n4️⃣ 测试无效站点配置的错误处理');
    
    try {
      const invalidResult = await window.sfeElectronAPI.collectorStartTask('invalidSite', 'https://example.com', {});
      
      if (!invalidResult.success) {
        console.log('✅ 正确处理了无效站点配置');
        console.log(`错误信息: ${invalidResult.error}`);
      } else {
        console.log('⚠️ 意外成功处理了无效站点配置');
      }
    } catch (error) {
      console.log('✅ 正确抛出了无效站点配置的异常');
      console.log(`异常信息: ${error.message}`);
    }
    
    // 5. 测试无效URL的错误处理
    console.log('\n5️⃣ 测试无效URL的错误处理');
    
    if (forumsResult.forums.length > 0) {
      try {
        const invalidUrlResult = await window.sfeElectronAPI.collectorStartTask(
          forumsResult.forums[0].key, 
          'invalid-url', 
          {}
        );
        
        if (!invalidUrlResult.success) {
          console.log('✅ 正确处理了无效URL');
          console.log(`错误信息: ${invalidUrlResult.error}`);
        } else {
          console.log('⚠️ 意外成功处理了无效URL');
        }
      } catch (error) {
        console.log('✅ 正确抛出了无效URL的异常');
        console.log(`异常信息: ${error.message}`);
      }
    }
    
    // 6. 测试停止任务功能
    console.log('\n6️⃣ 测试停止任务功能');
    
    try {
      const stopResult = await window.sfeElectronAPI.collectorStopTask();
      
      if (stopResult.success) {
        console.log('✅ 停止任务成功');
        console.log(`消息: ${stopResult.message}`);
      } else {
        console.log('ℹ️ 停止任务结果');
        console.log(`消息: ${stopResult.message}`);
      }
    } catch (error) {
      console.error('❌ 停止任务异常:', error);
    }
    
    // 7. 再次检查服务状态
    console.log('\n7️⃣ 再次检查服务状态');
    
    const finalStatusResult = await window.sfeElectronAPI.collectorGetStatus();
    
    if (finalStatusResult.success) {
      console.log('✅ 最终状态获取成功');
      console.log(`运行状态: ${finalStatusResult.status.isRunning ? '运行中' : '空闲'}`);
      console.log(`历史任务数: ${finalStatusResult.status.taskHistory.length}`);
      
      if (finalStatusResult.status.taskHistory.length > 0) {
        console.log('\n最近的任务历史:');
        finalStatusResult.status.taskHistory.slice(-3).forEach((task, index) => {
          console.log(`  ${index + 1}. 站点: ${task.siteKey}, 状态: ${task.status}`);
          console.log(`     开始时间: ${new Date(task.startTime).toLocaleString()}`);
          if (task.endTime) {
            console.log(`     结束时间: ${new Date(task.endTime).toLocaleString()}`);
          }
        });
      }
    }
    
    console.log('\n🎉 CollectorService 核心服务测试完成！');
    console.log('✅ 所有核心功能正常工作');
    console.log('✅ 站点配置加载成功');
    console.log('✅ 错误处理机制完善');
    console.log('✅ 任务状态管理正常');
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 测试站点配置加载
async function testSiteProfileLoading() {
  console.log('🔍 测试站点配置加载...\n');
  
  try {
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (forumsResult.success && forumsResult.forums.length > 0) {
      console.log('✅ 站点配置加载成功');
      console.log('📋 配置详情:');
      
      forumsResult.forums.forEach((forum, index) => {
        console.log(`\n${index + 1}. ${forum.name} (${forum.key})`);
      });
      
      return true;
    } else {
      console.error('❌ 站点配置加载失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 测试站点配置加载异常:', error);
    return false;
  }
}

// 导出函数到全局作用域
window.testCollectorService = testCollectorService;
window.testSiteProfileLoading = testSiteProfileLoading;

console.log(`
🛠️ CollectorService 测试工具已加载！

使用方法:
1. testCollectorService() - 完整的服务功能测试
2. testSiteProfileLoading() - 测试站点配置加载

推荐使用: testCollectorService()
`);

// 自动运行测试
testCollectorService();
