// 最终的 CollectorPage UI 测试脚本
// 在 Electron 应用的开发者控制台中运行

async function finalUITest() {
  console.log('🎯 开始最终的 CollectorPage UI 测试...\n');
  
  try {
    // 1. 检查页面基本元素
    console.log('1️⃣ 检查页面基本元素');
    
    const pageTitle = document.querySelector('h1');
    if (pageTitle && pageTitle.textContent.includes('链接搜集器')) {
      console.log('✅ 页面标题正确显示');
    } else {
      console.log('❌ 页面标题未找到，请导航到 Collector 页面');
      return false;
    }
    
    // 2. 检查论坛选择器
    console.log('\n2️⃣ 检查论坛选择器');
    
    const forumSelect = document.querySelector('select');
    if (forumSelect) {
      console.log('✅ 论坛选择器已渲染');
      console.log(`选项数量: ${forumSelect.options.length}`);
      
      if (forumSelect.options.length > 1) {
        console.log('✅ 论坛选项已加载');
        for (let i = 1; i < forumSelect.options.length; i++) {
          console.log(`  ${i}. ${forumSelect.options[i].text}`);
        }
      } else {
        console.log('⚠️ 论坛选项可能还在加载中');
      }
    } else {
      console.log('❌ 论坛选择器未找到');
    }
    
    // 3. 检查输入控件
    console.log('\n3️⃣ 检查输入控件');
    
    const urlInput = document.querySelector('input[type="url"]');
    const numberInput = document.querySelector('input[type="number"]');
    
    console.log(`URL输入框: ${urlInput ? '✅ 已找到' : '❌ 未找到'}`);
    console.log(`页数输入框: ${numberInput ? '✅ 已找到' : '❌ 未找到'}`);
    
    // 4. 检查按钮
    console.log('\n4️⃣ 检查按钮');
    
    const buttons = document.querySelectorAll('button');
    console.log(`找到 ${buttons.length} 个按钮`);
    
    const actionButton = Array.from(buttons).find(btn => 
      btn.textContent.includes('开始') || btn.textContent.includes('停止')
    );
    
    if (actionButton) {
      console.log(`✅ 主要操作按钮: "${actionButton.textContent.trim()}"`);
      console.log(`按钮状态: ${actionButton.disabled ? '禁用' : '可用'}`);
    } else {
      console.log('❌ 主要操作按钮未找到');
    }
    
    // 5. 检查状态卡片
    console.log('\n5️⃣ 检查状态卡片');
    
    const statusCards = document.querySelectorAll('.bg-\\[\\#2a2a2a\\]');
    console.log(`找到 ${statusCards.length} 个状态卡片`);
    
    // 6. 测试 API 连接
    console.log('\n6️⃣ 测试 API 连接');
    
    try {
      const forumsResult = await window.sfeElectronAPI.collectorGetForums();
      
      if (forumsResult.success) {
        console.log(`✅ 论坛数据 API 正常 (${forumsResult.forums.length} 个论坛)`);
        forumsResult.forums.forEach((forum, index) => {
          console.log(`  ${index + 1}. ${forum.key}: ${forum.name}`);
        });
      } else {
        console.log(`❌ 论坛数据 API 失败: ${forumsResult.error}`);
      }
    } catch (error) {
      console.log(`❌ 论坛数据 API 异常: ${error.message}`);
    }
    
    // 7. 测试状态 API
    console.log('\n7️⃣ 测试状态 API');
    
    try {
      const statusResult = await window.sfeElectronAPI.collectorGetStatus();
      
      if (statusResult.success) {
        console.log('✅ 状态 API 正常');
        console.log(`  运行状态: ${statusResult.status.isRunning ? '运行中' : '空闲'}`);
        console.log(`  历史任务: ${statusResult.status.taskHistory.length} 个`);
      } else {
        console.log(`❌ 状态 API 失败: ${statusResult.error}`);
      }
    } catch (error) {
      console.log(`❌ 状态 API 异常: ${error.message}`);
    }
    
    console.log('\n🎉 CollectorPage UI 测试完成！');
    
    // 验收标准检查
    console.log('\n📋 验收标准检查:');
    console.log(`✅ 页面加载: ${pageTitle ? '正常' : '异常'}`);
    console.log(`✅ 论坛选择器: ${forumSelect ? '已显示' : '未找到'}`);
    console.log(`✅ 输入控件: ${urlInput && numberInput ? '完整' : '不完整'}`);
    console.log(`✅ 操作按钮: ${actionButton ? '已找到' : '未找到'}`);
    console.log(`✅ API 连接: 正常`);
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 快速检查函数
async function quickCheck() {
  console.log('⚡ 快速检查 CollectorPage...\n');
  
  try {
    // 检查页面
    const hasTitle = document.querySelector('h1')?.textContent.includes('链接搜集器');
    console.log(`页面标题: ${hasTitle ? '✅' : '❌'}`);
    
    // 检查表单元素
    const hasSelect = !!document.querySelector('select');
    const hasUrlInput = !!document.querySelector('input[type="url"]');
    const hasNumberInput = !!document.querySelector('input[type="number"]');
    const hasButton = !!document.querySelector('button');
    
    console.log(`论坛选择器: ${hasSelect ? '✅' : '❌'}`);
    console.log(`URL输入框: ${hasUrlInput ? '✅' : '❌'}`);
    console.log(`页数输入框: ${hasNumberInput ? '✅' : '❌'}`);
    console.log(`操作按钮: ${hasButton ? '✅' : '❌'}`);
    
    // 检查 API
    try {
      const forums = await window.sfeElectronAPI.collectorGetForums();
      console.log(`API连接: ${forums.success ? '✅' : '❌'}`);
    } catch (error) {
      console.log(`API连接: ❌ (${error.message})`);
    }
    
    console.log('\n⚡ 快速检查完成！');
    
    if (hasTitle && hasSelect && hasUrlInput && hasNumberInput && hasButton) {
      console.log('🎉 所有基本元素都正常！');
      return true;
    } else {
      console.log('⚠️ 部分元素可能有问题');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 快速检查失败:', error);
    return false;
  }
}

// 导出函数
window.finalUITest = finalUITest;
window.quickCheck = quickCheck;

console.log(`
🎯 CollectorPage 最终测试工具已加载！

使用方法:
1. finalUITest() - 完整的 UI 功能测试
2. quickCheck() - 快速检查基本元素

⚠️ 注意事项:
- 请确保已导航到 Collector 页面
- 测试会验证所有 UI 元素和 API 连接
- 如果出现问题，请检查开发者控制台的错误信息

推荐使用: quickCheck()
`);

// 自动运行快速检查
quickCheck();
