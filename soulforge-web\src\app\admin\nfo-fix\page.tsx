'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/layout/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  AlertCircle, 
  CheckCircle, 
  FileText, 
  RefreshCw, 
  Database,
  TrendingUp,
  Copy
} from 'lucide-react';

interface NFOStats {
  total: number;
  with_nfo_id: number;
  without_nfo_id: number;
}

interface DuplicateGroup {
  nfoId: string;
  count: number;
}

interface FixResult {
  processed: number;
  updated: number;
  skipped: number;
  errors: number;
  details: any[];
}

export default function NFOFixPage() {
  const [stats, setStats] = useState<NFOStats | null>(null);
  const [duplicates, setDuplicates] = useState<DuplicateGroup[]>([]);
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);
  const [fixResult, setFixResult] = useState<FixResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/movies/fix-nfo-ids');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.statistics);
        setDuplicates(data.duplicateGroups);
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const runDryRun = async () => {
    setFixing(true);
    setFixResult(null);
    
    try {
      const response = await fetch('/api/movies/fix-nfo-ids', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dryRun: true }),
      });
      
      const data = await response.json();
      if (data.success) {
        setFixResult(data);
      }
    } catch (error) {
      console.error('Dry run failed:', error);
    } finally {
      setFixing(false);
    }
  };

  const runActualFix = async () => {
    setFixing(true);
    
    try {
      const response = await fetch('/api/movies/fix-nfo-ids', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dryRun: false }),
      });
      
      const data = await response.json();
      if (data.success) {
        setFixResult(data);
        // Reload stats after actual fix
        await loadStats();
      }
    } catch (error) {
      console.error('Fix failed:', error);
    } finally {
      setFixing(false);
    }
  };

  const getConfidenceBadge = (confidence: string) => {
    const variants = {
      high: 'default',
      medium: 'secondary',
      low: 'outline',
    } as const;
    
    return (
      <Badge variant={variants[confidence as keyof typeof variants] || 'outline'}>
        {confidence}
      </Badge>
    );
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'nfo_file':
        return <FileText className="h-4 w-4" />;
      case 'filename':
        return <Database className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">NFO ID 修复工具</h1>
          <Button onClick={loadStats} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新统计
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总电影数</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.total || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">有 NFO ID</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats?.with_nfo_id || 0}</div>
              {stats && (
                <p className="text-xs text-muted-foreground">
                  {((stats.with_nfo_id / stats.total) * 100).toFixed(1)}%
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">缺少 NFO ID</CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats?.without_nfo_id || 0}</div>
              {stats && (
                <p className="text-xs text-muted-foreground">
                  {((stats.without_nfo_id / stats.total) * 100).toFixed(1)}%
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Progress Bar */}
        {stats && (
          <Card>
            <CardHeader>
              <CardTitle>NFO ID 完整性</CardTitle>
            </CardHeader>
            <CardContent>
              <Progress 
                value={(stats.with_nfo_id / stats.total) * 100} 
                className="w-full"
              />
              <p className="text-sm text-muted-foreground mt-2">
                {stats.with_nfo_id} / {stats.total} 电影有 NFO ID
              </p>
            </CardContent>
          </Card>
        )}

        {/* Duplicate Groups */}
        {duplicates.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Copy className="h-5 w-5 mr-2" />
                重复的 NFO ID
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {duplicates.map((dup, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <span className="font-mono">{dup.nfoId}</span>
                    <Badge variant="secondary">{dup.count} 个版本</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Fix Actions */}
        <Card>
          <CardHeader>
            <CardTitle>修复操作</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-4">
              <Button 
                onClick={runDryRun} 
                disabled={fixing || !stats?.without_nfo_id}
                variant="outline"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                预览修复 (不修改数据)
              </Button>
              
              <Button 
                onClick={runActualFix} 
                disabled={fixing || !fixResult || fixResult.updated === 0}
                variant="default"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                执行修复
              </Button>
            </div>

            {fixing && (
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>正在处理...</span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Fix Results */}
        {fixResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                修复结果
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setShowDetails(!showDetails)}
                >
                  {showDetails ? '隐藏详情' : '显示详情'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{fixResult.processed}</div>
                  <div className="text-sm text-muted-foreground">已处理</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{fixResult.updated}</div>
                  <div className="text-sm text-muted-foreground">已更新</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{fixResult.skipped}</div>
                  <div className="text-sm text-muted-foreground">已跳过</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{fixResult.errors}</div>
                  <div className="text-sm text-muted-foreground">错误</div>
                </div>
              </div>

              {showDetails && fixResult.details && (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {fixResult.details.map((detail, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted rounded text-sm">
                      <div className="flex-1 min-w-0">
                        <div className="font-mono truncate">{detail.fileName}</div>
                        {detail.extractedNfoId && (
                          <div className="flex items-center space-x-2 mt-1">
                            {getSourceIcon(detail.source)}
                            <span className="font-mono">{detail.extractedNfoId}</span>
                            {getConfidenceBadge(detail.confidence)}
                          </div>
                        )}
                      </div>
                      <Badge 
                        variant={
                          detail.action === 'updated' || detail.action === 'would_update' 
                            ? 'default' 
                            : detail.action === 'error' 
                            ? 'destructive' 
                            : 'secondary'
                        }
                      >
                        {detail.action}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
