// main_process/services/trailerDownloadService.js
const fs = require('fs').promises;
const path = require('path');
const https = require('https');
const http = require('http');
const log = require('electron-log');
const settingsService = require('./settingsService');

/**
 * 预告片下载服务
 * 负责下载和管理影片预告片
 */

/**
 * 下载预告片
 * @param {string} trailerUrl - 预告片URL
 * @param {string} nfoId - 番号
 * @param {string} customPath - 自定义保存路径（可选）
 * @returns {Promise<object>} 下载结果
 */
async function downloadTrailer(trailerUrl, nfoId, customPath = null) {
    try {
        log.info(`[预告片下载] 开始下载: ${nfoId} - ${trailerUrl}`);

        let filePath;

        if (customPath) {
            // 如果提供了自定义路径，直接使用
            filePath = customPath;
        } else {
            // 使用 pathResolverService 计算标准路径
            const pathResolverService = require('./pathResolverService');

            const movieData = {
                nfoId: nfoId,
                title: `影片 ${nfoId}`,
                studio: '未知制作商',
                tags: ['预告片下载'],
                mosaic: '有码'  // 默认有码，确保正确分类
            };

            const assetPaths = await pathResolverService.resolveAssetPaths(movieData);
            filePath = assetPaths.trailerPath;  // 使用新的三位一体架构预告片仓库路径

            log.info(`[预告片下载] 使用三位一体架构预告片仓库路径: ${filePath}`);
        }

        // 确保目录存在
        await ensureDirectoryExists(path.dirname(filePath));
        
        // 检查文件是否已存在
        if (await fileExists(filePath)) {
            log.info(`[预告片下载] 文件已存在，跳过下载: ${filePath}`);
            return {
                success: true,
                filePath: filePath,
                fileName: path.basename(filePath),
                skipped: true,
                message: '文件已存在'
            };
        }
        
        // 下载文件
        const downloadResult = await downloadFile(trailerUrl, filePath);
        
        if (downloadResult.success) {
            log.info(`[预告片下载] 下载成功: ${nfoId} -> ${filePath}`);
            return {
                success: true,
                filePath: filePath,
                fileName: path.basename(filePath),
                fileSize: downloadResult.fileSize,
                message: '下载成功'
            };
        } else {
            throw new Error(downloadResult.error);
        }
        
    } catch (error) {
        log.error(`[预告片下载] 下载失败: ${nfoId} - ${error.message}`);
        return {
            success: false,
            error: error.message,
            nfoId: nfoId,
            url: trailerUrl
        };
    }
}

/**
 * 批量下载预告片
 * @param {Array} trailerList - 预告片列表 [{url, nfoId, ...}, ...]
 * @param {function} progressCallback - 进度回调函数
 * @returns {Promise<object>} 批量下载结果
 */
async function downloadTrailersBatch(trailerList, progressCallback = null) {
    const results = {
        total: trailerList.length,
        success: 0,
        failed: 0,
        skipped: 0,
        details: []
    };
    
    log.info(`[预告片下载] 开始批量下载 ${trailerList.length} 个预告片`);
    
    for (let i = 0; i < trailerList.length; i++) {
        const trailer = trailerList[i];
        
        // 进度回调
        if (progressCallback) {
            progressCallback({
                current: i + 1,
                total: trailerList.length,
                percentage: Math.round(((i + 1) / trailerList.length) * 100),
                currentTrailer: trailer
            });
        }
        
        const downloadResult = await downloadTrailer(trailer.url, trailer.nfoId);
        
        if (downloadResult.success) {
            if (downloadResult.skipped) {
                results.skipped++;
            } else {
                results.success++;
            }
        } else {
            results.failed++;
        }
        
        results.details.push(downloadResult);
        
        // 添加延迟避免请求过快
        if (i < trailerList.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    log.info(`[预告片下载] 批量下载完成: 成功 ${results.success}, 失败 ${results.failed}, 跳过 ${results.skipped}`);
    return results;
}

/**
 * 获取预告片保存目录（旧版本兼容，仅用于向后兼容查找）
 * @returns {string} 预告片目录路径
 */
function getTrailerDirectory() {
    const settings = settingsService.getSettings();

    // 优先检查旧的 mediaAssetRootPath 设置
    let mediaRoot = settings.mediaAssetRootPath;

    // 如果没有旧设置，检查是否有新的元数据仓库设置
    if (!mediaRoot && settings.assetsPath) {
        // 从元数据仓库路径推断旧的媒体根目录
        const parentDir = path.dirname(settings.assetsPath);
        mediaRoot = path.join(parentDir, 'MediaAssets');
    }

    // 如果都没有设置，使用默认路径
    if (!mediaRoot) {
        const os = require('os');
        // 使用用户主目录下的 SoulForge/Media 作为默认路径
        mediaRoot = path.join(os.homedir(), 'SoulForge', 'Media');
    }

    const trailerDir = path.join(mediaRoot, 'Trailers');
    return trailerDir;
}

/**
 * 生成预告片文件名
 * @param {string} nfoId - 番号
 * @param {string} url - 预告片URL
 * @returns {string} 文件名
 */
function generateTrailerFileName(nfoId, url) {
    // 从URL中提取文件扩展名
    const urlPath = new URL(url).pathname;
    const ext = path.extname(urlPath) || '.mp4';
    
    // 清理番号，确保文件名安全
    const cleanNfoId = nfoId.replace(/[<>:"/\\|?*]/g, '_');
    
    return `${cleanNfoId}_trailer${ext}`;
}

/**
 * 确保目录存在
 * @param {string} dirPath - 目录路径
 */
async function ensureDirectoryExists(dirPath) {
    try {
        await fs.access(dirPath);
    } catch (error) {
        if (error.code === 'ENOENT') {
            await fs.mkdir(dirPath, { recursive: true });
            log.info(`[预告片下载] 创建目录: ${dirPath}`);
        } else {
            throw error;
        }
    }
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {Promise<boolean>} 是否存在
 */
async function fileExists(filePath) {
    try {
        await fs.access(filePath);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {string} filePath - 保存路径
 * @returns {Promise<object>} 下载结果
 */
function downloadFile(url, filePath) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        
        const request = protocol.get(url, (response) => {
            if (response.statusCode === 200) {
                const fileStream = require('fs').createWriteStream(filePath);
                let downloadedBytes = 0;
                
                response.on('data', (chunk) => {
                    downloadedBytes += chunk.length;
                });
                
                response.pipe(fileStream);
                
                fileStream.on('finish', () => {
                    fileStream.close();
                    resolve({
                        success: true,
                        fileSize: downloadedBytes
                    });
                });
                
                fileStream.on('error', (error) => {
                    fs.unlink(filePath).catch(() => {}); // 删除不完整的文件
                    reject(error);
                });
                
            } else if (response.statusCode === 302 || response.statusCode === 301) {
                // 处理重定向
                const redirectUrl = response.headers.location;
                if (redirectUrl) {
                    downloadFile(redirectUrl, filePath).then(resolve).catch(reject);
                } else {
                    reject(new Error(`重定向失败: ${response.statusCode}`));
                }
            } else {
                reject(new Error(`HTTP错误: ${response.statusCode}`));
            }
        });
        
        request.on('error', (error) => {
            reject(error);
        });
        
        request.setTimeout(30000, () => {
            request.abort();
            reject(new Error('下载超时'));
        });
    });
}

/**
 * 获取预告片文件信息
 * @param {string} nfoId - 番号
 * @returns {Promise<object>} 文件信息
 */
async function getTrailerInfo(nfoId) {
    try {
        // 使用 pathResolverService 计算标准路径
        const pathResolverService = require('./pathResolverService');

        const movieData = {
            nfoId: nfoId,
            title: `影片 ${nfoId}`,
            studio: '未知制作商',
            tags: ['预告片查询'],
            mosaic: '有码'  // 默认有码，确保正确分类
        };

        const assetPaths = await pathResolverService.resolveAssetPaths(movieData);
        const filePath = assetPaths.trailerPath;

        // 检查标准路径的文件是否存在
        if (await fileExists(filePath)) {
            const stats = await fs.stat(filePath);

            return {
                exists: true,
                filePath: filePath,
                fileName: path.basename(filePath),
                fileSize: stats.size,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
        }

        // 如果标准路径不存在，检查旧的路径（向后兼容）
        const trailerDir = getTrailerDirectory();
        if (await fileExists(trailerDir)) {
            const files = await fs.readdir(trailerDir);

            // 查找匹配的预告片文件
            const trailerFiles = files.filter(file =>
                file.toLowerCase().includes(nfoId.toLowerCase()) &&
                file.includes('trailer')
            );

            if (trailerFiles.length > 0) {
                const trailerFile = trailerFiles[0];
                const oldFilePath = path.join(trailerDir, trailerFile);
                const stats = await fs.stat(oldFilePath);

                log.info(`[预告片下载] 找到旧路径的预告片: ${oldFilePath}`);

                return {
                    exists: true,
                    filePath: oldFilePath,
                    fileName: trailerFile,
                    fileSize: stats.size,
                    createdAt: stats.birthtime,
                    modifiedAt: stats.mtime,
                    isLegacyPath: true
                };
            }
        }

        return { exists: false };

    } catch (error) {
        log.error(`[预告片下载] 获取预告片信息失败: ${nfoId} - ${error.message}`);
        return { exists: false, error: error.message };
    }
}

module.exports = {
    downloadTrailer,
    downloadTrailersBatch,
    getTrailerDirectory,
    getTrailerInfo,
    generateTrailerFileName
};
