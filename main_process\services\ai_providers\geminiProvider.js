// soul-forge-electron/main_process/services/ai_providers/geminiProvider.js

let GoogleGenAI, HarmCategory, HarmBlockThreshold; // Loaded from main @google/genai module
let log;
let currentApiKey = null;
let geminiClientInstance = null;

function initializeGeminiProvider(logger, googleGenAiModule) {
  log = logger;
  if (googleGenAiModule) {
    ({ GoogleGenAI, HarmCategory, HarmBlockThreshold } = googleGenAiModule);
    log.info('[Gemini提供商] @google/genai 模块已注入。');
  } else {
    log.error('[Gemini提供商] @google/genai 模块未提供，Gemini 功能将不可用。');
  }
}

function getClient(apiKey) {
  if (!GoogleGenAI) {
    log.error('[Gemini提供商] GoogleGenAI 模块不可用，无法创建客户端。');
    throw new Error('@google/genai 模块未加载。');
  }
  if (!apiKey) {
    log.error('[Gemini提供商] API Key 未提供，无法创建客户端。');
    throw new Error('Google Gemini API Key 未提供。');
  }

  if (geminiClientInstance && currentApiKey === apiKey) {
    return geminiClientInstance;
  }
  
  try {
    log.info('[Gemini提供商] 正在初始化 GoogleGenAI 客户端...');

    // 根据官方文档，设置正确的环境变量
    process.env.GOOGLE_API_KEY = apiKey;

    // 使用正确的初始化方式，传递空对象
    geminiClientInstance = new GoogleGenAI({});
    currentApiKey = apiKey;

    log.info('[Gemini提供商] GoogleGenAI 客户端已成功初始化。');
    return geminiClientInstance;
  } catch (error) {
    log.error('[Gemini提供商] 初始化 GoogleGenAI 客户端失败:', error);
    geminiClientInstance = null;
    currentApiKey = null;
    throw error;
  }
}

async function generateContent(client, prompt, config = {}) {
  if (!client) throw new Error('Gemini AI 客户端未初始化。');
  log.info(`[Gemini提供商] 生成内容。模型: ${config.model || 'gemini-1.5-flash'}, Prompt (前30): ${prompt.substring(0,30)}...`);
  try {
    // 使用正确的 API 调用方式
    const result = await client.models.generateContent({
      model: config.model || 'gemini-1.5-flash',
      contents: prompt,
      config: {
        generationConfig: config.generationConfig
      }
    });

    const text = result.text;
    log.info(`[Gemini提供商] 内容生成成功。`);
    return text.trim();
  } catch (error) {
    log.error(`[Gemini提供商] 调用 Gemini API 生成内容失败: ${error.message}`, error);
    throw error;
  }
}

async function generateContentStream(client, prompt, systemInstruction, webContentsSender, config = {}) {
  if (!client) {
    log.error('[Gemini提供商] 流式生成内容：Gemini AI 客户端未初始化。');
    throw new Error('Gemini AI 客户端未初始化。');
  }
  const modelName = config.model || 'gemini-1.5-flash';
  log.info(`[Gemini提供商] 流式生成内容。模型: ${modelName}, Prompt (前30): ${prompt.substring(0,30)}...`);

  try {
    // 使用正确的 API 调用方式
    const result = await client.models.generateContentStream({
      model: modelName,
      contents: prompt,
      config: {
        systemInstruction: systemInstruction
      }
    });

    (async () => {
      try {
        for await (const chunk of result.stream) {
          const textChunk = chunk.text;
          if (textChunk) {
            webContentsSender.send('linluo-chat-chunk', textChunk);
          }
        }
        webContentsSender.send('linluo-chat-end');
        log.info('[Gemini提供商] 流式响应结束。');
      } catch (streamError) {
        log.error('[Gemini提供商] 流处理错误:', streamError);
        webContentsSender.send('linluo-chat-error', `Gemini响应流处理错误: ${streamError.message}`);
      }
    })();
    return { streamStarted: true };
  } catch (error) {
    log.error(`[Gemini提供商] 调用 Gemini API 流式生成内容失败: ${error.message}`, error);
    webContentsSender.send('linluo-chat-error', `启动Gemini响应流失败: ${error.message}`);
    throw error; // Re-throw to be caught by aiService
  }
}

async function testConnection(apiKey, modelName = 'gemini-1.5-flash') {
  if (!modelName || modelName.trim() === '') {
    return { success: false, message: '模型名称不能为空！' };
  }
  try {
    const testClient = getClient(apiKey); // This will throw if API key is bad or module not loaded
    await generateContent(testClient, '你好，请确认你能正常工作。', { model: modelName });
    log.info(`[Gemini提供商] 连接测试成功。模型: ${modelName}`);
    return { success: true, message: `模型 ${modelName} 连接成功！` };
  } catch (error) {
    log.error(`[Gemini提供商] 连接测试失败 (模型: ${modelName}): ${error.message}`, error);
    return { success: false, message: `模型 ${modelName} 连接失败: ${error.message}` };
  }
}

async function generateJsonContent(client, prompt, config = {}) {
  if (!client) throw new Error('Gemini AI 客户端未初始化.');
  log.info(`[Gemini提供商] 生成 JSON 内容. 模型: ${config.model || 'gemini-1.5-flash'}, Prompt (前30): ${prompt.substring(0,30)}...`);

  try {
    // 使用正确的 API 调用方式
    const result = await client.models.generateContent({
      model: config.model || 'gemini-1.5-flash',
      contents: prompt,
      config: {
        generationConfig: {
          responseMimeType: "application/json",
          ...(config.generationConfig || {})
        }
      }
    });

    const text = result.text;

    let jsonStr = text.trim();
    log.info(`[Gemini提供商] 原始JSON响应 (前100): ${jsonStr.substring(0,100)}`);

    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) jsonStr = match[2].trim();

    const parsedData = JSON.parse(jsonStr);
    return parsedData;

  } catch (error) {
    log.error(`[Gemini提供商] 调用 Gemini API 生成JSON内容失败: ${error.message}`, error);
    throw error;
  }
}


module.exports = {
  initializeGeminiProvider,
  getClient,
  generateContent,
  generateContentStream,
  testConnection,
  generateJsonContent,
};
