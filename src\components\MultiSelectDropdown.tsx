import React, { useState, useEffect, useRef } from 'react';

interface MultiSelectDropdownProps {
  label: string;
  options: string[];
  selectedOptions: string[];
  onChange: (selected: string[]) => void;
  className?: string;
  dropdownClassName?: string;
}

const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  label,
  options,
  selectedOptions,
  onChange,
  className = '',
  dropdownClassName = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [wrapperRef]);

  const handleToggle = (option: string) => {
    const newSelectedOptions = selectedOptions.includes(option)
      ? selectedOptions.filter((op) => op !== option)
      : [...selectedOptions, option];
    onChange(newSelectedOptions);
  };

  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const displayLabel = selectedOptions.length > 0 
    ? `${label} (${selectedOptions.length})` 
    : label;

  return (
    <div className={`relative ${className}`} ref={wrapperRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 bg-[#1e1e1e] border border-[#4a4a4a] rounded-md focus:ring-2 focus:ring-[#B8860B] focus:border-[#B8860B] outline-none text-sm text-left text-neutral-300 flex justify-between items-center appearance-none cursor-pointer"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <span className="truncate pr-1">{displayLabel}</span>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20" strokeWidth={1.5} stroke="currentColor" className={`w-5 h-5 text-neutral-400 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
        </svg>
      </button>
      {isOpen && (
        <div className={`absolute z-10 mt-1 w-full bg-[#2c2c2c] border border-[#4a4a4a] rounded-md shadow-lg max-h-60 overflow-y-auto ${dropdownClassName}`}>
          <input
            type="text"
            placeholder={`搜索${label}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 bg-[#1e1e1e] border-b border-[#4a4a4a] focus:ring-1 focus:ring-[#B8860B] focus:border-[#B8860B] outline-none text-sm text-neutral-200 sticky top-0 z-10"
          />
          {filteredOptions.length > 0 ? (
            <ul>
              {filteredOptions.map((option) => (
                <li
                  key={option}
                  className="px-3 py-2 text-sm text-neutral-200 hover:bg-[#3a3a3a] cursor-pointer flex items-center"
                  onClick={() => handleToggle(option)}
                  onKeyPress={(e) => e.key === 'Enter' && handleToggle(option)}
                  role="option"
                  aria-selected={selectedOptions.includes(option)}
                  tabIndex={0}
                >
                  <input
                    type="checkbox"
                    checked={selectedOptions.includes(option)}
                    onChange={() => {}} // Click handled by li
                    className="mr-2 h-4 w-4 text-[#B8860B] bg-neutral-700 border-neutral-500 rounded focus:ring-[#B8860B] focus:ring-offset-0 cursor-pointer"
                    readOnly // Actual change is on the li click
                  />
                  {option}
                </li>
              ))}
            </ul>
          ) : (
            <p className="px-3 py-2 text-sm text-neutral-400">无匹配选项</p>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;