import React, { useState, useEffect, useCallback } from 'react';
import { useAppSettings } from '../hooks/useAppSettings'; // Import for SFW placeholder

interface ImageWithFallbackProps {
  primarySrc?: string | null;    // e.g., movie.coverDataUrl (Base64 local)
  secondarySrc?: string | null;  // e.g., movie.posterUrl (Network URL 1)
  tertiarySrc?: string | null;   // e.g., movie.coverUrl (Network URL 2)
  appDefaultCoverDataUrl?: string | null; 
  alt: string;
  className?: string;
  placeholder: React.ReactNode; // This is the generic placeholder (e.g., PlayIcon)
  onClick?: () => void;
  forceShowPlaceholder?: boolean; // External control to force placeholder (e.g. SFW mode)
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  primarySrc,
  secondarySrc,
  tertiarySrc,
  appDefaultCoverDataUrl,
  alt,
  className,
  placeholder: genericPlaceholder, // Renamed to avoid conflict
  onClick,
  forceShowPlaceholder = false,
}) => {
  const { appSettings } = useAppSettings();

  const getSources = useCallback(() => {
    return [
        primarySrc, 
        secondarySrc, 
        tertiarySrc,
        appDefaultCoverDataUrl 
    ].filter(src => typeof src === 'string' && src.trim() !== '') as string[];
  }, [primarySrc, secondarySrc, tertiarySrc, appDefaultCoverDataUrl]);

  const [sources, setSources] = useState<string[]>(getSources());
  const [currentSourceIndex, setCurrentSourceIndex] = useState(0);
  const [imgKey, setImgKey] = useState(Date.now()); 

  useEffect(() => {
    const newSources = getSources();
    setSources(newSources);
    setCurrentSourceIndex(0);
    setImgKey(Date.now()); 
  }, [primarySrc, secondarySrc, tertiarySrc, appDefaultCoverDataUrl, getSources]);

  const handleError = () => {
    if (currentSourceIndex < sources.length - 1) {
      setCurrentSourceIndex(prevIndex => prevIndex + 1);
    } else {
       setCurrentSourceIndex(sources.length); // Mark as all sources failed
    }
  };

  const currentImgSrc = sources[currentSourceIndex];
  
  // Determine which placeholder to show
  let displayPlaceholder = genericPlaceholder;
  if (forceShowPlaceholder && appSettings.customSfwPlaceholderDataUrl) {
    // If SFW mode is on AND a custom SFW placeholder is set, use it
    displayPlaceholder = (
      <img 
        src={appSettings.customSfwPlaceholderDataUrl} 
        alt="SFW 占位符" 
        className="w-full h-full object-cover" // Ensure it covers the area
      />
    );
  } else if (forceShowPlaceholder) {
    // If SFW mode is on but no custom SFW placeholder, use the generic one
    displayPlaceholder = genericPlaceholder;
  }


  if (forceShowPlaceholder || !currentImgSrc || currentSourceIndex >= sources.length) {
    return <div className={className} onClick={onClick} role="img" aria-label={alt}>{displayPlaceholder}</div>;
  }

  return (
    <img
      key={`${currentImgSrc}-${imgKey}`} 
      src={currentImgSrc}
      alt={alt}
      className={className}
      onError={handleError}
      onClick={onClick}
      loading="lazy" // Added lazy loading
    />
  );
};

export default ImageWithFallback;
