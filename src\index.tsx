
import React from 'react';
import ReactDOM from 'react-dom/client';
import AppRouter from './AppRouter'; // 使用新的路由版本
import './index.css'; // 导入 Tailwind 指令和全局样式

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("未能找到用于挂载的根元素。请确保您的 HTML 文件中有一个 id 为 'root' 的 div。");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <AppRouter />
  </React.StrictMode>
);