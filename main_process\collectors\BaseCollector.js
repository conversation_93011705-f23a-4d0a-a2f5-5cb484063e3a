/**
 * 基础采集器类 - 重构后使用模块化架构
 *
 * 这个基类定义了所有论坛采集器必须实现的方法，
 * 现在使用新的功能模块来处理具体任务。
 */

const { EventEmitter } = require('events');
const DownloadManager = require('../services/DownloadManager');
const FileRenameManager = require('../services/FileRenameManager');
const RecordManager = require('../services/RecordManager');

class BaseCollector extends EventEmitter {
  /**
   * 构造函数
   * @param {Object} config - 采集器配置
   */
  constructor(config) {
    super();

    this.siteProfile = config.siteProfile;
    this.workspacePath = config.workspacePath;
    this.log = config.log;
    this.databaseService = config.databaseService;
    this.updateTaskStatus = config.updateTaskStatus;

    // 初始化功能模块
    this.fileRenameManager = new FileRenameManager({ log: this.log });

    this.downloadManager = new DownloadManager({
      log: this.log,
      databaseService: this.databaseService,
      fileRenameManager: this.fileRenameManager,
      workspacePath: this.workspacePath,
      updateTaskStatus: this.updateTaskStatus
    });

    this.recordManager = new RecordManager({
      log: this.log,
      databaseService: this.databaseService,
      workspacePath: this.workspacePath,
      updateTaskStatus: this.updateTaskStatus
    });

    // 任务状态
    this.isRunning = false;
    this.shouldStop = false;
    this.currentPage = null;

    // 统计信息
    this.stats = {
      totalProcessed: 0,
      successCount: 0,
      failedCount: 0,
      startTime: null,
      endTime: null
    };
  }

  /**
   * 设置工作区路径
   */
  setWorkspacePath(workspacePath) {
    this.workspacePath = workspacePath;
    this.downloadManager.setWorkspacePath(workspacePath);
    this.recordManager.setWorkspacePath(workspacePath);
  }

  /**
   * 下载附件 - 使用新的下载管理器
   */
  async downloadAttachments(page, postData, siteProfile) {
    try {
      return await this.downloadManager.downloadAttachments(page, postData, siteProfile);
    } catch (error) {
      this.log.error(`[${this.constructor.name}] 下载附件失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 生成标准化文件名 - 使用新的文件重命名管理器
   */
  generateStandardFileName(postData, fileExtension) {
    return this.fileRenameManager.generateStandardFileName(postData, fileExtension);
  }

  /**
   * 保存结果到数据库 - 使用新的记录管理器
   */
  async saveResults(results, siteProfile) {
    try {
      return await this.recordManager.saveResults(results, siteProfile);
    } catch (error) {
      this.log.error(`[${this.constructor.name}] 保存结果失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 生成档案文件 - 使用新的记录管理器
   */
  async generatePostArchiveFile(postData) {
    try {
      return await this.recordManager.generatePostArchiveFile(postData);
    } catch (error) {
      this.log.error(`[${this.constructor.name}] 生成档案文件失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 执行抓取逻辑 - 主要的采集方法
   * @param {Object} page - Playwright页面对象
   * @param {Object} siteProfile - 站点配置
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 采集选项
   * @returns {Promise<Array>} 采集结果
   */
  async executeScrapingLogic(page, siteProfile, targetUrl, options) {
    throw new Error('executeScrapingLogic method must be implemented by subclass');
  }

  /**
   * 解析帖子内容
   * @param {Object} page - Playwright页面对象
   * @param {Object} siteProfile - 站点配置
   * @param {string} postUrl - 帖子URL
   * @returns {Promise<Object>} 帖子数据
   */
  async parsePostContent(page, siteProfile, postUrl) {
    throw new Error('parsePostContent method must be implemented by subclass');
  }



  /**
   * 抓取单个页面的帖子
   * @param {Object} page - Playwright页面对象
   * @param {Object} siteProfile - 站点配置
   * @returns {Promise<Array>} 帖子URL列表
   */
  async scrapeSinglePage(page, siteProfile) {
    throw new Error('scrapeSinglePage method must be implemented by subclass');
  }

  /**
   * 翻页到下一页
   * @param {Object} page - Playwright页面对象
   * @param {Object} siteProfile - 站点配置
   * @returns {Promise<boolean>} 是否成功翻页
   */
  async goToNextPage(page, siteProfile) {
    throw new Error('goToNextPage method must be implemented by subclass');
  }

  /**
   * 检查是否应该继续采集
   * @param {Object} postData - 当前帖子数据
   * @param {Object} options - 采集选项
   * @returns {boolean} 是否继续
   */
  shouldContinueCollection(postData, options) {
    // 检查停止标志
    if (this.shouldStop) {
      return false;
    }

    // 检查日期限制
    if (options.scrapeDays > 0 && postData.postDate) {
      const thresholdDate = new Date();
      thresholdDate.setDate(thresholdDate.getDate() - options.scrapeDays);
      thresholdDate.setHours(0, 0, 0, 0);

      if (postData.postDate < thresholdDate) {
        this.log.info(`[${this.constructor.name}] 帖子日期超出设定范围，停止采集`);
        return false;
      }
    }

    return true;
  }

  /**
   * 停止采集任务
   * @param {boolean} force - 是否强制停止
   * @returns {Promise<void>}
   */
  async stopCollection(force = false) {
    this.log.info(`[${this.constructor.name}] 收到停止指令 (force: ${force})`);
    this.shouldStop = true;

    if (force && this.currentPage) {
      try {
        await this.currentPage.close();
      } catch (error) {
        this.log.warn(`[${this.constructor.name}] 强制关闭页面时出错: ${error.message}`);
      }
    }
  }


  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalProcessed: 0,
      successCount: 0,
      failedCount: 0,
      startTime: null,
      endTime: null
    };
  }

  /**
   * 执行搜集任务 - 调度器接口
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 搜集结果
   */
  async executeTask(targetUrl, options = {}) {
    this.log.info(`[${this.constructor.name}] 开始执行搜集任务: ${targetUrl}`);

    this.isRunning = true;
    this.shouldStop = false;
    this.stats.startTime = new Date();

    try {
      if (this.updateTaskStatus) {
        this.updateTaskStatus('connecting', '正在连接到论坛...');
      }

      // 这里应该调用具体的搜集逻辑
      // 子类应该重写这个方法或实现executeScrapingLogic方法
      let results = [];

      if (typeof this.executeScrapingLogic === 'function') {
        // 如果子类实现了executeScrapingLogic方法，则调用真正的抓取逻辑
        results = await this.executeRealCollection(targetUrl, options);
      } else {
        throw new Error('子类必须实现executeScrapingLogic方法或重写executeTask方法');
      }

      this.stats.endTime = new Date();

      if (this.updateTaskStatus) {
        this.updateTaskStatus('completed', `搜集完成，共处理 ${results.length} 个项目`);
      }

      const result = {
        success: true,
        message: `搜集任务完成，共处理 ${results.length} 个项目`,
        result: {
          collectedCount: results.length,
          links: results,
          data: results
        }
      };

      this.log.info(`[${this.constructor.name}] 搜集任务完成: ${result.message}`);
      return result;

    } catch (error) {
      this.stats.endTime = new Date();
      this.log.error(`[${this.constructor.name}] 搜集任务失败: ${error.message}`);

      if (this.updateTaskStatus) {
        this.updateTaskStatus('failed', `搜集失败: ${error.message}`);
      }

      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 执行真正的搜集过程
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 搜集结果
   */
  async executeRealCollection(targetUrl, options) {
    this.log.info(`[${this.constructor.name}] 开始真正的搜集过程`);

    // 检查子类是否有performActualScraping方法
    if (typeof this.performActualScraping === 'function') {
      return await this.performActualScraping(targetUrl, options);
    }

    // 如果子类没有实现performActualScraping，则抛出错误
    throw new Error(`${this.constructor.name} 需要实现performActualScraping方法或重写executeTask方法来实现完整的搜集逻辑`);
  }

  /**
   * 模拟搜集过程（临时实现）
   * @param {string} targetUrl - 目标URL
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 模拟结果
   */
  async simulateCollection(targetUrl, options) {
    if (this.updateTaskStatus) {
      this.updateTaskStatus('scraping', '正在解析页面内容...');
    }

    // 模拟工作过程
    await this.delay(2000);

    if (this.updateTaskStatus) {
      this.updateTaskStatus('processing', '正在处理搜集数据...');
    }

    await this.delay(1000);

    // 返回模拟结果
    return [{
      title: '模拟搜集结果',
      url: targetUrl,
      timestamp: new Date().toISOString(),
      note: '这是一个模拟结果，实际搜集功能正在开发中'
    }];
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise<void>}
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 析构函数 - 清理资源
   */
  async destroy() {
    this.shouldStop = true;
    this.removeAllListeners();
    
    if (this.currentPage) {
      try {
        await this.currentPage.close();
      } catch (error) {
        this.log.warn(`[${this.constructor.name}] 关闭页面时出错: ${error.message}`);
      }
    }
  }
}

module.exports = BaseCollector;
