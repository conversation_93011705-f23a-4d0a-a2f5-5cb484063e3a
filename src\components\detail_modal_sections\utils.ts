// soul-forge-electron/src/components/detail_modal_sections/utils.ts
import { Movie, EditableMovieState as EditableMovieStateTypeFromTypes } from '../../types'; // Import the more complete type

// Use the imported EditableMovieState type directly from types.ts
export type EditableMovieState = EditableMovieStateTypeFromTypes;


export const movieToEditableState = (currentMovie: Movie): EditableMovieState => {
  return {
    ...currentMovie, // Spread all properties from Movie first
    title: currentMovie.title || '',
    originalTitle: currentMovie.originalTitle || '',
    nfoId: currentMovie.nfoId || '',
    actors: currentMovie.actors?.join(', ') || '',
    genres: currentMovie.genres?.join(', ') || '',
    tags: currentMovie.tags?.join(', ') || '',
    aiAnalyzedTags: currentMovie.aiAnalyzedTags?.join(', ') || '', 
    year: currentMovie.year?.toString() || '',
    releaseDate: currentMovie.releaseDate || '', // Keep as string, HTML input type="date" handles it
    runtime: currentMovie.runtime?.toString() || '', // Keep as string for form input
    plot: currentMovie.plot || '',
    studio: currentMovie.studio || '',
    series: currentMovie.series || '',
    director: currentMovie.director || '',
    trailerUrl: currentMovie.trailerUrl || '',
    posterUrl: currentMovie.posterUrl || '',
    coverUrl: currentMovie.coverUrl || '',
    localCoverPath: currentMovie.localCoverPath || null,
    watched: !!currentMovie.watched, 
    personalRating: currentMovie.personalRating === undefined ? null : currentMovie.personalRating,
    // Ensure all other fields from Movie that are not explicitly converted are carried over
    // by the initial spread of currentMovie.
    // cdPartInfo is part of Movie, so it will be included by the spread.
    // If it needs specific transformation for edit state, add it here.
    // For now, assuming cdPartInfo is either display-only or handled as string.
  };
};