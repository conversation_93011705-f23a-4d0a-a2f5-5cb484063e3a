// 测试CDP连接的简单脚本
// 用于验证新的CDP连接方案是否正常工作

const { chromium } = require('playwright');

async function testCDPConnection() {
  console.log('开始测试CDP连接...');
  
  try {
    // 尝试连接到用户的Chrome实例
    console.log('正在连接到 localhost:9222...');
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    
    // 获取浏览器上下文
    const contexts = browser.contexts();
    console.log(`找到 ${contexts.length} 个浏览器上下文`);
    
    if (contexts.length === 0) {
      throw new Error('没有找到可用的浏览器上下文');
    }
    
    const context = contexts[0];
    const pages = context.pages();
    console.log(`找到 ${pages.length} 个打开的页面`);
    
    // 列出所有打开的页面
    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      const url = page.url();
      const title = await page.title();
      console.log(`页面 ${i + 1}: ${title} - ${url}`);
    }
    
    // 测试是否能找到目标页面（假设用户已经打开了某个论坛页面）
    const targetDomains = ['ccgga.net', 'ccgga.com', 'localhost'];
    let targetPage = null;
    
    for (const page of pages) {
      const url = page.url();
      for (const domain of targetDomains) {
        if (url.includes(domain)) {
          targetPage = page;
          console.log(`找到目标页面: ${url}`);
          break;
        }
      }
      if (targetPage) break;
    }
    
    if (targetPage) {
      // 测试页面操作
      console.log('测试页面操作...');
      await targetPage.bringToFront();
      
      // 获取页面标题
      const title = await targetPage.title();
      console.log(`页面标题: ${title}`);
      
      // 测试简单的页面交互
      const bodyText = await targetPage.evaluate(() => {
        return document.body ? document.body.innerText.substring(0, 100) : '无法获取页面内容';
      });
      console.log(`页面内容预览: ${bodyText}...`);
      
      console.log('✅ CDP连接测试成功！');
    } else {
      console.log('⚠️ 未找到目标论坛页面，请确保已在Chrome中打开论坛页面');
    }
    
    // 注意：我们不关闭浏览器，因为这是用户的浏览器
    console.log('测试完成，用户浏览器保持打开状态');
    
  } catch (error) {
    console.error('❌ CDP连接测试失败:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('');
      console.log('解决方案:');
      console.log('1. 确保Chrome已启动并开启调试端口');
      console.log('2. 使用以下命令启动Chrome:');
      console.log('   chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\\temp\\chrome-debug"');
      console.log('3. 或者创建快捷方式，在目标后添加上述参数');
    }
  }
}

// 运行测试
testCDPConnection().then(() => {
  console.log('测试脚本执行完成');
}).catch(error => {
  console.error('测试脚本执行失败:', error);
});
