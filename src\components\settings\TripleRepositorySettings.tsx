import React, { useState, useEffect } from 'react';
import { FolderOpen, Check, AlertCircle, Database, Film, FileImage } from 'lucide-react';

interface TripleRepositorySettingsProps {
  className?: string;
}

interface RepositoryConfig {
  key: 'assetsPath' | 'trailersPath' | 'mediaPath';
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  placeholder: string;
}

export function TripleRepositorySettings({ className = '' }: TripleRepositorySettingsProps) {
  const [paths, setPaths] = useState<{
    assetsPath: string;
    trailersPath: string;
    mediaPath: string;
  }>({
    assetsPath: '',
    trailersPath: '',
    mediaPath: ''
  });
  
  const [isLoading, setIsLoading] = useState<{
    assetsPath: boolean;
    trailersPath: boolean;
    mediaPath: boolean;
  }>({
    assetsPath: false,
    trailersPath: false,
    mediaPath: false
  });
  
  const [saveStatus, setSaveStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
    timestamp: number;
  } | null>(null);

  // 三个仓库的配置
  const repositories: RepositoryConfig[] = [
    {
      key: 'assetsPath',
      title: '元数据仓库 (Assets)',
      description: '用于存放图片、NFO、JSON、STRM 等轻量级元数据文件',
      icon: FileImage,
      placeholder: '未设置元数据仓库路径'
    },
    {
      key: 'trailersPath',
      title: '预告片仓库 (Trailers)',
      description: '用于存放预告片视频文件',
      icon: Film,
      placeholder: '未设置预告片仓库路径'
    },
    {
      key: 'mediaPath',
      title: '正片仓库 (Media)',
      description: '用于存放主要视频文件',
      icon: Database,
      placeholder: '未设置正片仓库路径'
    }
  ];

  // 加载当前设置
  useEffect(() => {
    loadCurrentPaths();
  }, []);

  // 组件每次变为可见时重新加载设置
  useEffect(() => {
    // 使用 Intersection Observer 检测组件可见性
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 组件变为可见时重新加载设置
            setTimeout(() => {
              loadCurrentPaths();
            }, 100);
          }
        });
      },
      { threshold: 0.1 }
    );

    // 观察组件容器
    const container = document.querySelector('[data-triple-repository-settings]');
    if (container) {
      observer.observe(container);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // 监听全局设置更新事件
  useEffect(() => {
    const handleSettingsUpdate = (event: CustomEvent) => {
      console.log('收到设置更新事件:', event.detail);
      loadCurrentPaths();
    };

    window.addEventListener('settings-updated', handleSettingsUpdate as EventListener);

    return () => {
      window.removeEventListener('settings-updated', handleSettingsUpdate as EventListener);
    };
  }, []);

  // 定期刷新设置（作为备用机制）
  useEffect(() => {
    const interval = setInterval(() => {
      loadCurrentPaths();
    }, 3000); // 每3秒刷新一次

    return () => clearInterval(interval);
  }, []);

  const loadCurrentPaths = async () => {
    try {
      const settings = await window.sfeElectronAPI.getSettings();
      setPaths({
        assetsPath: settings.assetsPath || '',
        trailersPath: settings.trailersPath || '',
        mediaPath: settings.mediaPath || ''
      });
    } catch (error) {
      console.error('加载仓库路径设置失败:', error);
      setSaveStatus({
        type: 'error',
        message: '加载设置失败',
        timestamp: Date.now()
      });
    }
  };

  const handleBrowseFolder = async (repositoryKey: 'assetsPath' | 'trailersPath' | 'mediaPath') => {
    setIsLoading(prev => ({ ...prev, [repositoryKey]: true }));
    
    try {
      const selectedPath = await window.sfeElectronAPI.dialogSelectDirectory();
      
      if (selectedPath) {
        // 保存新路径
        const settings = await window.sfeElectronAPI.getSettings();
        const updatedSettings = {
          ...settings,
          [repositoryKey]: selectedPath
        };
        
        const result = await window.sfeElectronAPI.saveSettings(updatedSettings);
        
        if (result.success) {
          // 立即更新本地状态
          setPaths(prev => ({ ...prev, [repositoryKey]: selectedPath }));

          const repoConfig = repositories.find(r => r.key === repositoryKey);
          setSaveStatus({
            type: 'success',
            message: `${repoConfig?.title}已更新`,
            timestamp: Date.now()
          });

          // 多重确保设置同步
          setTimeout(() => loadCurrentPaths(), 50);   // 立即刷新
          setTimeout(() => loadCurrentPaths(), 200);  // 延迟刷新
          setTimeout(() => loadCurrentPaths(), 500);  // 再次确认

          // 触发全局设置更新事件
          window.dispatchEvent(new CustomEvent('settings-updated', {
            detail: { [repositoryKey]: selectedPath }
          }));
        } else {
          setSaveStatus({
            type: 'error',
            message: result.error || '保存设置失败',
            timestamp: Date.now()
          });
        }
      }
    } catch (error) {
      console.error('选择文件夹失败:', error);
      setSaveStatus({
        type: 'error',
        message: '选择文件夹失败',
        timestamp: Date.now()
      });
    } finally {
      setIsLoading(prev => ({ ...prev, [repositoryKey]: false }));
    }
  };

  // 自动清除状态消息
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => {
        setSaveStatus(null);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  return (
    <div className={`space-y-8 ${className}`} data-triple-repository-settings>
      {/* 标题和说明 */}
      <div>
        <h3 className="text-lg font-medium text-white mb-2">三位一体分离式媒体资产存储</h3>
        <p className="text-sm text-gray-400 mb-4">
          SoulForge 采用分离式存储架构，将不同类型的文件存储在独立的仓库中，便于管理和备份。
        </p>
      </div>

      {/* 三个仓库设置 */}
      {repositories.map((repo) => {
        const IconComponent = repo.icon;
        const currentPath = paths[repo.key];
        const loading = isLoading[repo.key];
        
        return (
          <div key={repo.key} className="space-y-4 p-4 bg-[#1a1a1a] border border-[#333] rounded-lg">
            {/* 仓库标题和图标 */}
            <div className="flex items-center gap-3">
              <IconComponent className="h-5 w-5 text-[#B8860B]" />
              <div>
                <h4 className="text-base font-medium text-white">{repo.title}</h4>
                <p className="text-sm text-gray-400">{repo.description}</p>
              </div>
            </div>

            {/* 路径显示和选择 */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-300">
                当前路径
              </label>
              
              <div className="flex items-center gap-3">
                <input
                  type="text"
                  value={currentPath}
                  readOnly
                  placeholder={repo.placeholder}
                  className="flex-1 px-3 py-2 bg-[#0f0f0f] border border-[#444] rounded text-white placeholder-gray-500 focus:border-[#B8860B] focus:outline-none"
                />
                
                <button
                  onClick={() => handleBrowseFolder(repo.key)}
                  disabled={loading}
                  className="px-4 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 min-w-[100px] justify-center"
                >
                  {loading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin" />
                      选择中...
                    </>
                  ) : (
                    <>
                      <FolderOpen className="h-4 w-4" />
                      浏览...
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        );
      })}

      {/* 状态反馈 */}
      {saveStatus && (
        <div className={`p-3 rounded-lg flex items-center gap-2 ${
          saveStatus.type === 'success' 
            ? 'bg-green-900/20 border border-green-500/30' 
            : 'bg-red-900/20 border border-red-500/30'
        }`}>
          {saveStatus.type === 'success' ? (
            <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
          ) : (
            <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
          )}
          <span className={`text-sm ${
            saveStatus.type === 'success' ? 'text-green-300' : 'text-red-300'
          }`}>
            {saveStatus.message}
          </span>
        </div>
      )}

      {/* 架构说明 */}
      <div className="mt-6 p-4 bg-[#1a1a1a] border border-[#333] rounded-lg">
        <h4 className="text-sm font-medium text-white mb-3">分离式存储架构说明</h4>
        <div className="text-xs text-gray-400 space-y-3">
          <div>
            <p className="font-medium text-gray-300 mb-1">📁 元数据仓库 (Assets)：</p>
            <p className="ml-4">存储封面图、海报图、预览图、NFO文件、JSON元数据、STRM链接文件等</p>
          </div>
          
          <div>
            <p className="font-medium text-gray-300 mb-1">🎬 预告片仓库 (Trailers)：</p>
            <p className="ml-4">存储预告片视频文件，与正片分离管理</p>
          </div>
          
          <div>
            <p className="font-medium text-gray-300 mb-1">🎥 正片仓库 (Media)：</p>
            <p className="ml-4">存储主要视频文件，可以是本地存储或网络存储</p>
          </div>
          
          <div className="mt-3 p-3 bg-[#0f0f0f] border border-[#444] rounded">
            <p className="font-medium text-gray-300 mb-1">目录结构示例：</p>
            <div className="ml-4 space-y-1 font-mono text-xs">
              <p>Assets/jav_censored/JUR/JUR-001/</p>
              <p>├── poster.jpg</p>
              <p>├── fanart.jpg</p>
              <p>├── JUR-001.nfo</p>
              <p>└── JUR-001.strm</p>
              <p className="mt-2">Trailers/jav_censored/JUR/JUR-001-trailer.mp4</p>
              <p className="mt-2">Media/jav_censored/JUR/JUR-001.mkv</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
