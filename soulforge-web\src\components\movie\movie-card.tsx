'use client';

import React from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Movie } from '@/lib/types';
import { cn, formatFileSize, formatDuration } from '@/lib/utils';
import {
  Play,
  Heart,
  Star,
  Clock,
  HardDrive,
  Eye,
  EyeOff,
  MoreHorizontal,
  Copy,
  Disc3,
  Cloud,
  CheckSquare
} from 'lucide-react';

interface MovieCardProps {
  movie: Movie;
  viewMode?: 'grid' | 'list' | 'detailed';
  onPlay?: (movie: Movie) => void;
  onToggleFavorite?: (movie: Movie) => void;
  onToggleWatched?: (movie: Movie) => void;
  onEdit?: (movie: Movie) => void;
  onShowVersions?: (movie: Movie) => void;
  className?: string;
}

export function MovieCard({
  movie,
  viewMode = 'grid',
  onPlay,
  onToggleFavorite,
  onToggleWatched,
  onEdit,
  onShowVersions,
  className,
}: MovieCardProps) {
  const handlePlay = () => {
    if (isMultiVersion || isMultiCD) {
      onShowVersions?.(movie);
    } else {
      onPlay?.(movie);
    }
  };

  const handleToggleWatched = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleWatched?.(movie);
  };

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite?.(movie);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(movie);
  };

  const handleShowVersions = (e: React.MouseEvent) => {
    e.stopPropagation();
    onShowVersions?.(movie);
  };

  // Check if this movie has multiple versions or CDs
  const isMultiVersion = (movie.versionCount || 0) > 1;
  const isMultiCD = (movie.multiCdCountForNfoId || 0) > 1;

  if (viewMode === 'list') {
    return (
      <Card className={cn('hover:shadow-md transition-shadow cursor-pointer', className)}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            {/* Thumbnail */}
            <div className="relative w-16 h-24 flex-shrink-0">
              {movie.localCoverPath || movie.posterUrl ? (
                <Image
                  src={movie.localCoverPath || movie.posterUrl || ''}
                  alt={movie.title || movie.fileName}
                  fill
                  className="object-cover rounded"
                />
              ) : (
                <div className="w-full h-full bg-muted rounded flex items-center justify-center">
                  <Play className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
              {movie.watched && (
                <div className="absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full" />
              )}
            </div>

            {/* Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm truncate">
                {movie.title || movie.fileName}
              </h3>
              {movie.originalTitle && movie.originalTitle !== movie.title && (
                <p className="text-xs text-muted-foreground truncate">
                  {movie.originalTitle}
                </p>
              )}
              <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                {movie.year && <span>{movie.year}</span>}
                {movie.runtime && (
                  <span className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatDuration(movie.runtime * 60)}
                  </span>
                )}
                {movie.fileSize && (
                  <span className="flex items-center">
                    <HardDrive className="h-3 w-3 mr-1" />
                    {formatFileSize(movie.fileSize)}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleToggleWatched}
                className="h-8 w-8"
              >
                {movie.watched ? (
                  <Eye className="h-4 w-4 text-green-600" />
                ) : (
                  <EyeOff className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleToggleFavorite}
                className="h-8 w-8"
              >
                <Heart className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleEdit}
                className="h-8 w-8"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card 
      className={cn(
        'group hover:shadow-lg transition-all duration-200 cursor-pointer overflow-hidden',
        className
      )}
      onClick={handleCardClick}
    >
      <div className="relative aspect-[2/3]">
        {movie.localCoverPath || movie.posterUrl ? (
          <Image
            src={movie.localCoverPath || movie.posterUrl || ''}
            alt={movie.title || movie.fileName}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center">
            <Play className="h-12 w-12 text-muted-foreground" />
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors duration-200" />
        
        {/* Play button */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Button size="icon" className="h-12 w-12 rounded-full">
            <Play className="h-6 w-6" />
          </Button>
        </div>

        {/* Version badges */}
        <div className="absolute top-2 left-2 flex flex-col space-y-1">
          {isMultiVersion && (
            <div className="bg-amber-500 text-black text-xs font-bold px-2 py-1 rounded-sm flex items-center space-x-1">
              <Copy className="h-3 w-3" />
              <span>{movie.versionCount} 版本</span>
            </div>
          )}
          {isMultiCD && (
            <div className="bg-teal-500 text-black text-xs font-bold px-2 py-1 rounded-sm flex items-center space-x-1">
              <Disc3 className="h-3 w-3" />
              <span>{movie.multiCdCountForNfoId}-CD</span>
            </div>
          )}
        </div>

        {/* Status indicators */}
        <div className="absolute top-2 right-2 flex flex-col space-y-1">
          {/* Asset Status Icon */}
          {movie.asset_status === 'VIRTUAL' && (
            <div className="w-6 h-6 bg-purple-600/80 rounded-sm flex items-center justify-center" title="虚拟资产 (待获取)">
              <Cloud className="h-4 w-4 text-white" />
            </div>
          )}
          {movie.asset_status === 'AVAILABLE' && (
            <div className="w-6 h-6 bg-green-600/80 rounded-sm flex items-center justify-center" title="本地已拥有">
              <CheckSquare className="h-4 w-4 text-white" />
            </div>
          )}
          {movie.asset_status === 'MISSING' && (
            <div className="w-6 h-6 bg-red-600/80 rounded-sm flex items-center justify-center" title="文件缺失">
              <HardDrive className="h-4 w-4 text-white" />
            </div>
          )}

          {movie.watched && (
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <Eye className="h-3 w-3 text-white" />
            </div>
          )}
          {movie.personalRating && (
            <div className="bg-yellow-500 text-white text-xs px-1 rounded flex items-center">
              <Star className="h-3 w-3 mr-1" />
              {movie.personalRating}
            </div>
          )}
        </div>

        {/* Quick actions */}
        <div className="absolute bottom-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Button
            variant="secondary"
            size="icon"
            onClick={handleToggleWatched}
            className="h-8 w-8"
          >
            {movie.watched ? (
              <Eye className="h-4 w-4" />
            ) : (
              <EyeOff className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="secondary"
            size="icon"
            onClick={handleToggleFavorite}
            className="h-8 w-8"
          >
            <Heart className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <CardContent className="p-3">
        <h3 className="font-semibold text-sm truncate mb-1">
          {movie.title || movie.fileName}
        </h3>
        {movie.year && (
          <p className="text-xs text-muted-foreground">{movie.year}</p>
        )}
        {movie.genres && movie.genres.length > 0 && (
          <p className="text-xs text-muted-foreground truncate mt-1">
            {movie.genres.slice(0, 2).join(', ')}
          </p>
        )}
      </CardContent>
    </Card>
  );
}
