// main_process/services/dmmCalendarMonitor.js
// DMM发售日历监视器 - 专门负责扫描DMM发售日历，提取未来新品信息

const { chromium } = require('playwright');
const log = require('electron-log');

/**
 * DMM发售日历监视器
 * 定期扫描DMM/FANZA发售日历，主动抓取即将发售的影片信息
 */
class DmmCalendarMonitor {
  constructor() {
    this.name = 'DMM发售日历监视器';
    this.version = '1.0.0';
    this.timeout = 30000; // 30秒超时
    this.calendarUrl = 'https://www.dmm.co.jp/mono/dvd/-/calendar/';
    this.scanDaysAhead = 30; // 扫描未来30天
  }

  /**
   * 扫描即将发售的新品
   * @returns {Promise<Array>} 未来新品信息数组
   */
  async scanForUpcomingReleases() {
    log.info(`[${this.name}] 开始扫描未来新品发售信息...`);
    
    let browser = null;
    let context = null;
    let page = null;

    try {
      // 启动浏览器
      log.debug(`[${this.name}] 启动Playwright浏览器...`);
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      context = await browser.newContext({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1920, height: 1080 },
        ignoreHTTPSErrors: true
      });

      page = await context.newPage();

      // 处理年龄验证
      await this.handleAgeVerification(page);

      // 扫描发售日历
      const upcomingReleases = await this.scanCalendarPage(page);

      log.info(`[${this.name}] 扫描完成，发现 ${upcomingReleases.length} 部未来新品`);
      return upcomingReleases;

    } catch (error) {
      log.error(`[${this.name}] 扫描过程发生错误: ${error.message}`);
      log.error(`[${this.name}] 错误堆栈: ${error.stack}`);
      return [];
    } finally {
      // 资源清理
      try {
        if (page) await page.close();
        if (context) await context.close();
        if (browser) await browser.close();
        log.debug(`[${this.name}] 浏览器资源已清理`);
      } catch (cleanupError) {
        log.error(`[${this.name}] 清理资源时发生错误: ${cleanupError.message}`);
      }
    }
  }

  /**
   * 处理DMM年龄验证
   * @param {Page} page - Playwright页面对象
   */
  async handleAgeVerification(page) {
    log.debug(`[${this.name}] 开始处理年龄验证...`);

    try {
      // 设置年龄验证Cookie
      await page.context().addCookies([
        {
          name: 'age_check_done',
          value: '1',
          domain: '.dmm.co.jp',
          path: '/',
          httpOnly: false,
          secure: false
        },
        {
          name: 'ckcy',
          value: '1',
          domain: '.dmm.co.jp', 
          path: '/',
          httpOnly: false,
          secure: false
        }
      ]);

      // 访问DMM主站触发年龄验证
      log.debug(`[${this.name}] 访问DMM主站进行年龄验证...`);
      await page.goto('https://www.dmm.co.jp/', { 
        waitUntil: 'domcontentloaded',
        timeout: 10000 
      });

      // 检查是否出现年龄验证页面
      const ageVerificationExists = await page.locator('input[value="はい"]').count() > 0;
      
      if (ageVerificationExists) {
        log.debug(`[${this.name}] 检测到年龄验证页面，自动点击确认...`);
        await page.locator('input[value="はい"]').click();
        await page.waitForTimeout(2000);
      }

      log.debug(`[${this.name}] 年龄验证处理完成`);

    } catch (error) {
      log.warn(`[${this.name}] 年龄验证处理失败，但继续执行: ${error.message}`);
    }
  }

  /**
   * 扫描发售日历页面
   * @param {Page} page - Playwright页面对象
   * @returns {Promise<Array>} 未来新品信息数组
   */
  async scanCalendarPage(page) {
    log.debug(`[${this.name}] 开始扫描发售日历页面...`);

    try {
      // 导航到发售日历页面
      await page.goto(this.calendarUrl, { 
        waitUntil: 'networkidle',
        timeout: this.timeout 
      });

      log.debug(`[${this.name}] 发售日历页面加载完成`);

      // 等待页面内容加载
      await page.waitForTimeout(3000);

      // 提取未来新品信息
      const upcomingReleases = await page.evaluate((scanDaysAhead) => {
        const releases = [];
        const today = new Date();
        const futureDate = new Date(today.getTime() + scanDaysAhead * 24 * 60 * 60 * 1000);

        // 在浏览器环境中定义提取函数
        function extractMovieInfoInBrowser(movieElement, releaseDate) {
          try {
            const info = {};

            // 提取链接和番号
            const link = movieElement.querySelector('a') || movieElement;
            const href = link.href || '';

            // 从URL中提取番号
            const cidMatch = href.match(/cid=([^&]+)/);
            if (cidMatch) {
              const cid = cidMatch[1];
              // 转换CID为标准番号格式
              const nfoIdMatch = cid.match(/([a-z]+)(\d+)/i);
              if (nfoIdMatch) {
                info.nfoId = (nfoIdMatch[1] + '-' + nfoIdMatch[2]).toUpperCase();
              }
            }

            if (!info.nfoId) return null;

            // 提取标题
            const titleElement = movieElement.querySelector('.title, .product-title, h3, h4') ||
                                movieElement.querySelector('img[alt]');
            info.title = titleElement ?
                        (titleElement.textContent?.trim() || titleElement.alt?.trim() || '') : '';

            // 提取封面图
            const imgElement = movieElement.querySelector('img');
            info.coverUrl = imgElement ? imgElement.src || imgElement.getAttribute('data-src') || '' : '';

            // 提取演员信息
            const actorElements = movieElement.querySelectorAll('.actor, .performer, .actress');
            info.actors = Array.from(actorElements).map(el => el.textContent?.trim()).filter(Boolean);

            // 设置发售日期
            info.releaseDate = releaseDate.toISOString().split('T')[0]; // YYYY-MM-DD格式

            // 设置详情页URL
            info.detailPageUrl = href;

            return info;

          } catch (error) {
            console.warn('提取影片信息失败:', error.message);
            return null;
          }
        }

        function extractMovieInfoFromLinkInBrowser(linkElement) {
          try {
            const href = linkElement.href || '';

            // 从URL中提取番号
            const cidMatch = href.match(/cid=([^&]+)/);
            if (!cidMatch) return null;

            const cid = cidMatch[1];
            const nfoIdMatch = cid.match(/([a-z]+)(\d+)/i);
            if (!nfoIdMatch) return null;

            const info = {
              nfoId: (nfoIdMatch[1] + '-' + nfoIdMatch[2]).toUpperCase(),
              title: linkElement.textContent?.trim() || linkElement.title || '',
              coverUrl: '',
              actors: [],
              detailPageUrl: href
            };

            // 尝试从相邻元素获取更多信息
            const imgElement = linkElement.querySelector('img') ||
                              linkElement.parentElement?.querySelector('img');
            if (imgElement) {
              info.coverUrl = imgElement.src || imgElement.getAttribute('data-src') || '';
              if (!info.title && imgElement.alt) {
                info.title = imgElement.alt.trim();
              }
            }

            return info;

          } catch (error) {
            console.warn('从链接提取影片信息失败:', error.message);
            return null;
          }
        }

        // 查找日历中的日期元素
        const dateElements = document.querySelectorAll('.calendar-date, .date-item, [data-date], .day-item');
        
        for (const dateElement of dateElements) {
          try {
            // 尝试提取日期信息
            const dateText = dateElement.textContent || dateElement.getAttribute('data-date') || '';
            const dateMatch = dateText.match(/(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})/);
            
            if (!dateMatch) continue;
            
            const year = parseInt(dateMatch[1]);
            const month = parseInt(dateMatch[2]) - 1; // JavaScript月份从0开始
            const day = parseInt(dateMatch[3]);
            const releaseDate = new Date(year, month, day);
            
            // 只处理未来日期
            if (releaseDate <= today || releaseDate > futureDate) continue;
            
            // 查找该日期下的影片条目
            const movieElements = dateElement.querySelectorAll('.movie-item, .product-item, .item, a[href*="/detail/"]');
            
            for (const movieElement of movieElements) {
              try {
                const movieInfo = extractMovieInfoInBrowser(movieElement, releaseDate);
                if (movieInfo && movieInfo.nfoId) {
                  releases.push(movieInfo);
                }
              } catch (movieError) {
                console.warn('提取单个影片信息失败:', movieError.message);
              }
            }
            
          } catch (dateError) {
            console.warn('处理日期元素失败:', dateError.message);
          }
        }

        // 如果上述方法没有找到足够的信息，尝试备用方法
        if (releases.length === 0) {
          const allLinks = document.querySelectorAll('a[href*="/detail/"], a[href*="cid="]');
          
          for (const link of allLinks) {
            try {
              const movieInfo = extractMovieInfoFromLinkInBrowser(link);
              if (movieInfo && movieInfo.nfoId) {
                // 设置默认发售日期为今天+7天
                movieInfo.releaseDate = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                releases.push(movieInfo);
              }
            } catch (linkError) {
              console.warn('从链接提取影片信息失败:', linkError.message);
            }
          }
        }

        return releases;
      }, this.scanDaysAhead);

      log.debug(`[${this.name}] 页面解析完成，提取到 ${upcomingReleases.length} 部未来新品`);
      return upcomingReleases;

    } catch (error) {
      log.error(`[${this.name}] 扫描日历页面失败: ${error.message}`);
      return [];
    }
  }


}

// 创建单例实例
const dmmCalendarMonitor = new DmmCalendarMonitor();

/**
 * 导出主要函数
 * @returns {Promise<Array>} 未来新品信息数组
 */
async function scanForUpcomingReleases() {
  return await dmmCalendarMonitor.scanForUpcomingReleases();
}

module.exports = {
  scanForUpcomingReleases,
  DmmCalendarMonitor
};
