# "旧城改造区"现状勘探报告

## 📋 开发指令 [3.1-勘探] 执行报告

### 🎯 勘探概述
对原 Collector 系统进行深度勘探，为安全接入数据精炼厂提供详尽的现状分析和风险评估。

---

## 第一部分：架构与工作流现状 🏗️

### 1.1 "继任者"模块识别 ✅

**重要发现：collectorService.js 仍然存在且活跃！**

#### 核心模块架构
```
collectorService.js (主控制器)
├── BaseCollector.js (基础采集器)
├── ForumACollector.js (x1080x论坛采集器)
├── ForumBCollector.js (其他论坛采集器)
└── RecordManager.js (记录管理器 - 关键转换器)
```

#### 职责分工
- **collectorService.js**: 任务调度、状态管理、生命周期控制
- **BaseCollector.js**: 通用采集逻辑、浏览器管理、错误处理
- **ForumACollector.js**: x1080x论坛特定的采集实现
- **RecordManager.js**: 数据验证、格式转换、数据库交互

### 1.2 当前数据收集工作流图 📊

```mermaid
graph TD
    A[用户启动采集任务] --> B[collectorService.startTask]
    B --> C[选择对应Collector]
    C --> D[ForumACollector.executeTask]
    D --> E[连接Chrome实例 :9222]
    E --> F[performActualScraping]
    F --> G[executeScrapingLogic]
    G --> H[提取帖子数据]
    H --> I[RecordManager.saveResults]
    I --> J[validateResults]
    J --> K[insertCollectedLinks]
    K --> L[generatePostArchiveFile]
    L --> M[生成.md文档]
    M --> N[保存到数据库]
    N --> O[任务完成]
```

### 1.3 风险区域识别 ⚠️

#### 🔴 高风险区域
1. **Chrome依赖**: 依赖用户手动启动Chrome实例 (端口9222)
2. **单线程阻塞**: 采集过程会阻塞主线程
3. **错误传播**: 任何环节失败都会导致整个任务失败
4. **数据格式耦合**: 与特定论坛格式强耦合

#### 🟡 中风险区域
1. **浏览器资源**: 长时间占用浏览器资源
2. **内存泄漏**: 大量数据处理可能导致内存问题
3. **网络依赖**: 完全依赖网络连接稳定性

#### 🟢 低风险区域
1. **数据库操作**: 使用事务，相对安全
2. **文件操作**: 有完善的错误处理
3. **日志记录**: 详细的日志追踪

---

## 第二部分：关键逻辑定位 🔍

### 2.1 .md → .json 转换器定位 ✅

**关键发现：RecordManager.js 是核心转换器！**

#### 转换流程
```javascript
// 位置: RecordManager.js:416-500
generatePostArchiveFile(postData, siteProfile) {
  // 1. 生成.md内容
  const archiveContent = this.generateArchiveContent(postData, siteProfile);
  
  // 2. 保存.md文件
  const mdPath = path.join(archiveDir, `${filename}.md`);
  fs.writeFileSync(mdPath, archiveContent, 'utf8');
  
  // 3. 更新数据库中的md_document_path
  await this.databaseService.updateCollectedLinkMdPath(postData.id, mdPath);
}
```

#### .md 内容结构
```markdown
# 帖子标题

## 基本信息
- **论坛**: x1080x
- **发布时间**: 2024-01-01
- **番号**: SSIS-001

## 下载链接
### 磁力链接
magnet:?xt=urn:btih:...

### 网盘链接
https://pan.baidu.com/...

## 帖子内容
[原始HTML内容]

## 提取的图片
![图片1](url1)
![图片2](url2)
```

### 2.2 数据库交互模式分析 📊

#### 核心表结构
```sql
-- collected_links 表 (主要数据表)
CREATE TABLE collected_links (
  id INTEGER PRIMARY KEY,
  source_forum TEXT,           -- 来源论坛
  post_url TEXT UNIQUE,        -- 帖子URL
  post_title TEXT,             -- 帖子标题
  nfoId TEXT,                  -- 番号
  magnet_link TEXT,            -- 磁力链接
  ed2k_link TEXT,              -- ED2K链接
  attachment_url TEXT,         -- 附件URL
  decompression_password TEXT, -- 解压密码
  full_post_html TEXT,         -- 完整HTML
  full_post_text TEXT,         -- 纯文本
  post_body_text TEXT,         -- 帖子正文
  all_images TEXT,             -- 所有图片(JSON)
  all_links TEXT,              -- 所有链接(JSON)
  cloud_links TEXT,            -- 网盘链接(JSON)
  extracted_metadata TEXT,     -- 提取的元数据(JSON)
  board_info TEXT,             -- 版块信息(JSON)
  status TEXT DEFAULT 'pending', -- 状态
  preview_image_url TEXT,      -- 预览图URL
  post_date TEXT,              -- 发布日期
  md_document_path TEXT,       -- .md文档路径 ⭐
  ai_tags_json TEXT            -- AI标签(JSON)
);
```

#### 数据流向
```
论坛帖子 → Collector采集 → RecordManager验证 → 数据库存储 → .md文档生成
```

---

## 第三部分：接入风险评估 ⚠️

### 3.1 与数据精炼厂的冲突点

#### 🔴 严重冲突
1. **数据格式不兼容**: 
   - Collector输出: 论坛帖子数据
   - 精炼厂期望: 影片元数据
   
2. **工作流冲突**:
   - Collector: 批量采集 → 数据库存储
   - 精炼厂: 单个刮削 → 即时精炼

3. **数据源差异**:
   - Collector: 论坛、网盘链接
   - 精炼厂: 官方站点元数据

#### 🟡 潜在冲突
1. **性能影响**: 两个系统同时运行可能影响性能
2. **数据库竞争**: 可能同时写入数据库
3. **资源争夺**: 都需要浏览器资源

### 3.2 安全接入策略建议

#### 🟢 推荐方案：平行共存
```
数据精炼厂 (新系统)     Collector系统 (旧系统)
      ↓                        ↓
   .meta.json              collected_links表
      ↓                        ↓
  movies表(displayData)     现有工作流
```

#### 接入步骤
1. **第一阶段**: 完全独立运行，不修改Collector
2. **第二阶段**: 添加数据桥接，让精炼厂读取collected_links
3. **第三阶段**: 逐步迁移功能，最终替换

---

## 第四部分：技术债务分析 💳

### 4.1 代码质量问题
- **硬编码配置**: 大量硬编码的选择器和URL
- **错误处理不一致**: 不同模块的错误处理方式不统一
- **缺乏类型检查**: JavaScript代码缺乏类型安全
- **测试覆盖不足**: 缺乏自动化测试

### 4.2 架构问题
- **紧耦合**: 各模块之间耦合度较高
- **单一职责违反**: 某些类承担了过多职责
- **扩展性差**: 添加新论坛需要大量代码修改

### 4.3 维护成本
- **依赖外部环境**: 依赖用户手动启动Chrome
- **调试困难**: 复杂的异步流程难以调试
- **文档不足**: 缺乏详细的技术文档

---

## 第五部分：勘探结论与建议 📝

### 5.1 核心发现
1. **Collector系统仍然活跃**: 不是"废弃"系统，而是"现役"系统
2. **功能相对独立**: 与影片管理功能相对独立
3. **数据价值较高**: collected_links表包含大量有价值的论坛数据
4. **技术栈相似**: 都使用Playwright和数据库操作

### 5.2 接入建议
1. **避免直接替换**: Collector有其独特价值，不应直接替换
2. **数据桥接优先**: 通过数据层面的桥接实现互通
3. **渐进式集成**: 分阶段、低风险的集成方式
4. **保持独立性**: 两个系统保持相对独立，避免相互影响

### 5.3 下一步行动
1. **设计数据桥接方案**: 让精炼厂能够读取和利用collected_links数据
2. **建立隔离机制**: 确保两个系统不会相互干扰
3. **制定迁移计划**: 长期的功能整合和优化计划

---

## 📊 勘探统计

- **勘探文件数**: 8个核心文件
- **代码行数**: ~3000行
- **风险点识别**: 12个
- **接入方案**: 3个阶段
- **建议优先级**: 中等（非紧急，但有价值）

**勘探结论**: "旧城改造区"状况良好，具有改造价值，建议采用"平行共存"策略安全接入数据精炼厂。

---

## 附录A：数据桥接技术方案 🌉

### A.1 数据映射关系
```javascript
// collected_links → 精炼厂数据映射
const dataMapping = {
  // 基础信息映射
  nfoId: 'collected_links.nfoId',           // 番号
  title: 'collected_links.post_title',      // 标题
  sourceUrl: 'collected_links.post_url',    // 来源URL

  // 链接信息映射
  magnetLinks: 'collected_links.magnet_link', // 磁力链接
  cloudLinks: 'JSON.parse(collected_links.cloud_links)', // 网盘链接

  // 元数据映射
  extractedData: 'JSON.parse(collected_links.extracted_metadata)',
  images: 'JSON.parse(collected_links.all_images)',

  // 时间信息
  collectedAt: 'collected_links.created_at',
  postDate: 'collected_links.post_date'
};
```

### A.2 桥接服务设计
```javascript
// 新建: main_process/services/collectorBridgeService.js
class CollectorBridgeService {
  // 从collected_links提取影片信息
  async extractMovieDataFromCollectedLinks(nfoId) {
    const links = await this.getCollectedLinksByNfoId(nfoId);
    return this.transformToMovieData(links);
  }

  // 为精炼厂提供补充数据
  async getSupplementaryData(nfoId) {
    return {
      magnetLinks: await this.getMagnetLinks(nfoId),
      cloudLinks: await this.getCloudLinks(nfoId),
      forumDiscussions: await this.getForumDiscussions(nfoId),
      userComments: await this.getUserComments(nfoId)
    };
  }
}
```

### A.3 集成点设计
```javascript
// 在 scraperManager.js 中添加桥接
async function refineAndSaveData(nfoId, sourceData) {
  // 现有的精炼逻辑...
  const displayData = await refineDisplayData(nfoId, sourceData);

  // 【新增】从Collector系统获取补充数据
  const collectorBridge = require('./collectorBridgeService');
  const supplementaryData = await collectorBridge.getSupplementaryData(nfoId);

  // 合并到最终档案
  const metaData = {
    // 现有字段...
    display_data: displayData,
    source_data: sourceData,

    // 【新增】Collector补充数据
    collector_data: supplementaryData,

    custom_data: {
      // 现有字段...
      // 【新增】论坛相关数据
      forum_links: supplementaryData.magnetLinks,
      cloud_storage: supplementaryData.cloudLinks,
      community_discussions: supplementaryData.forumDiscussions
    }
  };

  // 保存逻辑...
}
```

---

## 附录B：风险缓解措施 🛡️

### B.1 数据库隔离
```sql
-- 为精炼厂创建专用视图，避免直接访问collected_links
CREATE VIEW collector_movie_data AS
SELECT
  nfoId,
  post_title as title,
  magnet_link,
  cloud_links,
  extracted_metadata,
  post_date,
  created_at
FROM collected_links
WHERE nfoId IS NOT NULL
  AND nfoId != '';
```

### B.2 性能隔离
```javascript
// 使用队列机制避免资源竞争
class ResourceManager {
  constructor() {
    this.collectorQueue = new Queue('collector-tasks');
    this.refineryQueue = new Queue('refinery-tasks');
  }

  async scheduleCollectorTask(task) {
    // 检查精炼厂是否繁忙
    if (this.refineryQueue.length > 10) {
      await this.waitForRefineryIdle();
    }
    return this.collectorQueue.add(task);
  }
}
```

### B.3 错误隔离
```javascript
// 确保一个系统的错误不影响另一个系统
try {
  const supplementaryData = await collectorBridge.getSupplementaryData(nfoId);
  metaData.collector_data = supplementaryData;
} catch (error) {
  log.warn(`[数据精炼厂] Collector桥接失败，继续使用基础数据: ${error.message}`);
  metaData.collector_data = null;
}
```

---

## 附录C：迁移路线图 🗺️

### C.1 第一阶段：观察期 (1-2周)
- ✅ 完成现状勘探
- ⏳ 监控两个系统的运行状况
- ⏳ 识别潜在冲突点
- ⏳ 制定详细的桥接方案

### C.2 第二阶段：桥接期 (2-3周)
- ⏳ 实现 collectorBridgeService
- ⏳ 创建数据库视图和安全访问层
- ⏳ 在精炼厂中集成补充数据
- ⏳ 进行小规模测试

### C.3 第三阶段：优化期 (长期)
- ⏳ 性能优化和资源管理
- ⏳ 用户界面集成
- ⏳ 功能整合和重复代码消除
- ⏳ 最终架构优化

**总体策略**: 保守稳健，确保现有功能不受影响的前提下，逐步实现系统间的数据互通和功能协同。
