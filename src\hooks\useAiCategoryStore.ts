// src/hooks/useAiCategoryStore.ts
import { create } from 'zustand';

export interface AiCategory {
  id: number;
  name: string;
  created_at: string;
}

interface AiCategoryState {
  categories: AiCategory[];
  isLoading: boolean;
  error: string | null;
  fetchCategories: () => Promise<void>;
  addCategory: (name: string) => Promise<boolean>;
  updateCategory: (id: number, newName: string) => Promise<boolean>;
  deleteCategory: (id: number) => Promise<boolean>;
  clearError: () => void;
}

export const useAiCategoryStore = create<AiCategoryState>((set, get) => ({
  categories: [],
  isLoading: false,
  error: null,

  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await window.sfeElectronAPI.aiCategoriesGetAll();
      if (response.success) {
        set({ categories: response.data, isLoading: false });
      } else {
        set({ error: response.error || '获取分类标签失败', isLoading: false });
      }
    } catch (error) {
      console.error('获取AI分类标签失败:', error);
      set({ error: '获取分类标签失败', isLoading: false });
    }
  },

  addCategory: async (name: string) => {
    if (!name.trim()) {
      set({ error: '分类名称不能为空' });
      return false;
    }

    set({ error: null });
    try {
      const result = await window.sfeElectronAPI.aiCategoriesAdd(name.trim());
      if (result.success) {
        await get().fetchCategories(); // 成功后刷新列表
        return true;
      } else {
        set({ error: result.error || '添加分类标签失败' });
        return false;
      }
    } catch (error) {
      console.error('添加AI分类标签失败:', error);
      set({ error: '添加分类标签失败' });
      return false;
    }
  },

  updateCategory: async (id: number, newName: string) => {
    if (!newName.trim()) {
      set({ error: '分类名称不能为空' });
      return false;
    }

    set({ error: null });
    try {
      const result = await window.sfeElectronAPI.aiCategoriesUpdate(id, newName.trim());
      if (result.success) {
        await get().fetchCategories(); // 成功后刷新列表
        return true;
      } else {
        set({ error: result.error || '更新分类标签失败' });
        return false;
      }
    } catch (error) {
      console.error('更新AI分类标签失败:', error);
      set({ error: '更新分类标签失败' });
      return false;
    }
  },

  deleteCategory: async (id: number) => {
    set({ error: null });
    try {
      const result = await window.sfeElectronAPI.aiCategoriesDelete(id);
      if (result.success) {
        await get().fetchCategories(); // 成功后刷新列表
        return true;
      } else {
        set({ error: result.error || '删除分类标签失败' });
        return false;
      }
    } catch (error) {
      console.error('删除AI分类标签失败:', error);
      set({ error: '删除分类标签失败' });
      return false;
    }
  },

  clearError: () => {
    set({ error: null });
  }
}));
