import { NextRequest, NextResponse } from 'next/server';
import { VideoProcessor } from '@/lib/services/video-processor';
import { promises as fs } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      videoPath, 
      timestamp = 10, 
      quality = 'hd_640p',
      outputDir = './cache/thumbnails'
    } = body;

    if (!videoPath) {
      return NextResponse.json(
        {
          success: false,
          error: 'Video path is required',
        },
        { status: 400 }
      );
    }

    // Ensure output directory exists
    await fs.mkdir(outputDir, { recursive: true });

    // Generate unique filename
    const videoName = path.basename(videoPath, path.extname(videoPath));
    const thumbnailName = `${videoName}_${timestamp}s_${quality}.jpg`;
    const outputPath = path.join(outputDir, thumbnailName);

    // Check if thumbnail already exists
    try {
      await fs.access(outputPath);
      // Thumbnail exists, return existing path
      return NextResponse.json({
        success: true,
        data: {
          thumbnailPath: outputPath,
          cached: true,
        },
      });
    } catch {
      // Thumbnail doesn't exist, generate it
    }

    // Generate thumbnail
    const thumbnailPath = await VideoProcessor.generateThumbnail(
      videoPath,
      outputPath,
      { timestamp, quality }
    );

    return NextResponse.json({
      success: true,
      data: {
        thumbnailPath,
        cached: false,
      },
    });
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate thumbnail',
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const videoPath = searchParams.get('videoPath');
    const count = parseInt(searchParams.get('count') || '6');
    const quality = searchParams.get('quality') as any || 'hd_640p';
    const outputDir = searchParams.get('outputDir') || './cache/thumbnails';

    if (!videoPath) {
      return NextResponse.json(
        {
          success: false,
          error: 'Video path is required',
        },
        { status: 400 }
      );
    }

    // Generate multiple thumbnails
    const videoName = path.basename(videoPath, path.extname(videoPath));
    const thumbnailDir = path.join(outputDir, videoName);

    const thumbnails = await VideoProcessor.generateMultipleThumbnails(
      videoPath,
      thumbnailDir,
      count,
      quality
    );

    return NextResponse.json({
      success: true,
      data: {
        thumbnails,
        count: thumbnails.length,
      },
    });
  } catch (error) {
    console.error('Error generating multiple thumbnails:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate thumbnails',
      },
      { status: 500 }
    );
  }
}
