import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>Folder, LuFile, LuDownload, LuTrash2, LuPlay } from 'react-icons/lu';

interface StagingItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  size: number;
  addedDate: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress?: number;
  nfoId?: string;
  filePath?: string;
  extension?: string;
}

const StagingAreaPage: React.FC = () => {
  const [stagingItems, setStagingItems] = useState<StagingItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStagingItems = async () => {
      try {
        setIsLoading(true);
        console.log('开始扫描下载中转站...');

        const result = await window.sfeElectronAPI.stagingScan();
        console.log('中转站扫描结果:', result);

        if (result.success && result.files) {
          // 转换数据格式以匹配界面需求
          const formattedItems: StagingItem[] = result.files.map((file: any, index: number) => ({
            id: `${index + 1}`,
            name: file.fileName,
            type: 'file' as const,
            size: file.fileSize,
            addedDate: new Date(file.lastModified).toLocaleString('zh-CN'),
            status: 'pending' as const,
            nfoId: file.nfoId,
            filePath: file.filePath,
            extension: file.extension
          }));

          setStagingItems(formattedItems);
          console.log(`成功加载 ${formattedItems.length} 个中转站文件`);
        } else {
          console.warn('中转站数据为空或扫描失败:', result.error);
          setStagingItems([]);
        }
      } catch (error) {
        console.error('扫描中转站失败:', error);
        setStagingItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStagingItems();
  }, []);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400';
      case 'processing': return 'text-blue-400';
      case 'completed': return 'text-green-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return '等待处理';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'error': return '处理失败';
      default: return '未知状态';
    }
  };

  const handleProcess = (item: StagingItem) => {
    // TODO: 实现处理逻辑
    console.log('处理项目:', item);
  };

  const handleDelete = (item: StagingItem) => {
    // TODO: 实现删除逻辑
    console.log('删除项目:', item);
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
            <LuClock className="h-8 w-8 text-blue-500" />
            中转站
          </h1>
          <p className="text-gray-400">管理待处理的影片文件和文件夹</p>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuClock className="h-8 w-8 text-yellow-400" />
              <div>
                <p className="text-gray-400 text-sm">等待处理</p>
                <p className="text-white text-2xl font-bold">
                  {stagingItems.filter(item => item.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuPlay className="h-8 w-8 text-blue-400" />
              <div>
                <p className="text-gray-400 text-sm">处理中</p>
                <p className="text-white text-2xl font-bold">
                  {stagingItems.filter(item => item.status === 'processing').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuDownload className="h-8 w-8 text-green-400" />
              <div>
                <p className="text-gray-400 text-sm">已完成</p>
                <p className="text-white text-2xl font-bold">
                  {stagingItems.filter(item => item.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center gap-3">
              <LuFolder className="h-8 w-8 text-[#B8860B]" />
              <div>
                <p className="text-gray-400 text-sm">总项目</p>
                <p className="text-white text-2xl font-bold">{stagingItems.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 项目列表 */}
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-6 animate-pulse">
                <div className="h-6 bg-gray-700 rounded w-1/3 mb-4"></div>
                <div className="h-4 bg-gray-700 rounded w-1/4 mb-2"></div>
                <div className="h-4 bg-gray-700 rounded w-1/6"></div>
              </div>
            ))}
          </div>
        ) : stagingItems.length > 0 ? (
          <div className="space-y-4">
            {stagingItems.map((item) => (
              <div
                key={item.id}
                className="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gray-700 rounded-lg">
                      {item.type === 'folder' ? (
                        <LuFolder className="h-6 w-6 text-blue-400" />
                      ) : (
                        <LuFile className="h-6 w-6 text-green-400" />
                      )}
                    </div>
                    
                    <div>
                      <h3 className="text-white font-semibold text-lg">{item.name}</h3>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-gray-400 text-sm">{formatFileSize(item.size)}</span>
                        <span className="text-gray-400 text-sm">{item.addedDate}</span>
                        <span className={`text-sm font-medium ${getStatusColor(item.status)}`}>
                          {getStatusLabel(item.status)}
                        </span>
                      </div>
                      
                      {item.status === 'processing' && item.progress && (
                        <div className="mt-2 w-64">
                          <div className="bg-gray-700 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${item.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-400 mt-1">{item.progress}%</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {item.status === 'pending' && (
                      <button
                        onClick={() => handleProcess(item)}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        开始处理
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDelete(item)}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      <LuTrash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <LuClock className="h-16 w-16 mx-auto mb-4 text-gray-600" />
            <h3 className="text-xl font-semibold text-white mb-2">中转站为空</h3>
            <p className="text-gray-400">暂无待处理的文件或文件夹</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StagingAreaPage;
