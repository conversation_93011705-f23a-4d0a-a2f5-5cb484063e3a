
// soul-forge-electron/src/components/EmbeddedTrailerPlayerModal.tsx
import React from 'react';

interface EmbeddedTrailerPlayerModalProps {
  isOpen: boolean;
  url: string | null;
  onClose: () => void;
  movieTitle?: string;
}

const EmbeddedTrailerPlayerModal: React.FC<EmbeddedTrailerPlayerModalProps> = ({ isOpen, url, onClose, movieTitle }) => {
  if (!isOpen || !url) return null;

  return (
    <div className="fixed inset-0 z-[90] flex items-center justify-center p-4 bg-black/85 backdrop-blur-md" onClick={onClose}>
      <div className="bg-[#1c1c1c] rounded-lg shadow-2xl w-full max-w-3xl max-h-[85vh] flex flex-col overflow-hidden border border-[#3a3a3a]" onClick={e => e.stopPropagation()}>
        <div className="flex items-center justify-between p-3 border-b border-[#333333] bg-[#222222]">
          <h3 className="text-lg font-semibold text-amber-400 truncate" title={`预告片: ${movieTitle || ''}`}>{`预告片: ${movieTitle || '视频播放'}`}</h3>
          <button onClick={onClose} className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-[#333333]" aria-label="关闭播放器">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>
        <div className="flex-grow p-1 bg-[#111111] flex items-center justify-center">
          <video controls autoPlay src={url} className="w-full h-auto max-h-[calc(85vh-80px)] outline-none" onError={(e) => console.error("视频播放错误:", e)}>
            您的浏览器不支持播放此视频。
          </video>
        </div>
      </div>
    </div>
  );
};

export default EmbeddedTrailerPlayerModal;
    