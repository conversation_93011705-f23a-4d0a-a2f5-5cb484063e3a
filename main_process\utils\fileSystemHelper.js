/**
 * 文件系统助手工具 - 从collectorService.js提取
 * 
 * 提供统一的文件和目录操作功能
 */

const fs = require('fs');
const path = require('path');

let log = null;

/**
 * 初始化文件系统助手
 * @param {Object} logger - 日志对象
 */
function initializeFileSystemHelper(logger) {
  log = logger;
}

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 是否成功
 */
function ensureDirectoryExists(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      if (log) {
        log.info(`[FileSystemHelper] 创建目录: ${dirPath}`);
      }
    }
    return true;
  } catch (error) {
    if (log) {
      log.error(`[FileSystemHelper] 创建目录失败 ${dirPath}: ${error.message}`);
    }
    return false;
  }
}

/**
 * 安全地写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {string} encoding - 编码格式，默认utf8
 * @returns {boolean} 是否成功
 */
function safeWriteFile(filePath, content, encoding = 'utf8') {
  try {
    // 确保目录存在
    const dirPath = path.dirname(filePath);
    if (!ensureDirectoryExists(dirPath)) {
      return false;
    }

    fs.writeFileSync(filePath, content, encoding);
    return true;
  } catch (error) {
    if (log) {
      log.error(`[FileSystemHelper] 写入文件失败 ${filePath}: ${error.message}`);
    }
    return false;
  }
}

/**
 * 安全地读取文件
 * @param {string} filePath - 文件路径
 * @param {string} encoding - 编码格式，默认utf8
 * @returns {string|null} 文件内容，失败返回null
 */
function safeReadFile(filePath, encoding = 'utf8') {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }
    return fs.readFileSync(filePath, encoding);
  } catch (error) {
    if (log) {
      log.error(`[FileSystemHelper] 读取文件失败 ${filePath}: ${error.message}`);
    }
    return null;
  }
}

/**
 * 安全地重命名文件
 * @param {string} oldPath - 原文件路径
 * @param {string} newPath - 新文件路径
 * @returns {boolean} 是否成功
 */
function safeRenameFile(oldPath, newPath) {
  try {
    if (!fs.existsSync(oldPath)) {
      if (log) {
        log.warn(`[FileSystemHelper] 源文件不存在: ${oldPath}`);
      }
      return false;
    }

    // 确保目标目录存在
    const targetDir = path.dirname(newPath);
    if (!ensureDirectoryExists(targetDir)) {
      return false;
    }

    fs.renameSync(oldPath, newPath);
    return true;
  } catch (error) {
    if (log) {
      log.error(`[FileSystemHelper] 重命名文件失败 ${oldPath} -> ${newPath}: ${error.message}`);
    }
    return false;
  }
}

/**
 * 获取目录中的文件列表
 * @param {string} dirPath - 目录路径
 * @param {Object} options - 选项
 * @param {Array<string>} options.extensions - 文件扩展名过滤
 * @param {boolean} options.recursive - 是否递归
 * @returns {Array<Object>} 文件信息数组
 */
function getFilesInDirectory(dirPath, options = {}) {
  const { extensions = [], recursive = false } = options;
  const files = [];

  try {
    if (!fs.existsSync(dirPath)) {
      return files;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stats = fs.statSync(fullPath);

      if (stats.isFile()) {
        // 检查扩展名过滤
        if (extensions.length > 0) {
          const ext = path.extname(item).toLowerCase();
          if (!extensions.includes(ext)) {
            continue;
          }
        }

        files.push({
          name: item,
          path: fullPath,
          size: stats.size,
          mtime: stats.mtime,
          isFile: true
        });
      } else if (stats.isDirectory() && recursive) {
        // 递归处理子目录
        const subFiles = getFilesInDirectory(fullPath, options);
        files.push(...subFiles);
      }
    }

    return files;
  } catch (error) {
    if (log) {
      log.error(`[FileSystemHelper] 读取目录失败 ${dirPath}: ${error.message}`);
    }
    return files;
  }
}

/**
 * 清理文件名，移除非法字符
 * @param {string} fileName - 原文件名
 * @returns {string} 清理后的文件名
 */
function sanitizeFileName(fileName) {
  if (!fileName) return '';

  // 移除或替换非法字符
  return fileName
    .replace(/[<>:"/\\|?*]/g, '_')  // 替换非法字符为下划线
    .replace(/\s+/g, ' ')          // 合并多个空格
    .trim()                        // 移除首尾空格
    .substring(0, 255);            // 限制长度
}

module.exports = {
  initializeFileSystemHelper,
  ensureDirectoryExists,
  safeWriteFile,
  safeReadFile,
  safeRenameFile,
  getFilesInDirectory,
  sanitizeFileName
};
