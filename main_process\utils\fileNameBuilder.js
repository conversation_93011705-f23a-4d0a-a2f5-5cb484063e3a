/**
 * 文件名构建服务 - 实施标准化重命名规则
 * 为生成的.md档案和下载的附件提供统一的命名规范
 * 格式：[番号] - [标题] [标签]
 */

const log = require('electron-log');

class FileNameBuilder {
  constructor() {
    // 智能清理与格式统一版 - 不再需要复杂的标签映射表
  }

  /**
   * 构建标准化文件名 - 智能清理与格式统一版
   * @param {Object} postData - 帖子数据对象
   * @param {string} postData.nfoId - 番号
   * @param {string} postData.postTitle - 帖子标题
   * @param {string} postData.decompressionPassword - 解压密码（可选）
   * @param {Object} postData.boardInfo - 板块信息（可选）
   * @returns {string} 标准化的文件名（不含扩展名）
   */
  buildStandardFileName(postData) {
    try {
      log.info('[FileNameBuilder] 🎯 开始构建智能清理文件名');
      log.info('[FileNameBuilder] 输入数据:', {
        nfoId: postData.nfoId,
        title: postData.postTitle,
        boardName: postData.boardInfo?.boardName,
        password: postData.decompressionPassword ? '***' : null
      });

      // 步骤一: 预处理帖子标题 (Pre-process the Post Title)
      const cleanedTitle = this._intelligentTitleCleaning(
        postData.postTitle,
        postData.nfoId,
        postData.boardInfo?.boardName
      );
      log.info('[FileNameBuilder] 智能清理后的标题:', cleanedTitle);

      // 步骤二: 构建最终文件名 (Construct the Final Filename)
      const nameParts = [];

      // 处理番号
      if (postData.nfoId && postData.nfoId.trim()) {
        nameParts.push(`[${postData.nfoId.trim()}]`);
        log.info('[FileNameBuilder] 📋 添加番号部分');
      }

      // 处理标题
      if (cleanedTitle) {
        nameParts.push(cleanedTitle);
        log.info('[FileNameBuilder] 📋 添加标题部分');
      }

      // 处理密码标签
      if (this._hasValidPassword(postData.decompressionPassword)) {
        nameParts.push('[PW]');
        log.info('[FileNameBuilder] 📋 添加密码标签 [PW]');
      }

      // 组合文件名
      const fileName = nameParts.join(' ');

      // 文件名安全处理
      const safeFileName = this._sanitizeFileName(fileName);

      log.info('[FileNameBuilder] 🎯 最终文件名:', safeFileName);
      return safeFileName;

    } catch (error) {
      log.error('[FileNameBuilder] ❌ 构建文件名失败:', error.message);
      // 返回安全的备用文件名
      return this._sanitizeFileName(postData.postTitle || `unknown_${Date.now()}`);
    }
  }

  /**
   * 智能清理帖子标题 - 核心方法
   * @param {string} originalTitle - 原始帖子标题
   * @param {string} nfoId - 番号
   * @param {string} boardName - 板块名称
   * @returns {string} 清理后的标题
   */
  _intelligentTitleCleaning(originalTitle, nfoId, boardName) {
    if (!originalTitle || typeof originalTitle !== 'string') {
      return '';
    }

    let cleanedTitle = originalTitle.trim();
    log.info('[FileNameBuilder] 🧹 开始智能清理标题');
    log.info('[FileNameBuilder] - 原始标题:', cleanedTitle);
    log.info('[FileNameBuilder] - 番号:', nfoId);
    log.info('[FileNameBuilder] - 板块名:', boardName);

    // 1. 清理重复番号
    if (nfoId && nfoId.trim()) {
      cleanedTitle = this._removeRedundantNfoId(cleanedTitle, nfoId.trim());
    }

    // 2. 清理板块名称
    if (boardName && boardName.trim()) {
      cleanedTitle = this._removeBoardName(cleanedTitle, boardName.trim());
    }

    // 3. 清理论坛后缀
    cleanedTitle = this._removeForumSuffixes(cleanedTitle);

    // 4. 清理多余空格和连字符
    cleanedTitle = this._normalizeSpacing(cleanedTitle);

    log.info('[FileNameBuilder] 🧹 智能清理完成:', cleanedTitle);
    return cleanedTitle;
  }

  /**
   * 移除标题开头的重复番号
   * @param {string} title - 标题
   * @param {string} nfoId - 番号
   * @returns {string} 清理后的标题
   */
  _removeRedundantNfoId(title, nfoId) {
    // 🔧 处理番号格式差异：MOND00296 vs MOND-296
    const normalizedNfoId = this._normalizeNfoId(nfoId);
    const alternativeFormats = this._generateNfoIdVariants(nfoId);

    log.info('[FileNameBuilder] 🔍 番号格式分析:');
    log.info('[FileNameBuilder] - 原始番号:', nfoId);
    log.info('[FileNameBuilder] - 标准化番号:', normalizedNfoId);
    log.info('[FileNameBuilder] - 可能的变体:', alternativeFormats);

    // 创建多种可能的番号匹配模式
    const allNfoIdVariants = [nfoId, normalizedNfoId, ...alternativeFormats];

    for (const variant of allNfoIdVariants) {
      if (!variant) continue;

      const patterns = [
        // 完全匹配：MOND-296
        new RegExp(`^${this._escapeRegex(variant)}\\s*[-\\s]*`, 'i'),
        // 带方括号：[MOND-296]
        new RegExp(`^\\[${this._escapeRegex(variant)}\\]\\s*[-\\s]*`, 'i'),
        // 带圆括号：(MOND-296)
        new RegExp(`^\\(${this._escapeRegex(variant)}\\)\\s*[-\\s]*`, 'i')
      ];

      for (const pattern of patterns) {
        if (pattern.test(title)) {
          const cleaned = title.replace(pattern, '').trim();
          log.info('[FileNameBuilder] 🗑️ 移除重复番号:', title, '->', cleaned);
          log.info('[FileNameBuilder] 🗑️ 匹配的变体:', variant);
          return cleaned;
        }
      }
    }

    log.info('[FileNameBuilder] ℹ️ 未找到重复番号，保持原标题');
    return title;
  }

  /**
   * 标准化番号格式
   * @param {string} nfoId - 原始番号
   * @returns {string} 标准化后的番号
   */
  _normalizeNfoId(nfoId) {
    if (!nfoId) return '';

    // MOND00296 -> MOND-296
    const match = nfoId.match(/^([A-Z]+)(\d+)([A-Z]*)$/i);
    if (match) {
      const [, prefix, number, suffix] = match;
      const normalizedNumber = parseInt(number, 10).toString(); // 移除前导零
      return suffix ? `${prefix}-${normalizedNumber}-${suffix}` : `${prefix}-${normalizedNumber}`;
    }

    return nfoId;
  }

  /**
   * 生成番号的可能变体
   * @param {string} nfoId - 原始番号
   * @returns {Array} 番号变体数组
   */
  _generateNfoIdVariants(nfoId) {
    if (!nfoId) return [];

    const variants = [];

    // 处理 MOND00296 -> MOND-296, MOND296 等格式
    const match1 = nfoId.match(/^([A-Z]+)0*(\d+)([A-Z]*)$/i);
    if (match1) {
      const [, prefix, number, suffix] = match1;
      // 添加带连字符的版本
      variants.push(suffix ? `${prefix}-${number}-${suffix}` : `${prefix}-${number}`);
      // 添加无连字符的版本
      variants.push(suffix ? `${prefix}${number}${suffix}` : `${prefix}${number}`);
    }

    // 处理 MOND-296 -> MOND00296, MOND296 等格式
    const match2 = nfoId.match(/^([A-Z]+)-(\d+)(-[A-Z]+)?$/i);
    if (match2) {
      const [, prefix, number, suffix] = match2;
      const paddedNumber = number.padStart(3, '0'); // 补零到3位
      // 添加补零版本
      variants.push(suffix ? `${prefix}0${paddedNumber}${suffix.substring(1)}` : `${prefix}0${paddedNumber}`);
      variants.push(suffix ? `${prefix}${paddedNumber}${suffix.substring(1)}` : `${prefix}${paddedNumber}`);
      // 添加无连字符版本
      variants.push(suffix ? `${prefix}${number}${suffix.substring(1)}` : `${prefix}${number}`);
    }

    return [...new Set(variants)]; // 去重
  }

  /**
   * 移除标题末尾的板块名称
   * @param {string} title - 标题
   * @param {string} boardName - 板块名称
   * @returns {string} 清理后的标题
   */
  _removeBoardName(title, boardName) {
    // 创建板块名称的匹配模式，更精确的匹配
    const boardPatterns = [
      // 精确匹配：- 高清有码
      new RegExp(`\\s*-\\s*${this._escapeRegex(boardName)}\\s*$`, 'i'),
      // 直接匹配：高清有码
      new RegExp(`\\s+${this._escapeRegex(boardName)}\\s*$`, 'i'),
      // 带多种分隔符：- VR视频 - 其他内容
      new RegExp(`\\s*-\\s*${this._escapeRegex(boardName)}(?:\\s*-.*)?$`, 'i')
    ];

    for (const pattern of boardPatterns) {
      if (pattern.test(title)) {
        const cleaned = title.replace(pattern, '').trim();
        log.info('[FileNameBuilder] 🗑️ 移除板块名称:', title, '->', cleaned);
        return cleaned;
      }
    }

    return title;
  }

  /**
   * 移除论坛后缀
   * @param {string} title - 标题
   * @returns {string} 清理后的标题
   */
  _removeForumSuffixes(title) {
    const suffixesToRemove = [
      // 🔧 修复：添加4K超清和高清有码板块的后缀
      / - 4K超清 - x1080x\.com - Powered by Discuz!$/i,
      / - 高清有码 - x1080x\.com - Powered by Discuz!$/i,
      / - S-cute \/ Mywife - x1080x\.com - Powered by Discuz!$/i,
      / - x1080x\.com - Powered by Discuz!$/i,
      / - Powered by Discuz!$/i,
      / - 4K超清$/i,
      / - 高清有码$/i,
      / - 98堂$/i,
      / - 98tang\.com$/i
    ];

    let cleaned = title;
    for (const suffix of suffixesToRemove) {
      if (suffix.test(cleaned)) {
        cleaned = cleaned.replace(suffix, '').trim();
        log.info('[FileNameBuilder] 🗑️ 移除论坛后缀');
        break;
      }
    }

    return cleaned;
  }

  /**
   * 标准化空格和连字符
   * @param {string} title - 标题
   * @returns {string} 标准化后的标题
   */
  _normalizeSpacing(title) {
    return title
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\s+-\s+/g, ' - ') // 只标准化有空格的连字符，保持FC2-PPV-123456这种格式
      .trim();
  }

  /**
   * 转义正则表达式特殊字符
   * @param {string} string - 要转义的字符串
   * @returns {string} 转义后的字符串
   */
  _escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }







  /**
   * 检查是否有有效的密码
   * @param {string} password - 密码字符串
   * @returns {boolean} 是否有有效密码
   */
  _hasValidPassword(password) {
    // 检查密码是否存在且不为空（去除空白字符后）
    return password && typeof password === 'string' && password.trim().length > 0;
  }

  /**
   * 文件名安全处理
   * @param {string} fileName - 原始文件名
   * @returns {string} 安全的文件名
   */
  _sanitizeFileName(fileName) {
    if (!fileName) return '';

    // 移除或替换不安全的字符
    return fileName
      .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim()
      .substring(0, 150); // 限制150个字符
  }
}

module.exports = new FileNameBuilder();
