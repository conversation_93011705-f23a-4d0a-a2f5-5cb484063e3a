import { Movie, FilterOptions, SortField, SortOrder, ApiResponse, PaginationInfo } from '@/lib/types';

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}/api${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Movie API methods
  async getMovies(
    filters: FilterOptions = {},
    sortField: SortField = 'lastScanned',
    sortOrder: SortOrder = 'desc',
    page: number = 1,
    limit: number = 20
  ): Promise<ApiResponse<Movie[]> & { pagination: PaginationInfo }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sortField,
      sortOrder,
    });

    // Add filters to params
    if (filters.search) params.append('search', filters.search);
    if (filters.genres?.length) params.append('genres', filters.genres.join(','));
    if (filters.actors?.length) params.append('actors', filters.actors.join(','));
    if (filters.studios?.length) params.append('studios', filters.studios.join(','));
    if (filters.years?.length) params.append('years', filters.years.join(','));
    if (filters.watched !== undefined) params.append('watched', filters.watched.toString());
    if (filters.favorited !== undefined) params.append('favorited', filters.favorited.toString());
    if (filters.libraryId) params.append('libraryId', filters.libraryId);

    return this.request<Movie[]>(`/movies?${params}`);
  }

  async getMovie(id: string): Promise<ApiResponse<Movie>> {
    return this.request<Movie>(`/movies/${id}`);
  }

  async createMovie(movie: Partial<Movie>): Promise<ApiResponse<Movie>> {
    return this.request<Movie>('/movies', {
      method: 'POST',
      body: JSON.stringify(movie),
    });
  }

  async updateMovie(id: string, updates: Partial<Movie>): Promise<ApiResponse<Movie>> {
    return this.request<Movie>(`/movies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteMovie(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/movies/${id}`, {
      method: 'DELETE',
    });
  }

  async toggleMovieWatched(id: string): Promise<ApiResponse<Movie>> {
    return this.request<Movie>(`/movies/${id}/watched`, {
      method: 'PATCH',
    });
  }

  async scanMovies(directories: string[], libraryId?: string): Promise<ApiResponse<any>> {
    return this.request<any>('/movies/scan', {
      method: 'POST',
      body: JSON.stringify({ directories, libraryId }),
    });
  }

  // Media API methods
  async generateThumbnail(
    videoPath: string,
    timestamp?: number,
    quality?: string,
    outputDir?: string
  ): Promise<ApiResponse<{ thumbnailPath: string; cached: boolean }>> {
    return this.request<{ thumbnailPath: string; cached: boolean }>('/media/thumbnails', {
      method: 'POST',
      body: JSON.stringify({ videoPath, timestamp, quality, outputDir }),
    });
  }

  async generateMultipleThumbnails(
    videoPath: string,
    count?: number,
    quality?: string,
    outputDir?: string
  ): Promise<ApiResponse<{ thumbnails: string[]; count: number }>> {
    const params = new URLSearchParams({
      videoPath,
      count: count?.toString() || '6',
      quality: quality || 'hd_640p',
      outputDir: outputDir || './cache/thumbnails',
    });

    return this.request<{ thumbnails: string[]; count: number }>(`/media/thumbnails?${params}`);
  }

  // Library API methods (placeholder)
  async getLibraries(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>('/libraries');
  }

  async createLibrary(library: any): Promise<ApiResponse<any>> {
    return this.request<any>('/libraries', {
      method: 'POST',
      body: JSON.stringify(library),
    });
  }

  async updateLibrary(id: string, updates: any): Promise<ApiResponse<any>> {
    return this.request<any>(`/libraries/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteLibrary(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/libraries/${id}`, {
      method: 'DELETE',
    });
  }

  // Settings API methods (placeholder)
  async getSettings(): Promise<ApiResponse<any>> {
    return this.request<any>('/settings');
  }

  async updateSettings(settings: any): Promise<ApiResponse<any>> {
    return this.request<any>('/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // AI API methods (placeholder)
  async analyzeMovie(movieId: string): Promise<ApiResponse<any>> {
    return this.request<any>('/ai/analyze', {
      method: 'POST',
      body: JSON.stringify({ movieId }),
    });
  }

  async translateText(text: string, targetLanguage: string): Promise<ApiResponse<any>> {
    return this.request<any>('/ai/translate', {
      method: 'POST',
      body: JSON.stringify({ text, targetLanguage }),
    });
  }

  async chatWithAI(message: string, context?: any): Promise<ApiResponse<any>> {
    return this.request<any>('/ai/chat', {
      method: 'POST',
      body: JSON.stringify({ message, context }),
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for testing or custom instances
export { ApiClient };
