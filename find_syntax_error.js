const fs = require('fs');
const { spawn } = require('child_process');

async function findSyntaxError() {
  const content = fs.readFileSync('main.js', 'utf8');
  const lines = content.split('\n');
  
  console.log(`总共 ${lines.length} 行，开始二分查找语法错误...`);
  
  let left = 1;
  let right = lines.length;
  let lastGoodLine = 0;
  
  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const testContent = lines.slice(0, mid).join('\n');
    
    // 写入临时文件
    fs.writeFileSync('temp_test.js', testContent);
    
    try {
      // 测试语法
      const result = await new Promise((resolve) => {
        const child = spawn('node', ['-c', 'temp_test.js'], { stdio: 'pipe' });
        child.on('close', (code) => {
          resolve(code === 0);
        });
      });
      
      if (result) {
        console.log(`✅ 前 ${mid} 行语法正确`);
        lastGoodLine = mid;
        left = mid + 1;
      } else {
        console.log(`❌ 前 ${mid} 行有语法错误`);
        right = mid - 1;
      }
    } catch (error) {
      console.log(`❌ 前 ${mid} 行有语法错误: ${error.message}`);
      right = mid - 1;
    }
  }
  
  console.log(`\n🎯 语法错误可能在第 ${lastGoodLine + 1} 行附近`);
  
  // 显示问题行附近的代码
  const start = Math.max(0, lastGoodLine - 5);
  const end = Math.min(lines.length, lastGoodLine + 10);
  
  console.log(`\n📋 第 ${start + 1} - ${end} 行的代码:`);
  for (let i = start; i < end; i++) {
    const marker = i === lastGoodLine ? ' ← 最后正确行' : '';
    console.log(`${String(i + 1).padStart(4)}: ${lines[i]}${marker}`);
  }
  
  // 清理临时文件
  try {
    fs.unlinkSync('temp_test.js');
  } catch (e) {}
}

findSyntaxError().catch(console.error);
