/**
 * 测试修复后的 collectorService
 */

const path = require('path');

// 模拟日志对象
const mockLog = {
  info: (msg) => console.log(`[INFO] ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${msg}`),
  error: (msg) => console.error(`[ERROR] ${msg}`)
};

async function testCollectorService() {
  console.log('🔧 测试修复后的 collectorService');
  
  try {
    // 1. 测试导入
    console.log('\n1️⃣ 测试导入 collectorService');
    const collectorService = require('./main_process/services/collectorService');
    console.log('✅ collectorService 导入成功');
    
    // 2. 测试初始化
    console.log('\n2️⃣ 测试初始化');
    collectorService.initializeCollectorService(mockLog, process.cwd());
    console.log('✅ collectorService 初始化成功');
    
    // 3. 测试获取论坛列表
    console.log('\n3️⃣ 测试获取论坛列表');
    const forumsResult = collectorService.getForums();
    if (forumsResult.success) {
      console.log('✅ 获取论坛列表成功');
      console.log(`论坛数量: ${forumsResult.forums.length}`);
      forumsResult.forums.forEach((forum, index) => {
        console.log(`  ${index + 1}. ${forum.key}: ${forum.name}`);
      });
    } else {
      console.log('❌ 获取论坛列表失败:', forumsResult.error);
      return false;
    }
    
    // 4. 测试状态获取
    console.log('\n4️⃣ 测试状态获取');
    const status = collectorService.getStatus();
    console.log('✅ 状态获取成功');
    console.log(`当前状态: ${status.currentStatus.status}`);
    console.log(`是否运行中: ${status.isRunning}`);
    
    // 5. 测试方法存在性
    console.log('\n5️⃣ 测试关键方法存在性');
    const methods = [
      'startTask',
      'stopTask',
      'executeCollectionTask',
      'executeScrapingLogic',
      'parsePostContent',
      'downloadAttachments',
      'saveResults'
    ];
    
    for (const method of methods) {
      if (typeof collectorService[method] === 'function') {
        console.log(`✅ ${method} 方法存在`);
      } else {
        console.log(`❌ ${method} 方法缺失`);
      }
    }
    
    // 6. 测试调度器方法是否被移除
    console.log('\n6️⃣ 检查调度器方法是否已移除');
    if (typeof collectorService.executeWithCollectorDispatcher === 'function') {
      console.log('⚠️ executeWithCollectorDispatcher 方法仍然存在（可能导致问题）');
    } else {
      console.log('✅ executeWithCollectorDispatcher 方法已移除');
    }
    
    // 7. 测试采集器类导入是否被移除
    console.log('\n7️⃣ 检查采集器类导入');
    try {
      // 尝试访问可能存在的采集器引用
      if (collectorService.currentCollector !== undefined) {
        console.log('⚠️ currentCollector 属性仍然存在');
      } else {
        console.log('✅ currentCollector 属性不存在');
      }
    } catch (error) {
      console.log('✅ 没有采集器相关属性');
    }
    
    console.log('\n🎉 collectorService 基础测试完成');
    return true;
    
  } catch (error) {
    console.error('❌ collectorService 测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
    return false;
  }
}

async function testReturnStructure() {
  console.log('\n🔍 测试返回值结构');
  
  try {
    // 模拟一个简单的 saveResults 调用
    const collectorService = require('./main_process/services/collectorService');
    
    // 创建模拟数据
    const mockResults = [
      {
        postUrl: 'http://example.com/post1',
        postTitle: '测试帖子1',
        magnetLink: 'magnet:?xt=urn:btih:test1',
        nfoId: 'TEST-001'
      },
      {
        postUrl: 'http://example.com/post2', 
        postTitle: '测试帖子2',
        ed2kLink: 'ed2k://|file|test2.avi|123456|abcdef|/',
        nfoId: 'TEST-002'
      }
    ];
    
    const mockSiteProfile = { key: 'test', name: '测试论坛' };
    
    // 测试 saveResults 方法
    console.log('测试 saveResults 方法返回结构...');
    const saveResult = await collectorService.saveResults(mockResults, mockSiteProfile);
    
    console.log('saveResults 返回结构:');
    console.log(`  collectedCount: ${saveResult.collectedCount}`);
    console.log(`  pages: ${saveResult.pages}`);
    console.log(`  message: ${saveResult.message}`);
    console.log(`  links 数组长度: ${saveResult.links ? saveResult.links.length : 'undefined'}`);
    console.log(`  dbResult: ${saveResult.dbResult ? 'exists' : 'null'}`);
    
    // 验证结构
    if (saveResult.collectedCount === mockResults.length &&
        saveResult.message &&
        saveResult.links &&
        Array.isArray(saveResult.links)) {
      console.log('✅ saveResults 返回结构正确');
    } else {
      console.log('❌ saveResults 返回结构异常');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 返回结构测试失败:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 开始 collectorService 修复验证\n');
  
  const results = [];
  
  results.push(await testCollectorService());
  results.push(await testReturnStructure());
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log('\n=== 测试结果汇总 ===');
  console.log(`通过测试: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！collectorService 修复成功');
    console.log('\n📋 修复内容:');
    console.log('   ✅ 恢复使用 executeCollectionTask 而不是调度器');
    console.log('   ✅ 移除采集器类导入');
    console.log('   ✅ 移除 currentCollector 相关代码');
    console.log('   ✅ 保持原有的返回值结构');
  } else {
    console.log('❌ 部分测试失败，需要进一步检查');
  }
  
  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  testCollectorService,
  testReturnStructure
};
