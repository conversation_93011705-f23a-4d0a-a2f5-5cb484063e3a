import React, { useState, useEffect } from 'react';
import { Radar, Play, Pause, RotateCcw, AlertCircle, CheckCircle, Loader, TrendingUp, Calendar, Clock } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface HistoryScanProgress {
  status: 'started' | 'fetching' | 'filtering' | 'processing' | 'completed' | 'error';
  scanId?: string;
  message?: string;
  dateRange?: string;
  currentDate?: string;
  totalFound?: number;
  currentNfoId?: string;
  dayProgress?: {
    current: number;
    total: number;
    percentage: number;
  };
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
  result?: {
    success: boolean;
    message: string;
    dateRange: string;
    totalFound: number;
    processed: number;
    success: number;
    failed: number;
    skipped: number;
    details: Array<{
      nfoId: string;
      status: 'success' | 'failed';
      message?: string;
      error?: string;
    }>;
  };
}

interface HistoryScannerProps {
  onScanCompleted?: () => void;
  className?: string;
}

export const DailyScanner: React.FC<HistoryScannerProps> = ({
  onScanCompleted,
  className = ''
}) => {
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [progress, setProgress] = useState<HistoryScanProgress | null>(null);
  const [lastScanResult, setLastScanResult] = useState<any>(null);

  // 日期选择状态
  const [startDate, setStartDate] = useState<string>(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // YYYY-MM-DD 格式
  });
  const [endDate, setEndDate] = useState<string>(() => {
    const today = new Date();
    return today.toISOString().split('T')[0]; // YYYY-MM-DD 格式
  });

  // 监听扫描进度
  useEffect(() => {
    const removeListener = window.sfeElectronAPI.onDailyScanProgress((data: HistoryScanProgress) => {
      console.log('[HistoryScanner] 收到进度更新:', data);
      setProgress(data);

      // 处理不同的状态
      switch (data.status) {
        case 'started':
          setIsScanning(true);
          const dateRangeText = data.dateRange || '指定日期范围';
          toast.loading(`开始历史回溯扫描 (${dateRangeText})...`, { id: 'history-scan' });
          break;

        case 'fetching':
          if (data.currentDate && data.dayProgress) {
            toast.loading(
              `正在获取 ${data.currentDate} 的新作... (${data.dayProgress.current}/${data.dayProgress.total})`,
              { id: 'history-scan' }
            );
          } else {
            toast.loading('正在从AV Help Wiki获取历史新作...', { id: 'history-scan' });
          }
          break;

        case 'filtering':
          toast.loading(`发现 ${data.totalFound} 个番号，正在过滤...`, { id: 'history-scan' });
          break;

        case 'processing':
          if (data.currentNfoId && data.progress) {
            toast.loading(
              `处理中 (${data.progress.current}/${data.progress.total}): ${data.currentNfoId}`,
              { id: 'history-scan' }
            );
          }
          break;

        case 'completed':
          setIsScanning(false);
          setLastScanResult(data.result);

          if (data.result) {
            const { totalFound, processed, success: successCount, failed, skipped, dateRange } = data.result;

            if (totalFound === 0) {
              toast.success(`${dateRange} 暂无新发现的番号`, { id: 'history-scan', duration: 4000 });
            } else if (processed === 0) {
              toast.success(
                `${dateRange} 发现 ${totalFound} 个番号，但都已存在于数据库中`,
                { id: 'history-scan', duration: 4000 }
              );
            } else {
              toast.success(
                `历史回溯扫描完成！\n时间范围: ${dateRange}\n发现: ${totalFound} 个\n成功入库: ${successCount} 个\n失败: ${failed} 个\n跳过: ${skipped} 个`,
                { id: 'history-scan', duration: 8000 }
              );
            }

            // 如果有成功入库的项目，触发刷新回调
            if (successCount > 0 && onScanCompleted) {
              onScanCompleted();
            }
          }
          break;

        case 'error':
          setIsScanning(false);
          toast.error(`扫描失败: ${data.message}`, { id: 'history-scan', duration: 6000 });
          break;
      }
    });

    return removeListener;
  }, [onScanCompleted]);

  // 启动历史回溯扫描
  const handleStartScan = async () => {
    try {
      // 验证日期
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        toast.error('请选择有效的日期');
        return;
      }

      if (start > end) {
        toast.error('开始日期不能晚于结束日期');
        return;
      }

      // 检查日期范围是否过大
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      if (daysDiff > 30) {
        toast.error('日期范围不能超过30天，请缩小范围');
        return;
      }

      setProgress(null);
      setLastScanResult(null);

      const result = await window.sfeElectronAPI.startHistoryScan({
        startDate: startDate,
        endDate: endDate
      });

      if (!result.success && result.error) {
        toast.error(`启动扫描失败: ${result.error}`, { duration: 6000 });
      }
    } catch (error) {
      console.error('启动历史回溯扫描失败:', error);
      toast.error('启动扫描失败，请重试');
    }
  };

  // 启动今日扫描（便捷操作）
  const handleStartTodayScan = async () => {
    try {
      const today = new Date().toISOString().split('T')[0];
      setStartDate(today);
      setEndDate(today);

      setProgress(null);
      setLastScanResult(null);

      const result = await window.sfeElectronAPI.startHistoryScan({
        startDate: today,
        endDate: today
      });

      if (!result.success && result.error) {
        toast.error(`启动今日扫描失败: ${result.error}`, { duration: 6000 });
      }
    } catch (error) {
      console.error('启动今日扫描失败:', error);
      toast.error('启动今日扫描失败，请重试');
    }
  };

  // 停止扫描
  const handleStopScan = async () => {
    try {
      const result = await window.sfeElectronAPI.stopDailyScan();
      
      if (result.success && result.stopped) {
        toast.success('扫描已停止');
        setIsScanning(false);
      } else {
        toast.error('停止扫描失败');
      }
    } catch (error) {
      console.error('停止扫描失败:', error);
      toast.error('停止扫描失败');
    }
  };

  // 获取当前状态文本
  const getStatusText = (): string => {
    if (!progress) return '准备就绪';

    switch (progress.status) {
      case 'started':
        return `正在启动... (${progress.dateRange || '指定范围'})`;
      case 'fetching':
        if (progress.currentDate && progress.dayProgress) {
          return `获取 ${progress.currentDate} 新作... (${progress.dayProgress.current}/${progress.dayProgress.total})`;
        }
        return `获取历史新作列表... (${progress.dateRange || '指定范围'})`;
      case 'filtering':
        return `过滤已存在番号... (发现 ${progress.totalFound} 个)`;
      case 'processing':
        if (progress.currentNfoId && progress.progress) {
          return `处理中 (${progress.progress.current}/${progress.progress.total}): ${progress.currentNfoId}`;
        }
        return '批量处理中...';
      case 'completed':
        return '扫描完成';
      case 'error':
        return '扫描失败';
      default:
        return '准备就绪';
    }
  };

  // 获取进度百分比
  const getProgressPercentage = (): number => {
    if (!progress) return 0;

    // 优先使用处理进度
    if (progress.progress) {
      return progress.progress.percentage || 0;
    }

    // 其次使用天数进度
    if (progress.dayProgress) {
      return progress.dayProgress.percentage || 0;
    }

    return 0;
  };

  // 获取进度文本
  const getProgressText = (): string => {
    if (!progress) return '';

    if (progress.progress) {
      return `${progress.progress.current} / ${progress.progress.total}`;
    }

    if (progress.dayProgress) {
      return `第 ${progress.dayProgress.current} / ${progress.dayProgress.total} 天`;
    }

    return '';
  };

  return (
    <div className={`bg-gray-800 rounded-lg p-6 ${className}`}>
      {/* 标题 */}
      <div className="flex items-center gap-3 mb-4">
        <Radar className="h-6 w-6 text-[#B8860B]" />
        <h3 className="text-lg font-bold text-white">历史回溯扫描器</h3>
      </div>

      <p className="text-gray-400 text-sm mb-6">
        从 AV Help Wiki 获取指定日期范围内发布的所有新作番号，支持历史数据回溯和批量虚拟资产创建。
      </p>

      {/* 日期范围选择 */}
      <div className="bg-gray-700 rounded-lg p-4 mb-6">
        <h4 className="text-white font-medium mb-3 flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          选择扫描日期范围
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">开始日期</label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              disabled={isScanning}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:border-[#B8860B] focus:outline-none disabled:opacity-50"
            />
          </div>

          <div>
            <label className="block text-sm text-gray-400 mb-2">结束日期</label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              disabled={isScanning}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:border-[#B8860B] focus:outline-none disabled:opacity-50"
            />
          </div>
        </div>

        <div className="mt-3 text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3" />
            <span>最大支持30天范围，建议单次扫描不超过7天以获得最佳性能</span>
          </div>
        </div>
      </div>

      {/* 状态显示 */}
      <div className="space-y-4">
        {/* 当前状态 */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isScanning ? (
              <Loader className="h-4 w-4 text-[#B8860B] animate-spin" />
            ) : lastScanResult ? (
              <CheckCircle className="h-4 w-4 text-green-400" />
            ) : (
              <Radar className="h-4 w-4 text-gray-400" />
            )}
            <span className="text-white font-medium">{getStatusText()}</span>
          </div>
        </div>

        {/* 进度条 */}
        {isScanning && progress && (progress.progress || progress.dayProgress) && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">
                {progress.progress ? '处理进度' : '扫描进度'}
              </span>
              <span className="text-white">{getProgressPercentage()}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-[#B8860B] h-2 rounded-full transition-all duration-300"
                style={{ width: `${getProgressPercentage()}%` }}
              />
            </div>
            <div className="text-xs text-gray-500 text-center">
              {getProgressText()}
            </div>

            {/* 当前处理信息 */}
            {progress.currentDate && (
              <div className="text-xs text-blue-400 text-center">
                当前日期: {progress.currentDate}
              </div>
            )}
            {progress.currentNfoId && (
              <div className="text-xs text-green-400 text-center">
                当前番号: {progress.currentNfoId}
              </div>
            )}
          </div>
        )}

        {/* 最后扫描结果 */}
        {lastScanResult && !isScanning && (
          <div className="bg-gray-700 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              最近扫描结果
            </h4>

            {/* 日期范围显示 */}
            {lastScanResult.dateRange && (
              <div className="mb-3 text-sm text-blue-300 bg-blue-900/30 rounded px-3 py-2">
                扫描范围: {lastScanResult.dateRange}
              </div>
            )}

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
              <div className="text-center">
                <div className="text-blue-400 font-bold">{lastScanResult.totalFound}</div>
                <div className="text-gray-400">发现</div>
              </div>
              <div className="text-center">
                <div className="text-green-400 font-bold">{lastScanResult.success}</div>
                <div className="text-gray-400">成功</div>
              </div>
              <div className="text-center">
                <div className="text-red-400 font-bold">{lastScanResult.failed}</div>
                <div className="text-gray-400">失败</div>
              </div>
              <div className="text-center">
                <div className="text-yellow-400 font-bold">{lastScanResult.skipped}</div>
                <div className="text-gray-400">跳过</div>
              </div>
            </div>
          </div>
        )}

        {/* 操作说明 */}
        <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
          <h4 className="text-blue-300 font-medium mb-2 flex items-center gap-2">
            <Radar className="h-4 w-4" />
            历史回溯扫描流程
          </h4>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>• 根据指定日期范围访问 AV Help Wiki 历史页面</li>
            <li>• 逐日扫描并自动识别提取所有有效番号</li>
            <li>• 合并多天数据并过滤数据库中已存在的番号</li>
            <li>• 批量刮削新番号的详细信息</li>
            <li>• 创建虚拟资产并入库到数据库</li>
            <li>• 支持中断和恢复，确保数据完整性</li>
          </ul>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-3">
          {/* 主要操作按钮 */}
          <div className="flex gap-3">
            <button
              onClick={handleStartScan}
              disabled={isScanning}
              className="flex-1 px-6 py-3 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isScanning ? (
                <>
                  <Loader className="h-5 w-5 animate-spin" />
                  扫描中...
                </>
              ) : (
                <>
                  <Play className="h-5 w-5" />
                  开始历史扫描
                </>
              )}
            </button>

            {isScanning && (
              <button
                onClick={handleStopScan}
                className="px-4 py-3 bg-red-600 text-white font-medium rounded hover:bg-red-700 flex items-center gap-2"
              >
                <Pause className="h-4 w-4" />
                停止
              </button>
            )}
          </div>

          {/* 便捷操作按钮 */}
          {!isScanning && (
            <div className="flex gap-3">
              <button
                onClick={handleStartTodayScan}
                className="flex-1 px-4 py-2 bg-gray-600 text-white font-medium rounded hover:bg-gray-500 flex items-center justify-center gap-2 text-sm"
              >
                <Calendar className="h-4 w-4" />
                仅扫描今日
              </button>

              <button
                onClick={() => {
                  const yesterday = new Date();
                  yesterday.setDate(yesterday.getDate() - 1);
                  const yesterdayStr = yesterday.toISOString().split('T')[0];
                  setStartDate(yesterdayStr);
                  setEndDate(yesterdayStr);
                }}
                className="flex-1 px-4 py-2 bg-gray-600 text-white font-medium rounded hover:bg-gray-500 flex items-center justify-center gap-2 text-sm"
              >
                <RotateCcw className="h-4 w-4" />
                设为昨日
              </button>

              <button
                onClick={() => {
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  const today = new Date();
                  setStartDate(weekAgo.toISOString().split('T')[0]);
                  setEndDate(today.toISOString().split('T')[0]);
                }}
                className="flex-1 px-4 py-2 bg-gray-600 text-white font-medium rounded hover:bg-gray-500 flex items-center justify-center gap-2 text-sm"
              >
                <Clock className="h-4 w-4" />
                最近一周
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
