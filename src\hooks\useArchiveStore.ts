// src/hooks/useArchiveStore.ts
import { create } from 'zustand';

export interface ArchiveFilters {
  topCategory?: string;
  aiTags?: string[];
  sourceForum?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  keyword?: string;
  page: number;
  pageSize: number;
}

export interface ArchiveRecord {
  id: number;
  source_forum: string;
  post_url: string;
  post_title: string;
  nfoId?: string;
  magnet_link?: string;
  ed2k_link?: string;
  attachment_url?: string;
  decompression_password?: string;
  collection_date: string;
  download_status: string;
  error_message?: string;
  download_path?: string;
  download_date?: string;
  archive_path?: string;
  full_post_html?: string;
  full_post_text?: string;
  post_body_text?: string;
  all_images?: string;
  all_links?: string;
  cloud_links?: string;
  extracted_metadata?: string;
  board_info?: string;
  status: string;
  preview_image_url?: string;
  post_date?: string;
  md_document_path?: string;
  ai_tags_json?: string;
}

interface ArchiveState {
  filters: ArchiveFilters;
  results: ArchiveRecord[];
  total: number;
  loading: boolean;
  error: string | null;

  // 链接预览弹窗状态
  isLinkModalOpen: boolean;
  linksForPreview: string[];

  // 批量选择和AI分析状态
  selectedRecordIds: Set<number>;
  isBatchAnalyzing: boolean;
  batchProgress: {
    status: string;
    processed?: number;
    total?: number;
    currentTitle?: string;
    message?: string;
    successCount?: number;
    errorCount?: number;
  };

  // Actions
  setFilters: (newFilters: Partial<ArchiveFilters>) => void;
  setPage: (page: number) => void;
  fetchResults: () => Promise<void>;
  clearError: () => void;
  resetFilters: () => void;
  openLinkModal: (links: string[]) => void;
  closeLinkModal: () => void;

  // 批量选择和AI分析 Actions
  toggleSelection: (id: number) => void;
  selectAll: (ids: number[]) => void;
  clearSelection: () => void;
  setBatchStatus: (status: any) => void;
}

const defaultFilters: ArchiveFilters = {
  topCategory: '全部',
  aiTags: [],
  sourceForum: '全部',
  dateRange: undefined,
  keyword: '',
  page: 1,
  pageSize: 20
};

export const useArchiveStore = create<ArchiveState>((set, get) => ({
  filters: { ...defaultFilters },
  results: [],
  total: 0,
  loading: false,
  error: null,

  // 链接预览弹窗状态
  isLinkModalOpen: false,
  linksForPreview: [],

  // 批量选择和AI分析状态
  selectedRecordIds: new Set<number>(),
  isBatchAnalyzing: false,
  batchProgress: { status: 'idle' },

  setFilters: (newFilters) => {
    const updatedFilters = { 
      ...get().filters, 
      ...newFilters, 
      page: newFilters.page !== undefined ? newFilters.page : 1 // 筛选变更时重置到第一页，除非明确指定页码
    };
    set({ filters: updatedFilters, error: null });
    get().fetchResults();
  },

  setPage: (page) => {
    set(state => ({ 
      filters: { ...state.filters, page },
      error: null 
    }));
    get().fetchResults();
  },

  fetchResults: async () => {
    set({ loading: true, error: null });
    try {
      const { filters } = get();
      console.log('🔍 开始查询历史档案:', filters);
      
      const response = await window.sfeElectronAPI.archiveComplexQuery(filters);
      
      if (response.success) {
        console.log('✅ 查询成功:', { total: response.total, count: response.data.length });
        set({ 
          results: response.data, 
          total: response.total, 
          loading: false 
        });
      } else {
        console.error('❌ 查询失败:', response.error);
        set({ 
          error: response.error || '查询失败', 
          loading: false,
          results: [],
          total: 0
        });
      }
    } catch (error) {
      console.error('❌ 查询异常:', error);
      set({ 
        error: '查询异常: ' + (error as Error).message, 
        loading: false,
        results: [],
        total: 0
      });
    }
  },

  clearError: () => {
    set({ error: null });
  },

  resetFilters: () => {
    set({ filters: { ...defaultFilters }, error: null });
    get().fetchResults();
  },

  openLinkModal: (links) => {
    set({ isLinkModalOpen: true, linksForPreview: links });
  },

  closeLinkModal: () => {
    set({ isLinkModalOpen: false, linksForPreview: [] });
  },

  // 批量选择和AI分析方法
  toggleSelection: (id) => {
    set(state => {
      const newSelectedIds = new Set(state.selectedRecordIds);
      if (newSelectedIds.has(id)) {
        newSelectedIds.delete(id);
      } else {
        newSelectedIds.add(id);
      }
      return { selectedRecordIds: newSelectedIds };
    });
  },

  selectAll: (ids) => {
    set({ selectedRecordIds: new Set(ids) });
  },

  clearSelection: () => {
    set({ selectedRecordIds: new Set<number>() });
  },

  setBatchStatus: (status) => {
    set(state => ({
      batchProgress: { ...state.batchProgress, ...status },
      isBatchAnalyzing: status.status === 'started' || status.status === 'processing'
    }));
  },
}));
