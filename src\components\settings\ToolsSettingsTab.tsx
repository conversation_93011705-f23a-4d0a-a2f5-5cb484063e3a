// soul-forge-electron/src/components/settings/ToolsSettingsTab.tsx
import React from 'react';
import { LuTextSelect, LuUsers, LuScanSearch, LuTriangle, LuGlobe } from 'react-icons/lu';

interface ToolsSettingsTabProps {
  onLaunchNfoPlotPolisher: () => void;
  onLaunchScraperTest?: () => void;
}

const ToolsSettingsTab: React.FC<ToolsSettingsTabProps> = ({
  onLaunchNfoPlotPolisher,
  onLaunchScraperTest,
}) => {
  
  const handleAvatarScraping = async () => {
    try {
      console.log('启动演员头像刮削...');
      const result = await window.sfeElectronAPI.scrapeActorAvatars();
      console.log('演员头像刮削启动结果:', result);
      if (result.success) {
        alert(`演员头像刮削启动成功！\n\n${result.message || '刮削任务已开始，请查看进度。'}`);
      } else {
        alert(`演员头像刮削启动失败：\n${result.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('启动演员头像刮削时出错:', error);
      alert(`演员头像刮削启动失败：\n${error.message || '请检查控制台错误信息'}`);
    }
  };

  const handleScanAllLibraries = () => {
    // 这个功能确实还没有实现，提供友好的提示
    alert('扫描全部片库\n\n功能说明：\n- 一次性扫描所有已配置的片库\n- 自动更新影片信息和元数据\n- 支持批量处理和进度显示\n\n此功能正在快马加鞭开发中，敬请期待！\n\n您目前可以通过以下方式扫描片库：\n• 主界面片库卡片上的扫描按钮\n• 顶部导航栏的扫描按钮\n• 设置页面中的片库管理');
  };

  return (
    <div className="settings-group-content space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-amber-400 mb-3">工具箱</h3>
        
        <div className="settings-tab-content-item p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-md font-medium text-neutral-100 flex items-center">
                <LuTextSelect size={20} className="mr-2 text-sky-400" />
                NFO 剧情简介 AI 润色工具
              </h4>
              <p className="settings-description mt-1">
                批量扫描指定目录下的 NFO 文件，提取剧情简介，
                使用林珞姐姐的 AI 能力进行润色优化，并将结果写回 NFO 文件。
              </p>
            </div>
            <button 
              onClick={onLaunchNfoPlotPolisher} 
              className="button-primary-app px-4 py-2 text-sm whitespace-nowrap"
            >
              启动 NFO 润色
            </button>
          </div>
        </div>

        <div className="settings-tab-content-item p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-md font-medium text-neutral-100 flex items-center">
                <LuUsers size={20} className="mr-2 text-pink-400" />
                演员头像刮削
              </h4>
              <p className="settings-description mt-1">
                根据配置的头像数据源，为数据库中所有演员查找并缓存头像图片。
              </p>
            </div>
            <button
              onClick={handleAvatarScraping}
              className="button-primary-app px-4 py-2 text-sm whitespace-nowrap"
            >
              开始刮削头像
            </button>
          </div>
        </div>
        
        <div className="settings-tab-content-item p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-md font-medium text-neutral-100 flex items-center">
                <LuGlobe size={20} className="mr-2 text-purple-400" />
                刮削器测试工具
              </h4>
              <p className="settings-description mt-1">
                测试新的刮削器框架，支持从 JavBus 等网站刮削影片元数据。
              </p>
            </div>
            <button
              onClick={onLaunchScraperTest}
              className="button-primary-app px-4 py-2 text-sm whitespace-nowrap"
            >
              启动测试工具
            </button>
          </div>
        </div>

        <div className="settings-tab-content-item p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-md font-medium text-neutral-100 flex items-center">
                <LuScanSearch size={20} className="mr-2 text-teal-400" />
                扫描全部片库
              </h4>
              <p className="settings-description mt-1">
                一次性扫描所有已配置的片库，更新影片信息。
              </p>
            </div>
            <button
              onClick={handleScanAllLibraries}
              className="button-warning-app px-4 py-2 text-sm whitespace-nowrap flex items-center"
              title="此功能正在开发中"
            >
              <LuTriangle size={14} className="mr-1.5" />
              扫描全部 (待开发)
            </button>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ToolsSettingsTab;
