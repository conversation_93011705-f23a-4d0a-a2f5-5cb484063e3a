# 附件重命名问题排查指南

## 问题描述
用户反馈：当前下载的附件未自动重命名，还是原名！

## 已完成的修复

### ✅ 1. 验证文件名构建器功能
- fileNameBuilder.js 正常工作
- generateStandardFileName 方法正常工作
- 能够生成正确的标准化文件名格式：`[番号] - [标题] [标签]`

### ✅ 2. 修复验证逻辑
- 删除了重复的 validateFileRename 方法
- 优化了验证逻辑，适应标准化文件名格式
- 放宽了验证条件，支持番号匹配和标题关键词匹配

### ✅ 3. 修复配置文件
- 修复了 site-profiles.json 中的 passwordSelector 字段
- 从 null 改为空字符串 ""，避免配置验证错误

### ✅ 4. 全面测试验证
- 所有单元测试通过
- 集成测试通过
- 文件重命名逻辑测试通过

## 可能的原因分析

如果附件仍然保持原名，可能的原因包括：

### 1. 浏览器下载设置问题
**现象**: saveAs 方法可能没有按预期工作
**排查方法**:
- 检查 Chrome 浏览器的下载设置
- 确认是否启用了"下载前询问每个文件的保存位置"
- 检查是否有下载管理器插件干扰

### 2. 文件权限问题
**现象**: 重命名操作被系统阻止
**排查方法**:
- 确保工作区目录有完整的读写权限
- 检查是否有杀毒软件阻止文件操作
- 尝试以管理员权限运行程序

### 3. 下载过程异常
**现象**: 下载过程中发生错误，走了异常处理路径
**排查方法**:
- 查看详细的日志输出
- 检查是否有网络超时或连接问题
- 确认是否触发了下载限制或人机验证

### 4. 备用检测机制
**现象**: 主要下载流程失败，使用了备用检测机制
**排查方法**:
- 检查日志中是否有"备用重命名处理"的信息
- 确认备用机制是否正确识别了下载的文件

## 排查步骤

### 第一步：检查日志输出
在实际下载时，查看控制台日志，寻找以下关键信息：

```
[Collector] 🔍 开始处理下载文件:
[Collector] - 原始文件名: xxx
[Collector] - 文件扩展名: xxx
[Collector] ✅ 文件名校验通过:
[Collector] - 新文件名: xxx
[Collector] 💾 开始保存文件: xxx -> xxx
[Collector] ✅ 文件已成功重命名
```

### 第二步：检查文件系统
1. 确认工作区目录结构：`/工作区/论坛/板块/`
2. 检查是否有重复文件名冲突
3. 验证目录权限设置

### 第三步：测试简化场景
1. 尝试下载单个小文件
2. 检查是否特定类型的文件有问题
3. 测试不同的板块和论坛

### 第四步：检查浏览器设置
1. 打开 Chrome 设置 → 高级 → 下载内容
2. 确认下载位置设置
3. 检查是否启用了"下载前询问每个文件的保存位置"

## 代码中的重命名流程

### 主要流程（handleDownload 函数）
```javascript
// 1. 获取原始文件名和扩展名
const originalFileName = download.suggestedFilename();
const fileExtension = originalFileName.split('.').pop() || 'rar';

// 2. 生成标准化文件名
const fileNameResult = this.generateStandardFileName(postData, fileExtension);

// 3. 构建完整路径
const newFileName = fileNameResult.fileName;
const fullPath = path.join(attachmentsDir, newFileName);

// 4. 保存文件到指定路径（这里就应该是新文件名）
await download.saveAs(fullPath);
```

### 备用流程（文件检测机制）
```javascript
// 如果主流程失败，会检测下载目录中的新文件
// 然后进行重命名操作
if (originalFileName !== newFileName) {
  fs.renameSync(newestFile.path, newPath);
}
```

## 建议的解决方案

### 立即可尝试的方案
1. **重启程序**: 清除可能的状态缓存
2. **检查权限**: 确保工作区目录有完整权限
3. **清理下载目录**: 删除可能的冲突文件
4. **更新 Chrome**: 确保浏览器版本最新

### 深度排查方案
1. **启用详细日志**: 在下载时密切关注控制台输出
2. **单步调试**: 在关键位置添加更多日志输出
3. **文件监控**: 使用文件系统监控工具观察下载过程

## 联系支持

如果问题仍然存在，请提供：
1. 完整的控制台日志输出
2. 具体的下载场景（论坛、板块、文件类型）
3. 系统环境信息（操作系统、Chrome版本）
4. 工作区目录的权限设置

## 更新记录

- **2025-07-26**: 完成重命名功能修复和优化
- **2025-07-26**: 添加问题排查指南
