#!/usr/bin/env node

// test-javdb-simple.js - 简单的 JavDB Provider 测试
const path = require('path');
const os = require('os');

async function testJavdbSimple() {
  console.log('🧪 JavDB Provider 简单测试开始...\n');

  try {
    // 初始化设置服务
    console.log('初始化设置服务...');
    const settingsService = require('./main_process/services/settingsService');
    const userDataPath = path.join(os.tmpdir(), 'soulforge-test');
    settingsService.initializeSettings(console, userDataPath);
    console.log('✅ 设置服务初始化成功\n');

    // 加载 JavDB Provider
    console.log('加载 JavDB Provider...');
    const javdbProvider = require('./main_process/services/scrapers/javdbProvider');
    console.log(`✅ JavDB Provider 加载成功 (版本: ${javdbProvider.version})\n`);

    // 测试多个番号
    const testNfoIds = [
      'SSIS-001',  // 河北彩花的作品
      'JUFE-585',  // 常见测试番号
      'MIDV-018'   // 另一个常见番号
    ];

    for (const nfoId of testNfoIds) {
      console.log(`🔍 测试番号: ${nfoId}`);
      console.log('⚠️  注意: 如果没有配置 JavDB Cookie 或网络问题，可能会失败');
      
      try {
        const startTime = Date.now();
        const scrapedData = await javdbProvider.scrape(nfoId);
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`✅ ${nfoId} 刮削成功! (耗时: ${duration.toFixed(2)}s)`);
        console.log(`📺 标题: ${scrapedData.title}`);
        console.log(`🎭 演员: ${scrapedData.actors.join(', ')}`);
        console.log(`📅 发行日期: ${scrapedData.releaseDate}`);
        console.log(`🏢 制作商: ${scrapedData.studio}`);
        console.log(`⭐ 评分: ${scrapedData.rating || '无'}`);
        console.log(`🏷️  标签: ${scrapedData.tags.slice(0, 3).join(', ')}${scrapedData.tags.length > 3 ? '...' : ''}`);
        console.log(`🖼️  封面: ${scrapedData.coverUrl ? '✅' : '❌'}`);
        console.log(`🎬 预览图: ${scrapedData.previewImages.length} 张`);
        console.log(`🧲 磁力链接: ${scrapedData.magnet_links.length} 个`);
        
        if (scrapedData.magnet_links.length > 0) {
          console.log('🧲 磁力链接示例:');
          const firstMagnet = scrapedData.magnet_links[0];
          console.log(`   名称: ${firstMagnet.name}`);
          console.log(`   大小: ${firstMagnet.size}`);
          console.log(`   字幕: ${firstMagnet.has_subtitles ? '✅' : '❌'}`);
        }
        
        console.log(''); // 空行分隔
        
        // 只测试第一个成功的就够了
        break;
        
      } catch (error) {
        console.log(`❌ ${nfoId} 刮削失败: ${error.message}`);
        
        if (error.message.includes('Timeout')) {
          console.log('💡 建议: 网络超时，可能是网络连接问题');
        } else if (error.message.includes('Cloudflare')) {
          console.log('💡 建议: 被 Cloudflare 拦截，需要配置有效的 Cookie');
        } else if (error.message.includes('未匹配到番号')) {
          console.log('💡 建议: 该番号在 JavDB 中不存在');
        } else if (error.message.includes('需要 VIP')) {
          console.log('💡 建议: 该内容需要 VIP 权限');
        } else if (error.message.includes('需要登入')) {
          console.log('💡 建议: 需要登录，请配置 Cookie');
        }
        
        console.log(''); // 空行分隔
        
        // 继续测试下一个番号
        continue;
      }
    }
    
    // 清理资源
    console.log('清理资源...');
    const browserManager = require('./main_process/services/browserManager');
    await browserManager.closeBrowser();
    console.log('✅ 浏览器已关闭');
    
    console.log('\n🎉 JavDB Provider 简单测试完成!');
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testJavdbSimple().catch(console.error);
}

module.exports = { testJavdbSimple };
