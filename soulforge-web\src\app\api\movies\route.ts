import { NextRequest, NextResponse } from 'next/server';
import { MovieService } from '@/lib/services/movie-service';
import { FilterOptions, SortField, SortOrder } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortField = (searchParams.get('sortField') || 'lastScanned') as SortField;
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as SortOrder;
    
    // Parse filters
    const filters: FilterOptions = {};
    
    if (searchParams.get('search')) {
      filters.search = searchParams.get('search')!;
    }
    
    if (searchParams.get('genres')) {
      filters.genres = searchParams.get('genres')!.split(',');
    }
    
    if (searchParams.get('actors')) {
      filters.actors = searchParams.get('actors')!.split(',');
    }
    
    if (searchParams.get('studios')) {
      filters.studios = searchParams.get('studios')!.split(',');
    }
    
    if (searchParams.get('years')) {
      filters.years = searchParams.get('years')!.split(',').map(Number);
    }
    
    if (searchParams.get('watched')) {
      filters.watched = searchParams.get('watched') === 'true';
    }
    
    if (searchParams.get('favorited')) {
      filters.favorited = searchParams.get('favorited') === 'true';
    }
    
    if (searchParams.get('libraryId')) {
      filters.libraryId = searchParams.get('libraryId')!;
    }

    const result = await MovieService.getMovies(
      filters,
      sortField,
      sortOrder,
      page,
      limit
    );

    return NextResponse.json({
      success: true,
      data: result.movies,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching movies:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch movies',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const movie = await MovieService.createMovie(body);

    return NextResponse.json({
      success: true,
      data: movie,
    });
  } catch (error) {
    console.error('Error creating movie:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create movie',
      },
      { status: 500 }
    );
  }
}
