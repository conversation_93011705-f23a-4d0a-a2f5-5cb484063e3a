import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { MovieScanner } from '@/lib/services/movie-scanner';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { updateExisting = false, extractTechnicalInfo = false } = await request.json();
    const libraryId = params.id;

    // Get library information
    const library = await prisma.library.findUnique({
      where: { id: libraryId },
    });

    if (!library) {
      return NextResponse.json(
        { success: false, error: 'Library not found' },
        { status: 404 }
      );
    }

    console.log(`Starting scan for library: ${library.name} (${library.path})`);

    // Start the scan
    const scanResult = await MovieScanner.scanLibrary({
      libraryPath: library.path,
      libraryId: library.id,
      recursive: true,
      updateExisting,
      extractTechnicalInfo,
    });

    // Update library last scan time
    await prisma.library.update({
      where: { id: libraryId },
      data: { lastScanned: new Date() },
    });

    return NextResponse.json({
      success: true,
      message: `<PERSON>an completed for library: ${library.name}`,
      result: scanResult,
    });

  } catch (error) {
    console.error('Library scan failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Library scan failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const libraryId = params.id;

    // Get library with scan status
    const library = await prisma.library.findUnique({
      where: { id: libraryId },
      include: {
        movieLinks: {
          include: {
            movie: true,
          },
        },
      },
    });

    if (!library) {
      return NextResponse.json(
        { success: false, error: 'Library not found' },
        { status: 404 }
      );
    }

    const movieCount = library.movieLinks.length;
    const moviesWithNfoId = library.movieLinks.filter(
      link => link.movie.nfoId && link.movie.nfoId.trim() !== ''
    ).length;

    return NextResponse.json({
      success: true,
      library: {
        id: library.id,
        name: library.name,
        path: library.path,
        lastScanned: library.lastScanned,
        movieCount,
        moviesWithNfoId,
        nfoIdCompleteness: movieCount > 0 ? (moviesWithNfoId / movieCount) * 100 : 0,
      },
    });

  } catch (error) {
    console.error('Failed to get library scan status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get library status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
