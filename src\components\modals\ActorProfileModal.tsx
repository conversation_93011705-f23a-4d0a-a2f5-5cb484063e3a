// src/components/modals/ActorProfileModal.tsx
// "名人堂"模态框 - 展示人物详细档案和作品集

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>ser, <PERSON><PERSON><PERSON>der, LuRefreshCw, LuFilm, LuCalendar } from 'react-icons/lu';
import MovieCard from '../MovieCard';

interface ActorProfile {
  id: number;
  name: string;
  aliases: string[];
  bio?: string;
  tags: string[];
  avatar_local_path?: string;
  avatar_remote_url?: string;
}

interface Movie {
  db_id: number;
  nfoId: string;
  title: string;
  actors: string[];
  coverUrl?: string;
  year?: number;
  isLocal?: boolean;
  isRemote?: boolean;
  source?: 'local' | 'dmm';
  releaseDate?: string;
  detailUrl?: string;
}

interface CompleteFilmographyData {
  localMovies: Movie[];
  remoteMovies: Movie[];
  allMovies: Movie[];
  totalCount: number;
  localCount: number;
  remoteCount: number;
}

interface ActorProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  actorName: string;
  actorProfile: ActorProfile | null;
  filmography: Movie[];
  completeFilmography: CompleteFilmographyData | null;
  isLoading: boolean;
  isLoadingCompleteFilmography: boolean;
  error?: string;
  onRefresh?: () => void;
  onMovieCardClick?: (movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => void;
  appDefaultCover?: string | null;
}

export const ActorProfileModal: React.FC<ActorProfileModalProps> = ({
  isOpen,
  onClose,
  actorName,
  actorProfile,
  filmography,
  completeFilmography,
  isLoading,
  isLoadingCompleteFilmography,
  error,
  onRefresh,
  onMovieCardClick,
  appDefaultCover
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[90] p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-gray-900 rounded-lg max-w-[95vw] w-full max-h-[95vh] overflow-hidden border border-gray-700">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <LuUser className="h-6 w-6 text-[#B8860B]" />
            <h2 className="text-2xl font-bold text-white">名人堂</h2>
            {actorProfile ? (
              <span className="text-gray-400">- {actorProfile.name}</span>
            ) : (
              <span className="text-gray-400">- {actorName}</span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {onRefresh && (
              <button
                onClick={onRefresh}
                disabled={isLoading}
                className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg disabled:opacity-50"
                title="刷新演员档案"
              >
                <LuRefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"
            >
              <LuX className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(95vh-140px)]">
          {(isLoading || isLoadingCompleteFilmography) && (
            <div className="flex items-center justify-center py-12">
              <LuLoader className="h-8 w-8 text-[#B8860B] animate-spin" />
              <span className="ml-3 text-white">
                正在加载 {actorName} 的{isLoading ? '档案' : ''}
                {isLoading && isLoadingCompleteFilmography ? '和' : ''}
                {isLoadingCompleteFilmography ? '完整作品列表' : ''}...
              </span>
            </div>
          )}

          {!isLoading && (actorProfile || filmography.length > 0 || completeFilmography) && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
              {/* 左侧：演员档案信息 */}
              <div className="lg:col-span-1">
                {actorProfile && (
                  <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                    <div className="flex flex-col items-center space-y-4">
                      {actorProfile.avatar_local_path || actorProfile.avatar_remote_url ? (
                        <img
                          src={actorProfile.avatar_local_path || actorProfile.avatar_remote_url}
                          alt={actorProfile.name}
                          className="w-32 h-32 object-cover rounded-full border-4 border-[#B8860B]"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-32 h-32 bg-gray-700 rounded-full flex items-center justify-center border-4 border-gray-600 ${actorProfile.avatar_local_path || actorProfile.avatar_remote_url ? 'hidden' : ''}`}>
                        <LuUser className="h-16 w-16 text-gray-500" />
                      </div>

                      <h3 className="text-xl font-bold text-white text-center">{actorProfile.name}</h3>
                      {actorProfile.aliases && actorProfile.aliases.length > 0 && (
                        <p className="text-gray-400 text-sm text-center">别名: {actorProfile.aliases.join(', ')}</p>
                      )}
                      {actorProfile.bio && (
                        <div>
                          <h4 className="text-lg font-medium text-white mb-2">简介</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">{actorProfile.bio}</p>
                        </div>
                      )}

                      {actorProfile.tags && actorProfile.tags.length > 0 && (
                        <div>
                          <h4 className="text-lg font-medium text-white mb-2">标签</h4>
                          <div className="flex flex-wrap gap-2">
                            {actorProfile.tags.map((tag, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* 右侧：作品列表 */}
              <div className="lg:col-span-2">

                {/* 完整作品列表 */}
                {completeFilmography && (
                <div>
                  <div className="flex items-center gap-3 mb-4">
                    <LuFilm className="h-5 w-5 text-[#B8860B]" />
                    <h4 className="text-lg font-medium text-white">
                      完整作品集 (总计{completeFilmography.totalCount}部)
                    </h4>
                    <div className="flex gap-2 text-sm">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        本地 {completeFilmography.localCount}部
                      </span>
                      <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs">
                        远程 {completeFilmography.remoteCount}部
                      </span>
                    </div>
                  </div>

                  {/* 本地作品 */}
                  {completeFilmography.localMovies.length > 0 && (
                    <div className="mb-6">
                      <h5 className="text-md font-medium text-green-400 mb-3">
                        📁 本地收藏 ({completeFilmography.localMovies.length}部)
                      </h5>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {completeFilmography.localMovies.map((movie) => (
                          <div key={`local-${movie.db_id}`} className="relative">
                            {/* 本地标识 */}
                            <div className="absolute top-2 left-2 z-10">
                              <span className="px-2 py-1 bg-green-600 text-white text-xs rounded shadow-lg">
                                本地
                              </span>
                            </div>
                            <MovieCard
                              movie={movie}
                              onCardClick={onMovieCardClick}
                              appDefaultCover={appDefaultCover}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 没有任何作品时的提示 */}
                  {completeFilmography.localMovies.length === 0 && completeFilmography.remoteMovies.length === 0 && (
                    <div className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700">
                      <LuFilm className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                      <div className="text-gray-400 mb-2">未找到 {actorName} 的任何作品</div>
                      <div className="text-gray-500 text-sm">
                        本地收藏：{completeFilmography.localCount}部，远程作品：{completeFilmography.remoteCount}部
                      </div>
                    </div>
                  )}

                  {/* 远程作品 */}
                  {completeFilmography.remoteMovies.length > 0 && (
                    <div>
                      <h5 className="text-md font-medium text-blue-400 mb-3">
                        🌐 DMM作品库 ({completeFilmography.remoteMovies.length}部)
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                        {completeFilmography.remoteMovies.map((movie, index) => (
                          <div key={`remote-${movie.nfoId || index}`} className="bg-gray-800 rounded-lg p-3 border-2 border-blue-600 hover:border-blue-500 transition-colors opacity-75">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">远程</span>
                              <h5 className="text-white font-medium truncate flex-1" title={movie.title}>
                                {movie.title}
                              </h5>
                            </div>
                            <div className="text-gray-400 text-sm space-y-1">
                              {movie.nfoId && (
                                <div>编号: {movie.nfoId}</div>
                              )}
                              {movie.releaseDate && (
                                <div className="flex items-center gap-1">
                                  <LuCalendar className="h-3 w-3" />
                                  {movie.releaseDate}
                                </div>
                              )}
                              {movie.coverUrl && (
                                <div className="mt-2">
                                  <img
                                    src={movie.coverUrl}
                                    alt={movie.title}
                                    className="w-full h-24 object-cover rounded grayscale hover:grayscale-0 transition-all"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      target.style.display = 'none';
                                    }}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 回退显示：当没有完整作品列表但有本地作品时 */}
              {!completeFilmography && filmography.length > 0 && (
            <div className="space-y-6">
              <div className="text-center py-8 bg-gray-800 rounded-lg border border-gray-700">
                <LuUser className="h-12 w-12 text-gray-500 mx-auto mb-3" />
                <div className="text-gray-400 mb-2">暂无 {actorName} 的详细档案</div>
                <div className="text-gray-500 text-sm mb-4">但找到了本地作品集</div>
                {onRefresh && (
                  <button
                    onClick={onRefresh}
                    disabled={isLoading || isLoadingCompleteFilmography}
                    className="px-3 py-1.5 bg-[#B8860B] hover:bg-[#9A7209] text-white rounded text-sm transition-colors disabled:opacity-50 flex items-center gap-2 mx-auto"
                  >
                    <LuRefreshCw className={`h-3 w-3 ${(isLoading || isLoadingCompleteFilmography) ? 'animate-spin' : ''}`} />
                    刮削档案
                  </button>
                )}
              </div>

              {/* 本地作品列表 */}
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <LuFilm className="h-5 w-5 text-[#B8860B]" />
                  <h4 className="text-lg font-medium text-white">本地作品集 ({filmography.length}部)</h4>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  {filmography.map((movie) => (
                    <MovieCard
                      key={movie.db_id}
                      movie={movie}
                      onCardClick={onMovieCardClick}
                      appDefaultCover={appDefaultCover}
                    />
                  ))}
                </div>
              </div>
            </div>
              )}
              </div>
            </div>
          )}

          {!isLoading && !actorProfile && filmography.length === 0 && (
            <div className="text-center py-12">
              <LuUser className="h-16 w-16 text-gray-500 mx-auto mb-4" />
              <div className="text-gray-400 mb-4">未找到 {actorName} 的档案信息</div>
              {error && (
                <div className="text-red-400 text-sm mb-4">{error}</div>
              )}
              <div className="text-gray-500 text-sm mb-6">
                <p>可能的原因：</p>
                <ul className="text-left inline-block mt-2 space-y-1">
                  <li>• 演员档案尚未刮削</li>
                  <li>• 演员姓名拼写不匹配</li>
                  <li>• 本地数据库中无此演员作品</li>
                </ul>
              </div>
              {onRefresh && (
                <button
                  onClick={onRefresh}
                  disabled={isLoading}
                  className="px-4 py-2 bg-[#B8860B] hover:bg-[#9A7209] text-white rounded-lg transition-colors disabled:opacity-50 flex items-center gap-2 mx-auto"
                >
                  <LuRefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  手动刮削档案
                </button>
              )}
            </div>
          )}

          {!isLoading && !actorProfile && filmography.length > 0 && (
            <div className="space-y-6">
              <div className="text-center py-8 bg-gray-800 rounded-lg border border-gray-700">
                <LuUser className="h-12 w-12 text-gray-500 mx-auto mb-3" />
                <div className="text-gray-400 mb-2">暂无 {actorName} 的详细档案</div>
                <div className="text-gray-500 text-sm mb-4">但找到了本地作品集</div>
                {onRefresh && (
                  <button
                    onClick={onRefresh}
                    disabled={isLoading}
                    className="px-3 py-1.5 bg-[#B8860B] hover:bg-[#9A7209] text-white rounded text-sm transition-colors disabled:opacity-50 flex items-center gap-2 mx-auto"
                  >
                    <LuRefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
                    刮削档案
                  </button>
                )}
              </div>

              {/* 作品列表 */}
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <LuFilm className="h-5 w-5 text-[#B8860B]" />
                  <h4 className="text-lg font-medium text-white">本地作品集 ({filmography.length}部)</h4>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filmography.map((movie) => (
                    <div key={movie.db_id} className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-[#B8860B] transition-colors">
                      <h5 className="text-white font-medium mb-2 truncate" title={movie.title}>
                        {movie.title}
                      </h5>
                      <div className="text-gray-400 text-sm space-y-1">
                        {movie.nfoId && (
                          <div>编号: {movie.nfoId}</div>
                        )}
                        {movie.year && (
                          <div className="flex items-center gap-1">
                            <LuCalendar className="h-3 w-3" />
                            {movie.year}年
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActorProfileModal;
