// soul-forge-electron/src/hooks/useScanStatus.ts
import { useState, useEffect, useCallback } from 'react';
import { ScanProgressData, ScanCompleteData, ScanLibraryResult } from '../types';

interface UseScanStatusProps {
  onScanCompleteCallback: (data: ScanCompleteData | ScanLibraryResult) => void; 
}

export function useScanStatus({ onScanCompleteCallback }: UseScanStatusProps) {
  const [scanProgress, setScanProgress] = useState<ScanProgressData | null>(null);
  const [isScanning, setIsScanning] = useState(false);

  useEffect(() => {
    const removeScanCompleteListener = window.sfeElectronAPI.onScanComplete((data: ScanCompleteData | ScanLibraryResult) => {
      setIsScanning(false);
      setScanProgress(null);
      onScanCompleteCallback(data); // Pass data to the callback
      alert(`扫描完成！新增: ${data.newCount}, 更新: ${data.updatedCount}, 总共处理: ${data.totalScanned}`);
    });

    const removeScanProgressListener = window.sfeElectronAPI.onScanProgressUpdate((progress: ScanProgressData) => {
      setScanProgress(progress);
    });

    const removeScanErrorListener = window.sfeElectronAPI.onScanError((errorMsg: string) => {
      setIsScanning(false);
      setScanProgress(prev => ({
        ...(prev || { overallPercentage: 0, currentPathMessage: null, filesInPathProcessed: null, totalFilesInPath: null, currentFileMessage: null }),
        error: errorMsg,
      }));
    });

    const removeScanStatusListener = window.sfeElectronAPI.onScanStatusUpdate((status: string) => {
      setScanProgress(prev => ({
        ...(prev || { overallPercentage: 0, currentPathMessage: null, filesInPathProcessed: null, totalFilesInPath: null, currentFileMessage: null }),
        currentPathMessage: status,
        error: null,
      }));
    });

    return () => {
      removeScanCompleteListener();
      removeScanProgressListener();
      removeScanErrorListener();
      removeScanStatusListener();
    };
  }, [onScanCompleteCallback]);

  const triggerScan = useCallback((defaultScanPaths?: string[], libraryId?: string) => { // Added libraryId parameter
    setIsScanning(true);
    setScanProgress({ 
      overallPercentage: 0, 
      currentPathMessage: "启动扫描...", 
      filesInPathProcessed: 0, 
      totalFilesInPath: 0, 
      currentFileMessage: null, 
      error: null 
    });
    window.sfeElectronAPI.triggerScan(defaultScanPaths, libraryId); // Pass libraryId
  }, []);


  return {
    scanProgress,
    isScanning,
    triggerScan,
  };
}