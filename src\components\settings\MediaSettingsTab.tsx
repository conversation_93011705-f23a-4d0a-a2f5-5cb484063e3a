// soul-forge-electron/src/components/settings/MediaSettingsTab.tsx
import React, { useState, useEffect } from 'react';
import { AppSettings, SnapshotQuality, AvatarDataSourceType, ScrapeActorAvatarsProgress, ScrapeActorAvatarsResult } from '../../types';

interface MediaSettingsTabProps {
  settings: Partial<AppSettings>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  setSettings: React.Dispatch<React.SetStateAction<Partial<AppSettings>>>;
  handleBrowsePathForInput: (settingName: keyof AppSettings) => Promise<void>;
  handleBrowseFileForInput: (settingName: keyof AppSettings) => Promise<void>; 
  pathSep: string;
}

const snapshotQualityOptions: { value: SnapshotQuality; label: string }[] = [
  { value: 'sd_320p', label: '标清快照 (320px 宽)' },
  { value: 'hd_640p', label: '高清快照 (640px 宽)' },
  { value: 'fhd_1280p_720h', label: '全高清快照 (720p 高 / 1280px 宽)' },
];

const avatarDataSourceOptions: { value: AvatarDataSourceType; label: string }[] = [
    { value: 'none', label: '不使用演员头像' },
    { value: 'localSimple', label: '本地简单头像库 (旧版)' },
    { value: 'localFileTree', label: '本地 Filetree.json 头像库' },
    { value: 'remoteGfriends', label: '远程 gfriends 仓库 (Filetree.json)' },
];

const MediaSettingsTab: React.FC<MediaSettingsTabProps> = ({
  settings,
  handleInputChange,
  setSettings,
  handleBrowsePathForInput,
  pathSep
}) => {
  const [isScrapingAvatars, setIsScrapingAvatars] = useState(false);
  const [avatarScrapingProgress, setAvatarScrapingProgress] = useState<ScrapeActorAvatarsProgress | null>(null);
  const [avatarScrapingResult, setAvatarScrapingResult] = useState<ScrapeActorAvatarsResult | null>(null);


  useEffect(() => {
    if (!isScrapingAvatars) return;

    const removeProgressListener = window.sfeElectronAPI.onScrapeAvatarsProgress(setAvatarScrapingProgress);
    const removeCompleteListener = window.sfeElectronAPI.onScrapeAvatarsComplete((result) => {
      setAvatarScrapingResult(result);
      setIsScrapingAvatars(false);
      setAvatarScrapingProgress(null);
    });
    const removeErrorListener = window.sfeElectronAPI.onScrapeAvatarsError((errorMsg) => {
      setAvatarScrapingResult({ success: false, error: errorMsg, totalActors: avatarScrapingProgress?.totalActors || 0, processedCount: avatarScrapingProgress?.currentIndex || 0, failedCount: (avatarScrapingProgress?.totalActors || 0) - (avatarScrapingProgress?.currentIndex || 0) });
      setIsScrapingAvatars(false);
      setAvatarScrapingProgress(null);
    });
    
    return () => {
      removeProgressListener();
      removeCompleteListener();
      removeErrorListener();
    };
  }, [isScrapingAvatars, avatarScrapingProgress]);


  const handleScrapeAvatars = async () => {
    setIsScrapingAvatars(true);
    setAvatarScrapingProgress(null);
    setAvatarScrapingResult(null);
    try {
      const result = await window.sfeElectronAPI.scrapeActorAvatars();
      // Result is also handled by onScrapeAvatarsComplete, but we can log it here too
      console.log("Initial scrape call result:", result);
    } catch (e: any) {
      setAvatarScrapingResult({ success: false, error: `刮削启动错误: ${e.message}`, totalActors:0, processedCount:0, failedCount:0});
      setIsScrapingAvatars(false);
    }
  };

  const handleBrowseCustomImage = async (settingName: 'customDefaultCoverDataUrl' | 'defaultActorAvatarDataUrl' | 'customSfwPlaceholderDataUrl') => {
    try {
      const result = await window.sfeElectronAPI.browseImageForDataUrl();
      if (result.success && result.dataUrl) {
        setSettings(prev => ({ ...prev, [settingName]: result.dataUrl }));
      } else if (result.success && !result.dataUrl) {
        // User cancelled
      } else if (!result.success && result.error) {
        alert(`选择图片失败: ${result.error}`);
      }
    } catch (err: any) {
      console.error(`Error browsing for ${settingName}:`, err);
      alert(`选择图片时发生前端错误: ${err.message}`);
    }
  };

  const handleClearCustomImage = (settingName: 'customDefaultCoverDataUrl' | 'defaultActorAvatarDataUrl' | 'customSfwPlaceholderDataUrl') => {
    setSettings(prev => ({ ...prev, [settingName]: null }));
  };
  
  const handleFFmpegHelp = (toolName: 'FFmpeg' | 'FFprobe') => {
    alert(
      `${toolName} 路径帮助:\n\n` +
      `1. 您可以直接在此输入框中填写【${toolName} 可执行文件自身的完整路径】。\n` +
      `   - Windows 示例: D:\\ffmpeg\\bin\\${toolName.toLowerCase()}.exe\n` +
      `   - Linux/macOS 示例: /usr/local/bin/${toolName.toLowerCase()}\n\n` +
      `2. 或者，您可以将包含 ${toolName} 可执行文件的【目录】(例如 D:\\ffmpeg\\bin 或 /usr/local/bin) 添加到您操作系统的 PATH 环境变量中，然后将此设置项留空，程序将尝试自动在 PATH 中查找 '${toolName.toLowerCase()}' 命令。\n\n` +
      `不正确的路径是导致 "${toolName} not found at ..." 或类似错误的常见原因。\n` +
      `请确保:\n` +
      `  - 文件确实存在于指定路径。\n` +
      `  - 程序有权限访问和执行该文件。\n` +
      `  - 如果填写路径，请确保是【可执行文件本身】，而不是其所在的文件夹。\n\n` +
      `通常 FFmpeg 和 FFprobe 位于同一目录下。`
    );
  };


  return (
    <div className="settings-group-content space-y-4">
      <div>
        <label htmlFor="ffmpegPath" className="settings-label">FFmpeg 路径 (可选)</label>
        <div className="flex items-center gap-2">
            <input type="text" id="ffmpegPath" name="ffmpegPath" value={settings.ffmpegPath || ''} onChange={handleInputChange} placeholder="例如: D:\\ffmpeg\\bin\\ffmpeg.exe 或留空以使用系统 PATH" className="form-input-app flex-grow"/>
            <button onClick={() => handleFFmpegHelp('FFmpeg')} className="button-secondary-app px-3 py-2 text-sm">帮助</button>
        </div>
        <p className="settings-description">
          用于视频截图。请填写 <strong>ffmpeg 可执行文件的【完整路径】</strong>，或确保其在系统 PATH 中。
        </p>
      </div>
      <div>
        <label htmlFor="ffprobePath" className="settings-label mt-3">FFprobe 路径 (可选)</label>
         <div className="flex items-center gap-2">
            <input type="text" id="ffprobePath" name="ffprobePath" value={settings.ffprobePath || ''} onChange={handleInputChange} placeholder="例如: D:\\ffmpeg\\bin\\ffprobe.exe 或留空以使用系统 PATH" className="form-input-app flex-grow"/>
            <button onClick={() => handleFFmpegHelp('FFprobe')} className="button-secondary-app px-3 py-2 text-sm">帮助</button>
        </div>
        <p className="settings-description">
          用于读取媒体信息。请填写 <strong>ffprobe 可执行文件的【完整路径】</strong>，或确保其在系统 PATH 中。
        </p>
      </div>
      <div>
        <label htmlFor="snapshotCachePath" className="settings-label mt-3">快照缓存路径 (可选)</label>
        <div className="flex items-center gap-2">
            <input type="text" id="snapshotCachePath" name="snapshotCachePath" value={settings.snapshotCachePath || ''} onChange={handleInputChange} placeholder="选择快照存储文件夹..." className="form-input-app flex-grow"/>
            <button onClick={() => handleBrowsePathForInput('snapshotCachePath')} className="button-secondary-app px-3 py-2 text-sm">浏览</button>
        </div>
        <p className="settings-description">留空则使用应用数据目录。</p>
      </div>
      <div>
        <label htmlFor="snapshotQuality" className="settings-label mt-3">快照质量/尺寸</label>
        <select id="snapshotQuality" name="snapshotQuality" value={settings.snapshotQuality || 'hd_640p'} onChange={handleInputChange} className="form-select-app">
          {snapshotQualityOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
        </select>
      </div>
      
      <div className="mt-4 pt-4 border-t border-[#3a3a3a]">
        <label className="settings-label">自定义无封面影片的默认封面图</label>
        <div className="mt-1 p-3 bg-[#2d2d2d] border border-[#4f4f4f] rounded-md space-y-2">
          {settings.customDefaultCoverDataUrl ? (
            <img 
              src={settings.customDefaultCoverDataUrl} 
              alt="自定义默认影片封面预览" 
              className="max-w-full h-auto max-h-28 object-contain rounded border border-[#4f4f4f] mb-2 shadow-sm"
            />
          ) : (
            <p className="text-xs text-neutral-400 italic">当前未使用自定义默认影片封面图。</p>
          )}
          <div className="flex items-center gap-2">
            <button 
              onClick={() => handleBrowseCustomImage('customDefaultCoverDataUrl')}
              className="button-secondary-app px-3 py-1.5 text-sm"
            >
              浏览影片封面...
            </button>
            {settings.customDefaultCoverDataUrl && (
              <button 
                onClick={() => handleClearCustomImage('customDefaultCoverDataUrl')}
                className="button-danger-app px-3 py-1.5 text-sm"
              >
                清除影片封面
              </button>
            )}
          </div>
          <p className="settings-description">选择一张图片，当影片没有自己的封面时将显示此图。</p>
        </div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-[#3a3a3a]">
        <label className="settings-label">SFW模式占位图 (全局图片隐藏时使用)</label>
        <div className="mt-1 p-3 bg-[#2d2d2d] border border-[#4f4f4f] rounded-md space-y-2">
          {settings.customSfwPlaceholderDataUrl ? (
            <img 
              src={settings.customSfwPlaceholderDataUrl} 
              alt="SFW 模式占位图预览" 
              className="max-w-full h-auto max-h-28 object-contain rounded border border-[#4f4f4f] mb-2 shadow-sm"
            />
          ) : (
            <p className="text-xs text-neutral-400 italic">当前未使用自定义SFW占位图 (将使用通用占位符)。</p>
          )}
          <div className="flex items-center gap-2">
            <button 
              onClick={() => handleBrowseCustomImage('customSfwPlaceholderDataUrl')}
              className="button-secondary-app px-3 py-1.5 text-sm"
            >
              浏览SFW占位图...
            </button>
            {settings.customSfwPlaceholderDataUrl && (
              <button 
                onClick={() => handleClearCustomImage('customSfwPlaceholderDataUrl')}
                className="button-danger-app px-3 py-1.5 text-sm"
              >
                清除SFW占位图
              </button>
            )}
          </div>
          <p className="settings-description">选择一张图片，当全局隐藏图片(SFW模式)时，将用此图作为所有图片的占位符。若未设置，则使用内置占位符。</p>
        </div>
      </div>


      <div className="mt-4 pt-4 border-t border-[#3a3a3a] space-y-3">
        <h4 className="settings-label font-semibold">演员头像设置</h4>
        <div>
          <label htmlFor="avatarDataSourceType" className="settings-label">头像数据源类型</label>
          <select 
            id="avatarDataSourceType" 
            name="avatarDataSourceType" 
            value={settings.avatarDataSourceType || 'none'} 
            onChange={handleInputChange} 
            className="form-select-app"
          >
            {avatarDataSourceOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
          </select>
        </div>

        {settings.avatarDataSourceType === 'localSimple' && (
          <div>
            <label htmlFor="actorAvatarLibraryPath" className="settings-label">本地简单头像库目录</label>
            <div className="flex items-center gap-2">
              <input type="text" id="actorAvatarLibraryPath" name="actorAvatarLibraryPath" value={settings.actorAvatarLibraryPath || ''} onChange={handleInputChange} placeholder="选择本地演员头像存储文件夹..." className="form-input-app flex-grow"/>
              <button onClick={() => handleBrowsePathForInput('actorAvatarLibraryPath')} className="button-secondary-app px-3 py-2 text-sm">浏览</button>
            </div>
            <p className="settings-description">旧版行为：指定包含演员头像图片的本地文件夹。图片应以 "演员姓名.jpg" (或.png, .webp) 命名。</p>
          </div>
        )}

        {settings.avatarDataSourceType === 'localFileTree' && (
          <>
            <div>
              <label htmlFor="localFileTreePath" className="settings-label">本地 Filetree.json 头像库根目录</label>
              <div className="flex items-center gap-2">
                <input type="text" id="localFileTreePath" name="localFileTreePath" value={settings.localFileTreePath || ''} onChange={handleInputChange} placeholder="选择包含 Filetree.json 和图片子目录的根目录..." className="form-input-app flex-grow"/>
                <button onClick={() => handleBrowsePathForInput('localFileTreePath')} className="button-secondary-app px-3 py-2 text-sm">浏览</button>
              </div>
              <p className="settings-description">此目录应包含 `Filetree.json` 文件和相应的图片子文件夹 (如 CompanyNameA)。</p>
            </div>
            <div className="flex items-center">
              <input type="checkbox" id="avatarPreferAiFixed" name="avatarPreferAiFixed" checked={settings.avatarPreferAiFixed === undefined ? true : !!settings.avatarPreferAiFixed} onChange={handleInputChange} className="form-checkbox-app"/>
              <label htmlFor="avatarPreferAiFixed" className="ml-2 text-sm text-neutral-100">优先使用 AI 优化的头像 (如果 Filetree.json 中提供)</label>
            </div>
          </>
        )}
        
        {settings.avatarDataSourceType === 'remoteGfriends' && (
             <>
                <p className="settings-description">将从 gfriends GitHub 仓库 (通过 CDN) 获取 Filetree.json 和演员头像。默认 URL 已内置。</p>
                <div className="flex items-center">
                <input type="checkbox" id="avatarPreferAiFixed" name="avatarPreferAiFixed" checked={settings.avatarPreferAiFixed === undefined ? true : !!settings.avatarPreferAiFixed} onChange={handleInputChange} className="form-checkbox-app"/>
                <label htmlFor="avatarPreferAiFixed" className="ml-2 text-sm text-neutral-100">优先使用 AI 优化的头像 (如果 Filetree.json 中提供)</label>
                </div>
            </>
        )}


        <div className="mt-3 pt-3 border-t border-neutral-700/50">
            <label className="settings-label">默认演员头像 (备用)</label>
            <div className="mt-1 p-3 bg-[#2d2d2d]/70 border border-[#4f4f4f] rounded-md space-y-2">
            {settings.defaultActorAvatarDataUrl ? (
                <img 
                src={settings.defaultActorAvatarDataUrl} 
                alt="自定义默认演员头像预览" 
                className="w-20 h-20 object-cover rounded-full border border-[#4f4f4f] mb-2 shadow-sm"
                />
            ) : (
                <p className="text-xs text-neutral-400 italic">当前未使用自定义默认演员头像。</p>
            )}
            <div className="flex items-center gap-2">
                <button 
                onClick={() => handleBrowseCustomImage('defaultActorAvatarDataUrl')}
                className="button-secondary-app px-3 py-1.5 text-sm"
                >
                浏览备用头像...
                </button>
                {settings.defaultActorAvatarDataUrl && (
                <button 
                    onClick={() => handleClearCustomImage('defaultActorAvatarDataUrl')}
                    className="button-danger-app px-3 py-1.5 text-sm"
                >
                    清除备用头像
                </button>
                )}
            </div>
            <p className="settings-description">选择一张图片，当按上述配置无法找到演员头像时，将显示此图。建议使用方形图片。</p>
            </div>
        </div>

        <div className="mt-4 pt-4 border-t border-neutral-700/50">
            <h4 className="settings-label font-semibold">头像刮削与缓存</h4>
            <button
                onClick={handleScrapeAvatars}
                disabled={isScrapingAvatars || settings.avatarDataSourceType === 'none'}
                className="button-primary-app px-4 py-2 text-sm disabled:opacity-60"
            >
                {isScrapingAvatars ? '刮削中...' : '开始刮削所有演员头像'}
            </button>
            {settings.avatarDataSourceType === 'none' && <p className="settings-description text-amber-400">请先选择一个有效的头像数据源才能进行刮削。</p>}

            {isScrapingAvatars && avatarScrapingProgress && (
                <div className="mt-2 text-xs text-neutral-300">
                    <p>刮削进度: {avatarScrapingProgress.currentIndex + 1} / {avatarScrapingProgress.totalActors}</p>
                    <p>当前演员: {avatarScrapingProgress.currentActorName} - 状态: {avatarScrapingProgress.status}</p>
                    {avatarScrapingProgress.error && <p className="text-red-400">错误: {avatarScrapingProgress.error}</p>}
                    <div className="w-full bg-neutral-600 rounded-full h-1.5 mt-1">
                        <div 
                            className="bg-sky-500 h-1.5 rounded-full transition-all duration-150" 
                            style={{ width: `${(avatarScrapingProgress.currentIndex + 1) / avatarScrapingProgress.totalActors * 100}%` }}
                        ></div>
                    </div>
                </div>
            )}
            {avatarScrapingResult && (
                <div className={`mt-2 text-xs p-2 rounded-md ${avatarScrapingResult.success ? 'bg-green-700/30 text-green-300' : 'bg-red-700/30 text-red-300'}`}>
                    <p>{avatarScrapingResult.message || avatarScrapingResult.error}</p>
                    {!avatarScrapingResult.success && avatarScrapingResult.error && <p>错误详情: {avatarScrapingResult.error}</p>}
                    <p>总数: {avatarScrapingResult.totalActors}, 成功: {avatarScrapingResult.processedCount}, 失败: {avatarScrapingResult.failedCount}</p>
                </div>
            )}
            <p className="settings-description mt-1">
                此操作会尝试为数据库中所有演员查找并缓存头像。如果演员已有本地缓存头像，则会跳过。
                这可能需要一些时间，具体取决于演员数量和网络速度。
            </p>
        </div>


      </div>

    </div>
  );
};

export default MediaSettingsTab;
