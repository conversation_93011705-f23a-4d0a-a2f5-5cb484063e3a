import React from 'react';

const DetailedListViewIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={className} aria-hidden="true">
    <path d="M3 4.5A1.5 1.5 0 014.5 3h11A1.5 1.5 0 0117 4.5v11a1.5 1.5 0 01-1.5 1.5h-11A1.5 1.5 0 013 15.5v-11zM4.5 4a.5.5 0 00-.5.5v2.5h12V4.5a.5.5 0 00-.5-.5h-11zM4 8v7.5a.5.5 0 00.5.5h11a.5.5 0 00.5-.5V8H4z" />
    <rect x="5" y="9" width="3" height="5" rx=".5" opacity="0.7" />
    <path d="M9.5 9.5h6a.5.5 0 01.5.5v1a.5.5 0 01-.5.5h-6a.5.5 0 01-.5-.5v-1a.5.5 0 01.5-.5zm0 3h4a.5.5 0 01.5.5v1a.5.5 0 01-.5.5h-4a.5.5 0 01-.5-.5v-1a.5.5 0 01.5-.5z" opacity="0.7"/>
  </svg>
);
export default DetailedListViewIcon;