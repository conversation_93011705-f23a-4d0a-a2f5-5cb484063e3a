{"name": "linlang-secret-manor", "version": "1.3.3", "description": "麟琅秘府 Electron 应用，为小龙哥哥倾心打造。", "main": "main.js", "scripts": {"dev": "vite", "build": "vite build", "start": "electron .", "electron:dev": "cross-env ELECTRON_IS_DEV=true electron .", "app": "cross-env ELECTRON_IS_DEV=true electron .", "lint": "echo \"代码风格检查尚未配置\"", "dist": "vite build && electron-builder", "rebuild-deps": "electron-rebuild"}, "keywords": ["Electron", "React", "TailwindCSS", "Python", "Vite", "麟琅秘府"], "author": "Soul Forge Industries (林珞)", "license": "ISC", "dependencies": {"@google/genai": "^0.14.1", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@types/fluent-ffmpeg": "^2.1.27", "@types/multer": "^2.0.0", "@types/xml2js": "^0.4.14", "axios": "^1.11.0", "better-sqlite3": "^9.4.3", "cheerio": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-log": "^5.1.5", "electron-store": "^8.2.0", "fast-glob": "^3.3.3", "fluent-ffmpeg": "^2.1.3", "framer-motion": "^12.23.6", "glob": "^11.0.3", "lucide-react": "^0.525.0", "multer": "^2.0.2", "playwright": "^1.54.1", "prisma": "^6.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.7.1", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "uuid": "^10.0.0", "xml2js": "^0.6.2", "xmlbuilder2": "^3.1.1", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^24.0.15", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "electron": "^28.3.3", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9", "postcss": "^8.4.39", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "react-icons": "^5.2.1", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "vite": "^5.3.1"}, "build": {"appId": "com.soulforge.app.linluo", "productName": "麟琅秘府", "copyright": "Copyright © 2025 麟琅秘府 (林珞)", "directories": {"output": "release_builds", "buildResources": "build"}, "files": ["main.js", "preload.js", "dist/", "node_modules/", "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/*.d.ts", "!node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "./python_scripts", "to": "python_scripts", "filter": ["**/*"]}, {"from": "./ffmpeg_binaries", "to": "ffmpeg_binaries", "filter": ["**/*"]}], "win": {"target": ["nsis", "portable"], "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": false, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "mac": {"target": ["dmg", "zip"], "icon": "build/icon.png", "category": "public.app-category.video"}, "linux": {"target": ["AppImage", "deb", "tar.gz"], "icon": "build/icon.png", "category": "Video"}, "asarUnpack": ["**/python_scripts/**", "**/ffmpeg_binaries/**", "**/node_modules/better-sqlite3/**"]}}