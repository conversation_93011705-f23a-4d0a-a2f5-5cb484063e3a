// soul-forge-electron/main_process/utils/httpClient.js
const http = require('node:http');
const https = require('node:https');

let log; // Logger will be injected

function initializeHttpClient(logger) {
  log = logger;
  log.info('[HTTP客户端工具] 初始化。');
}

async function makeAiRequest(url, method, headers, body, timeout = 20000, abortSignal = null) {
  if (!log) {
    log = { info: console.info, warn: console.warn, error: console.error };
    log.warn('[HTTP客户端工具] Logger未初始化，使用console。这不应该发生。');
  }

  return new Promise((resolve, reject) => {
    if (abortSignal?.aborted) {
      log.warn(`[AI 请求] 请求在开始前已中止. URL: ${url}`);
      return reject(new Error('Request aborted before sending.'));
    }

    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
      path: parsedUrl.pathname + parsedUrl.search,
      method: method,
      headers: headers,
      timeout: timeout,
      signal: abortSignal, // Pass AbortSignal to native http/https request (Node.js v15.4.0+, v14.17.0+)
    };

    const req = client.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(JSON.parse(data));
          } else {
            log.error(`[AI 请求] API 错误. URL: ${url}, 状态: ${res.statusCode}, 数据: ${data.substring(0, 200)}`);
            reject(new Error(`API 错误 (状态 ${res.statusCode}): ${data.substring(0,100)}`));
          }
        } catch (e) {
          log.error(`[AI 请求] 解析 JSON 响应失败: ${e.message}, URL: ${url}, 数据: ${data.substring(0, 200)}`);
          reject(new Error(`解析 JSON 响应失败: ${e.message}`));
        }
      });
    });

    req.on('error', (e) => {
      if (e.name === 'AbortError' || (abortSignal?.aborted && e.code === 'ECONNABORTED')) { // Node 16 uses AbortError, older might just close
        log.warn(`[AI 请求] 请求被中止. URL: ${url}, 错误: ${e.message}`);
        reject(new Error('Request aborted.'));
      } else {
        log.error(`[AI 请求] 请求错误. URL: ${url}, 错误: ${e.message}`);
        reject(new Error(`请求错误: ${e.message}`));
      }
    });

    req.on('timeout', () => {
      req.destroy(new Error('请求超时.')); // Ensure request is destroyed and error is emitted
      log.error(`[AI 请求] 请求超时. URL: ${url}`);
    });
    
    // If an AbortSignal is used directly with http.request (Node v15.4.0+),
    // it handles aborting automatically. For older Node or manual handling:
    // const onAbort = () => {
    //   req.destroy(new Error('Request aborted.'));
    // };
    // if (abortSignal) {
    //   abortSignal.addEventListener('abort', onAbort, { once: true });
    //   req.on('close', () => abortSignal.removeEventListener('abort', onAbort)); // Clean up listener
    // }


    if (body) req.write(body);
    req.end();
  });
}

module.exports = {
  initializeHttpClient,
  makeAiRequest,
};