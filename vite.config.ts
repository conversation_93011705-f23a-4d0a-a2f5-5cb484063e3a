
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const config = {
    plugins: [react()],
    base: '', // 确保 Electron 生产构建时使用相对路径 (file://)
    build: {
      outDir: 'dist',
    },
    server: {
      port: 5173, // Vite 默认端口
    },
    optimizeDeps: {
      include: [
        'react-icons/lu' // Explicitly include the 'react-icons/lu' module
        // Entries like 'react-icons/lu > LuSearch' were incorrect and caused export errors.
        // Vite's automatic dependency scanning should handle react-icons imports correctly.
        // If pre-bundling 'react-icons/lu' is necessary for performance, 
        // the entry should be 'react-icons/lu', but it's often not needed.
      ],
    },
  };

  // 对于 Electron，确保构建时 base 为空字符串 (file:// 协议)
  // 服务时 base 为 '/' (http://localhost)
  if (command === 'serve') {
    config.base = '/';
  } else { // command === 'build'
    config.base = './'; // 或者 '' 也应适用于相对路径
  }

  return config;
});