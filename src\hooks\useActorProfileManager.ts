// src/hooks/useActorProfileManager.ts
// 演员档案管理状态Hook

import { useState, useCallback } from 'react';

interface ActorStats {
  birthday?: string;
  year?: string;
  height?: string;
  measurements?: string;
  birthplace?: string;
  bloodType?: string;
  activeYears?: string;
}

interface ActorProfile {
  id: number;
  name: string;
  aliases: string[];
  avatar_remote_url?: string;
  avatar_local_path?: string;
  stats: ActorStats;
  bio: string;
  tags: string[];
  source_url: string;
  external_ids: {
    imdb?: string;
    tmdb?: string;
    twitter?: string;
    instagram?: string;
    fanza?: string;
    xhamster?: string;
  };
  last_scraped_at: string;
}

interface Movie {
  db_id: number;
  nfoId: string;
  title: string;
  actors: string[];
  coverUrl?: string;
  year?: number;
  isLocal?: boolean;
  isRemote?: boolean;
  source?: 'local' | 'dmm';
  releaseDate?: string;
  detailUrl?: string;
  [key: string]: any;
}

interface CompleteFilmographyData {
  localMovies: Movie[];
  remoteMovies: Movie[];
  allMovies: Movie[];
  totalCount: number;
  localCount: number;
  remoteCount: number;
}

interface ActorProfileState {
  isActorProfileModalOpen: boolean;
  currentActorName: string;
  actorProfileData: ActorProfile | null;
  actorFilmography: Movie[];
  completeFilmography: CompleteFilmographyData | null;
  isLoadingActorProfile: boolean;
  isLoadingCompleteFilmography: boolean;
  error: string | null;
}

export const useActorProfileManager = () => {
  const [state, setState] = useState<ActorProfileState>({
    isActorProfileModalOpen: false,
    currentActorName: '',
    actorProfileData: null,
    actorFilmography: [],
    completeFilmography: null,
    isLoadingActorProfile: false,
    isLoadingCompleteFilmography: false,
    error: null
  });

  /**
   * 打开演员档案模态框
   * @param actorName - 演员姓名
   */
  const openActorProfileModal = useCallback(async (actorName: string) => {
    if (!actorName.trim()) {
      console.warn('演员姓名不能为空');
      return;
    }

    // 设置加载状态并打开模态框
    setState(prev => ({
      ...prev,
      isActorProfileModalOpen: true,
      currentActorName: actorName,
      isLoadingActorProfile: true,
      isLoadingCompleteFilmography: true,
      error: null,
      actorProfileData: null,
      actorFilmography: [],
      completeFilmography: null
    }));

    try {
      // 并行发起演员档案和完整作品列表的调用
      const [profileResult, completeFilmographyResult] = await Promise.all([
        window.sfeElectronAPI.getActorProfile(actorName),
        window.sfeElectronAPI.getActorCompleteFilmography(actorName)
      ]);

      // 处理档案数据
      let profileData: ActorProfile | null = null;
      let profileError: string | null = null;

      if (profileResult.success && profileResult.data) {
        profileData = profileResult.data;
      } else if (!profileResult.success) {
        profileError = profileResult.message || '获取演员档案失败';
      }

      // 处理完整作品集数据
      let completeFilmographyData: CompleteFilmographyData | null = null;
      let filmographyData: Movie[] = [];

      if (completeFilmographyResult.success && completeFilmographyResult.data) {
        completeFilmographyData = completeFilmographyResult.data;
        filmographyData = completeFilmographyData.allMovies || [];
      } else {
        // 如果完整作品列表获取失败，回退到仅本地作品
        const localResult = await window.sfeElectronAPI.getMoviesByActor(actorName);
        if (localResult.success && localResult.data) {
          filmographyData = localResult.data.map((movie: Movie) => ({
            ...movie,
            isLocal: true,
            isRemote: false,
            source: 'local'
          }));
        }
      }

      // 更新状态
      setState(prev => ({
        ...prev,
        actorProfileData: profileData,
        actorFilmography: filmographyData,
        completeFilmography: completeFilmographyData,
        isLoadingActorProfile: false,
        isLoadingCompleteFilmography: false,
        error: profileError
      }));

    } catch (error) {
      console.error('获取演员档案时发生错误:', error);
      setState(prev => ({
        ...prev,
        isLoadingActorProfile: false,
        isLoadingCompleteFilmography: false,
        error: error instanceof Error ? error.message : '发生未知错误'
      }));
    }
  }, []);

  /**
   * 关闭演员档案模态框
   */
  const closeActorProfileModal = useCallback(() => {
    setState(prev => ({
      ...prev,
      isActorProfileModalOpen: false,
      currentActorName: '',
      actorProfileData: null,
      actorFilmography: [],
      completeFilmography: null,
      isLoadingActorProfile: false,
      isLoadingCompleteFilmography: false,
      error: null
    }));
  }, []);

  /**
   * 刷新演员档案数据
   */
  const refreshActorProfile = useCallback(async () => {
    if (!state.currentActorName) return;
    
    await openActorProfileModal(state.currentActorName);
  }, [state.currentActorName, openActorProfileModal]);

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null
    }));
  }, []);

  return {
    // 状态
    isActorProfileModalOpen: state.isActorProfileModalOpen,
    currentActorName: state.currentActorName,
    actorProfileData: state.actorProfileData,
    actorFilmography: state.actorFilmography,
    completeFilmography: state.completeFilmography,
    isLoadingActorProfile: state.isLoadingActorProfile,
    isLoadingCompleteFilmography: state.isLoadingCompleteFilmography,
    error: state.error,

    // 操作函数
    openActorProfileModal,
    closeActorProfileModal,
    refreshActorProfile,
    clearError
  };
};

export default useActorProfileManager;
