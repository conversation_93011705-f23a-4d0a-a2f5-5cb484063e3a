import React from 'react';
import { AppSettings } from '../../types';

interface HomePageSettingsTabProps {
  settings: Partial<AppSettings>;
  onSettingsChange: (newSettings: Partial<AppSettings>) => void;
}

const HomePageSettingsTab: React.FC<HomePageSettingsTabProps> = ({
  settings,
  onSettingsChange
}) => {
  // 获取当前首页设置，如果不存在则使用默认值
  const homePageSettings = settings.homePageSettings || {
    showRecentAdded: true,
    showRecentPlayed: true,
    recentAddedCount: 12,
    recentPlayedCount: 8
  };

  // 更新首页设置的辅助函数
  const updateHomePageSettings = (updates: Partial<typeof homePageSettings>) => {
    onSettingsChange({
      ...settings,
      homePageSettings: {
        ...homePageSettings,
        ...updates
      }
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-neutral-100 mb-4">首页显示设置</h3>
        <p className="text-sm text-neutral-400 mb-6">
          配置主页显示的模块和内容数量
        </p>
      </div>

      {/* 最新入库模块设置 */}
      <div className="bg-neutral-800 rounded-lg p-6 space-y-4">
        <h4 className="text-md font-medium text-neutral-200 mb-3">最新入库模块</h4>
        
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="showRecentAdded"
            checked={homePageSettings.showRecentAdded}
            onChange={(e) => updateHomePageSettings({ showRecentAdded: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-neutral-700 border-neutral-600 rounded focus:ring-blue-500 focus:ring-2"
          />
          <label htmlFor="showRecentAdded" className="text-sm text-neutral-300">
            在主页显示最新入库模块
          </label>
        </div>

        {homePageSettings.showRecentAdded && (
          <div className="ml-7 space-y-2">
            <label className="block text-sm text-neutral-400">
              显示数量
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="number"
                min="1"
                max="50"
                value={homePageSettings.recentAddedCount}
                onChange={(e) => updateHomePageSettings({ recentAddedCount: parseInt(e.target.value) || 12 })}
                className="w-20 px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-neutral-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-neutral-400">部影片</span>
            </div>
            <p className="text-xs text-neutral-500">
              建议设置为 6-24 之间，过多可能影响页面加载速度
            </p>
          </div>
        )}
      </div>

      {/* 最近播放模块设置 */}
      <div className="bg-neutral-800 rounded-lg p-6 space-y-4">
        <h4 className="text-md font-medium text-neutral-200 mb-3">最近播放模块</h4>
        
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="showRecentPlayed"
            checked={homePageSettings.showRecentPlayed}
            onChange={(e) => updateHomePageSettings({ showRecentPlayed: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-neutral-700 border-neutral-600 rounded focus:ring-blue-500 focus:ring-2"
          />
          <label htmlFor="showRecentPlayed" className="text-sm text-neutral-300">
            在主页显示最近播放模块
          </label>
        </div>

        {homePageSettings.showRecentPlayed && (
          <div className="ml-7 space-y-2">
            <label className="block text-sm text-neutral-400">
              显示数量
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="number"
                min="1"
                max="30"
                value={homePageSettings.recentPlayedCount}
                onChange={(e) => updateHomePageSettings({ recentPlayedCount: parseInt(e.target.value) || 8 })}
                className="w-20 px-3 py-2 bg-neutral-700 border border-neutral-600 rounded-md text-neutral-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-neutral-400">部影片</span>
            </div>
            <p className="text-xs text-neutral-500">
              建议设置为 4-20 之间，显示最近观看的影片
            </p>
          </div>
        )}
      </div>

      {/* 预览说明 */}
      <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-300 mb-2">设置说明</h4>
        <ul className="text-xs text-blue-200 space-y-1">
          <li>• 关闭模块后，该模块将不会在主页显示</li>
          <li>• 数量设置会影响主页加载速度，建议根据设备性能调整</li>
          <li>• 设置保存后需要刷新主页才能看到效果</li>
          <li>• 未来可能会添加更多主页模块的显示选项</li>
        </ul>
      </div>
    </div>
  );
};

export default HomePageSettingsTab;
