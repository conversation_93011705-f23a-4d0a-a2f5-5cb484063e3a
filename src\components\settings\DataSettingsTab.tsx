// soul-forge-electron/src/components/settings/DataSettingsTab.tsx
import React from 'react';

interface DataSettingsTabProps {
  handleBackupDatabase: () => Promise<void>;
  handleRestoreDatabase: () => Promise<void>;
}

const DataSettingsTab: React.FC<DataSettingsTabProps> = ({
  handleBackupDatabase,
  handleRestoreDatabase,
}) => {
  return (
    <div className="settings-group-content space-y-4">
      <h4 className="settings-label font-semibold mb-2">数据库管理</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button onClick={handleBackupDatabase} className="button-secondary-app py-2">备份数据库</button>
          <button onClick={handleRestoreDatabase} className="button-danger-app py-2">恢复数据库</button>
      </div>
      <p className="settings-description">备份会将当前数据库保存到安全位置。恢复将使用选定的备份文件覆盖当前数据库，此操作不可撤销，请谨慎操作！</p>
    </div>
  );
};

export default DataSettingsTab;
