
// soul-forge-electron/src/components/modals/PrivacyUnlockModal.tsx
import React, { useRef } from 'react';

interface PrivacyUnlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUnlock: (password: string) => Promise<boolean>;
}

const PrivacyUnlockModal: React.FC<PrivacyUnlockModalProps> = ({ isOpen, onClose, onUnlock }) => {
  const passwordInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  const handleUnlockAttempt = async () => {
    const password = passwordInputRef.current?.value;
    if (password) {
      const success = await onUnlock(password);
      if (success) {
        onClose();
      } else {
        alert("密码错误");
        if (passwordInputRef.current) {
          passwordInputRef.current.focus();
          passwordInputRef.current.select();
        }
      }
    }
  };
  
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleUnlockAttempt();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-[100]"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="privacy-unlock-title"
    >
      <div 
        className="bg-neutral-800 p-6 rounded-lg shadow-xl text-white w-full max-w-sm"
        onClick={(e) => e.stopPropagation()}
      >
        <h3 id="privacy-unlock-title" className="text-lg font-semibold mb-4 text-amber-400">输入隐私模式密码</h3>
        <input
          ref={passwordInputRef}
          type="password"
          placeholder="密码"
          className="form-input-app mb-4 w-full"
          id="privacy-password-input"
          autoFocus
          onKeyPress={handleKeyPress}
        />
        <div className="flex justify-end space-x-3">
          <button onClick={onClose} className="button-secondary-app">
            取消
          </button>
          <button
            onClick={handleUnlockAttempt}
            className="button-primary-app"
          >
            解锁
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrivacyUnlockModal;
