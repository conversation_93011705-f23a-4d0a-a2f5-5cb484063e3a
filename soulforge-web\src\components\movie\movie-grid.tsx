'use client';

import React from 'react';
import { MovieCard } from './movie-card';
import { Movie, ViewMode } from '@/lib/types';
import { cn } from '@/lib/utils';

interface MovieGridProps {
  movies: Movie[];
  viewMode?: ViewMode;
  loading?: boolean;
  onMoviePlay?: (movie: Movie) => void;
  onMovieToggleFavorite?: (movie: Movie) => void;
  onMovieToggleWatched?: (movie: Movie) => void;
  onMovieEdit?: (movie: Movie) => void;
  onShowVersions?: (movie: Movie) => void;
  className?: string;
}

export function MovieGrid({
  movies,
  viewMode = 'grid',
  loading = false,
  onMoviePlay,
  onMovieToggleFavorite,
  onMovieToggleWatched,
  onMovieEdit,
  onShowVersions,
  className,
}: MovieGridProps) {
  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {Array.from({ length: 12 }).map((_, i) => (
            <div
              key={i}
              className="aspect-[2/3] bg-muted animate-pulse rounded-lg"
            />
          ))}
        </div>
      </div>
    );
  }

  if (movies.length === 0) {
    return (
      <div className={cn('flex flex-col items-center justify-center py-12', className)}>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-muted-foreground mb-2">
            暂无电影
          </h3>
          <p className="text-sm text-muted-foreground">
            尝试调整筛选条件或添加新的电影库
          </p>
        </div>
      </div>
    );
  }

  const getGridClassName = () => {
    switch (viewMode) {
      case 'list':
        return 'space-y-2';
      case 'detailed':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
      case 'waterfall':
        return 'columns-2 md:columns-4 lg:columns-6 gap-4 space-y-4';
      default: // grid
        return 'grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4';
    }
  };

  return (
    <div className={cn(getGridClassName(), className)}>
      {movies.map((movie) => (
        <MovieCard
          key={movie.id}
          movie={movie}
          viewMode={viewMode === 'waterfall' ? 'grid' : viewMode}
          onPlay={onMoviePlay}
          onToggleFavorite={onMovieToggleFavorite}
          onToggleWatched={onMovieToggleWatched}
          onEdit={onMovieEdit}
          onShowVersions={onShowVersions}
          className={viewMode === 'waterfall' ? 'break-inside-avoid mb-4' : ''}
        />
      ))}
    </div>
  );
}
