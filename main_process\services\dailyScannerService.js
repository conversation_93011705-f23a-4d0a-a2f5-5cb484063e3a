// main_process/services/dailyScannerService.js
const log = require('electron-log');
const avhelpwikiProvider = require('./scrapers/avhelpwikiProvider');
const scraperManager = require('./scraperManager');
const databaseService = require('./databaseService');
const settingsService = require('./settingsService');
const trailerDownloadService = require('./trailerDownloadService');
const dmmCalendarMonitor = require('./dmmCalendarMonitor');

/**
 * 每日扫描控制中心 - 编排整个"每日简报"的工作流程
 * 连接"侦察兵"和"总指挥"，实现自动化的每日新作发现和入库
 */

// 全局变量，防止重复执行
let isRunning = false;
let currentScanId = null;

/**
 * 启动历史回溯扫描和入库流程
 * @param {object} event - Electron 的 IPC 事件对象，用于发送进度
 * @param {object} options - 扫描选项
 * @param {Date|string} options.startDate - 开始日期
 * @param {Date|string} options.endDate - 结束日期
 * @returns {Promise<object>} 扫描结果
 */
async function startScan(event, options = {}) {
    // 处理参数，支持向后兼容
    let startDate, endDate;

    if (options.startDate && options.endDate) {
        // 新的日期范围模式
        startDate = typeof options.startDate === 'string' ? new Date(options.startDate) : options.startDate;
        endDate = typeof options.endDate === 'string' ? new Date(options.endDate) : options.endDate;

        // 验证日期
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('无效的日期格式');
        }

        if (startDate > endDate) {
            throw new Error('开始日期不能晚于结束日期');
        }
    } else {
        // 向后兼容：默认为今日
        startDate = new Date();
        endDate = new Date();
    }

    const dateRangeStr = formatDateRange(startDate, endDate);
    log.info(`[HistoryScan] 开始历史回溯扫描: ${dateRangeStr}`);
    if (isRunning) {
        log.warn('[HistoryScan] 任务已在运行，请勿重复启动');
        const errorResult = {
            success: false,
            error: '历史回溯扫描任务已在运行中，请等待当前任务完成'
        };

        if (event && event.sender) {
            event.sender.send('daily-scan-progress', {
                status: 'error',
                message: errorResult.error
            });
        }

        return errorResult;
    }

    isRunning = true;
    currentScanId = Date.now().toString();

    log.info(`[HistoryScan] 开始历史回溯扫描任务 (ID: ${currentScanId}): ${dateRangeStr}`);

    if (event && event.sender) {
        event.sender.send('daily-scan-progress', {
            status: 'started',
            scanId: currentScanId,
            dateRange: dateRangeStr,
            message: `正在启动历史回溯扫描 (${dateRangeStr})...`
        });
    }

    try {
        // 第一阶段：获取指定日期范围的番号列表
        log.info(`[HistoryScan] 阶段1: 从AV Help Wiki获取 ${dateRangeStr} 的番号列表`);

        if (event && event.sender) {
            event.sender.send('daily-scan-progress', {
                status: 'fetching',
                scanId: currentScanId,
                dateRange: dateRangeStr,
                message: `正在从AV Help Wiki获取 ${dateRangeStr} 的新作列表...`
            });
        }

        // 生成日期范围内的所有日期
        const dateList = generateDateList(startDate, endDate);
        log.info(`[HistoryScan] 需要扫描 ${dateList.length} 天: ${dateList.map(d => formatDateForDisplay(d)).join(', ')}`);

        const allNfoIds = new Set();
        let processedDays = 0;

        // 循环处理每一天
        for (const currentDate of dateList) {
            // 检查是否被用户停止
            if (!isRunning) {
                log.warn(`[HistoryScan] 扫描被用户停止 (ID: ${currentScanId})`);
                break;
            }

            const dateStr = formatDateForDisplay(currentDate);
            processedDays++;

            log.info(`[HistoryScan] 处理第 ${processedDays}/${dateList.length} 天: ${dateStr}`);

            if (event && event.sender) {
                event.sender.send('daily-scan-progress', {
                    status: 'fetching',
                    scanId: currentScanId,
                    currentDate: dateStr,
                    dayProgress: {
                        current: processedDays,
                        total: dateList.length,
                        percentage: Math.round((processedDays / dateList.length) * 100)
                    },
                    message: `正在获取 ${dateStr} 的新作列表... (${processedDays}/${dateList.length})`
                });
            }

            try {
                const dayNfoIds = await avhelpwikiProvider.fetchNfoIdsForDate(currentDate);

                log.info(`[HistoryScan] ${dateStr} 发现 ${dayNfoIds.length} 个番号`);

                // 合并到总集合中
                dayNfoIds.forEach(id => allNfoIds.add(id));

                // 添加延迟避免请求过快，同时检查停止状态
                if (processedDays < dateList.length && isRunning) {
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }

            } catch (error) {
                log.error(`[HistoryScan] 获取 ${dateStr} 数据失败: ${error.message}`);
                // 继续处理下一天，不中断整个流程
            }
        }

        const nfoIds = Array.from(allNfoIds);
        log.info(`[HistoryScan] ${dateRangeStr} 总共发现 ${nfoIds.length} 个唯一番号`);

        // 预告片链接将在刮削过程中自动处理
        log.info(`[HistoryScan] 预告片将在刮削过程中自动下载到标准路径`);
        
        if (!nfoIds || nfoIds.length === 0) {
            const result = {
                success: true,
                message: `${dateRangeStr} 暂无新发现的番号`,
                dateRange: dateRangeStr,
                totalFound: 0,
                processed: 0,
                success: 0,
                failed: 0,
                skipped: 0,
                details: []
            };

            log.info(`[HistoryScan] ${dateRangeStr} 暂无新发现的番号`);

            if (event && event.sender) {
                event.sender.send('daily-scan-progress', {
                    status: 'completed',
                    scanId: currentScanId,
                    result: result
                });
            }

            return result;
        }

        log.info(`[HistoryScan] ${dateRangeStr} 发现 ${nfoIds.length} 个番号: ${nfoIds.slice(0, 5).join(', ')}${nfoIds.length > 5 ? '...' : ''}`);

        // 第二阶段：过滤已存在的番号
        log.info('[HistoryScan] 阶段2: 过滤数据库中已存在的番号');

        if (event && event.sender) {
            event.sender.send('daily-scan-progress', {
                status: 'filtering',
                scanId: currentScanId,
                dateRange: dateRangeStr,
                message: '正在过滤已存在的番号...',
                totalFound: nfoIds.length
            });
        }

        const newNfoIds = await filterExistingNfoIds(nfoIds);
        const skippedCount = nfoIds.length - newNfoIds.length;

        log.info(`[HistoryScan] 过滤完成: 新番号 ${newNfoIds.length} 个，已存在 ${skippedCount} 个`);

        // 详细记录跳过的番号
        if (skippedCount > 0) {
            const existingNfoIds = nfoIds.filter(id => !newNfoIds.includes(id));
            log.info(`[HistoryScan] 跳过已存在的番号: ${existingNfoIds.slice(0, 10).join(', ')}${existingNfoIds.length > 10 ? '...' : ''}`);
        }

        if (newNfoIds.length === 0) {
            const result = {
                success: true,
                message: `${dateRangeStr} 所有发现的番号都已存在于数据库中`,
                dateRange: dateRangeStr,
                totalFound: nfoIds.length,
                processed: 0,
                success: 0,
                failed: 0,
                skipped: skippedCount,
                details: []
            };

            if (event && event.sender) {
                event.sender.send('daily-scan-progress', {
                    status: 'completed',
                    scanId: currentScanId,
                    result: result
                });
            }

            return result;
        }

        // 第三阶段：批量刮削和入库
        log.info(`[HistoryScan] 阶段3: 开始批量刮削和入库 ${newNfoIds.length} 个新番号`);

        const processingResult = await processBatchNfoIds(newNfoIds, event, currentScanId);

        // 汇总最终结果
        const finalResult = {
            success: true,
            message: `历史回溯扫描完成！${dateRangeStr} 发现 ${nfoIds.length} 个番号，成功入库 ${processingResult.successCount} 个`,
            dateRange: dateRangeStr,
            totalFound: nfoIds.length,
            processed: newNfoIds.length,
            success: processingResult.successCount,
            failed: processingResult.failCount,
            skipped: skippedCount,
            details: processingResult.details,
            scanId: currentScanId
        };

        log.info(`[HistoryScan] 扫描任务完成 (ID: ${currentScanId}): ${dateRangeStr} 总计 ${nfoIds.length}, 新增 ${newNfoIds.length}, 成功 ${processingResult.successCount}, 失败 ${processingResult.failCount}, 跳过 ${skippedCount}`);
        
        if (event && event.sender) {
            event.sender.send('daily-scan-progress', { 
                status: 'completed',
                scanId: currentScanId,
                result: finalResult
            });
        }

        return finalResult;

    } catch (error) {
        log.error(`[DailyScan] 扫描任务失败 (ID: ${currentScanId}):`, error.message);
        
        const errorResult = {
            success: false,
            error: error.message,
            scanId: currentScanId
        };
        
        if (event && event.sender) {
            event.sender.send('daily-scan-progress', { 
                status: 'error',
                scanId: currentScanId,
                message: `扫描失败: ${error.message}`
            });
        }
        
        return errorResult;
        
    } finally {
        isRunning = false;
        currentScanId = null;
    }
}

/**
 * 启动每日简报扫描和入库流程（向后兼容接口）
 * @param {object} event - Electron 的 IPC 事件对象，用于发送进度
 * @returns {Promise<object>} 扫描结果
 */
async function startDailyScan(event) {
    log.info('[HistoryScan] 调用向后兼容接口，启动今日扫描...');
    return await startScan(event, {
        startDate: new Date(),
        endDate: new Date()
    });
}

/**
 * 生成日期范围内的所有日期
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {Date[]} 日期数组
 */
function generateDateList(startDate, endDate) {
    const dates = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
        dates.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
}

/**
 * 格式化日期为显示用的字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD)
 */
function formatDateForDisplay(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 格式化日期范围为显示字符串
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {string} 格式化后的日期范围字符串
 */
function formatDateRange(startDate, endDate) {
    const startStr = formatDateForDisplay(startDate);
    const endStr = formatDateForDisplay(endDate);

    if (startStr === endStr) {
        return startStr;
    } else {
        return `${startStr} 至 ${endStr}`;
    }
}

/**
 * 过滤数据库中已存在的番号
 * @param {string[]} nfoIds - 番号列表
 * @returns {Promise<string[]>} 不存在于数据库中的番号列表
 */
async function filterExistingNfoIds(nfoIds) {
    const newNfoIds = [];
    const existingNfoIds = [];

    log.info(`[HistoryScan] 开始过滤 ${nfoIds.length} 个番号...`);

    for (const nfoId of nfoIds) {
        try {
            const existing = await databaseService.getMovieByNfoId(nfoId);
            if (!existing) {
                newNfoIds.push(nfoId);
            } else {
                existingNfoIds.push(nfoId);
                log.debug(`[HistoryScan] ${nfoId} 已存在 (${existing.asset_status})，跳过`);
            }
        } catch (error) {
            // 如果查询出错，保守起见认为不存在
            log.warn(`[HistoryScan] 查询 ${nfoId} 时出错，将其视为新番号: ${error.message}`);
            newNfoIds.push(nfoId);
        }
    }

    // 统计信息
    log.info(`[HistoryScan] 过滤结果: 新番号 ${newNfoIds.length} 个，已存在 ${existingNfoIds.length} 个`);

    if (existingNfoIds.length > 0) {
        log.info(`[HistoryScan] 跳过的已存在番号: ${existingNfoIds.slice(0, 5).join(', ')}${existingNfoIds.length > 5 ? ` 等${existingNfoIds.length}个` : ''}`);
    }

    return newNfoIds;
}

/**
 * 批量处理番号列表
 * @param {string[]} nfoIds - 要处理的番号列表
 * @param {object} event - IPC事件对象
 * @param {string} scanId - 扫描任务ID
 * @returns {Promise<object>} 处理结果
 */
async function processBatchNfoIds(nfoIds, event, scanId) {
    let successCount = 0;
    let failCount = 0;
    const details = [];
    
    // 获取延迟设置
    const settings = settingsService.getSettings();
    const delay = settings.aiApiDelay || 3000; // 默认3秒延迟
    
    log.info(`[DailyScan] 开始批量处理，延迟设置: ${delay}ms`);
    
    for (let i = 0; i < nfoIds.length; i++) {
        // 检查是否被用户停止
        if (!isRunning) {
            log.warn(`[HistoryScan] 批量处理被用户停止 (ID: ${scanId})`);
            break;
        }

        const nfoId = nfoIds[i];
        const progress = {
            current: i + 1,
            total: nfoIds.length,
            percentage: Math.round(((i + 1) / nfoIds.length) * 100)
        };
        
        log.info(`[DailyScan] 处理进度 ${progress.current}/${progress.total} (${progress.percentage}%): ${nfoId}`);
        
        if (event && event.sender) {
            event.sender.send('daily-scan-progress', { 
                status: 'processing',
                scanId: scanId,
                currentNfoId: nfoId,
                progress: progress,
                message: `正在处理 ${nfoId} (${progress.current}/${progress.total})`
            });
        }

        try {
            // 刮削影片数据
            log.debug(`[DailyScan] 开始刮削: ${nfoId}`);
            const scrapedData = await scraperManager.scrapeMovieById(nfoId);
            
            if (!scrapedData || !scrapedData.success) {
                throw new Error(scrapedData?.error || '刮削失败');
            }
            
            // 创建虚拟资产
            log.debug(`[DailyScan] 开始创建虚拟资产: ${nfoId}`);
            const createResult = await databaseService.createVirtualAsset(scrapedData.data);
            
            if (!createResult || !createResult.success) {
                throw new Error(createResult?.error || '创建虚拟资产失败');
            }
            
            successCount++;
            details.push({
                nfoId: nfoId,
                status: 'success',
                message: '成功入库'
            });
            
            log.info(`[DailyScan] ${nfoId} 处理成功`);

        } catch (error) {
            failCount++;
            const errorMessage = error.message || '未知错误';
            
            details.push({
                nfoId: nfoId,
                status: 'failed',
                error: errorMessage
            });
            
            log.error(`[DailyScan] ${nfoId} 处理失败: ${errorMessage}`);
        }
        
        // 安全延迟，避免请求过快，同时检查停止状态
        if (i < nfoIds.length - 1 && isRunning) { // 最后一个不需要延迟
            log.debug(`[DailyScan] 等待 ${delay}ms 后继续下一个...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    
    // 预告片已在刮削过程中自动下载到标准路径
    log.info(`[DailyScan] 预告片已通过 scraperManager 自动下载到标准路径`);

    return {
        successCount,
        failCount,
        details
    };
}

/**
 * 获取当前扫描状态
 * @returns {object} 当前扫描状态
 */
function getScanStatus() {
    return {
        isRunning: isRunning,
        currentScanId: currentScanId
    };
}

/**
 * 停止当前扫描任务
 * @returns {boolean} 是否成功停止
 */
function stopCurrentScan() {
    if (!isRunning) {
        return false;
    }
    
    log.warn(`[DailyScan] 用户请求停止扫描任务 (ID: ${currentScanId})`);
    
    // 注意：这里只是设置标志，实际的停止需要在处理循环中检查
    isRunning = false;
    
    return true;
}

/**
 * 扫描未来新品发售信息
 * @param {object} event - Electron 的 IPC 事件对象，用于发送进度
 * @returns {Promise<object>} 扫描结果
 */
async function scanUpcomingReleases(event) {
    log.info('[UpcomingScan] 开始扫描未来新品发售信息...');

    try {
        // 发送开始扫描的进度
        if (event) {
            event.sender.send('upcoming-scan-progress', {
                stage: 'scanning',
                message: '正在扫描DMM发售日历...',
                progress: 10
            });
        }

        // 调用DMM日历监视器
        const upcomingReleases = await dmmCalendarMonitor.scanForUpcomingReleases();

        if (event) {
            event.sender.send('upcoming-scan-progress', {
                stage: 'processing',
                message: `发现 ${upcomingReleases.length} 部未来新品，开始处理...`,
                progress: 50
            });
        }

        // 处理扫描结果
        const processResult = await processUpcomingReleases(upcomingReleases, event);

        if (event) {
            event.sender.send('upcoming-scan-progress', {
                stage: 'complete',
                message: `扫描完成！新增 ${processResult.newCount} 部，更新 ${processResult.updatedCount} 部`,
                progress: 100
            });
        }

        log.info(`[UpcomingScan] 扫描完成: 新增 ${processResult.newCount} 部，更新 ${processResult.updatedCount} 部`);

        return {
            success: true,
            totalFound: upcomingReleases.length,
            newCount: processResult.newCount,
            updatedCount: processResult.updatedCount,
            message: `扫描完成！发现 ${upcomingReleases.length} 部未来新品，新增 ${processResult.newCount} 部，更新 ${processResult.updatedCount} 部`
        };

    } catch (error) {
        log.error(`[UpcomingScan] 扫描失败: ${error.message}`);

        if (event) {
            event.sender.send('upcoming-scan-progress', {
                stage: 'error',
                message: `扫描失败: ${error.message}`,
                progress: 0
            });
        }

        return {
            success: false,
            error: error.message,
            message: `扫描失败: ${error.message}`
        };
    }
}

/**
 * 处理未来新品发售信息
 * @param {Array} upcomingReleases - 未来新品信息数组
 * @param {object} event - Electron 的 IPC 事件对象
 * @returns {Promise<object>} 处理结果
 */
async function processUpcomingReleases(upcomingReleases, event) {
    let newCount = 0;
    let updatedCount = 0;

    for (let i = 0; i < upcomingReleases.length; i++) {
        const release = upcomingReleases[i];

        try {
            // 检查数据库中是否已存在该番号
            const existingMovie = databaseService.getMovieByNfoId(release.nfoId);

            if (!existingMovie) {
                // 不存在，创建新的预发布资产
                await createUpcomingAsset(release);
                newCount++;
                log.debug(`[UpcomingScan] 新增未来新品: ${release.nfoId} - ${release.title}`);
            } else {
                // 已存在，更新发售日期等信息
                await updateUpcomingAsset(existingMovie, release);
                updatedCount++;
                log.debug(`[UpcomingScan] 更新未来新品: ${release.nfoId} - ${release.title}`);
            }

            // 发送进度更新
            if (event && i % 5 === 0) { // 每5个更新一次进度
                const progress = 50 + Math.floor((i / upcomingReleases.length) * 40);
                event.sender.send('upcoming-scan-progress', {
                    stage: 'processing',
                    message: `正在处理: ${release.nfoId} (${i + 1}/${upcomingReleases.length})`,
                    progress: progress
                });
            }

        } catch (error) {
            log.error(`[UpcomingScan] 处理 ${release.nfoId} 失败: ${error.message}`);
        }
    }

    return { newCount, updatedCount };
}

/**
 * 创建未来新品资产
 * @param {object} release - 未来新品信息
 */
async function createUpcomingAsset(release) {
    const movieData = {
        nfoId: release.nfoId,
        title: release.title || '',
        releaseDate: release.releaseDate || '',
        coverUrl: release.coverUrl || '',
        actors: Array.isArray(release.actors) ? release.actors.join(', ') : '',
        trailerUrl: release.detailPageUrl || '',
        release_status: 'upcoming', // 关键：设置为未来发售状态
        // 虚拟资产，没有filePath
        filePath: null,
        fileName: null,
        // 其他默认值
        year: release.releaseDate ? new Date(release.releaseDate).getFullYear() : null,
        plot: `即将发售的新作品。预定发售日期：${release.releaseDate || '未知'}`,
        studio: '',
        director: '',
        genres: '',
        tags: 'upcoming,预告',
        watched: 0,
        personalRating: 0
    };

    // 调用数据库服务创建虚拟资产
    const result = await databaseService.createVirtualAsset(movieData);

    if (!result.success) {
        throw new Error(`创建虚拟资产失败: ${result.error}`);
    }

    return result;
}

/**
 * 更新未来新品资产
 * @param {object} existingMovie - 现有影片记录
 * @param {object} release - 新的发售信息
 */
async function updateUpcomingAsset(existingMovie, release) {
    // 只更新特定字段，避免覆盖用户数据
    const updateData = {};

    // 更新发售日期（如果有新信息）
    if (release.releaseDate && release.releaseDate !== existingMovie.releaseDate) {
        updateData.releaseDate = release.releaseDate;
        updateData.year = new Date(release.releaseDate).getFullYear();
    }

    // 更新封面URL（如果有新信息）
    if (release.coverUrl && release.coverUrl !== existingMovie.coverUrl) {
        updateData.coverUrl = release.coverUrl;
    }

    // 更新演员信息（如果有新信息）
    if (release.actors && release.actors.length > 0) {
        const newActors = Array.isArray(release.actors) ? release.actors.join(', ') : release.actors;
        if (newActors !== existingMovie.actors) {
            updateData.actors = newActors;
        }
    }

    // 确保release_status为upcoming（如果当前不是released状态）
    if (existingMovie.release_status !== 'released') {
        updateData.release_status = 'upcoming';
    }

    // 如果有更新内容，执行更新
    if (Object.keys(updateData).length > 0) {
        const result = databaseService.updateMovie(existingMovie.db_id, updateData);
        if (!result.success) {
            throw new Error(`更新影片信息失败: ${result.error}`);
        }
    }

    return true;
}

module.exports = {
    startScan,
    startDailyScan,
    getScanStatus,
    stopCurrentScan,
    filterExistingNfoIds,
    processBatchNfoIds,
    generateDateList,
    formatDateForDisplay,
    formatDateRange,
    scanUpcomingReleases,
    processUpcomingReleases,
    createUpcomingAsset,
    updateUpcomingAsset
};
