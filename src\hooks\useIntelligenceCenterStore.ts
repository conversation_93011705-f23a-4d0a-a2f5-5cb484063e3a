// src/hooks/useIntelligenceCenterStore.ts
// 情报中心统一状态管理Store

import { create } from 'zustand';

export type IntelligenceTab = 'daily-briefing' | 'upcoming-releases' | 'instant-recon';

export interface TaskStatus {
  isRunning: boolean;
  progress?: number;
  message?: string;
  startTime?: number;
}

export interface UpcomingRelease {
  nfoId: string;
  title: string;
  releaseDate: string;
  coverUrl?: string;
  actors?: string[];
  detailPageUrl?: string;
}

export interface IntelligenceCenterState {
  // 标签页状态
  activeTab: IntelligenceTab;
  setActiveTab: (tab: IntelligenceTab) => void;

  // 任务状态管理
  dailyBriefingTask: TaskStatus;
  upcomingReleasesTask: TaskStatus;
  instantReconTask: TaskStatus;
  
  // 任务状态更新方法
  updateDailyBriefingTask: (status: Partial<TaskStatus>) => void;
  updateUpcomingReleasesTask: (status: Partial<TaskStatus>) => void;
  updateInstantReconTask: (status: Partial<TaskStatus>) => void;

  // 全局任务状态
  isAnyTaskRunning: () => boolean;
  getRunningTasksCount: () => number;

  // 未来新品数据
  upcomingReleases: UpcomingRelease[];
  setUpcomingReleases: (releases: UpcomingRelease[]) => void;
  addUpcomingRelease: (release: UpcomingRelease) => void;

  // 即时侦察联动
  instantReconTarget: string;
  setInstantReconTarget: (nfoId: string) => void;
  triggerInstantRecon: (nfoId: string) => void;

  // 结果统计
  lastScanResults: {
    dailyBriefing?: {
      timestamp: number;
      foundCount: number;
      processedCount: number;
    };
    upcomingReleases?: {
      timestamp: number;
      foundCount: number;
      newCount: number;
      updatedCount: number;
    };
    instantRecon?: {
      timestamp: number;
      nfoId: string;
      success: boolean;
    };
  };
  updateScanResult: (type: keyof IntelligenceCenterState['lastScanResults'], result: any) => void;

  // 重置所有状态
  resetAllTasks: () => void;
}

export const useIntelligenceCenterStore = create<IntelligenceCenterState>((set, get) => ({
  // 初始状态
  activeTab: 'daily-briefing',
  
  dailyBriefingTask: { isRunning: false },
  upcomingReleasesTask: { isRunning: false },
  instantReconTask: { isRunning: false },
  
  upcomingReleases: [],
  instantReconTarget: '',
  lastScanResults: {},

  // 标签页管理
  setActiveTab: (tab) => set({ activeTab: tab }),

  // 任务状态更新
  updateDailyBriefingTask: (status) => set((state) => ({
    dailyBriefingTask: { ...state.dailyBriefingTask, ...status }
  })),

  updateUpcomingReleasesTask: (status) => set((state) => ({
    upcomingReleasesTask: { ...state.upcomingReleasesTask, ...status }
  })),

  updateInstantReconTask: (status) => set((state) => ({
    instantReconTask: { ...state.instantReconTask, ...status }
  })),

  // 全局任务状态
  isAnyTaskRunning: () => {
    const state = get();
    return state.dailyBriefingTask.isRunning || 
           state.upcomingReleasesTask.isRunning || 
           state.instantReconTask.isRunning;
  },

  getRunningTasksCount: () => {
    const state = get();
    let count = 0;
    if (state.dailyBriefingTask.isRunning) count++;
    if (state.upcomingReleasesTask.isRunning) count++;
    if (state.instantReconTask.isRunning) count++;
    return count;
  },

  // 未来新品管理
  setUpcomingReleases: (releases) => set({ upcomingReleases: releases }),
  
  addUpcomingRelease: (release) => set((state) => ({
    upcomingReleases: [...state.upcomingReleases, release]
  })),

  // 即时侦察联动
  setInstantReconTarget: (nfoId) => set({ instantReconTarget: nfoId }),
  
  triggerInstantRecon: (nfoId) => set((state) => ({
    instantReconTarget: nfoId,
    activeTab: 'instant-recon'
  })),

  // 结果统计更新
  updateScanResult: (type, result) => set((state) => ({
    lastScanResults: {
      ...state.lastScanResults,
      [type]: { ...result, timestamp: Date.now() }
    }
  })),

  // 重置所有任务
  resetAllTasks: () => set({
    dailyBriefingTask: { isRunning: false },
    upcomingReleasesTask: { isRunning: false },
    instantReconTask: { isRunning: false },
    instantReconTarget: ''
  })
}));
