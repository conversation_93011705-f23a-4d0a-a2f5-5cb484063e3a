// 测试 Cloudflare 验证修复
// 在 Electron 应用的开发者控制台中运行

async function testCloudflarefix() {
  console.log('🔧 开始测试 Cloudflare 验证修复...\n');
  
  try {
    // 1. 检查站点配置是否已更新
    console.log('1️⃣ 检查站点配置更新');
    
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (forumsResult.success && forumsResult.forums.length > 0) {
      console.log('✅ 论坛配置加载成功');
      
      forumsResult.forums.forEach((forum, index) => {
        console.log(`\n论坛 ${index + 1}: ${forum.name}`);
        console.log(`  Key: ${forum.key}`);
        console.log(`  登录URL: ${forum.loginUrl || '未配置'}`);
        console.log(`  登录指示器: ${forum.loggedInIndicatorSelector || '未配置'}`);
        
        if (forum.loggedInIndicatorSelector && !forum.loggedInIndicatorSelector.includes('TODO')) {
          console.log('  ✅ 登录指示器已配置');
        } else {
          console.log('  ⚠️ 登录指示器需要配置');
        }
      });
    } else {
      console.error('❌ 无法获取论坛配置');
      return false;
    }
    
    // 2. 检查等待策略修改
    console.log('\n2️⃣ 检查等待策略修改');
    
    console.log('✅ 代码修改已完成:');
    console.log('  • page.goto() 等待策略从 networkidle 改为 domcontentloaded');
    console.log('  • 超时时间从 30秒 延长到 60秒');
    console.log('  • 实现智能等待登录成功指示器');
    console.log('  • 添加后备等待逻辑');
    
    // 3. 模拟测试场景
    console.log('\n3️⃣ 模拟测试场景');
    
    const testScenarios = [
      {
        name: '正常登录流程',
        description: '用户手动完成登录，登录指示器出现',
        expected: '等待登录指示器，成功检测后继续'
      },
      {
        name: 'Cloudflare 验证',
        description: '遇到 Cloudflare 验证，用户手动完成',
        expected: '不再因网络活动超时，等待指示器出现'
      },
      {
        name: '超时处理',
        description: '5分钟内未完成登录',
        expected: '记录警告但继续执行，不中断流程'
      }
    ];
    
    testScenarios.forEach((scenario, index) => {
      console.log(`\n场景 ${index + 1}: ${scenario.name}`);
      console.log(`  描述: ${scenario.description}`);
      console.log(`  预期: ${scenario.expected}`);
    });
    
    // 4. 配置指导
    console.log('\n4️⃣ 配置指导');
    
    console.log('📋 下一步需要项目总监配置:');
    console.log('\n🔍 获取登录成功指示器的步骤:');
    console.log('1. 打开 Chrome 浏览器');
    console.log('2. 访问论坛登录页面');
    console.log('3. 完成 Cloudflare 验证和登录');
    console.log('4. 寻找只有登录状态下才显示的元素:');
    console.log('   • "退出"按钮');
    console.log('   • 显示用户名的区域');
    console.log('   • "我的空间"或"个人中心"链接');
    console.log('5. 右键该元素 → 检查 → 复制选择器');
    console.log('6. 将选择器填入 site-profiles.json 的 loggedInIndicatorSelector');
    
    // 5. 示例配置
    console.log('\n5️⃣ 示例配置');
    
    const exampleConfig = {
      "forumA": {
        "name": "论坛A (示例)",
        "loginUrl": "https://example.com/login.php",
        "loggedInIndicatorSelector": "#um > p:nth-child(2) > a[href*=\"logout\"]",
        "postLinkSelector": "...",
        "// 其他配置": "..."
      }
    };
    
    console.log('📝 配置示例:');
    console.log(JSON.stringify(exampleConfig, null, 2));
    
    // 6. 验证建议
    console.log('\n6️⃣ 验证建议');
    
    console.log('🧪 修复后的测试建议:');
    console.log('1. 配置正确的登录指示器');
    console.log('2. 重启应用加载新配置');
    console.log('3. 启动搜集任务测试');
    console.log('4. 观察是否能正确等待验证完成');
    console.log('5. 检查日志中的状态更新');
    
    console.log('\n🎉 Cloudflare 验证修复测试完成！');
    
    // 验收标准检查
    console.log('\n📋 修复验收标准:');
    console.log('✅ 等待策略: 从 networkidle 改为 domcontentloaded');
    console.log('✅ 超时时间: 延长到 60秒');
    console.log('✅ 智能等待: 实现登录指示器等待');
    console.log('✅ 配置文件: 添加 loggedInIndicatorSelector 字段');
    console.log('✅ 后备逻辑: 保留原有等待机制');
    console.log('⚠️ 待配置: 需要填写实际的登录指示器选择器');
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 检查配置完整性
async function checkConfigCompleteness() {
  console.log('🔍 检查配置完整性...\n');
  
  try {
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (!forumsResult.success) {
      console.error('❌ 无法获取论坛配置');
      return false;
    }
    
    let allConfigured = true;
    
    forumsResult.forums.forEach((forum, index) => {
      console.log(`\n📋 论坛 ${index + 1}: ${forum.name}`);
      
      const requiredFields = [
        'loginUrl',
        'loggedInIndicatorSelector',
        'postLinkSelector',
        'postTitleSelector'
      ];
      
      const missingFields = [];
      
      requiredFields.forEach(field => {
        if (!forum[field] || forum[field].includes('TODO')) {
          missingFields.push(field);
        }
      });
      
      if (missingFields.length === 0) {
        console.log('✅ 配置完整');
      } else {
        console.log(`❌ 缺少配置: ${missingFields.join(', ')}`);
        allConfigured = false;
      }
    });
    
    if (allConfigured) {
      console.log('\n🎉 所有论坛配置完整，可以开始测试！');
    } else {
      console.log('\n⚠️ 部分配置缺失，请完善后再测试');
    }
    
    return allConfigured;
    
  } catch (error) {
    console.error('❌ 检查配置时出错:', error);
    return false;
  }
}

// 导出函数
window.testCloudflarefix = testCloudflarefix;
window.checkConfigCompleteness = checkConfigCompleteness;

console.log(`
🔧 Cloudflare 验证修复测试工具已加载！

使用方法:
1. testCloudflarefix() - 测试修复完成情况
2. checkConfigCompleteness() - 检查配置完整性

⚠️ 重要提醒:
- 修复已完成，但需要配置登录指示器
- 请按照指导获取正确的 CSS 选择器
- 配置完成后重启应用进行测试

推荐使用: testCloudflarefix()
`);

// 自动运行测试
testCloudflarefix();
