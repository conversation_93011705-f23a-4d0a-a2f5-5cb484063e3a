// main_process/services/browserManager.js
const { chromium } = require('playwright');
const log = require('electron-log');

let globalBrowser = null;

/**
 * 获取全局浏览器实例，如果不存在则创建一个
 * @returns {Promise<Browser>}
 */
async function getBrowser() {
    if (!globalBrowser) {
        log.info('[BrowserManager] 创建新的浏览器实例...');
        
        globalBrowser = await chromium.launch({
            headless: true, // 静默模式，不显示浏览器窗口
            args: [
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-sandbox',
                '--disable-setuid-sandbox'
            ]
        });

        // 监听浏览器关闭事件
        globalBrowser.on('disconnected', () => {
            log.info('[BrowserManager] 浏览器实例已断开连接');
            globalBrowser = null;
        });

        log.info('[BrowserManager] 浏览器实例创建成功');
    }

    return globalBrowser;
}

/**
 * 关闭全局浏览器实例
 */
async function closeBrowser() {
    if (globalBrowser) {
        log.info('[BrowserManager] 关闭浏览器实例...');
        await globalBrowser.close();
        globalBrowser = null;
        log.info('[BrowserManager] 浏览器实例已关闭');
    }
}

/**
 * 创建一个新的浏览器上下文，带有常用配置
 * @param {Object} options - 上下文选项
 * @returns {Promise<BrowserContext>}
 */
async function createContext(options = {}) {
    const browser = await getBrowser();
    
    const defaultOptions = {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1920, height: 1080 },
        ignoreHTTPSErrors: true,
        ...options
    };

    const context = await browser.newContext(defaultOptions);
    
    // 设置默认超时
    context.setDefaultTimeout(30000);
    context.setDefaultNavigationTimeout(30000);

    return context;
}

module.exports = {
    getBrowser,
    closeBrowser,
    createContext
};
