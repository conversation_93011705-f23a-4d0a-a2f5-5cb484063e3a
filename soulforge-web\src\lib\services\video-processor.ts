import ffmpeg from 'fluent-ffmpeg';
import { promises as fs } from 'fs';
import path from 'path';
import sharp from 'sharp';

export interface VideoInfo {
  duration: number;
  width: number;
  height: number;
  fps: number;
  videoCodec: string;
  audioCodec: string;
  bitrate: number;
  fileSize: number;
}

export interface ThumbnailOptions {
  timestamp?: number; // seconds
  width?: number;
  height?: number;
  quality?: 'sd_320p' | 'hd_640p' | 'fhd_1280p_720h';
}

export class VideoProcessor {
  private static readonly QUALITY_SETTINGS = {
    sd_320p: { width: 320, height: 240 },
    hd_640p: { width: 640, height: 480 },
    fhd_1280p_720h: { width: 1280, height: 720 },
  };

  static async getVideoInfo(filePath: string): Promise<VideoInfo> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

        if (!videoStream) {
          reject(new Error('No video stream found'));
          return;
        }

        const info: VideoInfo = {
          duration: metadata.format.duration || 0,
          width: videoStream.width || 0,
          height: videoStream.height || 0,
          fps: this.parseFps(videoStream.r_frame_rate || '0/1'),
          videoCodec: videoStream.codec_name || 'unknown',
          audioCodec: audioStream?.codec_name || 'unknown',
          bitrate: parseInt(metadata.format.bit_rate || '0'),
          fileSize: parseInt(metadata.format.size || '0'),
        };

        resolve(info);
      });
    });
  }

  static async generateThumbnail(
    videoPath: string,
    outputPath: string,
    options: ThumbnailOptions = {}
  ): Promise<string> {
    const {
      timestamp = 10,
      quality = 'hd_640p',
    } = options;

    const settings = this.QUALITY_SETTINGS[quality];
    const tempPath = `${outputPath}.temp.png`;

    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .seekInput(timestamp)
        .frames(1)
        .size(`${settings.width}x${settings.height}`)
        .output(tempPath)
        .on('end', async () => {
          try {
            // Optimize the image with sharp
            await sharp(tempPath)
              .jpeg({ quality: 85 })
              .toFile(outputPath);
            
            // Clean up temp file
            await fs.unlink(tempPath);
            
            resolve(outputPath);
          } catch (error) {
            reject(error);
          }
        })
        .on('error', reject)
        .run();
    });
  }

  static async generateMultipleThumbnails(
    videoPath: string,
    outputDir: string,
    count: number = 6,
    quality: ThumbnailOptions['quality'] = 'hd_640p'
  ): Promise<string[]> {
    const videoInfo = await this.getVideoInfo(videoPath);
    const duration = videoInfo.duration;
    
    if (duration <= 0) {
      throw new Error('Invalid video duration');
    }

    const thumbnails: string[] = [];
    const interval = duration / (count + 1);

    // Ensure output directory exists
    await fs.mkdir(outputDir, { recursive: true });

    for (let i = 1; i <= count; i++) {
      const timestamp = interval * i;
      const outputPath = path.join(outputDir, `thumbnail_${i}.jpg`);
      
      try {
        await this.generateThumbnail(videoPath, outputPath, {
          timestamp,
          quality,
        });
        thumbnails.push(outputPath);
      } catch (error) {
        console.warn(`Failed to generate thumbnail ${i}:`, error);
      }
    }

    return thumbnails;
  }

  static async extractVideoMetadata(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');

        const result = {
          format: metadata.format,
          videoStream: videoStream ? {
            codec: videoStream.codec_name,
            codecLong: videoStream.codec_long_name,
            width: videoStream.width,
            height: videoStream.height,
            fps: this.parseFps(videoStream.r_frame_rate || '0/1'),
            bitrate: videoStream.bit_rate,
            pixelFormat: videoStream.pix_fmt,
            profile: videoStream.profile,
            level: videoStream.level,
          } : null,
          audioStream: audioStream ? {
            codec: audioStream.codec_name,
            codecLong: audioStream.codec_long_name,
            channels: audioStream.channels,
            channelLayout: audioStream.channel_layout,
            sampleRate: audioStream.sample_rate,
            bitrate: audioStream.bit_rate,
          } : null,
        };

        resolve(result);
      });
    });
  }

  static async createVideoPreview(
    videoPath: string,
    outputPath: string,
    duration: number = 30,
    startTime: number = 60
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .seekInput(startTime)
        .duration(duration)
        .videoCodec('libx264')
        .audioCodec('aac')
        .size('640x480')
        .videoBitrate('500k')
        .audioBitrate('128k')
        .output(outputPath)
        .on('end', () => resolve(outputPath))
        .on('error', reject)
        .run();
    });
  }

  static async optimizeImage(
    inputPath: string,
    outputPath: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'jpeg' | 'png' | 'webp';
    } = {}
  ): Promise<string> {
    const {
      width,
      height,
      quality = 85,
      format = 'jpeg',
    } = options;

    let processor = sharp(inputPath);

    if (width || height) {
      processor = processor.resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true,
      });
    }

    switch (format) {
      case 'jpeg':
        processor = processor.jpeg({ quality });
        break;
      case 'png':
        processor = processor.png({ quality });
        break;
      case 'webp':
        processor = processor.webp({ quality });
        break;
    }

    await processor.toFile(outputPath);
    return outputPath;
  }

  private static parseFps(fpsString: string): number {
    const [num, den] = fpsString.split('/').map(Number);
    return den ? num / den : 0;
  }

  static async checkVideoIntegrity(filePath: string): Promise<boolean> {
    return new Promise((resolve) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          resolve(false);
          return;
        }

        // Basic checks
        const hasVideoStream = metadata.streams.some(s => s.codec_type === 'video');
        const hasDuration = metadata.format.duration && metadata.format.duration > 0;
        
        resolve(hasVideoStream && hasDuration);
      });
    });
  }
}
