'use client';

import React, { useState, useEffect } from 'react';
import { Movie } from '@/lib/types';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';
import { 
  Play, 
  Copy, 
  Disc3, 
  Clock, 
  HardDrive, 
  Eye,
  Star,
  Calendar,
  Film
} from 'lucide-react';
import { formatFileSize, formatDuration } from '@/lib/utils';

interface VersionSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  movie: Movie | null;
  onSelectVersion: (version: Movie) => void;
}

interface MovieVersion extends Movie {
  preferredStatus?: string;
  customFileTags?: string[];
  versionCategories?: string[];
}

export function VersionSelectionModal({
  isOpen,
  onClose,
  movie,
  onSelectVersion,
}: VersionSelectionModalProps) {
  const [versions, setVersions] = useState<MovieVersion[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && movie?.nfoId) {
      fetchVersions(movie.nfoId);
    }
  }, [isOpen, movie?.nfoId]);

  const fetchVersions = async (nfoId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/movies/versions/${encodeURIComponent(nfoId)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch versions');
      }
      
      const data = await response.json();
      setVersions(data.versions || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load versions');
      setVersions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleVersionSelect = (version: MovieVersion) => {
    onSelectVersion(version);
    onClose();
  };

  if (!movie) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Film className="h-5 w-5" />
            <span>版本选择: {movie.title || movie.fileName}</span>
            <Badge variant="secondary">{versions.length} 个版本</Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-muted-foreground">加载版本信息...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-destructive mb-4">{error}</p>
              <Button onClick={() => movie.nfoId && fetchVersions(movie.nfoId)}>
                重试
              </Button>
            </div>
          )}

          {!loading && !error && versions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">没有找到版本信息</p>
            </div>
          )}

          {!loading && !error && versions.length > 0 && (
            <div className="space-y-4">
              {versions.map((version) => (
                <VersionCard
                  key={version.id}
                  version={version}
                  onSelect={() => handleVersionSelect(version)}
                />
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface VersionCardProps {
  version: MovieVersion;
  onSelect: () => void;
}

function VersionCard({ version, onSelect }: VersionCardProps) {
  const isPreferred = version.preferredStatus === 'preferred';
  const isMultiCD = (version.multiCdCountForNfoId || 0) > 1;

  return (
    <Card className={`cursor-pointer hover:shadow-md transition-shadow ${isPreferred ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="flex space-x-4">
          {/* Thumbnail */}
          <div className="relative w-20 h-28 flex-shrink-0 rounded overflow-hidden">
            {version.localCoverPath || version.posterUrl ? (
              <Image
                src={version.localCoverPath || version.posterUrl || ''}
                alt={version.fileName}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <Play className="h-6 w-6 text-muted-foreground" />
              </div>
            )}
            
            {/* Play overlay */}
            <div 
              className="absolute inset-0 bg-black/0 hover:bg-black/50 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100"
              onClick={onSelect}
            >
              <Button size="icon" className="h-8 w-8 rounded-full">
                <Play className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <h3 
                className="font-semibold text-sm hover:text-primary hover:underline cursor-pointer truncate"
                onClick={onSelect}
                title={version.fileName}
              >
                {version.fileName}
              </h3>
              
              <div className="flex items-center space-x-2 ml-2">
                {isPreferred && (
                  <Badge variant="default" className="text-xs">
                    首选版本
                  </Badge>
                )}
                {isMultiCD && (
                  <Badge variant="secondary" className="text-xs">
                    <Disc3 className="h-3 w-3 mr-1" />
                    {version.multiCdCountForNfoId}-CD
                  </Badge>
                )}
              </div>
            </div>

            <p className="text-xs text-muted-foreground truncate mb-2" title={version.filePath}>
              {version.filePath}
            </p>

            {/* Technical info */}
            <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <div className="space-y-1">
                {version.year && (
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    <span>{version.year}</span>
                  </div>
                )}
                {version.runtime && (
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{formatDuration(version.runtime * 60)}</span>
                  </div>
                )}
                {version.personalRating && (
                  <div className="flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    <span>{version.personalRating}/10</span>
                  </div>
                )}
              </div>
              
              <div className="space-y-1">
                {version.fileSize && (
                  <div className="flex items-center">
                    <HardDrive className="h-3 w-3 mr-1" />
                    <span>{formatFileSize(version.fileSize)}</span>
                  </div>
                )}
                {version.resolution && (
                  <div className="flex items-center">
                    <Film className="h-3 w-3 mr-1" />
                    <span>{version.resolution}</span>
                  </div>
                )}
                {version.watched && (
                  <div className="flex items-center">
                    <Eye className="h-3 w-3 mr-1" />
                    <span>已观看</span>
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            {(version.customFileTags?.length || version.versionCategories?.length) && (
              <div className="mt-2 flex flex-wrap gap-1">
                {version.customFileTags?.map((tag, index) => (
                  <Badge key={`tag-${index}`} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {version.versionCategories?.map((category, index) => (
                  <Badge key={`cat-${index}`} variant="secondary" className="text-xs">
                    {category}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
