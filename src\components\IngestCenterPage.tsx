// src/components/IngestCenterPage.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { FolderOpen, Play, FileText, AlertCircle, CheckCircle, Loader2, Download, Archive, Trash2, Shield } from 'lucide-react';

interface MdFileInfo {
  path: string;
  name: string;
  directory: string;
  size: number;
  created: Date;
  modified: Date;
  isReadable: boolean;
}

interface ScanProgressData {
  type: 'start' | 'progress' | 'complete' | 'error';
  message: string;
}

const IngestCenterPage: React.FC = () => {
  const [workspacePath, setWorkspacePath] = useState<string>('');
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [scanLogs, setScanLogs] = useState<string[]>([]);
  const [mdFiles, setMdFiles] = useState<MdFileInfo[]>([]);
  const [totalFiles, setTotalFiles] = useState<number>(0);
  const [workflowResults, setWorkflowResults] = useState<any>(null);

  // 监听扫描进度
  useEffect(() => {
    const unsubscribeScan = window.sfeElectronAPI.onIngestScanProgress((data: ScanProgressData) => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${data.message}`;

      setScanLogs(prev => [...prev, logMessage]);

      if (data.type === 'complete' || data.type === 'error') {
        setIsScanning(false);
      }
    });

    const unsubscribeWorkflow = window.sfeElectronAPI.onIngestWorkflowProgress((data: ScanProgressData) => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${data.message}`;

      setScanLogs(prev => [...prev, logMessage]);

      if (data.type === 'complete' || data.type === 'error') {
        setIsProcessing(false);
      }
    });

    return () => {
      unsubscribeScan();
      unsubscribeWorkflow();
    };
  }, []);

  // 选择工作区
  const handleSelectWorkspace = useCallback(async () => {
    try {
      const result = await window.sfeElectronAPI.ingestSelectWorkspace();
      
      if (result.success && result.workspacePath) {
        setWorkspacePath(result.workspacePath);
        setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 已选择工作区: ${result.workspacePath}`]);
      } else if (result.canceled) {
        setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 用户取消了工作区选择`]);
      } else if (result.error) {
        setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 错误: ${result.error}`]);
      }
    } catch (error) {
      console.error('选择工作区失败:', error);
      setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 选择工作区失败: ${error}`]);
    }
  }, []);

  // 开始扫描
  const handleStartScan = useCallback(async () => {
    if (!workspacePath) {
      setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 错误: 请先选择工作区`]);
      return;
    }

    setIsScanning(true);
    setMdFiles([]);
    setTotalFiles(0);
    setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 开始扫描工作区...`]);

    try {
      const result = await window.sfeElectronAPI.ingestStartScan(workspacePath);
      
      if (result.success && result.files) {
        setMdFiles(result.files);
        setTotalFiles(result.totalCount || 0);
      } else if (result.error) {
        setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 扫描失败: ${result.error}`]);
      }
    } catch (error) {
      console.error('扫描失败:', error);
      setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 扫描失败: ${error}`]);
      setIsScanning(false);
    }
  }, [workspacePath]);

  // 开始汇入工作流
  const handleStartWorkflow = useCallback(async () => {
    if (!workspacePath || mdFiles.length === 0) {
      setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 错误: 请先扫描工作区并确保有.md文件`]);
      return;
    }

    setIsProcessing(true);
    setWorkflowResults(null);
    setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 开始情报汇入与解析工作流...`]);

    try {
      const result = await window.sfeElectronAPI.ingestStartWorkflow(workspacePath, mdFiles);

      if (result.success && result.results) {
        setWorkflowResults(result.results);
        setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 汇入工作流完成！`]);
      } else if (result.error) {
        setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 汇入工作流失败: ${result.error}`]);
      }
    } catch (error) {
      console.error('汇入工作流失败:', error);
      setScanLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] 汇入工作流失败: ${error}`]);
      setIsProcessing(false);
    }
  }, [workspacePath, mdFiles]);

  // 清空日志
  const handleClearLogs = useCallback(() => {
    setScanLogs([]);
  }, []);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (date: Date | string): string => {
    const d = new Date(date);
    return d.toLocaleString();
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-900">
      {/* 页面标题 */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
          <FileText className="w-6 h-6" />
          情报中心
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          扫描和管理Collector工作区中的.md情报档案
        </p>
      </div>

      <div className="flex-1 flex gap-6 p-6 min-h-0">
        {/* 左侧控制面板 - 固定宽度，防止挤压变形 */}
        <div className="w-80 min-w-80 flex-shrink-0 space-y-6">
          {/* 工作区选择 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">工作区设置</h2>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  当前工作区路径
                </label>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600 min-h-[60px] flex items-center">
                  {workspacePath ? (
                    <span className="text-sm text-gray-900 dark:text-white break-all">
                      {workspacePath}
                    </span>
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      未选择工作区
                    </span>
                  )}
                </div>
              </div>

              <button
                onClick={handleSelectWorkspace}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                <FolderOpen className="w-4 h-4" />
                选择工作区
              </button>
            </div>
          </div>

          {/* 扫描控制 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">扫描控制</h2>

            <div className="space-y-3">
              <button
                onClick={handleStartScan}
                disabled={!workspacePath || isScanning}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-md transition-colors"
              >
                {isScanning ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    扫描中...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    开始扫描
                  </>
                )}
              </button>

              {totalFiles > 0 && (
                <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <span className="text-sm text-green-700 dark:text-green-300 flex items-center justify-center gap-1">
                    <CheckCircle className="w-4 h-4" />
                    发现 {totalFiles} 个.md情报档案
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 汇入工作流控制 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">汇入工作流</h2>

            <div className="space-y-3">
              <button
                onClick={handleStartWorkflow}
                disabled={!workspacePath || mdFiles.length === 0 || isProcessing}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-md transition-colors"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4" />
                    开始汇入解析
                  </>
                )}
              </button>

              <div className="text-xs text-gray-500 dark:text-gray-400">
                <p>• 自动解压附件文件</p>
                <p>• .txt文件内容直接追加到.md文档</p>
                <p>• 非.txt文件信息记录到YAML元数据</p>
                <p>• 处理完成的附件移至回收站</p>
                <p>• 密码错误的附件移至隔离区</p>
              </div>

              {workflowResults && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      <span>处理完成: {workflowResults.processed}/{workflowResults.total}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>成功: {workflowResults.successful}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                      <span>失败: {workflowResults.failed}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Trash2 className="w-4 h-4 text-gray-500" />
                      <span>回收: {workflowResults.recycled}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-orange-500" />
                      <span>隔离: {workflowResults.quarantined}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧内容区域 - 自适应剩余空间 */}
        <div className="flex-1 min-w-0 space-y-6">
          {/* 扫描日志 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">扫描日志</h2>
              <button
                onClick={handleClearLogs}
                className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                清空日志
              </button>
            </div>

            <div className="bg-gray-50 dark:bg-gray-900 rounded-md p-3 h-64 overflow-y-auto font-mono text-sm">
              {scanLogs.length > 0 ? (
                scanLogs.map((log, index) => (
                  <div key={index} className="text-gray-700 dark:text-gray-300 mb-1 break-words">
                    {log}
                  </div>
                ))
              ) : (
                <div className="text-gray-500 dark:text-gray-400 italic">
                  暂无日志信息
                </div>
              )}
            </div>
          </div>

          {/* 文件列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 flex flex-col min-h-0">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              情报档案列表 ({mdFiles.length})
            </h2>

            <div className="flex-1 overflow-y-auto min-h-0">
              {mdFiles.length > 0 ? (
                <div className="space-y-2">
                  {mdFiles.map((file, index) => (
                    <div
                      key={index}
                      className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 dark:text-white break-words">
                            {file.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 break-words">
                            {file.directory}
                          </p>
                          <div className="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                            <span>大小: {formatFileSize(file.size)}</span>
                            <span>修改: {formatDate(file.modified)}</span>
                          </div>
                        </div>
                        <FileText className="w-5 h-5 text-gray-400 flex-shrink-0 ml-2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>暂无情报档案</p>
                  <p className="text-sm">请选择工作区并开始扫描</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngestCenterPage;
