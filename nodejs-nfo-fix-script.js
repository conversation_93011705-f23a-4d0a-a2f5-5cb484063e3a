// Node.js 版本的 NFO ID 修复脚本
// 在 Electron 应用的开发者控制台中运行

// 快速修复所有缺失的 NFO ID
async function quickFixNfoIds() {
  try {
    console.log('🚀 开始快速修复 NFO ID (Node.js 版本)...');
    
    const result = await window.sfeElectronAPI.batchFixNfoIds();
    
    if (result.success) {
      console.log('✅ 修复完成！');
      console.log(`📊 统计: 处理 ${result.processed}, 成功 ${result.updated}, 跳过 ${result.skipped}, 错误 ${result.errors}`);
      
      if (result.updated > 0) {
        console.log('🎉 修复成功！现在相同 NFO ID 的电影应该会合并显示。');
        
        const shouldRefresh = confirm('是否立即刷新页面查看效果？');
        if (shouldRefresh) {
          location.reload();
        }
      }
    } else {
      console.error('❌ 修复失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 修复异常:', error);
  }
}

// 分析 NFO ID 状态
async function analyzeNfoStatus() {
  try {
    console.log('🔍 分析 NFO ID 状态...');
    
    const result = await window.sfeElectronAPI.analyzeNfoIdStatus();
    
    if (result.success) {
      console.log('\n📊 统计信息:');
      console.log(`总电影数: ${result.statistics.total}`);
      console.log(`有 NFO ID: ${result.statistics.with_nfo_id} (${(result.statistics.with_nfo_id / result.statistics.total * 100).toFixed(1)}%)`);
      console.log(`缺少 NFO ID: ${result.statistics.without_nfo_id} (${(result.statistics.without_nfo_id / result.statistics.total * 100).toFixed(1)}%)`);
      console.log(`可以修复: ${result.canFixCount}`);
      
      if (result.duplicateGroups.length > 0) {
        console.log(`\n🔄 重复的 NFO ID (${result.duplicateGroups.length} 组):`);
        result.duplicateGroups.slice(0, 5).forEach((dup, index) => {
          console.log(`  ${index + 1}. "${dup.nfoId}" - ${dup.count} 个版本`);
        });
      }
      
      if (result.extractableDetails.length > 0) {
        console.log(`\n🛠️ 可以修复的示例:`);
        result.extractableDetails.slice(0, 5).forEach((item, index) => {
          console.log(`  ${index + 1}. ${item.fileName} -> ${item.extractedId}`);
        });
      }
    } else {
      console.error('❌ 分析失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 分析异常:', error);
  }
}

// 验证 NFO ID 质量
async function validateNfoIds() {
  try {
    console.log('🔍 验证 NFO ID 质量...');
    
    const result = await window.sfeElectronAPI.validateNfoIds();
    
    if (result.success) {
      console.log('\n📊 验证结果:');
      console.log(`总计有 NFO ID: ${result.summary.totalWithNfoId}`);
      console.log(`重复组数: ${result.summary.duplicateGroups}`);
      console.log(`格式异常: ${result.summary.invalidFormats}`);
      
      if (result.duplicates.length > 0) {
        console.log(`\n🔄 重复的 NFO ID:`);
        result.duplicates.slice(0, 5).forEach((dup, index) => {
          console.log(`  ${index + 1}. "${dup.nfoId}" - ${dup.count} 个版本 (ID: ${dup.movieIds.join(', ')})`);
        });
      }
      
      if (result.invalidFormats.length > 0) {
        console.log(`\n⚠️ 格式异常的 NFO ID:`);
        result.invalidFormats.slice(0, 5).forEach((item, index) => {
          console.log(`  ${index + 1}. "${item.nfoId}" - ${item.fileName}`);
        });
      }
    } else {
      console.error('❌ 验证失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 验证异常:', error);
  }
}

// 完整的修复流程
async function fullFixProcess() {
  try {
    console.log('🔧 开始完整的 NFO ID 修复流程...\n');
    
    // 1. 分析当前状态
    console.log('步骤 1: 分析当前状态');
    await analyzeNfoStatus();
    
    // 2. 询问是否继续
    const shouldContinue = confirm('\n是否继续执行修复？');
    if (!shouldContinue) {
      console.log('用户取消修复。');
      return;
    }
    
    // 3. 执行修复
    console.log('\n步骤 2: 执行修复');
    await quickFixNfoIds();
    
    // 4. 验证结果
    console.log('\n步骤 3: 验证修复结果');
    await validateNfoIds();
    
    console.log('\n🎉 完整修复流程完成！');
  } catch (error) {
    console.error('❌ 修复流程失败:', error);
  }
}

// 导出函数到全局作用域
window.quickFixNfoIds = quickFixNfoIds;
window.analyzeNfoStatus = analyzeNfoStatus;
window.validateNfoIds = validateNfoIds;
window.fullFixProcess = fullFixProcess;

// 显示使用说明
console.log(`
🛠️ Node.js NFO ID 修复工具已加载！

快速使用：
1. quickFixNfoIds()     - 立即修复所有缺失的 NFO ID
2. analyzeNfoStatus()   - 分析当前 NFO ID 状态
3. validateNfoIds()     - 验证 NFO ID 质量
4. fullFixProcess()     - 完整的修复流程

推荐使用: quickFixNfoIds()
`);

// 自动运行分析
analyzeNfoStatus();
