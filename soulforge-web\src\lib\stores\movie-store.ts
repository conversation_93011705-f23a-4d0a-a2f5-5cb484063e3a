import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Movie, FilterOptions, SortField, SortOrder, ViewMode, PaginationInfo } from '../types';
import { apiClient } from '../api/client';

interface MovieState {
  // Data
  movies: Movie[];
  selectedMovie: Movie | null;
  loading: boolean;
  error: string | null;
  
  // Filters and Sorting
  filters: FilterOptions;
  sortField: SortField;
  sortOrder: SortOrder;
  viewMode: ViewMode;
  
  // Pagination
  pagination: PaginationInfo;
  
  // Actions
  setMovies: (movies: Movie[]) => void;
  setSelectedMovie: (movie: Movie | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Filter Actions
  setFilters: (filters: Partial<FilterOptions>) => void;
  clearFilters: () => void;
  setSortField: (field: SortField) => void;
  setSortOrder: (order: SortOrder) => void;
  setViewMode: (mode: ViewMode) => void;
  
  // Pagination Actions
  setPagination: (pagination: Partial<PaginationInfo>) => void;
  setPage: (page: number) => void;
  
  // Movie Actions
  addMovie: (movie: Movie) => void;
  updateMovie: (id: string, updates: Partial<Movie>) => void;
  removeMovie: (id: string) => void;
  toggleWatched: (id: string) => void;
  setPersonalRating: (id: string, rating: number) => void;

  // Async Actions
  fetchMovies: () => Promise<void>;
  fetchMovie: (id: string) => Promise<Movie | null>;
  saveMovie: (movie: Partial<Movie>) => Promise<Movie | null>;
  deleteMovieAsync: (id: string) => Promise<void>;
  toggleWatchedAsync: (id: string) => Promise<void>;
  scanMoviesAsync: (directories: string[], libraryId?: string) => Promise<void>;
}

const initialPagination: PaginationInfo = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0,
};

const initialFilters: FilterOptions = {
  search: '',
  genres: [],
  actors: [],
  studios: [],
  years: [],
  watched: undefined,
  favorited: undefined,
  libraryId: undefined,
};

export const useMovieStore = create<MovieState>()(
  devtools(
    (set, get) => ({
      // Initial State
      movies: [],
      selectedMovie: null,
      loading: false,
      error: null,
      filters: initialFilters,
      sortField: 'lastScanned',
      sortOrder: 'desc',
      viewMode: 'grid',
      pagination: initialPagination,
      
      // Basic Actions
      setMovies: (movies) => set({ movies }),
      setSelectedMovie: (movie) => set({ selectedMovie: movie }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      
      // Filter Actions
      setFilters: (newFilters) => 
        set((state) => ({ 
          filters: { ...state.filters, ...newFilters },
          pagination: { ...state.pagination, page: 1 } // Reset to first page when filtering
        })),
      
      clearFilters: () => 
        set({ 
          filters: initialFilters,
          pagination: { ...initialPagination }
        }),
      
      setSortField: (field) => 
        set((state) => ({
          sortField: field,
          sortOrder: state.sortField === field && state.sortOrder === 'asc' ? 'desc' : 'asc',
          pagination: { ...state.pagination, page: 1 }
        })),
      
      setSortOrder: (order) => set({ sortOrder: order }),
      setViewMode: (mode) => set({ viewMode: mode }),
      
      // Pagination Actions
      setPagination: (newPagination) =>
        set((state) => ({
          pagination: { ...state.pagination, ...newPagination }
        })),
      
      setPage: (page) =>
        set((state) => ({
          pagination: { ...state.pagination, page }
        })),
      
      // Movie Actions
      addMovie: (movie) =>
        set((state) => ({
          movies: [...state.movies, movie]
        })),
      
      updateMovie: (id, updates) =>
        set((state) => ({
          movies: state.movies.map((movie) =>
            movie.id === id ? { ...movie, ...updates } : movie
          ),
          selectedMovie: state.selectedMovie?.id === id 
            ? { ...state.selectedMovie, ...updates }
            : state.selectedMovie
        })),
      
      removeMovie: (id) =>
        set((state) => ({
          movies: state.movies.filter((movie) => movie.id !== id),
          selectedMovie: state.selectedMovie?.id === id ? null : state.selectedMovie
        })),
      
      toggleWatched: (id) =>
        set((state) => ({
          movies: state.movies.map((movie) =>
            movie.id === id ? { ...movie, watched: !movie.watched } : movie
          )
        })),
      
      setPersonalRating: (id, rating) =>
        set((state) => ({
          movies: state.movies.map((movie) =>
            movie.id === id ? { ...movie, personalRating: rating } : movie
          )
        })),

      // Async Actions
      fetchMovies: async () => {
        const state = get();
        set({ loading: true, error: null });

        try {
          const response = await apiClient.getMovies(
            state.filters,
            state.sortField,
            state.sortOrder,
            state.pagination.page,
            state.pagination.limit
          );

          if (response.success && response.data) {
            set({
              movies: response.data,
              pagination: response.pagination || state.pagination,
              loading: false
            });
          } else {
            throw new Error(response.error || 'Failed to fetch movies');
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false
          });
        }
      },

      fetchMovie: async (id: string) => {
        try {
          const response = await apiClient.getMovie(id);
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.error || 'Failed to fetch movie');
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return null;
        }
      },

      saveMovie: async (movie: Partial<Movie>) => {
        try {
          const response = movie.id
            ? await apiClient.updateMovie(movie.id, movie)
            : await apiClient.createMovie(movie);

          if (response.success && response.data) {
            if (movie.id) {
              // Update existing movie
              set((state) => ({
                movies: state.movies.map((m) =>
                  m.id === movie.id ? response.data! : m
                )
              }));
            } else {
              // Add new movie
              set((state) => ({
                movies: [...state.movies, response.data!]
              }));
            }
            return response.data;
          }
          throw new Error(response.error || 'Failed to save movie');
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
          return null;
        }
      },

      deleteMovieAsync: async (id: string) => {
        try {
          const response = await apiClient.deleteMovie(id);
          if (response.success) {
            set((state) => ({
              movies: state.movies.filter((movie) => movie.id !== id),
              selectedMovie: state.selectedMovie?.id === id ? null : state.selectedMovie
            }));
          } else {
            throw new Error(response.error || 'Failed to delete movie');
          }
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
      },

      toggleWatchedAsync: async (id: string) => {
        try {
          const response = await apiClient.toggleMovieWatched(id);
          if (response.success && response.data) {
            set((state) => ({
              movies: state.movies.map((movie) =>
                movie.id === id ? response.data! : movie
              )
            }));
          } else {
            throw new Error(response.error || 'Failed to toggle watched status');
          }
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
      },

      scanMoviesAsync: async (directories: string[], libraryId?: string) => {
        set({ loading: true, error: null });

        try {
          const response = await apiClient.scanMovies(directories, libraryId);
          if (response.success) {
            // Refresh movies after scan
            await get().fetchMovies();
          } else {
            throw new Error(response.error || 'Failed to scan movies');
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Unknown error',
            loading: false
          });
        }
      },
    }),
    {
      name: 'movie-store',
    }
  )
);
