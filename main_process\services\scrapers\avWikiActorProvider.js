const log = require('electron-log');
const { chromium } = require('playwright');
const cheerio = require('cheerio');

const PROVIDER_NAME = 'AV-Wiki演员刮削器';
const BASE_URL = 'https://av-wiki.net';

let browser = null;

/**
 * 获取浏览器实例
 */
async function getBrowser() {
  if (!browser) {
    try {
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });
      log.info(`[${PROVIDER_NAME}] 浏览器启动成功`);
    } catch (error) {
      log.error(`[${PROVIDER_NAME}] 浏览器启动失败: ${error.message}`);
      return null;
    }
  }
  return browser;
}

/**
 * 关闭浏览器
 */
async function closeBrowser() {
  if (browser) {
    try {
      await browser.close();
      browser = null;
      log.info(`[${PROVIDER_NAME}] 浏览器已关闭`);
    } catch (error) {
      log.error(`[${PROVIDER_NAME}] 关闭浏览器失败: ${error.message}`);
    }
  }
}

/**
 * 刮削演员作品列表
 * @param {string} actorName - 演员姓名
 * @returns {Promise<Object>} 刮削结果
 */
async function scrapeActorFilmography(actorName) {
  const browser = await getBrowser();
  if (!browser) {
    return {
      success: false,
      error: '浏览器初始化失败',
      data: []
    };
  }

  try {
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });
    
    const page = await context.newPage();
    
    // 智能处理演员姓名：生成多个搜索候选
    const searchCandidates = [];
    
    // 1. 原始名称
    searchCandidates.push(actorName);
    
    // 2. 括号内的别名
    const aliasMatch = actorName.match(/[（(](.*?)[）)]/);
    if (aliasMatch) {
      const alias = aliasMatch[1].trim();
      searchCandidates.push(alias);
      log.info(`[${PROVIDER_NAME}] 检测到别名: "${alias}"`);
    }
    
    // 3. 移除括号的名称
    const cleanName = actorName.replace(/[（(].*?[）)]/g, '').trim();
    if (cleanName !== actorName) {
      searchCandidates.push(cleanName);
    }
    
    log.info(`[${PROVIDER_NAME}] 搜索候选名称: ${searchCandidates.join(', ')}`);
    
    // 尝试每个候选名称
    let movies = [];
    let foundMovies = false;
    
    for (const searchName of searchCandidates) {
      if (foundMovies) break; // 如果已经找到作品，停止搜索
      
      log.info(`[${PROVIDER_NAME}] 尝试搜索: "${searchName}"`);
      
      // 访问AV-Wiki首页
      await page.goto(BASE_URL, { timeout: 30000, waitUntil: 'domcontentloaded' });
      
      // 等待页面加载
      await page.waitForTimeout(2000);
      
      // 查找搜索框并输入演员名
      const searchInput = page.locator('input[name="s"].header_search_input');
      if (await searchInput.count() > 0) {
        await searchInput.fill(searchName);
        await page.keyboard.press('Enter');
        
        log.info(`[${PROVIDER_NAME}] 已提交搜索: "${searchName}"`);
        
        // 等待搜索结果页面加载
        try {
          // 等待页面导航完成
          await page.waitForLoadState('domcontentloaded', { timeout: 10000 });
          await page.waitForTimeout(3000);
        } catch (error) {
          log.warn(`[${PROVIDER_NAME}] 等待页面加载超时: ${error.message}`);
        }

        let content;
        try {
          content = await page.content();
        } catch (error) {
          log.error(`[${PROVIDER_NAME}] 获取页面内容失败: ${error.message}`);
          continue; // 跳过这个搜索候选，尝试下一个
        }
        const $ = cheerio.load(content);

        log.info(`[${PROVIDER_NAME}] 页面内容长度: ${content.length} 字符`);

        // 检查是否有搜索结果
        const pageTitle = $('title').text();
        log.info(`[${PROVIDER_NAME}] 页面标题: ${pageTitle}`);
        
        // 提取作品列表
        const currentMovies = [];
        
        // AV-Wiki的作品列表选择器（根据实际页面结构）
        const movieSelectors = [
          '.search-result-item',
          '.movie-item',
          '.video-item',
          '.content-item',
          'article.post',
          '.post',
          '.entry',
          '.search-results .item',
          '.result-item',
          'div[class*="movie"]',
          'div[class*="video"]'
        ];
        
        let currentFoundMovies = false;
        
        // 首先尝试通用的链接提取方法
        const allLinks = $('a[href*="/video/"], a[href*="/movie/"], a[href*="/av/"]');
        log.info(`[${PROVIDER_NAME}] 找到 ${allLinks.length} 个可能的作品链接`);

        if (allLinks.length > 0) {
          allLinks.each((index, element) => {
            const movie = extractMovieInfoFromLink($, element);
            if (movie) {
              currentMovies.push(movie);
            }
          });

          if (currentMovies.length > 0) {
            currentFoundMovies = true;
          }
        }

        // 如果通用方法没找到，尝试特定选择器
        if (!currentFoundMovies) {
          for (const selector of movieSelectors) {
            const movieElements = $(selector);
            if (movieElements.length > 0) {
              log.info(`[${PROVIDER_NAME}] 使用选择器 ${selector} 找到 ${movieElements.length} 个元素`);

              movieElements.each((index, element) => {
                const movie = extractMovieInfo($, element);
                if (movie) {
                  currentMovies.push(movie);
                }
              });

              if (currentMovies.length > 0) {
                currentFoundMovies = true;
                break;
              }
            }
          }
        }
        
        if (currentFoundMovies && currentMovies.length > 0) {
          log.info(`[${PROVIDER_NAME}] 使用搜索名称 "${searchName}" 找到 ${currentMovies.length} 个作品`);
          movies = currentMovies;
          foundMovies = true;
          break; // 找到作品后停止尝试其他候选名称
        } else {
          log.warn(`[${PROVIDER_NAME}] 搜索名称 "${searchName}" 未找到作品`);
        }
      } else {
        log.warn(`[${PROVIDER_NAME}] 未找到搜索框`);
      }
    }
    
    await context.close();
    
    if (!foundMovies) {
      log.warn(`[${PROVIDER_NAME}] 所有搜索候选都未找到演员作品列表`);
      return {
        success: false,
        error: '未找到演员作品列表',
        data: []
      };
    }
    
    // 去重
    const uniqueMovies = removeDuplicates(movies);
    
    log.info(`[${PROVIDER_NAME}] 成功刮削演员 ${actorName} 的作品: ${uniqueMovies.length} 部`);
    
    return {
      success: true,
      data: uniqueMovies
    };
    
  } catch (error) {
    log.error(`[${PROVIDER_NAME}] 刮削失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * 从链接元素中提取电影信息（简化版）
 * @param {Object} $ - Cheerio对象
 * @param {Object} element - 链接元素
 * @returns {Object|null} 电影信息对象
 */
function extractMovieInfoFromLink($, element) {
  try {
    const $link = $(element);
    const title = $link.text().trim();
    const href = $link.attr('href') || '';

    if (!title || !href) return null;

    // 提取NFO ID
    const nfoId = extractNfoIdFromTitle(title);

    // 构造完整URL
    const fullUrl = href.startsWith('http') ? href : (BASE_URL + href);

    return {
      title: title,
      nfoId: nfoId,
      coverUrl: '',
      releaseDate: '',
      sourceUrl: fullUrl,
      source: 'av-wiki'
    };

  } catch (error) {
    log.error(`[${PROVIDER_NAME}] 从链接提取电影信息失败: ${error.message}`);
    return null;
  }
}

/**
 * 从元素中提取电影信息
 * @param {Object} $ - Cheerio对象
 * @param {Object} element - DOM元素
 * @returns {Object|null} 电影信息对象
 */
function extractMovieInfo($, element) {
  try {
    const $element = $(element);
    
    // 提取标题
    const titleSelectors = [
      'h2 a', 'h3 a', '.title a', '.movie-title', 
      'a[href*="/video/"]', 'a[href*="/movie/"]',
      '.entry-title a', '.post-title a'
    ];
    
    let title = '';
    let link = '';
    
    for (const selector of titleSelectors) {
      const titleElement = $element.find(selector).first();
      if (titleElement.length > 0) {
        title = titleElement.text().trim();
        link = titleElement.attr('href') || '';
        if (title) break;
      }
    }
    
    if (!title) return null;
    
    // 提取NFO ID（番号）
    const nfoId = extractNfoIdFromTitle(title);
    
    // 提取封面图片
    const coverSelectors = [
      'img', '.thumbnail img', '.cover img', '.movie-cover img'
    ];
    
    let coverUrl = '';
    for (const selector of coverSelectors) {
      const imgElement = $element.find(selector).first();
      if (imgElement.length > 0) {
        coverUrl = imgElement.attr('src') || imgElement.attr('data-src') || '';
        if (coverUrl && !coverUrl.startsWith('http')) {
          coverUrl = BASE_URL + coverUrl;
        }
        if (coverUrl) break;
      }
    }
    
    // 提取发布日期
    const dateSelectors = [
      '.date', '.release-date', '.post-date', '.entry-date'
    ];
    
    let releaseDate = '';
    for (const selector of dateSelectors) {
      const dateElement = $element.find(selector).first();
      if (dateElement.length > 0) {
        releaseDate = dateElement.text().trim();
        if (releaseDate) break;
      }
    }
    
    return {
      title: title,
      nfoId: nfoId,
      coverUrl: coverUrl,
      releaseDate: releaseDate,
      sourceUrl: link.startsWith('http') ? link : (BASE_URL + link),
      source: 'av-wiki'
    };
    
  } catch (error) {
    log.error(`[${PROVIDER_NAME}] 提取电影信息失败: ${error.message}`);
    return null;
  }
}

/**
 * 从标题中提取NFO ID
 * @param {string} title - 标题
 * @returns {string} NFO ID
 */
function extractNfoIdFromTitle(title) {
  // 常见的番号格式
  const patterns = [
    /([A-Z]{2,10}-?\d{3,5})/i,  // ABC-123, ABCD123
    /(\d{6}[-_]\d{3})/,         // 123456-123
    /([A-Z]+\d+)/i              // ABC123
  ];
  
  for (const pattern of patterns) {
    const match = title.match(pattern);
    if (match) {
      return match[1].toUpperCase();
    }
  }
  
  return '';
}

/**
 * 去重函数
 * @param {Array} movies - 电影数组
 * @returns {Array} 去重后的电影数组
 */
function removeDuplicates(movies) {
  const seen = new Set();
  return movies.filter(movie => {
    const key = movie.nfoId || movie.title;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

module.exports = {
  scrapeActorFilmography,
  closeBrowser
};
