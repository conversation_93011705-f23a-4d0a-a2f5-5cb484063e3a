import { promises as fs } from 'fs';
import path from 'path';
import { parseString } from 'xml2js';

export interface NFOIdExtractionResult {
  nfoId: string | null;
  source: 'nfo_file' | 'filename' | 'none';
  confidence: 'high' | 'medium' | 'low';
}

export class NFOIdExtractor {
  /**
   * Extract NFO ID from various sources
   */
  static async extractNfoId(filePath: string): Promise<NFOIdExtractionResult> {
    // Try NFO file first
    const nfoResult = await this.extractFromNFOFile(filePath);
    if (nfoResult.nfoId) {
      return nfoResult;
    }

    // Fallback to filename extraction
    const filenameResult = this.extractFromFilename(filePath);
    if (filenameResult.nfoId) {
      return filenameResult;
    }

    return {
      nfoId: null,
      source: 'none',
      confidence: 'low'
    };
  }

  /**
   * Extract NFO ID from NFO file
   */
  static async extractFromNFOFile(videoFilePath: string): Promise<NFOIdExtractionResult> {
    try {
      const videoDir = path.dirname(videoFilePath);
      const videoBaseName = path.basename(videoFilePath, path.extname(videoFilePath));
      const nfoPath = path.join(videoDir, `${videoBaseName}.nfo`);

      // Check if NFO file exists
      try {
        await fs.access(nfoPath);
      } catch {
        return { nfoId: null, source: 'nfo_file', confidence: 'low' };
      }

      // Read and parse NFO file
      const nfoContent = await fs.readFile(nfoPath, 'utf-8');
      const nfoId = await this.parseNFOContent(nfoContent);

      return {
        nfoId,
        source: 'nfo_file',
        confidence: nfoId ? 'high' : 'low'
      };
    } catch (error) {
      console.error('Error extracting NFO ID from file:', error);
      return { nfoId: null, source: 'nfo_file', confidence: 'low' };
    }
  }

  /**
   * Parse NFO content and extract ID
   */
  private static async parseNFOContent(content: string): Promise<string | null> {
    try {
      // Clean content
      const cleanContent = content
        .replace(/^\uFEFF/, '') // Remove BOM
        .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;'); // Fix unescaped ampersands

      // Try XML parsing
      return new Promise((resolve) => {
        parseString(cleanContent, { 
          explicitArray: false,
          ignoreAttrs: false,
          trim: true,
        }, (err, result) => {
          if (err) {
            // Fallback to regex parsing
            resolve(this.parseNFOWithRegex(content));
            return;
          }

          try {
            const movieData = result.movie || result;
            let nfoId = null;

            // Try different ID sources in order of preference
            // 1. Primary uniqueid
            if (movieData.uniqueid) {
              const uniqueids = Array.isArray(movieData.uniqueid) ? movieData.uniqueid : [movieData.uniqueid];
              
              for (const uid of uniqueids) {
                if (typeof uid === 'object' && uid.$) {
                  const type = uid.$.type?.toLowerCase();
                  const isDefault = uid.$.default === 'true';
                  const isPrimary = type === 'primary';
                  
                  if ((isDefault || isPrimary) && uid._) {
                    nfoId = uid._;
                    break;
                  }
                  
                  // Store specific types for fallback
                  if (['imdb', 'tmdb', 'tvdb', 'javdb', 'javlibrary', 'javbus'].includes(type) && uid._) {
                    nfoId = uid._;
                  }
                } else if (typeof uid === 'string') {
                  nfoId = uid;
                  break;
                }
              }
            }

            // 2. Simple <id> tag
            if (!nfoId && movieData.id) {
              nfoId = movieData.id;
            }

            // 3. <num> tag (alternative)
            if (!nfoId && movieData.num) {
              nfoId = movieData.num;
            }

            // 4. Extract from filenameandpath
            if (!nfoId && movieData.filenameandpath) {
              const javMatch = movieData.filenameandpath.match(/([A-Z]{2,5}-\d{3,5})/i);
              if (javMatch) {
                nfoId = javMatch[1].toUpperCase();
              }
            }

            resolve(nfoId?.trim() || null);
          } catch (parseError) {
            resolve(this.parseNFOWithRegex(content));
          }
        });
      });
    } catch (error) {
      return this.parseNFOWithRegex(content);
    }
  }

  /**
   * Fallback regex parsing for NFO content
   */
  private static parseNFOWithRegex(content: string): string | null {
    // Try different patterns
    const patterns = [
      /<id[^>]*>(.*?)<\/id>/i,
      /<uniqueid[^>]*>(.*?)<\/uniqueid>/i,
      /<num[^>]*>(.*?)<\/num>/i,
      /<filenameandpath[^>]*>.*?([A-Z]{2,5}-\d{3,5}).*?<\/filenameandpath>/i,
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match && match[1]?.trim()) {
        return match[1].trim();
      }
    }

    return null;
  }

  /**
   * Extract NFO ID from filename using JAV patterns
   */
  static extractFromFilename(filePath: string): NFOIdExtractionResult {
    const filename = path.basename(filePath);
    const baseName = filename.replace(/\.[^.]+$/, ''); // Remove extension

    // JAV ID patterns (from most specific to least specific)
    const patterns = [
      // Standard JAV format: ABC-123, ABCD-1234, etc.
      /([A-Z]{2,5}-\d{3,5})/i,
      
      // No hyphen format: ABC123, ABCD1234
      /([A-Z]{2,4}\d{3,5})/i,
      
      // With underscore: ABC_123
      /([A-Z]{2,5}_\d{3,5})/i,
      
      // Generic format: LETTERS-NUMBERS or LETTERS_NUMBERS
      /([A-Z]{2,6}[-_]\d{2,6})/i,
      
      // At word boundaries to avoid false matches
      /\b([A-Z]{2,4}\d{3,5})\b/i,
    ];

    for (const pattern of patterns) {
      const match = baseName.match(pattern);
      if (match) {
        const extracted = match[1].toUpperCase().replace('_', '-');
        
        // Validate the extracted ID (basic sanity checks)
        if (this.isValidJavId(extracted)) {
          return {
            nfoId: extracted,
            source: 'filename',
            confidence: 'medium'
          };
        }
      }
    }

    return {
      nfoId: null,
      source: 'filename',
      confidence: 'low'
    };
  }

  /**
   * Validate if extracted ID looks like a valid JAV ID
   */
  private static isValidJavId(id: string): boolean {
    // Basic validation rules
    if (id.length < 5 || id.length > 12) return false;
    
    // Should have letters followed by numbers
    const pattern = /^[A-Z]{2,5}[-_]?\d{2,5}$/;
    if (!pattern.test(id)) return false;
    
    // Avoid common false positives
    const blacklist = ['DVD', 'CD', 'HD', 'SD', 'MP4', 'AVI', 'MKV'];
    const prefix = id.split(/[-_]/)[0];
    if (blacklist.includes(prefix)) return false;
    
    return true;
  }

  /**
   * Batch extract NFO IDs for multiple files
   */
  static async batchExtractNfoIds(filePaths: string[]): Promise<Map<string, NFOIdExtractionResult>> {
    const results = new Map<string, NFOIdExtractionResult>();
    
    // Process in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < filePaths.length; i += batchSize) {
      const batch = filePaths.slice(i, i + batchSize);
      const batchPromises = batch.map(async (filePath) => {
        const result = await this.extractNfoId(filePath);
        return { filePath, result };
      });
      
      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ filePath, result }) => {
        results.set(filePath, result);
      });
    }
    
    return results;
  }
}
