// main_process/services/collectorBridgeService.js
// 【数据天桥】Collector 系统与数据精炼厂之间的安全桥接服务

const log = require('electron-log');

/**
 * Collector 桥接服务 - 数据天桥
 * 
 * 核心原则：
 * 1. 只读不写 - 绝不对 collected_links 表进行写操作
 * 2. 隔离运行 - 桥接失败不影响精炼厂主流程
 * 3. 安全访问 - 通过数据库视图增加安全屏障
 */
class CollectorBridgeService {
  constructor() {
    this.db = null;
    this.log = log;
    this.isInitialized = false;
  }

  /**
   * 初始化桥接服务
   * @param {Object} database - 数据库连接实例
   */
  initialize(database) {
    try {
      this.db = database;
      this.isInitialized = true;
      this.log.info('[数据天桥] 桥接服务初始化成功');
    } catch (error) {
      this.log.error('[数据天桥] 桥接服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 检查桥接服务是否可用
   * @returns {boolean} 是否可用
   */
  isAvailable() {
    if (!this.isInitialized || !this.db) {
      return false;
    }

    try {
      // 检查安全视图是否存在
      const viewExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='view' AND name='collector_movie_data'
      `).get();
      
      return !!viewExists;
    } catch (error) {
      this.log.warn('[数据天桥] 可用性检查失败:', error.message);
      return false;
    }
  }

  /**
   * 为指定番号获取补充数据
   * @param {string} nfoId - 番号
   * @returns {Promise<Object|null>} 补充数据或null
   */
  async getSupplementaryData(nfoId) {
    if (!this.isAvailable()) {
      this.log.debug('[数据天桥] 服务不可用，跳过补充数据获取');
      return null;
    }

    try {
      this.log.debug(`[数据天桥] 开始为 ${nfoId} 获取补充数据...`);

      // 从安全视图获取数据
      const collectorData = await this.getCollectorDataByNfoId(nfoId);
      
      if (!collectorData || collectorData.length === 0) {
        this.log.debug(`[数据天桥] 未找到 ${nfoId} 的 Collector 数据`);
        return null;
      }

      // 处理和聚合数据
      const supplementaryData = await this.processCollectorData(collectorData);
      
      this.log.info(`[数据天桥] 成功为 ${nfoId} 获取补充数据: ${Object.keys(supplementaryData).length} 个字段`);
      return supplementaryData;

    } catch (error) {
      this.log.warn(`[数据天桥] 获取 ${nfoId} 补充数据失败: ${error.message}`);
      return null; // 失败时返回null，不影响主流程
    }
  }

  /**
   * 从安全视图获取 Collector 数据
   * @param {string} nfoId - 番号
   * @returns {Promise<Array>} Collector 数据数组
   */
  async getCollectorDataByNfoId(nfoId) {
    try {
      const stmt = this.db.prepare(`
        SELECT 
          nfoId,
          title,
          source_url,
          magnet_link,
          ed2k_link,
          cloud_links,
          extracted_metadata,
          all_images,
          all_links,
          post_date,
          collected_at,
          source_forum,
          decompression_password,
          preview_image_url,
          md_document_path
        FROM collector_movie_data 
        WHERE nfoId = ?
        ORDER BY collected_at DESC
      `);

      const results = stmt.all(nfoId);
      this.log.debug(`[数据天桥] 从视图获取到 ${results.length} 条 ${nfoId} 的记录`);
      
      return results;
    } catch (error) {
      this.log.error(`[数据天桥] 查询 ${nfoId} 数据失败:`, error.message);
      throw error;
    }
  }

  /**
   * 处理和聚合 Collector 数据
   * @param {Array} collectorData - 原始 Collector 数据
   * @returns {Promise<Object>} 处理后的补充数据
   */
  async processCollectorData(collectorData) {
    const supplementaryData = {
      magnetLinks: [],
      ed2kLinks: [],
      cloudLinks: [],
      forumDiscussions: [],
      extractedImages: [],
      allLinks: [],
      forumMetadata: {},
      downloadInfo: {}
    };

    for (const record of collectorData) {
      try {
        // 处理磁力链接
        if (record.magnet_link) {
          supplementaryData.magnetLinks.push({
            link: record.magnet_link,
            source: record.source_forum || 'unknown',
            collectedAt: record.collected_at,
            password: record.decompression_password || null
          });
        }

        // 处理 ED2K 链接
        if (record.ed2k_link) {
          supplementaryData.ed2kLinks.push({
            link: record.ed2k_link,
            source: record.source_forum || 'unknown',
            collectedAt: record.collected_at
          });
        }

        // 处理网盘链接
        if (record.cloud_links) {
          try {
            const cloudLinks = JSON.parse(record.cloud_links);
            if (Array.isArray(cloudLinks)) {
              supplementaryData.cloudLinks.push(...cloudLinks.map(link => ({
                ...link,
                source: record.source_forum || 'unknown',
                collectedAt: record.collected_at,
                password: record.decompression_password || null
              })));
            }
          } catch (parseError) {
            this.log.warn('[数据天桥] 解析网盘链接JSON失败:', parseError.message);
          }
        }

        // 处理论坛讨论
        if (record.source_url) {
          supplementaryData.forumDiscussions.push({
            url: record.source_url,
            title: record.title || '',
            forum: record.source_forum || 'unknown',
            postDate: record.post_date || '',
            collectedAt: record.collected_at,
            mdDocumentPath: record.md_document_path || null
          });
        }

        // 处理提取的图片
        if (record.all_images) {
          try {
            const images = JSON.parse(record.all_images);
            if (Array.isArray(images)) {
              supplementaryData.extractedImages.push(...images.map(img => ({
                url: img,
                source: record.source_forum || 'unknown',
                collectedAt: record.collected_at
              })));
            }
          } catch (parseError) {
            this.log.warn('[数据天桥] 解析图片JSON失败:', parseError.message);
          }
        }

        // 处理所有链接
        if (record.all_links) {
          try {
            const links = JSON.parse(record.all_links);
            if (Array.isArray(links)) {
              supplementaryData.allLinks.push(...links.map(link => ({
                url: link,
                source: record.source_forum || 'unknown',
                collectedAt: record.collected_at
              })));
            }
          } catch (parseError) {
            this.log.warn('[数据天桥] 解析链接JSON失败:', parseError.message);
          }
        }

        // 处理提取的元数据
        if (record.extracted_metadata) {
          try {
            const metadata = JSON.parse(record.extracted_metadata);
            supplementaryData.forumMetadata[record.source_forum || 'unknown'] = {
              ...metadata,
              collectedAt: record.collected_at
            };
          } catch (parseError) {
            this.log.warn('[数据天桥] 解析元数据JSON失败:', parseError.message);
          }
        }

      } catch (error) {
        this.log.warn('[数据天桥] 处理单条记录失败:', error.message);
        continue; // 跳过有问题的记录，继续处理其他记录
      }
    }

    // 去重和统计
    supplementaryData.magnetLinks = this.deduplicateLinks(supplementaryData.magnetLinks, 'link');
    supplementaryData.ed2kLinks = this.deduplicateLinks(supplementaryData.ed2kLinks, 'link');
    supplementaryData.cloudLinks = this.deduplicateLinks(supplementaryData.cloudLinks, 'url');
    supplementaryData.extractedImages = this.deduplicateLinks(supplementaryData.extractedImages, 'url');
    supplementaryData.allLinks = this.deduplicateLinks(supplementaryData.allLinks, 'url');

    // 添加统计信息
    supplementaryData.statistics = {
      totalRecords: collectorData.length,
      magnetLinksCount: supplementaryData.magnetLinks.length,
      ed2kLinksCount: supplementaryData.ed2kLinks.length,
      cloudLinksCount: supplementaryData.cloudLinks.length,
      forumDiscussionsCount: supplementaryData.forumDiscussions.length,
      extractedImagesCount: supplementaryData.extractedImages.length,
      allLinksCount: supplementaryData.allLinks.length,
      forumSources: [...new Set(collectorData.map(r => r.source_forum).filter(Boolean))]
    };

    return supplementaryData;
  }

  /**
   * 去重链接数组
   * @param {Array} links - 链接数组
   * @param {string} keyField - 用于去重的字段名
   * @returns {Array} 去重后的数组
   */
  deduplicateLinks(links, keyField) {
    const seen = new Set();
    return links.filter(link => {
      const key = link[keyField];
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 获取统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStatistics() {
    if (!this.isAvailable()) {
      return { available: false };
    }

    try {
      const totalRecords = this.db.prepare(`
        SELECT COUNT(*) as count FROM collector_movie_data
      `).get();

      const uniqueNfoIds = this.db.prepare(`
        SELECT COUNT(DISTINCT nfoId) as count FROM collector_movie_data
      `).get();

      const forumSources = this.db.prepare(`
        SELECT DISTINCT source_forum, COUNT(*) as count 
        FROM collector_movie_data 
        GROUP BY source_forum
      `).all();

      return {
        available: true,
        totalRecords: totalRecords.count,
        uniqueMovies: uniqueNfoIds.count,
        forumSources: forumSources
      };
    } catch (error) {
      this.log.error('[数据天桥] 获取统计信息失败:', error.message);
      return { available: false, error: error.message };
    }
  }
}

// 创建单例实例
const collectorBridgeService = new CollectorBridgeService();

module.exports = collectorBridgeService;
