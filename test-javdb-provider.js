#!/usr/bin/env node

// test-javdb-provider.js - JavDB Provider 测试脚本
const path = require('path');
const os = require('os');

async function testJavdbProvider() {
  console.log('🧪 JavDB Provider 测试开始...\n');

  try {
    // 0. 初始化设置服务
    console.log('0️⃣ 初始化设置服务...');
    const settingsService = require('./main_process/services/settingsService');
    const userDataPath = path.join(os.tmpdir(), 'soulforge-test');
    settingsService.initializeSettings(console, userDataPath);
    console.log('✅ 设置服务初始化成功');

    // 1. 测试模块加载
    console.log('\n1️⃣ 测试模块加载...');
    
    console.log('加载 browserManager...');
    const browserManager = require('./main_process/services/browserManager');
    console.log('✅ browserManager 加载成功');
    
    console.log('加载 javdbProvider...');
    const javdbProvider = require('./main_process/services/scrapers/javdbProvider');
    console.log('✅ javdbProvider 加载成功');
    console.log(`📦 Provider 版本: ${javdbProvider.version}`);
    
    console.log('加载 scraperManager...');
    const scraperManager = require('./main_process/services/scraperManager');
    console.log('✅ scraperManager 加载成功');
    
    // 2. 测试基本功能
    console.log('\n2️⃣ 测试基本功能...');
    
    console.log('测试 browserManager.getBrowser()...');
    const browser = await browserManager.getBrowser();
    console.log('✅ 浏览器实例创建成功');
    
    console.log('测试创建浏览器上下文...');
    const context = await browserManager.createContext();
    console.log('✅ 浏览器上下文创建成功');
    
    console.log('关闭上下文...');
    await context.close();
    console.log('✅ 上下文关闭成功');
    
    // 3. 测试 JavDB Provider
    console.log('\n3️⃣ 测试 JavDB Provider...');
    
    console.log('测试 javdbProvider.scrape()...');
    const testNfoId = 'SSIS-001';
    
    console.log(`🔍 开始刮削测试番号: ${testNfoId}`);
    console.log('⚠️  注意: 如果没有配置 JavDB Cookie，可能会遇到访问限制');
    
    try {
      const scrapedData = await javdbProvider.scrape(testNfoId);
      
      console.log('✅ JavDB Provider 刮削成功!');
      console.log('\n📊 刮削结果摘要:');
      console.log(`📺 标题: ${scrapedData.title}`);
      console.log(`🎭 演员: ${scrapedData.actors.join(', ')}`);
      console.log(`📅 发行日期: ${scrapedData.releaseDate}`);
      console.log(`🏢 制作商: ${scrapedData.studio}`);
      console.log(`⭐ 评分: ${scrapedData.rating}`);
      console.log(`🏷️  标签: ${scrapedData.tags.join(', ')}`);
      console.log(`🖼️  封面: ${scrapedData.coverUrl ? '✅' : '❌'}`);
      console.log(`🎬 预览图: ${scrapedData.previewImages.length} 张`);
      console.log(`🧲 磁力链接: ${scrapedData.magnet_links.length} 个`);
      
      if (scrapedData.magnet_links.length > 0) {
        console.log('\n🧲 磁力链接详情:');
        scrapedData.magnet_links.forEach((magnet, index) => {
          console.log(`  ${index + 1}. ${magnet.name}`);
          console.log(`     大小: ${magnet.size}`);
          console.log(`     字幕: ${magnet.has_subtitles ? '✅' : '❌'}`);
          console.log(`     链接: ${magnet.link.substring(0, 50)}...`);
        });
      }
      
    } catch (error) {
      console.log(`❌ JavDB Provider 刮削失败: ${error.message}`);
      
      if (error.message.includes('Cloudflare')) {
        console.log('💡 建议: 请在设置中配置有效的 JavDB Cookie');
      } else if (error.message.includes('未匹配到番号')) {
        console.log('💡 建议: 尝试使用其他番号进行测试');
      } else if (error.message.includes('需要 VIP')) {
        console.log('💡 建议: 该内容需要 VIP 权限，请尝试其他番号');
      }
    }
    
    // 4. 测试 ScraperManager 集成
    console.log('\n4️⃣ 测试 ScraperManager 集成...');
    
    try {
      console.log(`🔄 通过 ScraperManager 刮削 ${testNfoId}...`);
      const managerResult = await scraperManager.scrapeMovieById(testNfoId);
      
      console.log('✅ ScraperManager 刮削成功!');
      console.log(`📊 最终结果来源: ${managerResult.source || '未知'}`);
      
    } catch (error) {
      console.log(`❌ ScraperManager 刮削失败: ${error.message}`);
      console.log('💡 这可能是因为所有 Provider 都失败了，属于正常情况');
    }
    
    // 5. 清理资源
    console.log('\n5️⃣ 清理资源...');
    await browserManager.closeBrowser();
    console.log('✅ 浏览器已关闭');
    
    console.log('\n🎉 JavDB Provider 测试完成!');
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testJavdbProvider().catch(console.error);
}

module.exports = { testJavdbProvider };
