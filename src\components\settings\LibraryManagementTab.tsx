// soul-forge-electron/src/components/settings/LibraryManagementTab.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { MovieLibrary, ManageMovieLibraryParams, ManageMovieLibraryResult } from '../../types';
import { LuPencil, LuTrash2, LuFolderPlus, LuSave, LuImagePlus, LuX } from 'react-icons/lu';

interface LibraryManagementTabProps {}

interface EditableLibrary extends MovieLibrary {
  isEditing?: boolean;
  newName?: string;
  newPaths?: string[];
  newPathInput?: string; 
  newCoverDataUrl?: string | null; 
}

const LibraryManagementTab: React.FC<LibraryManagementTabProps> = () => {
  const [libraries, setLibraries] = useState<EditableLibrary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const fetchLibraries = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await window.sfeElectronAPI.getMovieLibraries();
      if (result.success) {
        setLibraries(result.libraries.map(lib => ({ ...lib, isEditing: false, newName: lib.name, newPaths: [...lib.paths], newPathInput: '', newCoverDataUrl: null })));
      } else {
        setError(result.error || '获取片库列表失败。');
        setLibraries([]);
      }
    } catch (e: any) {
      setError(`获取片库列表时发生前端错误: ${e.message}`);
      setLibraries([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLibraries();
  }, [fetchLibraries]);

  const handleEditToggle = (id: string) => {
    setLibraries(prev => prev.map(lib => 
      lib.id === id ? { ...lib, isEditing: !lib.isEditing, newName: lib.name, newPaths: [...lib.paths], newPathInput: '', newCoverDataUrl: null } : lib
    ));
  };

  const handleInputChange = (id: string, field: keyof Pick<EditableLibrary, 'newName' | 'newPathInput'>, value: string) => {
    setLibraries(prev => prev.map(lib =>
      lib.id === id ? { ...lib, [field]: value } : lib
    ));
  };

  const handleAddPath = async (id: string) => {
    const lib = libraries.find(l => l.id === id);
    if (!lib) return;

    let pathsToAdd: string[] = [];
    if (lib.newPathInput?.trim()) {
        if (!lib.newPaths?.includes(lib.newPathInput.trim())) {
            pathsToAdd.push(lib.newPathInput.trim());
        } else {
            alert('该路径已存在。');
            return;
        }
    } else {
        const selectedPaths = await window.sfeElectronAPI.selectDirectory(); 
        if (selectedPaths && selectedPaths.length > 0) {
            pathsToAdd = selectedPaths.filter(p => !lib.newPaths?.includes(p));
        }
    }
    
    if (pathsToAdd.length > 0) {
        setLibraries(prev => prev.map(l => 
            l.id === id ? { ...l, newPaths: Array.from(new Set([...(l.newPaths || []), ...pathsToAdd])), newPathInput: '' } : l
        ));
    }
  };

  const handleRemovePath = (id: string, pathToRemove: string) => {
    setLibraries(prev => prev.map(lib => 
      lib.id === id ? { ...lib, newPaths: (lib.newPaths || []).filter(p => p !== pathToRemove) } : lib
    ));
  };

  const handleCoverChange = async (id: string) => {
    const result = await window.sfeElectronAPI.browseImageForDataUrl();
    if (result.success && result.dataUrl) {
      setLibraries(prev => prev.map(lib => 
        lib.id === id ? { ...lib, newCoverDataUrl: result.dataUrl } : lib
      ));
      alert("片库封面预览已更新。注意：片库封面持久化保存功能暂未完全实现，此更改可能不会在下次启动时保留。");
    } else if (result.error) {
      alert(`选择封面失败: ${result.error}`);
    }
  };

  const handleSaveChanges = async (id: string) => {
    const libToSave = libraries.find(l => l.id === id);
    if (!libToSave || !libToSave.newName?.trim() || !libToSave.newPaths || libToSave.newPaths.length === 0) {
      alert('片库名称和路径不能为空。');
      return;
    }
    setIsLoading(true);
    const params: ManageMovieLibraryParams = {
      id,
      name: libToSave.newName.trim(),
      paths: libToSave.newPaths,
    };
    
    const result = await window.sfeElectronAPI.manageMovieLibrary(params);
    setIsLoading(false);
    if (result.success) {
      fetchLibraries(); 
    } else {
      alert(`更新片库失败: ${result.error}`);
    }
  };

  const handleDeleteLibrary = async (id: string, name: string) => {
    if (window.confirm(`确定要删除片库 "${name}" 吗？此操作也会删除片库与影片的关联，但不会删除影片文件本身。`)) {
      setIsLoading(true);
      const result = await window.sfeElectronAPI.manageMovieLibrary({ id, delete: true });
      setIsLoading(false);
      if (result.success) {
        fetchLibraries(); 
      } else {
        alert(`删除片库失败: ${result.error}`);
      }
    }
  };
  

  if (isLoading && libraries.length === 0) return <p className="text-center text-neutral-400 p-4">正在加载片库列表...</p>;
  if (error) return <p className="text-center text-red-400 p-4 bg-red-900/30 rounded-md">{error}</p>;

  return (
    <div className="settings-group-content space-y-4">
      <h3 className="text-lg font-semibold text-amber-400">管理我的片库</h3>
      {libraries.length === 0 && !isLoading && <p className="text-neutral-400">您还没有创建任何片库。</p>}
      
      <div className="space-y-3 max-h-[calc(80vh-200px)] overflow-y-auto settings-scroll-container pr-2">
        {libraries.map(lib => (
          <div key={lib.id} className="bg-[#2d2d2d] p-3 rounded-md border border-[#4f4f4f]">
            {!lib.isEditing ? (
              <>
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-md font-semibold text-neutral-100">{lib.name}</h4>
                    <p className="text-xs text-neutral-400">包含 {lib.paths.length} 个扫描路径, {lib.movieCount || 0} 部影片</p>
                  </div>
                  <div className="flex space-x-2 flex-shrink-0">
                    <button onClick={() => handleEditToggle(lib.id)} className="icon-button-app text-sky-400 hover:text-sky-300" title="编辑"><LuPencil size={16}/></button>
                    <button onClick={() => handleDeleteLibrary(lib.id, lib.name)} className="icon-button-app text-red-400 hover:text-red-300" title="删除"><LuTrash2 size={16}/></button>
                  </div>
                </div>
                {/* Display current cover */}
                {lib.coverMovies?.[0]?.coverDataUrl && 
                    <img src={lib.coverMovies[0].coverDataUrl} alt={`${lib.name} cover`} className="mt-2 h-20 w-auto rounded object-contain border border-neutral-600"/>
                }
              </>
            ) : (
              <div className="space-y-3">
                <div>
                  <label htmlFor={`name-${lib.id}`} className="settings-label text-xs">片库名称</label>
                  <input type="text" id={`name-${lib.id}`} value={lib.newName || ''} onChange={e => handleInputChange(lib.id, 'newName', e.target.value)} className="form-input-app text-sm"/>
                </div>
                <div>
                  <label className="settings-label text-xs">扫描路径</label>
                  <div className="space-y-1 max-h-24 overflow-y-auto settings-scroll-container pr-1 mb-1">
                    {(lib.newPaths || []).map(p => (
                      <div key={p} className="flex items-center justify-between bg-[#272727] p-1 rounded text-xs">
                        <span className="truncate" title={p}>{p}</span>
                        <button onClick={() => handleRemovePath(lib.id, p)} className="text-red-400 hover:text-red-300 p-0.5"><LuX size={14}/></button>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center gap-1">
                    <input type="text" value={lib.newPathInput || ''} onChange={e => handleInputChange(lib.id, 'newPathInput', e.target.value)} placeholder="添加或浏览路径..." className="form-input-app text-xs flex-grow"/>
                    <button onClick={() => handleAddPath(lib.id)} className="button-secondary-app !px-2 !py-1 text-xs"><LuFolderPlus size={14}/></button>
                  </div>
                </div>
                <div>
                  <label className="settings-label text-xs">片库封面 (可选)</label>
                  <div className="flex items-center gap-2">
                    {(lib.newCoverDataUrl || lib.coverMovies?.[0]?.coverDataUrl) && 
                        <img 
                            src={lib.newCoverDataUrl || lib.coverMovies![0].coverDataUrl!} 
                            alt="封面预览" 
                            className="h-16 w-auto rounded object-contain border border-neutral-500"
                        />
                    }
                    <button onClick={() => handleCoverChange(lib.id)} className="button-secondary-app !px-2 !py-1 text-xs flex items-center">
                        <LuImagePlus size={14} className="mr-1"/>选择新封面
                    </button>
                  </div>
                  <p className="settings-description text-xs mt-1">注意：片库封面的持久化保存功能仍在开发中，当前更改可能不会在下次启动时保留。</p>
                </div>
                <div className="flex justify-end space-x-2 mt-2">
                  <button onClick={() => handleEditToggle(lib.id)} className="button-neutral-app text-sm">取消</button>
                  <button onClick={() => handleSaveChanges(lib.id)} disabled={isLoading} className="button-primary-app text-sm"><LuSave size={16} className="mr-1"/>保存更改</button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default LibraryManagementTab;