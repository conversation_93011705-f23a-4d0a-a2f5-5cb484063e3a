// soul-forge-electron/main_process/services/snapshotGeneratorService.js
// 快照生成服务 - 使用JavaScript/Node.js实现，不再依赖Python
const { spawn } = require('node:child_process');
const path = require('node:path');
const fs = require('node:fs');

let log;

function initializeSnapshotGenerator(logger) {
  log = logger;
  log.info('[快照生成服务] 初始化 - 使用JavaScript/Node.js实现，不再依赖Python。');
}

/**
 * 获取视频时长（秒）
 * @param {string} videoPath 视频文件路径
 * @param {string} ffmpegPath FFmpeg可执行文件路径
 * @returns {Promise<number>} 视频时长（秒）
 */
function getVideoDuration(videoPath, ffmpegPath = 'ffmpeg') {
  return new Promise((resolve, reject) => {
    // 智能生成 ffprobe 路径
    let ffprobeCommand;
    if (ffmpegPath.includes('ffmpeg.exe')) {
      // 如果是完整路径，替换文件名
      ffprobeCommand = ffmpegPath.replace('ffmpeg.exe', 'ffprobe.exe');
    } else if (ffmpegPath.includes('ffmpeg')) {
      // 如果包含 ffmpeg 但不是 .exe，进行简单替换
      ffprobeCommand = ffmpegPath.replace('ffmpeg', 'ffprobe');
    } else {
      // 默认情况
      ffprobeCommand = 'ffprobe';
    }
    const args = [
      '-v', 'quiet',
      '-print_format', 'json',
      '-show_format',
      videoPath
    ];

    log.info(`[快照生成服务] 获取视频时长: "${ffprobeCommand}" ${args.join(' ')}`);

    const process = spawn(ffprobeCommand, args);
    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0) {
        try {
          const info = JSON.parse(stdout);
          const duration = parseFloat(info.format.duration);
          log.info(`[快照生成服务] 视频时长: ${duration}秒`);
          resolve(duration);
        } catch (error) {
          log.error(`[快照生成服务] 解析视频信息失败: ${error.message}`);
          reject(new Error(`解析视频信息失败: ${error.message}`));
        }
      } else {
        log.error(`[快照生成服务] FFprobe执行失败，退出码: ${code}, 错误: ${stderr}`);
        reject(new Error(`FFprobe执行失败: ${stderr || `退出码 ${code}`}`));
      }
    });

    process.on('error', (error) => {
      log.error(`[快照生成服务] FFprobe启动失败: ${error.message}`);
      reject(new Error(`FFprobe启动失败: ${error.message}`));
    });
  });
}

/**
 * 获取快照质量对应的缩放参数
 * @param {string} quality 质量设置
 * @returns {string} FFmpeg缩放参数
 */
function getScaleFilter(quality) {
  const qualityMap = {
    'hd_640p': 'scale=640:-1',
    'hd_720p': 'scale=1280:-1',
    'hd_1080p': 'scale=1920:-1',
    'original': null
  };
  return qualityMap[quality] || qualityMap['hd_640p'];
}

/**
 * 生成单个快照
 * @param {string} videoPath 视频文件路径
 * @param {number} timestamp 时间戳（秒）
 * @param {string} outputPath 输出文件路径
 * @param {string} ffmpegPath FFmpeg可执行文件路径
 * @param {string} scaleFilter 缩放过滤器
 * @returns {Promise<boolean>} 是否成功
 */
function generateSingleSnapshot(videoPath, timestamp, outputPath, ffmpegPath = 'ffmpeg', scaleFilter = null) {
  return new Promise((resolve, reject) => {
    const args = [
      '-ss', timestamp.toString(),
      '-i', videoPath,
      '-vframes', '1',
      '-q:v', '3',
      '-y'
    ];

    if (scaleFilter) {
      args.splice(-1, 0, '-vf', scaleFilter);
    }

    args.push(outputPath);

    log.info(`[快照生成服务] 生成快照: "${ffmpegPath}" ${args.join(' ')}`);

    const process = spawn(ffmpegPath, args);
    let stderr = '';

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    process.on('close', (code) => {
      if (code === 0 && fs.existsSync(outputPath)) {
        log.info(`[快照生成服务] 快照生成成功: ${outputPath}`);
        resolve(true);
      } else {
        log.error(`[快照生成服务] 快照生成失败，退出码: ${code}, 错误: ${stderr}`);
        reject(new Error(`快照生成失败: ${stderr || `退出码 ${code}`}`));
      }
    });

    process.on('error', (error) => {
      log.error(`[快照生成服务] FFmpeg启动失败: ${error.message}`);
      reject(new Error(`FFmpeg启动失败: ${error.message}`));
    });
  });
}

/**
 * 生成视频快照
 * @param {string} videoPath 视频文件路径
 * @param {number} videoDbId 视频数据库ID
 * @param {string} quality 快照质量
 * @param {string} ffmpegPath FFmpeg路径
 * @param {string} cacheBasePath 缓存基础路径
 * @param {Function} imagePathToDataUrlFunc 图片转DataURL函数
 * @returns {Promise<Object>} 生成结果
 */
async function generateSnapshots(videoPath, videoDbId, quality = 'hd_640p', ffmpegPath = 'ffmpeg', cacheBasePath, imagePathToDataUrlFunc) {
  log.info(`[快照生成服务] 开始为视频生成快照: ${videoPath} (DB ID: ${videoDbId})`);
  
  try {
    // 检查视频文件是否存在
    if (!fs.existsSync(videoPath)) {
      throw new Error(`视频文件不存在: ${videoPath}`);
    }

    // 创建输出目录
    const outputDir = path.join(cacheBasePath, String(videoDbId));
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 获取视频时长
    const duration = await getVideoDuration(videoPath, ffmpegPath);
    
    // 计算快照数量和间隔
    const numSnapshots = 12; // 生成12个快照
    const interval = duration / (numSnapshots + 1); // 避免开头和结尾
    const scaleFilter = getScaleFilter(quality);

    log.info(`[快照生成服务] 视频时长: ${duration}秒, 生成${numSnapshots}个快照, 间隔: ${interval}秒`);

    // 生成快照
    const snapshotPaths = [];
    const promises = [];

    for (let i = 0; i < numSnapshots; i++) {
      const timestamp = interval * (i + 1);
      const outputFilename = `snapshot_${String(i + 1).padStart(3, '0')}.jpg`;
      const outputPath = path.join(outputDir, outputFilename);
      
      promises.push(
        generateSingleSnapshot(videoPath, timestamp, outputPath, ffmpegPath, scaleFilter)
          .then(() => {
            snapshotPaths.push(outputPath);
            return outputPath;
          })
          .catch((error) => {
            log.error(`[快照生成服务] 生成第${i + 1}个快照失败: ${error.message}`);
            return null;
          })
      );
    }

    // 等待所有快照生成完成
    const results = await Promise.allSettled(promises);
    const successfulSnapshots = results
      .filter(result => result.status === 'fulfilled' && result.value)
      .map(result => result.value);

    log.info(`[快照生成服务] 快照生成完成，成功: ${successfulSnapshots.length}/${numSnapshots}`);

    // 转换为DataURL
    const thumbnailDataUrls = successfulSnapshots.map(filePath => {
      try {
        return imagePathToDataUrlFunc(filePath);
      } catch (error) {
        log.error(`[快照生成服务] 转换图片为DataURL失败: ${filePath}, 错误: ${error.message}`);
        return null;
      }
    }).filter(Boolean);

    return {
      success: true,
      thumbnailDataUrls,
      snapshotPaths: successfulSnapshots,
      message: `成功生成 ${successfulSnapshots.length} 个快照`
    };

  } catch (error) {
    log.error(`[快照生成服务] 快照生成失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      thumbnailDataUrls: [],
      snapshotPaths: []
    };
  }
}

module.exports = {
  initializeSnapshotGenerator,
  generateSnapshots,
  getVideoDuration,
  generateSingleSnapshot
};
