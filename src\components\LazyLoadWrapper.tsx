import React, { useState, useEffect, useRef, ReactNode } from 'react';

interface LazyLoadWrapperProps {
  children: ReactNode;
  placeholder: ReactNode;
  rootMargin?: string;
  threshold?: number | number[];
  className?: string; // Optional className for the wrapper div
  triggerOnce?: boolean; // Whether to unobserve after first intersection
}

const LazyLoadWrapper: React.FC<LazyLoadWrapperProps> = ({
  children,
  placeholder,
  rootMargin = '200px', // Load when 200px away from viewport
  threshold = 0.01,
  className = '',
  triggerOnce = true,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const targetRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (triggerOnce && targetRef.current) {
            observer.unobserve(targetRef.current);
          }
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    const currentRef = targetRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef && observer) {
        observer.unobserve(currentRef);
      }
    };
  }, [rootMargin, threshold, triggerOnce]);

  return (
    <div ref={targetRef} className={className}>
      {isVisible ? children : placeholder}
    </div>
  );
};

export default LazyLoadWrapper;