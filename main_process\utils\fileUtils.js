// soul-forge-electron/main_process/utils/fileUtils.js
const fs = require('node:fs');
const path = require('node:path');
const http = require('node:http');
const https = require('node:https');

let log;

function initializeFileUtils(logger) {
  log = logger;
  log.info('[文件工具] 初始化。');
}

function imagePathToDataUrl(imagePath) {
  if (!imagePath || !fs.existsSync(imagePath)) {
    // log.warn(`[文件工具] 尝试将不存在的图片转换为DataURL: ${imagePath}`);
    return null;
  }
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    const extension = path.extname(imagePath).substring(1).toLowerCase();
    let mimeType;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        mimeType = 'image/jpeg';
        break;
      case 'png':
        mimeType = 'image/png';
        break;
      case 'gif':
        mimeType = 'image/gif';
        break;
      case 'webp':
        mimeType = 'image/webp';
        break;
      default:
        log.warn(`[文件工具] 不支持的图片扩展名 "${extension}" 用于路径: ${imagePath}`);
        return null;
    }
    return `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
  } catch (error) {
    log.error(`[文件工具] 将图片 ${imagePath} 转换为 DataURL 时出错:`, error);
    return null;
  }
}

function fetchNetworkCoverAsDataUrl(imageUrl) {
  return new Promise((resolve) => {
    if (!imageUrl || (!imageUrl.startsWith('http:') && !imageUrl.startsWith('https:'))) {
      log.warn(`[文件工具] 无效的网络图片 URL: ${imageUrl}`);
      return resolve(null);
    }
    const client = imageUrl.startsWith('https:') ? https : http;
    client.get(imageUrl, { timeout: 10000 }, (response) => {
      if (response.statusCode !== 200) {
        log.warn(`[文件工具] 下载网络图片 ${imageUrl} 失败，状态码: ${response.statusCode}`);
        response.resume(); // Consume response data to free up memory
        return resolve(null);
      }
      const chunks = [];
      response.on('data', (chunk) => chunks.push(chunk));
      response.on('error', (err) => {
        log.error(`[文件工具] 下载网络图片 ${imageUrl} 时发生错误:`, err);
        resolve(null);
      });
      response.on('end', () => {
        try {
          const imageBuffer = Buffer.concat(chunks);
          const mimeType = response.headers['content-type'] || 'image/jpeg'; // Default if not provided
          if (!mimeType.startsWith('image/')) {
            log.warn(`[文件工具] 下载的内容 ${imageUrl} 不是有效的图片类型: ${mimeType}`);
            return resolve(null);
          }
          resolve(`data:${mimeType};base64,${imageBuffer.toString('base64')}`);
        } catch (bufferError) {
          log.error(`[文件工具] 处理下载的图片数据时 ${imageUrl} 出错:`, bufferError);
          resolve(null);
        }
      });
    }).on('error', (err) => {
      log.error(`[文件工具] 网络请求 ${imageUrl} 失败:`, err);
      resolve(null);
    }).on('timeout', () => {
        log.warn(`[文件工具] 下载网络图片 ${imageUrl} 超时。`);
        resolve(null);
    });
  });
}

function downloadImage(url, destinationPath, maxRedirects = 5) {
  return new Promise((resolve, reject) => {
    if (maxRedirects < 0) {
      return reject(new Error(`Too many redirects for ${url}`));
    }

    const client = url.startsWith('https:') ? https : http;
    let fileStream; // Declare here to be accessible in error handlers for cleanup

    const request = client.get(url, { timeout: 20000 }, (response) => {
      if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
        log.info(`[文件工具] downloadImage: Redirecting to ${response.headers.location} for ${url}`);
        response.resume(); // consume data
        // Clean up temp file if created before redirect
        if (fileStream) fileStream.close(() => fs.unlink(destinationPath, () => {}));
        return downloadImage(response.headers.location, destinationPath, maxRedirects - 1).then(resolve).catch(reject);
      }

      if (response.statusCode !== 200) {
        response.resume(); 
        if (fileStream) fileStream.close(() => fs.unlink(destinationPath, () => {}));
        log.error(`[文件工具] 下载图片 ${url} 失败, 状态码: ${response.statusCode}`);
        return reject(new Error(`下载失败, 状态码: ${response.statusCode}`));
      }
      
      // Create writestream only after confirming 200 OK and no redirect
      fileStream = fs.createWriteStream(destinationPath);
      response.pipe(fileStream);

      fileStream.on('finish', () => {
        fileStream.close(() => resolve(destinationPath));
      });
      fileStream.on('error', (err) => { 
          fs.unlink(destinationPath, () => {});
          log.error(`[文件工具] 写入图片到 ${destinationPath} 失败:`, err);
          reject(err);
      });
    });

    request.on('error', (err) => {
      if (fileStream) fileStream.close(() => fs.unlink(destinationPath, () => {}));
      log.error(`[文件工具] 下载图片 ${url} 时发生网络错误:`, err);
      reject(err);
    });
    
    request.on('timeout', () => {
        request.destroy(); 
        if (fileStream) fileStream.close(() => fs.unlink(destinationPath, () => {}));
        log.error(`[文件工具] 下载图片 ${url} 超时.`);
        reject(new Error('下载图片超时'));
    });
  });
}


module.exports = {
  initializeFileUtils,
  imagePathToDataUrl,
  fetchNetworkCoverAsDataUrl,
  downloadImage,
};
