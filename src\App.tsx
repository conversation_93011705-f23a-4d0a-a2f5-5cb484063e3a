
/// <reference path="./vite-env.d.ts" />
import React, { useEffect, useCallback, useMemo, useRef, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { 
    Movie, AppSettings, SortableMovieField, AdvancedFilterOptions, 
    DetailFilterType, FavoriteItemType, SnapshotInfo, ViewMode, MovieLibrary,
    ScanCompleteData, ScanLibraryResult, PlayStrmUrlPayload, ToggleFavoriteResult 
} from './types';

// Hooks
import { useAppSettings } from './hooks/useAppSettings';
import { useMovieDataManager } from './hooks/useMovieDataManager';
import { useModalManager } from './hooks/useModalManager';
import { usePrivacyManager } from './hooks/usePrivacyManager';
import { useScanStatus } from './hooks/useScanStatus';
import { useActorProfileManager } from './hooks/useActorProfileManager';

import { useAppView, AppView } from './hooks/useAppView'; 
import { useRepresentativeMovieForGrouping } from './hooks/useRepresentativeMovie';
import { useMovieLibraryManager } from './hooks/useMovieLibraryManager'; 

// Modals & Components
import { MovieDetailModal } from './components/MovieDetailModal';
import CDSelectionView from './components/CDSelectionView';
import { SettingsPage, TabKey as SettingsTabKey } from './components/SettingsPage'; // Import TabKey
import LinLuoSummoningModal from './components/LinLuoSummoningModal';
import AdvancedFilterModal from './components/AdvancedFilterModal';
import ImagePreviewModal from './components/ImagePreviewModal';
import FavoritesView from './components/FavoritesView';
import CollectorPage from './components/CollectorPage';
import IngestCenterPage from './components/IngestCenterPage';
import ActorProfileModal from './components/modals/ActorProfileModal';
import { StagingAreaPage } from './components/StagingAreaPage';
import { RecycleBinPage } from './components/RecycleBinPage';
import IntelligenceCenterPage from './components/IntelligenceCenterPage';
import NfoPlotPolisherTool from './components/tools/NfoPlotPolisherTool';
import ScraperTestTool from './components/tools/ScraperTestTool';

// Pages
import DashboardPage from './pages/DashboardPage';
import LibraryPage from './pages/LibraryPage';
import LibraryCreationModal from './components/modals/LibraryCreationModal';
import LibraryCard from './components/LibraryCard';
import EmbeddedMediaPlayerModal from './components/EmbeddedMediaPlayerModal'; // For STRM
import MovieCard from './components/MovieCard'; // For direct rendering

// Layout Components
import AppHeader from './components/layout/AppHeader';
import SmartLibrarySidebar from './components/layout/SmartLibrarySidebar';
import LibraryToolbar from './components/layout/LibraryToolbar';
import MovieGridDisplay from './components/layout/MovieGridDisplay';
import ProgressBar from './components/common/ProgressBar';
import Pagination from './components/common/Pagination';
import PrivacyUnlockModal from './components/modals/PrivacyUnlockModal';
import RecommendationsModal from './components/modals/RecommendationsModal';


// Icons for ViewModeBar and Favorite Button
import GridViewIcon from './components/icons/GridViewIcon';
import ListViewIcon from './components/icons/ListViewIcon';
import DetailedListViewIcon from './components/icons/DetailedListViewIcon';
import WaterfallViewIcon from './components/icons/WaterfallViewIcon';
import HeartIcon from './components/icons/HeartIcon'; // For favorite button
import { LuTriangle, LuScanLine, LuLayoutGrid, LuListChecks, LuLibrary, LuPlus } from 'react-icons/lu'; 


const AVAILABLE_VIEW_MODES: ViewMode[] = ['card', 'compactList', 'detailedList', 'waterfall'];

const favoriteTypeLabels: Record<DetailFilterType | FavoriteItemType, string> = {
  actor: '演员', 
  studio: '制作商',
  series: '系列',
  director: '导演',
  nfoId: '影片ID', 
  tag: '标签',
  genre: '类型',
  library: '片库',
  actress: '演员', 
  movie_nfo_id: '影片ID',
  year: '年份',
  resolution: '分辨率',
  videoHeight: '视频高度',
};

const sortableFieldsList: { value: SortableMovieField; label: string }[] = [
    { value: 'releaseDate', label: '发行日期' }, { value: 'year', label: '年份' },
    { value: 'personalRating', label: '个人评分'}, { value: 'title', label: '标题' },
    { value: 'nfoId', label: '番号/ID' }, { value: 'fileName', label: '文件名' },
    { value: 'runtime', label: '时长'}, { value: 'studio', label: '制作商'},
    { value: 'series', label: '系列'}, { value: 'db_id', label: '添加顺序 (DB ID)'},
    { value: 'lastScanned', label: '扫描日期'}, { value: 'nfoLastModified', label: 'NFO修改日期'},
    { value: 'fileSize', label: '文件大小'}, { value: 'fps', label: '帧率 (FPS)'}, 
    { value: 'videoBitrate', label: '视频码率'}, { value: 'versionCount', label: '版本数量'},
    { value: 'multiCdCountForNfoId', label: 'CD分集数'},
    { value: 'aiRecommendationScore', label: 'AI推荐分' }
];


const App: React.FC = () => {
  const { appSettings, isLoadingSettings, saveSettings, setAppSettingsDirectly } = useAppSettings();
  
  const { 
    privacyState, isPrivacyUnlockModalOpen, isPrivacyUnlockedThisSession, 
    attemptUnlockPrivacy, setIsPrivacyUnlockModalOpen, handleTogglePrivacyLockIcon 
  } = usePrivacyManager();

  const { currentAppView, setCurrentAppView, activeFilterTag, navigateToMainWallWithFilter, clearActiveFilterTag, activeLibrary, setActiveLibrary, navigateToMainWall: navigateToMainWallFromHook } = useAppView();
  
  const {
    movieLibraries, isLoadingLibraries,
    isLibraryCreationModalOpen, setIsLibraryCreationModalOpen,
    manageMovieLibrary, refreshLibraries,
    triggerLibraryScan, 
  } = useMovieLibraryManager(appSettings, saveSettings, activeLibrary, setActiveLibrary); 

  const {
    movies, isLoading: isLoadingMovies, error: movieError, currentPage, totalPages,
    searchTerm, advancedFilters, sortField, sortOrder, pageSize,
    fetchMovies, updateSearchTerm, applyAdvancedFilters, updateSort, updatePageSize, goToPage,
    updateMovieInList, setMovies, setCurrentPage, resetFiltersAndFetch, 
    setSearchTerm, setAdvancedFilters, 
  } = useMovieDataManager({ 
    initialPageSize: appSettings.defaultPageSize || 50,
    initialSortField: appSettings.defaultSortField || 'releaseDate',
    initialSortOrder: appSettings.defaultSortOrder || 'desc',
    privacyState,
    isPrivacyUnlockedThisSession,
    appSettings,
    activeLibraryId: activeLibrary?.id, 
  });
  
  const { scanProgress, isScanning, triggerScan: triggerGlobalScan } = useScanStatus({ 
    onScanCompleteCallback: (data: ScanCompleteData | ScanLibraryResult) => { 
      const libraryId = data.libraryId; 
      if (libraryId && activeLibrary && libraryId === activeLibrary.id) { 
        fetchMovies(1, searchTerm, advancedFilters, sortField, sortOrder, pageSize, libraryId); 
      } else if (currentAppView === 'mainWall' && !activeLibrary) { 
        fetchRecentMovies(); 
      }
      refreshLibraries(); 
    } 
  });
  
  const {
    selectedMovie, currentNfoIdForGrouping,
    isDetailModalOpen, isCDModalOpen,
    isSettingsModalOpen, setIsSettingsModalOpen,
    isLinLuoModalOpen, setIsLinLuoModalOpen,
    isFilterModalOpen, setIsFilterModalOpen,
    isImagePreviewModalOpen, imagePreviewUrl,
    isRecommendationsModalOpen, setIsRecommendationsModalOpen,
    isNfoPlotPolisherModalOpen, setIsNfoPlotPolisherModalOpen,
    isScraperTestModalOpen, setIsScraperTestModalOpen,
    openDetailModal, openCDModal, openImagePreviewModal, closeModal,
  } = useModalManager();

  // 演员档案管理器
  const {
    isActorProfileModalOpen,
    currentActorName,
    actorProfileData,
    actorFilmography,
    completeFilmography,
    isLoadingActorProfile,
    isLoadingCompleteFilmography,
    error: actorProfileError,
    openActorProfileModal,
    closeActorProfileModal
  } = useActorProfileManager();


  
  const [viewMode, setViewModeState] = useState<ViewMode>('card'); 
  
  const [recommendedMovies, setRecommendedMovies] = useState<Movie[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const [linLuoFormattedRecommendation, setLinLuoFormattedRecommendation] = useState<string | null>(null);
  const [isFormattingRecommendation, setIsFormattingRecommendation] = useState(false);
  const [initialLinLuoMessage, setInitialLinLuoMessage] = useState<string | null>(null);
  
  const [availableGenres, setAvailableGenres] = useState<string[]>(['示例类型1', '示例类型2']); 
  const [availableTags, setAvailableTags] = useState<string[]>(['示例标签1', '示例标签2']); 

  const [recentPlayedMovies, setRecentPlayedMovies] = useState<Movie[]>([]);
  const [recentAddedMovies, setRecentAddedMovies] = useState<Movie[]>([]);
  const [isLoadingRecent, setIsLoadingRecent] = useState(false);

  const [strmPlayerState, setStrmPlayerState] = useState<{isOpen: boolean; url: string | null; title?: string}>({ isOpen: false, url: null });

  const [settingsInitialTab, setSettingsInitialTab] = useState<SettingsTabKey>('general');
  const [isCurrentFilterFavorite, setIsCurrentFilterFavorite] = useState(false);

  // 智能片库相关状态
  const [activeSmartLibraryId, setActiveSmartLibraryId] = useState<string | null>('all-movies');


  const representativeMovieForGrouping = useRepresentativeMovieForGrouping(currentNfoIdForGrouping, movies, selectedMovie);

  useEffect(() => {
    if (!isLoadingSettings) {
      setViewModeState(appSettings.defaultViewMode || 'card');
    }
  }, [isLoadingSettings, appSettings.defaultViewMode]);

  // 全局演员档案事件监听器
  useEffect(() => {
    const handleOpenActorProfile = (event: CustomEvent) => {
      const { actorName } = event.detail;
      if (actorName) {
        openActorProfileModal(actorName);
      }
    };

    document.addEventListener('openActorProfile', handleOpenActorProfile as EventListener);

    return () => {
      document.removeEventListener('openActorProfile', handleOpenActorProfile as EventListener);
    };
  }, [openActorProfileModal]);

  const navigateToMainWall = useCallback(async () => {
    navigateToMainWallFromHook(); 
    if (appSettings.activeLibraryId !== null) {
      await saveSettings({ activeLibraryId: null }); 
    }
  }, [navigateToMainWallFromHook, appSettings.activeLibraryId, saveSettings]); 

  useEffect(() => {
    if (!isLoadingSettings) { 
      navigateToMainWall();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoadingSettings]); 


  const fetchRecentMovies = useCallback(async (customSettings?: AppSettings) => {
    setIsLoadingRecent(true);
    try {
      // 使用传入的设置或当前设置中的数量，如果没有设置则使用默认值
      const settingsToUse = customSettings || appSettings;
      const homePageSettings = settingsToUse.homePageSettings || {
        showRecentAdded: true,
        showRecentPlayed: true,
        recentAddedCount: 12,
        recentPlayedCount: 8
      };

      const playedResult = await window.sfeElectronAPI.getRecentMovies('recent_played', homePageSettings.recentPlayedCount || 8);
      if (playedResult.success) setRecentPlayedMovies(playedResult.movies);

      const addedResult = await window.sfeElectronAPI.getRecentMovies('recent_added', homePageSettings.recentAddedCount || 12);
      if (addedResult.success) setRecentAddedMovies(addedResult.movies);
    } catch (e) {
      console.error("Error fetching recent movies:", e);
    } finally {
      setIsLoadingRecent(false);
    }
  }, [appSettings]);

  useEffect(() => {
    if (currentAppView === 'mainWall' && !activeLibrary && !isLoadingSettings) {
      fetchRecentMovies();
    }
  }, [currentAppView, activeLibrary, fetchRecentMovies, isLoadingSettings]);

  // STRM Playback Listener
  useEffect(() => {
    const removeListener = window.sfeElectronAPI.onPlayStrmUrl((payload: PlayStrmUrlPayload) => {
      setStrmPlayerState({ isOpen: true, url: payload.url, title: payload.title });
    });
    return removeListener;
  }, []);

  // NFO 修复完成监听器
  useEffect(() => {
    const handleNfoFixCompleted = (event: any, data: any) => {
      if (data.success && data.updated > 0) {
        console.log(`🎉 NFO ID 自动修复完成: 成功修复 ${data.updated} 部电影`);
        console.log('多版本合并功能现在应该正常工作了！');

        // 刷新电影数据
        if (currentAppView === 'mainWall' && !activeLibrary) {
          fetchRecentMovies();
        } else if (activeLibrary) {
          fetchMovies();
        }

        // 显示成功消息
        alert(`🎉 自动修复完成！\n\n成功修复了 ${data.updated} 部电影的 NFO ID。\n现在相同影片的多个版本会合并显示，并显示版本徽章。\n\n页面数据已自动刷新。`);
      }
    };

    // 添加监听器
    if (window.sfeElectronAPI && window.sfeElectronAPI.ipcRenderer) {
      window.sfeElectronAPI.ipcRenderer.on('nfo-fix-completed', handleNfoFixCompleted);

      return () => {
        window.sfeElectronAPI.ipcRenderer.removeListener('nfo-fix-completed', handleNfoFixCompleted);
      };
    }
  }, [currentAppView, activeLibrary, fetchRecentMovies, fetchMovies]);

  // Fetch favorite status for active filter tag
  useEffect(() => {
    if (activeFilterTag) {
      const fetchFavStatus = async () => {
        try {
          let typeForFavoriteCheck = activeFilterTag.type as FavoriteItemType;
          // Mapping DetailFilterType to FavoriteItemType if necessary
          if (activeFilterTag.type === 'actor') typeForFavoriteCheck = 'actress';
          // Add other mappings if needed, e.g. movie -> movie_nfo_id

          const result = await window.sfeElectronAPI.isFavorite(typeForFavoriteCheck, activeFilterTag.value);
          if (result.success) {
            setIsCurrentFilterFavorite(result.isFavorite);
          }
        } catch (e) {
          console.error("Error fetching favorite status for active filter:", e);
        }
      };
      fetchFavStatus();
    } else {
      setIsCurrentFilterFavorite(false);
    }
  }, [activeFilterTag]);

  const handleToggleCurrentFilterFavorite = async () => {
    if (!activeFilterTag) return;
    try {
      let typeForFavoriteToggle = activeFilterTag.type as FavoriteItemType;
      if (activeFilterTag.type === 'actor') typeForFavoriteToggle = 'actress';
      // Add other mappings if needed

      const result: ToggleFavoriteResult = await window.sfeElectronAPI.toggleFavorite(typeForFavoriteToggle, activeFilterTag.value);
      if (result.success) {
        setIsCurrentFilterFavorite(result.isFavorite);
      } else {
        alert(`操作收藏失败: ${result.error}`);
      }
    } catch (e: any) {
      alert(`操作收藏时发生前端错误: ${e.message}`);
    }
  };


  const handleCardClick = useCallback((movie: Movie, isMultiVersion: boolean, isMultiCD: boolean) => {
    if (isMultiCD && movie.nfoId) {
        openCDModal(movie);
    } else {
        // 新的、统一的逻辑
        openDetailModal(movie);
    }
  }, [openDetailModal, openCDModal]);

  const handleCloseDetailModal = () => {
    closeModal('detail');
  };

  const handleTagClickInDetail = (value: string, type: DetailFilterType) => {
    closeModal('detail');
    
    let newFilters: AdvancedFilterOptions = {};
    switch (type) {
        case 'actor': newFilters.selectedActors = [value]; break;
        case 'genre': newFilters.selectedGenres = [value]; break;
        case 'tag': newFilters.selectedTags = [value]; break;
        case 'studio': newFilters.selectedStudio = value; break;
        case 'series': newFilters.selectedSeries = value; break;
        case 'director': newFilters.director = value; break;
        case 'year': if (!isNaN(parseInt(value,10))) newFilters.year = parseInt(value,10); break;
        case 'resolution':
        case 'videoHeight': newFilters.resolution = [value]; break;
        case 'nfoId': 
            setSearchTerm(value);
            setAdvancedFilters(null);
            navigateToMainWallWithFilter(type, value); 
            return; 
        default: 
            setSearchTerm('');
            setAdvancedFilters({}); 
            navigateToMainWallWithFilter(type, value);
            return;
    }
    
    setSearchTerm(''); 
    setAdvancedFilters(newFilters); 
    navigateToMainWallWithFilter(type, value); 
  };
  
  const handleFetchRecommendations = async () => {
    setIsLoadingRecommendations(true);
    setLinLuoFormattedRecommendation(null);
    const params = { limitPerCategory: 5, maxTotal: 20 };
    const result = await window.sfeElectronAPI.getInitialRecommendations(params);
    if (result.success && result.recommendedMovies) {
      setRecommendedMovies(result.recommendedMovies);
    } else {
      setRecommendedMovies([]);
      alert(`获取推荐失败: ${result.error}`);
    }
    setIsLoadingRecommendations(false);
  };

  const handleFormatRecommendationForLinLuo = async () => {
    if (recommendedMovies.length === 0) return;
    setIsFormattingRecommendation(true);
    const result = await window.sfeElectronAPI.formatRecommendationsAsAiMessage({movies: recommendedMovies});
    if(result.success && result.formattedMessage){
        setLinLuoFormattedRecommendation(result.formattedMessage);
    } else {
        alert(`格式化推荐语失败: ${result.error}`);
        setLinLuoFormattedRecommendation(null);
    }
    setIsFormattingRecommendation(false);
  };

  const onFavoriteTagClickInFavoritesView = (itemType: FavoriteItemType, itemValue: string) => {
    let newFilters: AdvancedFilterOptions = {};
    let filterTypeForDisplay: DetailFilterType;

    switch (itemType) {
        case 'actress': filterTypeForDisplay = 'actor'; newFilters.selectedActors = [itemValue]; break;
        case 'genre': filterTypeForDisplay = 'genre'; newFilters.selectedGenres = [itemValue]; break;
        case 'tag': filterTypeForDisplay = 'tag'; newFilters.selectedTags = [itemValue]; break;
        case 'studio': filterTypeForDisplay = 'studio'; newFilters.selectedStudio = itemValue; break;
        case 'series': filterTypeForDisplay = 'series'; newFilters.selectedSeries = itemValue; break;
        case 'director': filterTypeForDisplay = 'director'; newFilters.director = itemValue; break;
        case 'movie_nfo_id':
            setSearchTerm(itemValue);
            setAdvancedFilters(null);
            navigateToMainWallWithFilter('nfoId', itemValue);
            return;
        default:
            console.warn(`Unhandled FavoriteItemType in onFavoriteTagClickInFavoritesView: ${itemType}`);
            return;
    }
    setSearchTerm('');
    setAdvancedFilters(newFilters);
    navigateToMainWallWithFilter(filterTypeForDisplay, itemValue);
};

  const handleSettingsSaved = (newSettings: AppSettings) => {
    setAppSettingsDirectly(newSettings); 
    if (newSettings.defaultViewMode && newSettings.defaultViewMode !== viewMode) {
      setViewModeState(newSettings.defaultViewMode);
    }
    if ((newSettings.defaultSortField && newSettings.defaultSortField !== sortField) || 
        (newSettings.defaultSortOrder && newSettings.defaultSortOrder !== sortOrder)) {
      updateSort(newSettings.defaultSortField || sortField, newSettings.defaultSortOrder || sortOrder);
    }
    if (newSettings.defaultPageSize && newSettings.defaultPageSize !== pageSize) {
      updatePageSize(newSettings.defaultPageSize);
    }
    
    if (activeLibrary) {
      fetchMovies(1, searchTerm, advancedFilters, newSettings.defaultSortField || sortField, newSettings.defaultSortOrder || sortOrder, newSettings.defaultPageSize || pageSize, activeLibrary.id);
    } else if (currentAppView === 'mainWall') {
      // 传入新的设置，确保立即使用更新后的首页设置
      fetchRecentMovies(newSettings);
    }
  };

  const handleLibraryCardClick = (library: MovieLibrary) => {
    setActiveLibrary(library); 
    if (appSettings.activeLibraryId !== library.id) {
        saveSettings({ activeLibraryId: library.id });
    }
  };
  
  const handleDeleteLibrary = async (libraryId: string) => {
    await manageMovieLibrary({ id: libraryId }, 'delete');
  };

  // 智能片库选择处理
  const handleSmartLibrarySelect = useCallback((library: any) => {
    if (!library) {
      setActiveSmartLibraryId(null);
      return;
    }

    setActiveSmartLibraryId(library.id);

    // 根据智能片库的规则应用筛选
    const rules = library.rules || {};

    // 清除搜索词
    setSearchTerm('');

    // 应用规则中的筛选条件
    if (rules.favorited) {
      // 收藏筛选
      applyAdvancedFilters({ favorited: true });
    } else if (rules.asset_status) {
      // 资产状态筛选
      applyAdvancedFilters({ asset_status: rules.asset_status });
    } else if (rules.sortField || rules.sortOrder) {
      // 排序规则
      updateSort(
        rules.sortField || sortField,
        rules.sortOrder || sortOrder
      );
      applyAdvancedFilters({});
    } else {
      // 默认：全部影片
      applyAdvancedFilters({});
    }
  }, [setSearchTerm, applyAdvancedFilters, updateSort, sortField, sortOrder]);


  if (isLoadingSettings || isLoadingLibraries) {
    return <div className="min-h-screen bg-[#1e1e1e] text-neutral-100 flex items-center justify-center"><p>正在加载应用设置与片库信息...</p></div>;
  }

  const renderMainWallContent = () => {
    // 移除了 activeLibrary 条件判断 - 现在直接渲染全局影片墙
    return (
      <>
        <div className="bg-[#252525]/60 p-2.5 sticky top-[65px] z-40 border-b border-neutral-700/30 backdrop-blur-sm">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-1 bg-[#1c1c1c]/70 p-1 rounded-md border border-neutral-600/60">
              {AVAILABLE_VIEW_MODES.map(mode => {
                let Icon;
                if (mode === 'card') Icon = GridViewIcon;
                else if (mode === 'compactList') Icon = ListViewIcon;
                else if (mode === 'detailedList') Icon = DetailedListViewIcon;
                else if (mode === 'waterfall') Icon = WaterfallViewIcon;
                else return null;
                return (
                  <button
                    key={mode}
                    onClick={() => setViewModeState(mode)}
                    className={`control-bar-button ${viewMode === mode ? 'control-bar-button-active' : ''}`}
                    title={mode === 'card' ? '卡片视图' : mode === 'compactList' ? '紧凑列表' : mode === 'detailedList' ? '详细列表' : '瀑布流'}
                  >
                    <Icon className="w-5 h-5" />
                  </button>
                );
              })}
            </div>
            {activeFilterTag && (
                <div className="flex items-center bg-pink-600/80 text-white px-3 py-1.5 rounded-md text-xs border border-pink-500 mx-2">
                    <span className="font-semibold mr-1.5">{favoriteTypeLabels[activeFilterTag.type] || activeFilterTag.type}:</span> {activeFilterTag.value}
                    <button
                        onClick={handleToggleCurrentFilterFavorite}
                        className={`p-1 rounded-full ml-2 transition-colors ${isCurrentFilterFavorite ? 'text-red-400 hover:text-red-300' : 'text-neutral-300 hover:text-white'}`}
                        title={isCurrentFilterFavorite ? '取消收藏此筛选条件' : '收藏此筛选条件'}
                      >
                        <HeartIcon filled={isCurrentFilterFavorite} className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => {
                            clearActiveFilterTag();
                            resetFiltersAndFetch();
                        }}
                        className="ml-2 text-pink-200 hover:text-white font-bold"
                        title="清除筛选"
                    >×</button>
                </div>
            )}
            <div className="flex items-center space-x-2">
              <select value={sortField} onChange={(e) => updateSort(e.target.value as SortableMovieField, sortOrder)} className="form-select-app !py-1.5 !text-xs w-32">
                {sortableFieldsList.map(field => <option key={field.value} value={field.value}>{field.label}</option>)}
              </select>
              <select value={sortOrder} onChange={(e) => updateSort(sortField, e.target.value as 'asc' | 'desc')} className="form-select-app !py-1.5 !text-xs w-20">
                <option value="desc">降序</option>
                <option value="asc">升序</option>
              </select>
               <select value={pageSize} onChange={(e) => updatePageSize(parseInt(e.target.value, 10))} className="form-select-app !py-1.5 !text-xs w-24">
                {[10, 20, 30, 50, 100, 150, 200, 500].map(ps => <option key={ps} value={ps}>每页 {ps}</option>)}
              </select>
            </div>
          </div>
        </div>
         <MovieGridDisplay
            movies={movies}
            isLoading={isLoadingMovies}
            viewMode={viewMode}
            appSettings={appSettings}
            pageSize={pageSize}
            onCardClick={handleCardClick}
        />
        {(totalPages > 1) && <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={goToPage} isLoading={isLoadingMovies}/>}
        {movieError && <div className="text-center text-red-400 bg-red-900/30 p-4 rounded-md m-4">{movieError}</div>}
        {!isLoadingMovies && movies.length === 0 && !movieError && (
          <div className="text-center text-neutral-500 pt-10">
            <LuTriangle size={48} className="mx-auto mb-4 opacity-50" />
            <p className="text-xl">{activeLibrary ? '此片库中暂无影片~' : '暂无影片，请尝试扫描或调整筛选条件'}</p>
            <p className="mt-2">尝试点击顶部的 <LuScanLine className="inline w-5 h-5" /> 扫描按钮，或调整您的筛选条件。</p>
          </div>
        )}
      </>
    );

    // 移除了片库选择页面的逻辑 - 现在主页直接是全局影片墙
  };


  return (
    <div className="min-h-screen bg-[#1e1e1e] text-neutral-100 flex flex-col relative">
      <AppHeader
        currentAppView={currentAppView}
        onSetCurrentAppView={setCurrentAppView}
        searchTerm={searchTerm}
        onSearchTermChange={updateSearchTerm}
        onTriggerScan={() => {
          if (activeLibrary) {
            triggerLibraryScan(activeLibrary);
          } else {
             alert("请先进入一个片库，或在片库卡片上进行扫描操作。如需扫描全部，请在设置的工具箱内查找。");
          }
        }}
        isScanning={isScanning}
        onOpenFilterModal={() => setIsFilterModalOpen(true)}
        onOpenLinLuoModal={() => setIsLinLuoModalOpen(true)}
        onOpenSettingsModal={(initialTab) => { 
            setSettingsInitialTab(initialTab || 'general'); 
            setIsSettingsModalOpen(true); 
        }}
        onOpenRecommendationsModal={() => { setIsRecommendationsModalOpen(true); handleFetchRecommendations(); }}
        privacyState={privacyState}
        isPrivacyUnlockedThisSession={isPrivacyUnlockedThisSession}
        onTogglePrivacyLockIcon={handleTogglePrivacyLockIcon}
        appSettings={appSettings}
        onUpdateSettings={saveSettings}
        onNavigateHome={navigateToMainWall}
      />

      {/* 主内容区域：侧边栏 + 内容区的两栏布局 */}
      <div className="flex-grow flex">
        {/* 智能片库侧边栏 - 仅在主影片墙视图显示 */}
        {currentAppView === 'mainWall' && (
          <SmartLibrarySidebar
            onLibrarySelect={handleSmartLibrarySelect}
            activeLibraryId={activeSmartLibraryId}
          />
        )}

        {/* 主内容区域 */}
        <main className={`flex-grow ${currentAppView === 'mainWall' ? '' : 'container mx-auto py-4'}`}>
          {currentAppView === 'mainWall' && renderMainWallContent()}
          {currentAppView === 'favoritesView' && (
            <div className="container mx-auto py-4">
              <FavoritesView onTagClick={onFavoriteTagClickInFavoritesView} />
            </div>
          )}
          {(currentAppView === 'collectorView' || currentAppView === 'ingestCenter') && (
            <div className="container mx-auto py-4">
              <CollectorPage onMovieDataChanged={() => {
                // 刷新主页影片库
                if (activeLibrary) {
                  fetchMovies(1, searchTerm, advancedFilters, sortField, sortOrder, pageSize, activeLibrary.id);
                } else {
                  fetchRecentMovies();
                }
              }} />
            </div>
          )}
          {currentAppView === 'stagingArea' && (
            <div className="container mx-auto py-4">
              <StagingAreaPage />
            </div>
          )}
          {currentAppView === 'recycleBin' && (
            <div className="container mx-auto py-4">
              <RecycleBinPage />
            </div>
          )}
          {currentAppView === 'intelligenceCenter' && (
            <IntelligenceCenterPage onMovieDataChanged={() => {
              // 刷新主页影片库
              if (activeLibrary) {
                fetchMovies(1, searchTerm, advancedFilters, sortField, sortOrder, pageSize, activeLibrary.id);
              } else {
                fetchRecentMovies();
              }
            }} />
          )}
          {currentAppView === 'intelligenceCenter' && (
            <IntelligenceCenterPage onMovieDataChanged={() => {
              // 刷新主页影片库
              if (activeLibrary) {
                fetchMovies(1, searchTerm, advancedFilters, sortField, sortOrder, pageSize, activeLibrary.id);
              } else {
                fetchRecentMovies();
              }
            }} />
          )}
        </main>
      </div>

      {isDetailModalOpen && selectedMovie && (
        <MovieDetailModal
          movie={selectedMovie}
          onClose={handleCloseDetailModal}
          onUpdateMovie={updateMovieInList}
          onTagClick={handleTagClickInDetail}
          appDefaultCover={appSettings.customDefaultCoverDataUrl}
          appDefaultActorAvatar={appSettings.defaultActorAvatarDataUrl}
          currentSettings={appSettings}
        />
      )}

      {isCDModalOpen && representativeMovieForGrouping && currentNfoIdForGrouping && (
        <CDSelectionView
            isOpen={isCDModalOpen}
            representativeMovie={representativeMovieForGrouping}
            nfoId={currentNfoIdForGrouping}
            onClose={() => closeModal('cd')}
            onSelectCdPart={(cdPart) => {
                closeModal('cd');
                openDetailModal(cdPart);
            }}
            appSettings={appSettings}
        />
      )}
      {isSettingsModalOpen && (
        <SettingsPage
          isOpen={isSettingsModalOpen}
          onClose={() => closeModal('settings')}
          currentSettings={appSettings}
          onSettingsSaved={handleSettingsSaved}
          onLaunchNfoPlotPolisher={() => { closeModal('settings'); setIsNfoPlotPolisherModalOpen(true);}}
          onLaunchScraperTest={() => { closeModal('settings'); setIsScraperTestModalOpen(true);}}
          initialTab={settingsInitialTab}
        />
      )}
      {isLinLuoModalOpen && 
        <LinLuoSummoningModal 
            isOpen={isLinLuoModalOpen} 
            onClose={() => closeModal('linluo')} 
            initialMessage={initialLinLuoMessage}
            setInitialMessage={setInitialLinLuoMessage} 
        />
      }
      {isFilterModalOpen && (
        <AdvancedFilterModal
          isOpen={isFilterModalOpen}
          onClose={() => closeModal('filter')}
          currentFilters={advancedFilters}
          onApplyFilters={(newFilters) => {
            applyAdvancedFilters(newFilters); 
            closeModal('filter');
          }}
          availableGenres={availableGenres}
          availableTags={availableTags}
        />
      )}
      {isImagePreviewModalOpen && <ImagePreviewModal isOpen={isImagePreviewModalOpen} imageUrl={imagePreviewUrl} altText="图片预览" onClose={() => closeModal('imagePreview')} />}
      <PrivacyUnlockModal isOpen={isPrivacyUnlockModalOpen} onClose={() => setIsPrivacyUnlockModalOpen(false)} onUnlock={attemptUnlockPrivacy} />
      <RecommendationsModal 
        isOpen={isRecommendationsModalOpen}
        onClose={() => closeModal('recommendations')}
        movies={recommendedMovies}
        isLoading={isLoadingRecommendations}
        appDefaultCover={appSettings.customDefaultCoverDataUrl}
        onMovieClick={handleCardClick}
        onFormatForLinLuo={handleFormatRecommendationForLinLuo}
        linLuoFormattedMessage={linLuoFormattedRecommendation}
        isFormatting={isFormattingRecommendation}
        onSendToLinLuo={(message) => { 
            setInitialLinLuoMessage(message); 
            setIsRecommendationsModalOpen(false); 
            setIsLinLuoModalOpen(true); 
        }}
      />
      {isNfoPlotPolisherModalOpen && <NfoPlotPolisherTool isOpen={isNfoPlotPolisherModalOpen} onClose={() => closeModal('nfoPlotPolisher')} />}
      {isScraperTestModalOpen && <ScraperTestTool isOpen={isScraperTestModalOpen} onClose={() => closeModal('scraperTest')} />}

      {/* 演员档案模态框 */}
      <ActorProfileModal
        isOpen={isActorProfileModalOpen}
        onClose={closeActorProfileModal}
        actorName={currentActorName}
        actorProfile={actorProfileData}
        filmography={actorFilmography}
        completeFilmography={completeFilmography}
        isLoading={isLoadingActorProfile}
        isLoadingCompleteFilmography={isLoadingCompleteFilmography}
        error={actorProfileError}
        onRefresh={() => openActorProfileModal(currentActorName)}
        onMovieCardClick={handleCardClick}
        appDefaultCover={appSettings.defaultCover}
      />

      {isLibraryCreationModalOpen && (
        <LibraryCreationModal
          isOpen={isLibraryCreationModalOpen}
          onClose={() => setIsLibraryCreationModalOpen(false)}
          onSave={(params, op) => manageMovieLibrary(params, op as 'create' | 'update')} 
          existingLibrary={null} 
        />
      )}

      {strmPlayerState.isOpen && strmPlayerState.url && (
        <EmbeddedMediaPlayerModal
          isOpen={strmPlayerState.isOpen}
          url={strmPlayerState.url}
          movieTitle={strmPlayerState.title}
          onClose={() => setStrmPlayerState({ isOpen: false, url: null, title: undefined })}
        />
      )}

      <ProgressBar 
        progress={scanProgress?.overallPercentage || 0} 
        message={scanProgress?.currentPathMessage || scanProgress?.currentFileMessage || (isScanning ? "扫描中..." : null)} 
        error={scanProgress?.error || null} 
      />
    </div>
  );
};

export default App;
      