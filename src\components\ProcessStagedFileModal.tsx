import React, { useState, useEffect } from 'react';
import { X, FileVideo, Upload, Cloud, AlertCircle, CheckCircle, Loader } from 'lucide-react';

interface StagedFile {
  filePath: string;
  fileName: string;
  fileSize: number;
  lastModified: Date;
  nfoId: string | null;
  extension: string;
}

interface ProcessStagedFileModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: StagedFile;
  onProcessComplete: () => void;
}

export const ProcessStagedFileModal: React.FC<ProcessStagedFileModalProps> = ({
  isOpen,
  onClose,
  file,
  onProcessComplete
}) => {
  const [nfoId, setNfoId] = useState<string>('');
  const [targetSeriesFolder, setTargetSeriesFolder] = useState<string>('');
  const [seriesFolders, setSeriesFolders] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processResult, setProcessResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 初始化数据
  useEffect(() => {
    if (isOpen && file) {
      setNfoId(file.nfoId || '');
      setProcessResult(null);
      setError(null);
      
      // 从番号推断系列文件夹
      if (file.nfoId) {
        const series = file.nfoId.split('-')[0];
        setTargetSeriesFolder(series);
      }
      
      // 加载可用的系列文件夹
      loadSeriesFolders();
    }
  }, [isOpen, file]);

  const loadSeriesFolders = async () => {
    try {
      const result = await window.sfeElectronAPI.stagingGetSeriesFolders();
      if (result.success) {
        setSeriesFolders(result.folders || []);
      }
    } catch (error) {
      console.error('加载系列文件夹失败:', error);
    }
  };

  const handleProcess = async () => {
    if (!nfoId.trim()) {
      setError('请输入或确认番号');
      return;
    }

    if (!targetSeriesFolder.trim()) {
      setError('请选择目标系列文件夹');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProcessResult(null);

    try {
      const result = await window.sfeElectronAPI.stagingProcessFile(
        file.filePath,
        nfoId.trim(),
        targetSeriesFolder.trim()
      );

      setProcessResult(result);

      if (result.success) {
        // 延迟关闭，让用户看到成功消息
        setTimeout(() => {
          onProcessComplete();
        }, 2000);
      } else {
        setError(result.error || '处理失败');
      }
    } catch (error) {
      console.error('处理文件失败:', error);
      setError('处理失败，请检查网络连接');
    } finally {
      setIsProcessing(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <FileVideo className="h-6 w-6 text-[#B8860B]" />
            <h2 className="text-xl font-bold text-white">处理下载文件</h2>
          </div>
          <button
            onClick={onClose}
            disabled={isProcessing}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* 文件信息 */}
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-medium text-white mb-3">文件信息</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">文件名:</span>
                <span className="text-white font-medium">{file.fileName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">文件大小:</span>
                <span className="text-white">{formatFileSize(file.fileSize)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">文件类型:</span>
                <span className="text-white">{file.extension.toUpperCase()}</span>
              </div>
            </div>
          </div>

          {/* 番号设置 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              番号 *
            </label>
            <input
              type="text"
              value={nfoId}
              onChange={(e) => setNfoId(e.target.value.toUpperCase())}
              placeholder="请输入或确认番号，如 JUFE-585"
              disabled={isProcessing}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B] disabled:opacity-50"
            />
            <p className="text-xs text-gray-500 mt-1">
              系统已自动识别，请确认或手动修正
            </p>
          </div>

          {/* 系列文件夹选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              目标系列文件夹 *
            </label>
            <div className="flex gap-2">
              <select
                value={targetSeriesFolder}
                onChange={(e) => setTargetSeriesFolder(e.target.value)}
                disabled={isProcessing}
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-[#B8860B] disabled:opacity-50"
              >
                <option value="">请选择系列文件夹</option>
                {seriesFolders.map((folder) => (
                  <option key={folder} value={folder}>
                    {folder}
                  </option>
                ))}
              </select>
              <input
                type="text"
                value={targetSeriesFolder}
                onChange={(e) => setTargetSeriesFolder(e.target.value.toUpperCase())}
                placeholder="或手动输入"
                disabled={isProcessing}
                className="w-32 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:border-[#B8860B] disabled:opacity-50"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              文件将移动到待上传队列的对应系列文件夹中
            </p>
          </div>

          {/* 处理说明 */}
          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
            <h4 className="text-blue-300 font-medium mb-2 flex items-center gap-2">
              <Cloud className="h-4 w-4" />
              处理流程说明
            </h4>
            <ul className="text-sm text-blue-200 space-y-1">
              <li>• 在媒体资产目录中生成 STRM 文件</li>
              <li>• 将原始文件移动到待上传队列</li>
              <li>• 更新数据库记录状态为 AVAILABLE</li>
              <li>• STRM 文件将指向 115 网盘路径</li>
            </ul>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
              <div>
                <p className="text-red-300 font-medium">处理失败</p>
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* 成功提示 */}
          {processResult && processResult.success && (
            <div className="bg-green-900/50 border border-green-700 rounded-lg p-4 flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-400 flex-shrink-0" />
              <div>
                <p className="text-green-300 font-medium">处理成功</p>
                <p className="text-green-400 text-sm">{processResult.message}</p>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
            <button
              onClick={onClose}
              disabled={isProcessing}
              className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 rounded hover:border-gray-500 disabled:opacity-50"
            >
              取消
            </button>
            <button
              onClick={handleProcess}
              disabled={isProcessing || !nfoId.trim() || !targetSeriesFolder.trim()}
              className="px-6 py-2 bg-[#B8860B] text-black font-medium rounded hover:bg-[#DAA520] disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isProcessing ? (
                <>
                  <Loader className="h-4 w-4 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  生成 STRM 并归档至待上传
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
