import React, { useEffect, useCallback, useMemo, useRef, useState } from 'react';
import {
    Movie, AppSettings, SortableMovieField, AdvancedFilterOptions,
    DetailFilterType, FavoriteItemType, SnapshotInfo, ViewMode, MovieLibrary,
    ScanCompleteData, ScanLibraryResult, PlayStrmUrlPayload, ToggleFavoriteResult
} from '../types';

// Hooks
import { useAppSettings } from '../hooks/useAppSettings';
import { useMovieDataManager } from '../hooks/useMovieDataManager';
import { useModalManager } from '../hooks/useModalManager';
import { usePrivacyManager } from '../hooks/usePrivacyManager';
import { useScanStatus } from '../hooks/useScanStatus';
import { useActorProfileManager } from '../hooks/useActorProfileManager';
import { useAppView, AppView } from '../hooks/useAppView';
import { useRepresentativeMovieForGrouping } from '../hooks/useRepresentativeMovie';
import { useMovieLibraryManager } from '../hooks/useMovieLibraryManager';

// Components
import { MovieDetailModal } from '../components/MovieDetailModal';
import CDSelectionView from '../components/CDSelectionView';
import ImagePreviewModal from '../components/ImagePreviewModal';
import ActorProfileModal from '../components/modals/ActorProfileModal';
import EmbeddedMediaPlayerModal from '../components/EmbeddedMediaPlayerModal';

// Layout Components
import SmartLibrarySidebar from '../components/layout/SmartLibrarySidebar';
import MovieGridDisplay from '../components/layout/MovieGridDisplay';
import ProgressBar from '../components/common/ProgressBar';
import Pagination from '../components/common/Pagination';

// Icons
import GridViewIcon from '../components/icons/GridViewIcon';
import ListViewIcon from '../components/icons/ListViewIcon';
import DetailedListViewIcon from '../components/icons/DetailedListViewIcon';
import WaterfallViewIcon from '../components/icons/WaterfallViewIcon';
import HeartIcon from '../components/icons/HeartIcon';
import { LuTriangle, LuScanLine, LuLayoutGrid, LuListChecks, LuLibrary, LuPlus } from 'react-icons/lu';
import EyeIcon from '../components/icons/EyeIcon';
import EyeSlashIcon from '../components/icons/EyeSlashIcon';

const AVAILABLE_VIEW_MODES: ViewMode[] = ['card', 'compactList', 'detailedList', 'waterfall'];

const favoriteTypeLabels: Record<DetailFilterType | FavoriteItemType, string> = {
  actor: '演员',
  studio: '制作商',
  series: '系列',
  director: '导演',
  nfoId: '影片ID',
  tag: '标签',
  genre: '类型',
  library: '片库',
  actress: '演员',
  movie_nfo_id: '影片ID',
  year: '年份',
  resolution: '分辨率',
  videoHeight: '视频高度',
};

const sortableFieldsList: { value: SortableMovieField; label: string }[] = [
    { value: 'releaseDate', label: '发行日期' }, { value: 'year', label: '年份' },
    { value: 'personalRating', label: '个人评分'}, { value: 'title', label: '标题' },
    { value: 'nfoId', label: '番号/ID' }, { value: 'fileName', label: '文件名' },
    { value: 'runtime', label: '时长'}, { value: 'studio', label: '制作商'},
    { value: 'series', label: '系列'}, { value: 'db_id', label: '添加顺序 (DB ID)'},
    { value: 'lastScanned', label: '扫描日期'}, { value: 'nfoLastModified', label: 'NFO修改日期'},
    { value: 'fileSize', label: '文件大小'}, { value: 'fps', label: '帧率 (FPS)'},
    { value: 'videoBitrate', label: '视频码率'}, { value: 'versionCount', label: '版本数量'},
    { value: 'multiCdCountForNfoId', label: 'CD分集数'},
    { value: 'aiRecommendationScore', label: 'AI推荐分' }
];

const LibraryPage: React.FC = () => {
  const { appSettings, isLoadingSettings, saveSettings, setAppSettingsDirectly } = useAppSettings();

  const {
    privacyState, isPrivacyUnlockModalOpen, isPrivacyUnlockedThisSession,
    attemptUnlockPrivacy, setIsPrivacyUnlockModalOpen, handleTogglePrivacyLockIcon
  } = usePrivacyManager();

  const { currentAppView, setCurrentAppView, activeFilterTag, navigateToMainWallWithFilter, clearActiveFilterTag, activeLibrary, setActiveLibrary, navigateToMainWall: navigateToMainWallFromHook } = useAppView();

  const {
    movieLibraries, isLoadingLibraries,
    isLibraryCreationModalOpen, setIsLibraryCreationModalOpen,
    manageMovieLibrary, refreshLibraries,
    triggerLibraryScan,
  } = useMovieLibraryManager(appSettings, saveSettings, activeLibrary, setActiveLibrary);

  const {
    movies, isLoading: isLoadingMovies, error: movieError, currentPage, totalPages,
    searchTerm, advancedFilters, sortField, sortOrder, pageSize,
    fetchMovies, updateSearchTerm, applyAdvancedFilters, updateSort, updatePageSize, goToPage,
    updateMovieInList, setMovies, setCurrentPage, resetFiltersAndFetch,
    setSearchTerm, setAdvancedFilters,
  } = useMovieDataManager({
    initialPageSize: appSettings.defaultPageSize || 50,
    initialSortField: appSettings.defaultSortField || 'releaseDate',
    initialSortOrder: appSettings.defaultSortOrder || 'desc',
    privacyState,
    isPrivacyUnlockedThisSession,
    appSettings,
    activeLibraryId: activeLibrary?.id,
  });

  const { scanProgress, isScanning, triggerScan: triggerGlobalScan } = useScanStatus({
    onScanCompleteCallback: (data: ScanCompleteData | ScanLibraryResult) => {
      const libraryId = data.libraryId;
      if (libraryId && activeLibrary && libraryId === activeLibrary.id) {
        fetchMovies(1, searchTerm, advancedFilters, sortField, sortOrder, pageSize, libraryId);
      } else if (!activeLibrary) {
        fetchRecentMovies();
      }
      refreshLibraries();
    }
  });

  const {
    selectedMovie, currentNfoIdForGrouping,
    isDetailModalOpen, isCDModalOpen,
    isSettingsModalOpen, setIsSettingsModalOpen,
    isLinLuoModalOpen, setIsLinLuoModalOpen,
    isFilterModalOpen, setIsFilterModalOpen,
    isImagePreviewModalOpen, imagePreviewUrl,
    isRecommendationsModalOpen, setIsRecommendationsModalOpen,
    isNfoPlotPolisherModalOpen, setIsNfoPlotPolisherModalOpen,
    isScraperTestModalOpen, setIsScraperTestModalOpen,
    openDetailModal, openCDModal, openImagePreviewModal, closeModal,
  } = useModalManager();

  // 演员档案管理器
  const {
    isActorProfileModalOpen,
    currentActorName,
    actorProfileData,
    actorFilmography,
    completeFilmography,
    isLoadingActorProfile,
    isLoadingCompleteFilmography,
    error: actorProfileError,
    openActorProfileModal,
    closeActorProfileModal
  } = useActorProfileManager();

  const [viewMode, setViewModeState] = useState<ViewMode>('card');

  const [recommendedMovies, setRecommendedMovies] = useState<Movie[]>([]);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);

  const [availableGenres, setAvailableGenres] = useState<string[]>(['示例类型1', '示例类型2']);
  const [availableTags, setAvailableTags] = useState<string[]>(['示例标签1', '示例标签2']);

  const [recentPlayedMovies, setRecentPlayedMovies] = useState<Movie[]>([]);
  const [recentAddedMovies, setRecentAddedMovies] = useState<Movie[]>([]);
  const [isLoadingRecent, setIsLoadingRecent] = useState(false);

  const [strmPlayerState, setStrmPlayerState] = useState<{isOpen: boolean; url: string | null; title?: string}>({ isOpen: false, url: null });

  const [isCurrentFilterFavorite, setIsCurrentFilterFavorite] = useState(false);

  // 智能片库相关状态
  const [activeSmartLibraryId, setActiveSmartLibraryId] = useState<string | null>('all-movies');

  const representativeMovieForGrouping = useRepresentativeMovieForGrouping(currentNfoIdForGrouping, movies, selectedMovie);

  useEffect(() => {
    if (!isLoadingSettings) {
      setViewModeState(appSettings.defaultViewMode || 'card');
    }
  }, [isLoadingSettings, appSettings.defaultViewMode]);

  // 全局演员档案事件监听器
  useEffect(() => {
    const handleOpenActorProfile = (event: CustomEvent) => {
      const { actorName } = event.detail;
      if (actorName) {
        openActorProfileModal(actorName);
      }
    };

    document.addEventListener('openActorProfile', handleOpenActorProfile as EventListener);

    return () => {
      document.removeEventListener('openActorProfile', handleOpenActorProfile as EventListener);
    };
  }, [openActorProfileModal]);

  const fetchRecentMovies = useCallback(async (customSettings?: AppSettings) => {
    setIsLoadingRecent(true);
    try {
      // 使用传入的设置或当前设置中的数量，如果没有设置则使用默认值
      const settingsToUse = customSettings || appSettings;
      const homePageSettings = settingsToUse.homePageSettings || {
        showRecentAdded: true,
        showRecentPlayed: true,
        recentAddedCount: 12,
        recentPlayedCount: 8
      };

      const playedResult = await window.sfeElectronAPI.getRecentMovies('recent_played', homePageSettings.recentPlayedCount || 8);
      if (playedResult.success) setRecentPlayedMovies(playedResult.movies);

      const addedResult = await window.sfeElectronAPI.getRecentMovies('recent_added', homePageSettings.recentAddedCount || 12);
      if (addedResult.success) setRecentAddedMovies(addedResult.movies);
    } catch (error) {
      console.error('获取最近影片失败:', error);
    } finally {
      setIsLoadingRecent(false);
    }
  }, [appSettings]);

  useEffect(() => {
    if (!activeLibrary && !isLoadingSettings) {
      fetchRecentMovies();
    }
  }, [activeLibrary, fetchRecentMovies, isLoadingSettings]);

  // STRM Playback Listener
  useEffect(() => {
    const removeListener = window.sfeElectronAPI.onPlayStrmUrl((payload: PlayStrmUrlPayload) => {
      setStrmPlayerState({ isOpen: true, url: payload.url, title: payload.title });
    });
    return removeListener;
  }, []);

  // NFO ID 自动修复监听器
  useEffect(() => {
    const handleNfoIdFixComplete = (data: any) => {
      if (data.success && data.updated > 0) {
        console.log(`🎉 NFO ID 自动修复完成: 成功修复 ${data.updated} 部电影`);
        console.log('多版本合并功能现在应该正常工作了！');

        // 刷新电影数据
        if (!activeLibrary) {
          fetchRecentMovies();
        } else if (activeLibrary) {
          fetchMovies();
        }

        // 显示成功消息
        alert(`🎉 自动修复完成！\n\n成功修复了 ${data.updated} 部电影的 NFO ID。\n现在相同影片的多个版本会合并显示，并显示版本徽章。\n\n页面数据已自动刷新。`);
      }
    };

    // 检查函数是否存在
    if (window.sfeElectronAPI && typeof window.sfeElectronAPI.onNfoIdFixComplete === 'function') {
      const removeListener = window.sfeElectronAPI.onNfoIdFixComplete(handleNfoIdFixComplete);
      return removeListener;
    }
  }, [activeLibrary, fetchMovies, fetchRecentMovies]);

  // 事件处理函数
  const handleCardClick = useCallback((movie: Movie, event?: React.MouseEvent) => {
    if (event?.ctrlKey || event?.metaKey) {
      // Ctrl/Cmd + 点击：打开多版本选择
      if (movie.versionCount && movie.versionCount > 1) {
        openCDModal(movie.nfoId);
      } else {
        openDetailModal(movie);
      }
    } else {
      // 普通点击：直接打开详情
      openDetailModal(movie);
    }
  }, [openDetailModal, openCDModal]);

  const handleViewModeChange = useCallback((newViewMode: ViewMode) => {
    setViewModeState(newViewMode);
    saveSettings({ defaultViewMode: newViewMode });
  }, [saveSettings]);

  const handleSortChange = useCallback((field: SortableMovieField, order: 'asc' | 'desc') => {
    updateSort(field, order);
    saveSettings({ defaultSortField: field, defaultSortOrder: order });
  }, [updateSort, saveSettings]);

  const handleTagClick = useCallback((value: string, type: DetailFilterType) => {
    closeModal('detail');
    // TODO: 实现标签筛选逻辑
    console.log('标签点击:', value, type);
  }, [closeModal]);

  const handleToggleImageVisibility = useCallback(() => {
    const newSettings = {
      ...appSettings,
      imagesGloballyVisible: !appSettings.imagesGloballyVisible
    };
    saveSettings(newSettings);
  }, [appSettings, saveSettings]);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    updatePageSize(newPageSize);
    saveSettings({ defaultPageSize: newPageSize });
  }, [updatePageSize, saveSettings]);

  const handleSmartLibrarySelect = useCallback((library: any) => {
    if (library) {
      setActiveSmartLibraryId(library.id);
      // 根据智能片库规则筛选影片
      if (library.rules) {
        const { sortField, sortOrder, ...filters } = library.rules;
        if (sortField && sortOrder) {
          updateSort(sortField, sortOrder);
        }
        if (Object.keys(filters).length > 0) {
          applyAdvancedFilters(filters);
        }
      }
      // 重新获取影片数据
      fetchMovies(1);
    } else {
      setActiveSmartLibraryId(null);
      // 重置为默认状态
      resetFiltersAndFetch();
    }
  }, [setActiveSmartLibraryId, updateSort, applyAdvancedFilters, fetchMovies, resetFiltersAndFetch]);

  const onFavoriteTagClickInFavoritesView = useCallback((type: FavoriteItemType, value: string) => {
    navigateToMainWallWithFilter(type, value);
  }, [navigateToMainWallWithFilter]);

  const handleSettingsSaved = useCallback((newSettings: AppSettings) => {
    setAppSettingsDirectly(newSettings);

    // 如果页面大小或排序设置发生变化，更新相关状态
    if (newSettings.defaultPageSize && newSettings.defaultPageSize !== pageSize) {
      updatePageSize(newSettings.defaultPageSize);
    }

    if (activeLibrary) {
      fetchMovies(1, searchTerm, advancedFilters, newSettings.defaultSortField || sortField, newSettings.defaultSortOrder || sortOrder, newSettings.defaultPageSize || pageSize, activeLibrary.id);
    } else {
      // 传入新的设置，确保立即使用更新后的首页设置
      fetchRecentMovies(newSettings);
    }
  }, [activeLibrary, fetchMovies, fetchRecentMovies, searchTerm, advancedFilters, sortField, sortOrder, pageSize, updatePageSize, setAppSettingsDirectly]);

  const handleLibraryCardClick = (library: MovieLibrary) => {
    setActiveLibrary(library);
    if (appSettings.activeLibraryId !== library.id) {
        saveSettings({ activeLibraryId: library.id });
    }
  };

  // 加载状态检查
  if (isLoadingSettings || isLoadingLibraries) {
    return <div className="min-h-screen bg-[#1e1e1e] text-neutral-100 flex items-center justify-center"><p>正在加载应用设置与片库信息...</p></div>;
  }

  const renderMainWallContent = () => {
    return (
      <>
        <div className="bg-[#252525]/60 p-2.5 sticky top-[65px] z-40 border-b border-neutral-700/30 backdrop-blur-sm">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-1 bg-[#1c1c1c]/70 p-1 rounded-md border border-neutral-600/60">
              {AVAILABLE_VIEW_MODES.map(mode => {
                let Icon;
                if (mode === 'card') Icon = GridViewIcon;
                else if (mode === 'compactList') Icon = ListViewIcon;
                else if (mode === 'detailedList') Icon = DetailedListViewIcon;
                else if (mode === 'waterfall') Icon = WaterfallViewIcon;

                return (
                  <button
                    key={mode}
                    onClick={() => handleViewModeChange(mode)}
                    className={`p-1.5 rounded transition-colors ${viewMode === mode ? 'bg-[#B8860B] text-white' : 'text-neutral-400 hover:text-neutral-200 hover:bg-neutral-700/50'}`}
                    title={mode === 'card' ? '网格视图' : mode === 'compactList' ? '紧凑列表' : mode === 'detailedList' ? '详细列表' : '瀑布流'}
                  >
                    <Icon className="w-4 h-4" />
                  </button>
                );
              })}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleToggleImageVisibility}
                title={appSettings.imagesGloballyVisible ? "隐藏所有图片 (SFW)" : "显示所有图片 (NSFW)"}
                className="p-1.5 rounded transition-colors text-neutral-400 hover:text-neutral-200 hover:bg-neutral-700/50"
              >
                {appSettings.imagesGloballyVisible ? <EyeIcon className="w-4 h-4"/> : <EyeSlashIcon className="w-4 h-4 text-red-400"/>}
              </button>

              <select
                value={`${sortField}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-') as [SortableMovieField, 'asc' | 'desc'];
                  handleSortChange(field, order);
                }}
                className="bg-[#1c1c1c]/70 border border-neutral-600/60 text-neutral-200 text-sm rounded px-2 py-1 focus:outline-none focus:border-[#B8860B]"
              >
                {sortableFieldsList.map(({ value, label }) => (
                  <React.Fragment key={value}>
                    <option value={`${value}-desc`}>{label} ↓</option>
                    <option value={`${value}-asc`}>{label} ↑</option>
                  </React.Fragment>
                ))}
              </select>

              <select
                value={pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="bg-[#1c1c1c]/70 border border-neutral-600/60 text-neutral-200 text-sm rounded px-2 py-1 focus:outline-none focus:border-[#B8860B]"
              >
                {[10, 20, 30, 50, 100, 150, 200, 500].map(ps => <option key={ps} value={ps}>每页 {ps}</option>)}
              </select>
            </div>
          </div>
        </div>
         <MovieGridDisplay
            movies={movies}
            isLoading={isLoadingMovies}
            viewMode={viewMode}
            appSettings={appSettings}
            pageSize={pageSize}
            onCardClick={handleCardClick}
        />
        {(totalPages > 1) && <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={goToPage} isLoading={isLoadingMovies}/>}
        {movieError && <div className="text-center text-red-400 bg-red-900/30 p-4 rounded-md m-4">{movieError}</div>}
        {!isLoadingMovies && movies.length === 0 && !movieError && (
          <div className="text-center text-neutral-400 py-16">
            <LuLibrary className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg mb-2">暂无影片</p>
            <p className="text-sm">尝试调整筛选条件或扫描新的影片文件夹</p>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e] text-neutral-100 flex flex-col relative">
      {/* 主内容区域：侧边栏 + 内容区的两栏布局 */}
      <div className="flex-grow flex">
        {/* 智能片库侧边栏 */}
        <SmartLibrarySidebar
          onLibrarySelect={handleSmartLibrarySelect}
          activeLibraryId={activeSmartLibraryId}
        />

        {/* 主内容区域 */}
        <main className="flex-grow">
          {renderMainWallContent()}
        </main>
      </div>

      {/* 扫描进度条 */}
      {isScanning && scanProgress && (
        <ProgressBar
          progress={scanProgress.overallPercentage}
          message={scanProgress.currentPathMessage || scanProgress.currentFileMessage || '正在扫描...'}
        />
      )}

      {/* 模态框 */}
      {isDetailModalOpen && selectedMovie && (
        <MovieDetailModal
          movie={selectedMovie}
          isOpen={isDetailModalOpen}
          onClose={() => closeModal('detail')}
          onUpdateMovie={updateMovieInList}
          onTagClick={handleTagClick}
          onOpenImagePreview={openImagePreviewModal}
          appDefaultCover={appSettings.customDefaultCoverDataUrl}
          appDefaultActorAvatar={appSettings.defaultActorAvatarDataUrl}
          currentSettings={appSettings}
        />
      )}

      {isCDModalOpen && currentNfoIdForGrouping && (
        <CDSelectionView
          nfoId={currentNfoIdForGrouping}
          isOpen={isCDModalOpen}
          onClose={() => closeModal('cd')}
          onMovieSelect={openDetailModal}
          representativeMovie={representativeMovieForGrouping}
        />
      )}

      {isImagePreviewModalOpen && imagePreviewUrl && (
        <ImagePreviewModal
          isOpen={isImagePreviewModalOpen}
          imageUrl={imagePreviewUrl}
          altText="预览图片"
          onClose={() => closeModal('imagePreview')}
        />
      )}

      {isActorProfileModalOpen && (
        <ActorProfileModal
          isOpen={isActorProfileModalOpen}
          actorName={currentActorName}
          onClose={closeActorProfileModal}
          profileData={actorProfileData}
          filmography={actorFilmography}
          completeFilmography={completeFilmography}
          isLoadingProfile={isLoadingActorProfile}
          isLoadingCompleteFilmography={isLoadingCompleteFilmography}
          error={actorProfileError}
        />
      )}

      {strmPlayerState.isOpen && strmPlayerState.url && (
        <EmbeddedMediaPlayerModal
          isOpen={strmPlayerState.isOpen}
          url={strmPlayerState.url}
          title={strmPlayerState.title || '播放视频'}
          onClose={() => setStrmPlayerState({ isOpen: false, url: null })}
        />
      )}
    </div>
  );
};

export default LibraryPage;
