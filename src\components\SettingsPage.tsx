
/// <reference path="../vite-env.d.ts" />
import React, { useState, useEffect, useCallback } from 'react';
import { AppSettings, SortableMovieField, FilenameSuffixRule, SnapshotQuality, ViewMode } from '../types'; 
import { v4 as uuidv4 } from 'uuid'; 
import GeneralSettingsTab from './settings/GeneralSettingsTab';
import AiSettingsTab from './settings/AiSettingsTab';
import MediaSettingsTab from './settings/MediaSettingsTab';
import NfoSettingsTab from './settings/NfoSettingsTab';
import PrivacySettingsTab from './settings/PrivacySettingsTab';
import DataSettingsTab from './settings/DataSettingsTab';
import ToolsSettingsTab from './settings/ToolsSettingsTab';
import LibraryManagementTab from './settings/LibraryManagementTab';
import HomePageSettingsTab from './settings/HomePageSettingsTab';
import { TripleRepositorySettings } from './settings/TripleRepositorySettings';
import { CategoryRulesEditor } from './settings/CategoryRulesEditor';
import { DownloadStagingSettings } from './settings/DownloadStagingSettings';
import { ScraperPrioritySettings } from './settings/ScraperPrioritySettings';

export type TabKey = 'general' | 'libraries' | 'homepage' | 'ai' | 'media' | 'nfo' | 'privacy' | 'data' | 'tools' | 'files' | 'scraper';

interface SettingsPageProps {
  isOpen: boolean;
  onClose: () => void;
  onSettingsSaved: (newSettings: AppSettings) => void;
  currentSettings: AppSettings;
  onLaunchNfoPlotPolisher: () => void;
  onLaunchScraperTest?: () => void;
  initialTab?: TabKey;
}


export const SettingsPage: React.FC<SettingsPageProps> = ({
  isOpen,
  onClose,
  onSettingsSaved,
  currentSettings,
  onLaunchNfoPlotPolisher,
  onLaunchScraperTest,
  initialTab = 'general'
}) => {
  const [settings, setSettings] = useState<Partial<AppSettings>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [pathSep, setPathSep] = useState('/');
  const [activeTab, setActiveTab] = useState<TabKey>(initialTab);
  
  const [currentSuffixRule, setCurrentSuffixRule] = useState<{ id: string; suffix: string; tags: string }>({ id: '', suffix: '', tags: '' });
  const [editingSuffixRuleId, setEditingSuffixRuleId] = useState<string | null>(null);
  const [newPresetCategory, setNewPresetCategory] = useState('');

  const [tempPrivacyHideTags, setTempPrivacyHideTags] = useState('');


  const defaultSettingsFromParent: AppSettings = {
    defaultScanPaths: [],
    pythonExecutablePath: null,
    defaultSortField: 'releaseDate',
    defaultSortOrder: 'desc',
    defaultPageSize: 50,
    defaultViewMode: 'card',
    customDefaultCoverDataUrl: null,
    defaultActorAvatarDataUrl: null,
    ffmpegPath: null,
    ffprobePath: null,
    snapshotCachePath: null,
    filenameSuffixRules: [],
    presetVersionCategories: [],
    filenameRenameTemplate: "{original_basename} [{nfoId}] [{year}].{extension}",
    snapshotQuality: 'hd_640p',
    autoUpdateNfoWatchedRating: true,
    autoCreateNfoOnSave: true,
    aiProvider: null,
    customGptEndpoint: null,
    customGptApiKey: null,
    customGptModel: null,
    grokApiKey: null,
    grokModel: 'grok-3-mini-fast',
    privacyModeEnabled: false,
    privacyModePassword: null,
    privacyHideTags: [],
    avatarDataSourceType: 'none',
    actorAvatarLibraryPath: null,
    localFileTreePath: null,
    avatarPreferAiFixed: true,
    remoteGfriendsFiletreeUrl: "https://cdn.jsdelivr.net/gh/xinxin8816/gfriends/Filetree.json",
    remoteGfriendsImageBaseUrl: "https://cdn.jsdelivr.net/gh/xinxin8816/gfriends/Content/",
    imagesGloballyVisible: true,
    customSfwPlaceholderDataUrl: null,
    activeLibraryId: null,
    homePageSettings: {
      showRecentAdded: true,
      showRecentPlayed: true,
      recentAddedCount: 12,
      recentPlayedCount: 8
    },

    // 三位一体分离式存储设置
    assetsPath: null,   // 元数据仓库
    trailersPath: null, // 预告片仓库
    mediaPath: null,    // 正片仓库
  };

  const getUniqueId = (): string => (typeof crypto !== 'undefined' && crypto.randomUUID) ? crypto.randomUUID() : uuidv4();

  useEffect(() => {
    if (isOpen) {
      setIsLoading(false); 
      setError(null);
      setSuccessMessage(null);
      const mergedSettings = { ...defaultSettingsFromParent, ...currentSettings };
      mergedSettings.filenameSuffixRules = (mergedSettings.filenameSuffixRules || []).map(rule => ({
        ...rule,
        id: String(rule.id || getUniqueId()),
      }));
      setSettings(mergedSettings);
      setTempPrivacyHideTags((mergedSettings.privacyHideTags || []).join(', '));
      setActiveTab(initialTab); 
      setCurrentSuffixRule({ id: '', suffix: '', tags: '' }); 
      setEditingSuffixRuleId(null);
      setNewPresetCategory('');


      if (window.sfeElectronAPI?.getPathSep) {
        window.sfeElectronAPI.getPathSep()
          .then(sep => setPathSep(sep))
          .catch(e => {
            console.error("Failed to get path separator from sfeElectronAPI, defaulting to '/'", e);
            setPathSep('/');
          });
      } else {
        console.warn("window.sfeElectronAPI or getPathSep is not available, defaulting to '/'");
        setPathSep('/');
      }
    }
  }, [isOpen, currentSettings, initialTab]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: any = value;
    if (type === 'number') {
      processedValue = value === '' ? undefined : Number(value);
    } else if ((e.target as HTMLInputElement).type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    } else if (value === '' && ['snapshotCachePath', 'pythonExecutablePath', 'ffmpegPath', 'ffprobePath', 'customGptEndpoint', 'customGptApiKey', 'grokApiKey', 'customGptModel', 'customDefaultCoverDataUrl', 'privacyModePassword', 'actorAvatarLibraryPath', 'localFileTreePath'].includes(name)) {
      processedValue = null;
    } else if (name === "aiProvider" && value === "") {
        processedValue = null;
    } else if (name === "avatarDataSourceType" && value === "") {
        processedValue = 'none';
    }
     else if (name === "tempPrivacyHideTags") { 
        return; 
    }


    setSettings(prev => ({
      ...prev,
      [name]: processedValue,
    }));
  };
  
  const handleBrowsePathForInput = async (settingName: keyof AppSettings) => {
    try {
      const selectedPath = await window.sfeElectronAPI.selectDirectory();
      if (selectedPath && selectedPath.length > 0) { 
        setSettings(prev => ({ ...prev, [settingName]: selectedPath[0] })); 
      }
    } catch (err) { console.error(`Error browsing directory for ${settingName}:`, err); alert("选择目录失败。"); }
  };

  // 删除了 handleSave 函数 - 不再需要"保存所有设置"按钮，每个设置都会自动保存

  const handleBackupDatabase = async () => {
    setError(null); setSuccessMessage(null);
    try {
      const result = await window.sfeElectronAPI.backupDatabase();
      if (result.success && result.filePath) {
        setSuccessMessage(`数据库已成功备份到: ${result.filePath}`);
      } else {
        setError(result.error || '数据库备份失败。');
      }
    } catch (err: any) { console.error("Error backing up database:", err); setError(`备份数据库时出错: ${err.message}`); }
  };

  const handleRestoreDatabase = async () => {
    setError(null); setSuccessMessage(null);
    const confirmRestore = window.confirm("恢复数据库将覆盖当前所有数据，此操作不可撤销！确定要继续吗？");
    if (!confirmRestore) return;
    try {
      const result = await window.sfeElectronAPI.restoreDatabase();
      if (result.success) {
        setSuccessMessage('数据库已成功恢复。应用将重新加载数据。');
         if (window.sfeElectronAPI.onDatabaseRestored) { 
            window.sfeElectronAPI.onDatabaseRestored(() => {
                alert("数据库恢复完成，请重新加载或重启应用以查看更改。");
            });
        }
      } else {
        setError(result.error || '数据库恢复失败。');
      }
    } catch (err: any) { console.error("Error restoring database:", err); setError(`恢复数据库时出错: ${err.message}`); }
  };


  if (!isOpen) return null;

  const tabs: { key: TabKey; label: string }[] = [
    { key: 'general', label: '常规与扫描' },
    { key: 'libraries', label: '片库管理' },
    { key: 'homepage', label: '首页显示' },
    { key: 'ai', label: 'AI 服务配置' },
    { key: 'scraper', label: '刮削源优先级' },
    { key: 'media', label: '媒体处理' },
    { key: 'files', label: '文件与路径' },
    { key: 'nfo', label: 'NFO与文件名' },
    { key: 'privacy', label: '隐私模式'},
    { key: 'data', label: '数据管理' },
    { key: 'tools', label: '工具箱' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <GeneralSettingsTab 
                  settings={settings} 
                  handleInputChange={handleInputChange}
                  setSettings={setSettings}
                  pathSep={pathSep}
               />;
      case 'libraries':
        return <LibraryManagementTab />;
      case 'homepage':
        return <HomePageSettingsTab
                  settings={settings}
                  onSettingsChange={setSettings}
               />;
      case 'ai':
        return <AiSettingsTab
                  settings={settings}
                  handleInputChange={handleInputChange}
               />;
      case 'scraper':
        return <ScraperPrioritySettings
                  initialRules={settings.scraperPriorityRules || {}}
                  onRulesChange={(rules) => setSettings(prev => ({ ...prev, scraperPriorityRules: rules }))}
               />;
      case 'media':
        return <MediaSettingsTab 
                  settings={settings}
                  handleInputChange={handleInputChange}
                  setSettings={setSettings}
                  handleBrowsePathForInput={handleBrowsePathForInput}
                  handleBrowseFileForInput={async (settingName) => { 
                      alert("媒体选项卡中的文件浏览功能暂未针对特定输入实现，请手动输入路径。");
                  }}
                  pathSep={pathSep}
               />;
      case 'files':
        return (
          <div className="space-y-8">
            <TripleRepositorySettings />
            <CategoryRulesEditor />
            <DownloadStagingSettings />
          </div>
        );
      case 'nfo':
        return <NfoSettingsTab
                  settings={settings}
                  handleInputChange={handleInputChange}
                  setSettings={setSettings}
                  getUniqueId={getUniqueId}
               />;
      case 'privacy':
        return <PrivacySettingsTab
                  settings={settings}
                  handleInputChange={handleInputChange}
                  tempPrivacyHideTags={tempPrivacyHideTags}
                  setTempPrivacyHideTags={setTempPrivacyHideTags}
               />;
      case 'data':
        return <DataSettingsTab
                  handleBackupDatabase={handleBackupDatabase}
                  handleRestoreDatabase={handleRestoreDatabase}
                />;
      case 'tools':
        return <ToolsSettingsTab
                  onLaunchNfoPlotPolisher={() => {
                    onClose();
                    onLaunchNfoPlotPolisher();
                  }}
                  onLaunchScraperTest={() => {
                    onClose();
                    onLaunchScraperTest?.();
                  }}
                />;
      default:
        return null;
    }
  };


  return (
    <div className="fixed inset-0 z-[70] flex items-center justify-center p-4 bg-black/80 backdrop-blur-md">
      <div 
        className="bg-[#232323] text-neutral-200 rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col border border-[#444444]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-5 border-b border-[#3a3a3a] bg-[#2a2a2a]">
          <h2 className="text-xl font-bold text-[#B8860B]">应用程序设置</h2>
          <button onClick={onClose} className="text-neutral-400 hover:text-white transition-colors p-1 rounded-full hover:bg-[#3a3a3a]" aria-label="关闭设置">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>

        <div className="flex flex-col md:flex-row flex-grow overflow-hidden">
          <nav className="w-full md:w-1/4 bg-[#2a2a2a] border-r border-[#3a3a3a] p-3 space-y-1 settings-scroll-container">
            {tabs.map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`w-full text-left px-3 py-2.5 rounded-md text-sm font-medium transition-colors
                  ${activeTab === tab.key 
                    ? 'bg-[#B8860B] text-white shadow-sm' 
                    : 'text-neutral-300 hover:bg-[#383838] hover:text-neutral-100'
                  }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>

          <div className="w-full md:w-3/4 flex-grow overflow-y-auto p-6 space-y-6 settings-scroll-container">
            {isLoading && !error && <p className="text-center text-neutral-400">正在加载设置...</p>}
            {error && <p className="text-center text-red-400 bg-red-900/30 p-3 rounded-md">{error}</p>}
            
            {!isLoading && renderTabContent()}
          </div>
        </div>

        <div className="p-4 border-t border-[#3a3a3a] bg-[#2a2a2a] flex justify-end items-center space-x-3">
            {error && <p className="text-sm text-red-400 mr-auto animate-pulse">{error}</p>}
            {successMessage && <p className="text-sm text-green-400 mr-auto animate-pulse">{successMessage}</p>}
            <button onClick={onClose} className="button-neutral-app px-4 py-2 text-sm">关闭</button>
            {/* 删除了该死的"保存所有设置"按钮 - 每个设置都会自动保存，这个按钮是多余的麻烦制造者 */}
        </div>
      </div>
    </div>
  );
};