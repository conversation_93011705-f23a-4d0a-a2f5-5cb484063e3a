// 测试 collected_links 表验证的脚本
// 在 Electron 应用的开发者控制台中运行

async function testCollectedLinksTable() {
  try {
    console.log('🔍 开始验证 collected_links 表...');
    
    // 调用验证 API
    const result = await window.sfeElectronAPI.verifyCollectedLinksTable();
    
    if (result.success) {
      console.log('✅ collected_links 表验证成功！');
      console.log('📋 表结构:');
      
      result.columns.forEach((col, index) => {
        const notNull = col.notNull ? ' NOT NULL' : '';
        const defaultVal = col.defaultValue ? ` DEFAULT ${col.defaultValue}` : '';
        console.log(`  ${index + 1}. ${col.name}: ${col.type}${notNull}${defaultVal}`);
      });
      
      console.log('\n🔍 索引:');
      result.indexes.forEach((idx, index) => {
        console.log(`  ${index + 1}. ${idx}`);
      });
      
      console.log('\n🎉 数据库扩展完成！Collector 模块可以开始使用 collected_links 表了。');
      
      // 显示表的用途说明
      console.log('\n📖 表字段说明:');
      console.log('  - id: 主键，自动递增');
      console.log('  - source_forum: 来源论坛标识');
      console.log('  - post_url: 原始帖子URL (唯一约束)');
      console.log('  - post_title: 帖子标题');
      console.log('  - nfoId: 提取的番号，用于与电影库联动');
      console.log('  - magnet_link: 磁力链接');
      console.log('  - ed2k_link: ed2k链接');
      console.log('  - attachment_url: 附件下载链接');
      console.log('  - decompression_password: 解压密码');
      console.log('  - collection_date: 搜集日期');
      console.log('  - download_status: 下载状态 (pending/downloading/completed/failed)');
      
    } else {
      console.error('❌ collected_links 表验证失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
  }
}

// 导出函数到全局作用域
window.testCollectedLinksTable = testCollectedLinksTable;

console.log(`
🛠️ collected_links 表验证工具已加载！

使用方法:
testCollectedLinksTable() - 验证 collected_links 表是否正确创建

立即运行: testCollectedLinksTable()
`);

// 自动运行验证
testCollectedLinksTable();
