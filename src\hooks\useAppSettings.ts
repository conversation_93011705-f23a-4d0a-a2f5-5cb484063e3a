// soul-forge-electron/src/hooks/useAppSettings.ts
import { useState, useEffect, useCallback } from 'react';
import { AppSettings, SettingsResult } from '../types';

const defaultSettings: AppSettings = {
  defaultScanPaths: [],
  pythonExecutablePath: null,
  defaultSortField: 'releaseDate',
  defaultSortOrder: 'desc',
  defaultPageSize: 50,
  defaultViewMode: 'card',
  customDefaultCoverDataUrl: null,
  defaultActorAvatarDataUrl: null,
  ffmpegPath: null,
  ffprobePath: null,
  snapshotCachePath: null,
  filenameSuffixRules: [],
  presetVersionCategories: [],
  filenameRenameTemplate: "{original_basename} [{nfoId}] [{year}].{extension}",
  snapshotQuality: 'hd_640p',
  autoUpdateNfoWatchedRating: true,
  autoCreateNfoOnSave: true,
  aiProvider: null,
  customGptEndpoint: null,
  customGptApiKey: null,
  customGptModel: null,
  grokApiKey: null,
  privacyModeEnabled: false,
  privacyModePassword: null,
  privacyHideTags: [],
  avatarDataSourceType: 'none',
  actorAvatarLibraryPath: null,
  localFileTreePath: null,
  avatarPreferAiFixed: true,
  remoteGfriendsFiletreeUrl: "https://cdn.jsdelivr.net/gh/xinxin8816/gfriends/Filetree.json",
  remoteGfriendsImageBaseUrl: "https://cdn.jsdelivr.net/gh/xinxin8816/gfriends/Content/",
  imagesGloballyVisible: true,
  customSfwPlaceholderDataUrl: null,

  // 三位一体分离式存储设置
  assetsPath: null,   // 元数据仓库
  trailersPath: null, // 预告片仓库
  mediaPath: null,    // 正片仓库
};

export function useAppSettings() {
  const [appSettings, setAppSettings] = useState<AppSettings>(defaultSettings);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [settingsError, setSettingsError] = useState<string | null>(null);

  const loadSettings = useCallback(async () => {
    setIsLoadingSettings(true);
    setSettingsError(null);
    try {
      const fetchedSettings = await window.sfeElectronAPI.getSettings();
      setAppSettings({ ...defaultSettings, ...fetchedSettings });
    } catch (e: any) {
      setSettingsError(`加载设置失败: ${e.message}`);
      console.error("Error loading app settings:", e);
      setAppSettings(defaultSettings); 
    } finally {
      setIsLoadingSettings(false);
    }
  }, []);

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  const saveSettings = useCallback(async (newSettingsToSave: Partial<AppSettings>): Promise<SettingsResult> => {
    try {
      // Ensure we're merging with the most current state before saving, 
      // especially if only partial settings are passed.
      const currentMergedSettings = { ...appSettings, ...newSettingsToSave };
      const result = await window.sfeElectronAPI.saveSettings(currentMergedSettings as AppSettings);
      if (result.success && result.newSettings) {
        setAppSettings(result.newSettings); // Update state with settings returned from main process
      } else if (result.success) { // Fallback if newSettings isn't returned but save was ok
        setAppSettings(currentMergedSettings as AppSettings);
      }
      return result;
    } catch (e: any) {
      console.error("Error saving app settings:", e);
      return { success: false, error: `保存设置时发生前端错误: ${e.message}` };
    }
  }, [appSettings]); // appSettings dependency ensures currentMergedSettings is up-to-date

  return {
    appSettings,
    isLoadingSettings,
    settingsError,
    loadSettings,
    saveSettings,
    setAppSettingsDirectly: setAppSettings, 
  };
}
