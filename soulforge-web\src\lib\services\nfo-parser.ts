import { promises as fs } from 'fs';
import { parseString } from 'xml2js';
import { Movie } from '@/lib/types';

export interface NFOData {
  title?: string;
  originalTitle?: string;
  plot?: string;
  year?: number;
  releaseDate?: string;
  runtime?: number;
  studio?: string;
  director?: string;
  genres?: string[];
  actors?: string[];
  tags?: string[];
  posterUrl?: string;
  trailerUrl?: string;
  rating?: number;
  mpaa?: string;
  country?: string;
  language?: string;
}

export class NFOParser {
  static async parseNFOFile(filePath: string): Promise<NFOData> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return this.parseNFOContent(content);
    } catch (error) {
      throw new Error(`Failed to read NFO file: ${error}`);
    }
  }

  static async parseNFOContent(content: string): Promise<NFOData> {
    // Clean up the content
    const cleanContent = this.cleanNFOContent(content);
    
    try {
      // Try XML parsing first
      return await this.parseXMLContent(cleanContent);
    } catch (xmlError) {
      // Fallback to regex parsing
      console.warn('XML parsing failed, using regex fallback:', xmlError);
      return this.parseWithRegex(cleanContent);
    }
  }

  private static cleanNFOContent(content: string): string {
    // Remove BOM if present
    content = content.replace(/^\uFEFF/, '');
    
    // Fix common XML issues
    content = content.replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;');
    
    // Ensure proper XML structure
    if (!content.trim().startsWith('<')) {
      content = `<movie>${content}</movie>`;
    }
    
    return content;
  }

  private static async parseXMLContent(content: string): Promise<NFOData> {
    return new Promise((resolve, reject) => {
      parseString(content, { 
        explicitArray: false,
        ignoreAttrs: false,
        trim: true,
        normalize: true,
      }, (err, result) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const movieData = result.movie || result;
          const nfoData: NFOData = {};

          // Basic fields
          if (movieData.title) nfoData.title = String(movieData.title);
          if (movieData.originaltitle) nfoData.originalTitle = String(movieData.originaltitle);
          if (movieData.plot) nfoData.plot = String(movieData.plot);
          if (movieData.year) nfoData.year = parseInt(String(movieData.year));
          if (movieData.releasedate) nfoData.releaseDate = String(movieData.releasedate);
          if (movieData.runtime) nfoData.runtime = parseInt(String(movieData.runtime));
          if (movieData.studio) nfoData.studio = String(movieData.studio);
          if (movieData.director) nfoData.director = String(movieData.director);
          if (movieData.poster) nfoData.posterUrl = String(movieData.poster);
          if (movieData.trailer) nfoData.trailerUrl = String(movieData.trailer);
          if (movieData.mpaa) nfoData.mpaa = String(movieData.mpaa);
          if (movieData.country) nfoData.country = String(movieData.country);

          // Rating
          if (movieData.rating) {
            const rating = parseFloat(String(movieData.rating));
            if (!isNaN(rating)) nfoData.rating = rating;
          }

          // Genres
          if (movieData.genre) {
            if (Array.isArray(movieData.genre)) {
              nfoData.genres = movieData.genre.map(g => String(g));
            } else {
              nfoData.genres = [String(movieData.genre)];
            }
          }

          // Tags
          if (movieData.tag) {
            if (Array.isArray(movieData.tag)) {
              nfoData.tags = movieData.tag.map(t => String(t));
            } else {
              nfoData.tags = [String(movieData.tag)];
            }
          }

          // Actors
          if (movieData.actor) {
            const actors = Array.isArray(movieData.actor) ? movieData.actor : [movieData.actor];
            nfoData.actors = actors.map(actor => {
              if (typeof actor === 'string') return actor;
              if (actor.name) return String(actor.name);
              return '';
            }).filter(Boolean);
          }

          resolve(nfoData);
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  private static parseWithRegex(content: string): NFOData {
    const nfoData: NFOData = {};

    // Helper function to extract text between tags
    const extractTag = (tagName: string): string | undefined => {
      const regex = new RegExp(`<${tagName}[^>]*>(.*?)<\/${tagName}>`, 'is');
      const match = content.match(regex);
      return match ? match[1].trim() : undefined;
    };

    // Helper function to extract multiple tags
    const extractMultipleTags = (tagName: string): string[] => {
      const regex = new RegExp(`<${tagName}[^>]*>(.*?)<\/${tagName}>`, 'gis');
      const matches = content.matchAll(regex);
      return Array.from(matches).map(match => match[1].trim()).filter(Boolean);
    };

    // Extract basic fields
    const title = extractTag('title');
    if (title) nfoData.title = title;

    const originalTitle = extractTag('originaltitle');
    if (originalTitle) nfoData.originalTitle = originalTitle;

    const plot = extractTag('plot');
    if (plot) nfoData.plot = plot;

    const year = extractTag('year');
    if (year) {
      const yearNum = parseInt(year);
      if (!isNaN(yearNum)) nfoData.year = yearNum;
    }

    const releaseDate = extractTag('releasedate');
    if (releaseDate) nfoData.releaseDate = releaseDate;

    const runtime = extractTag('runtime');
    if (runtime) {
      const runtimeNum = parseInt(runtime);
      if (!isNaN(runtimeNum)) nfoData.runtime = runtimeNum;
    }

    const studio = extractTag('studio');
    if (studio) nfoData.studio = studio;

    const director = extractTag('director');
    if (director) nfoData.director = director;

    const poster = extractTag('poster');
    if (poster) nfoData.posterUrl = poster;

    const trailer = extractTag('trailer');
    if (trailer) nfoData.trailerUrl = trailer;

    const mpaa = extractTag('mpaa');
    if (mpaa) nfoData.mpaa = mpaa;

    const country = extractTag('country');
    if (country) nfoData.country = country;

    const rating = extractTag('rating');
    if (rating) {
      const ratingNum = parseFloat(rating);
      if (!isNaN(ratingNum)) nfoData.rating = ratingNum;
    }

    // Extract arrays
    const genres = extractMultipleTags('genre');
    if (genres.length > 0) nfoData.genres = genres;

    const tags = extractMultipleTags('tag');
    if (tags.length > 0) nfoData.tags = tags;

    // Extract actors (more complex)
    const actorMatches = content.matchAll(/<actor[^>]*>(.*?)<\/actor>/gis);
    const actors: string[] = [];
    
    for (const match of actorMatches) {
      const actorContent = match[1];
      const nameMatch = actorContent.match(/<name[^>]*>(.*?)<\/name>/is);
      if (nameMatch) {
        actors.push(nameMatch[1].trim());
      }
    }
    
    if (actors.length > 0) nfoData.actors = actors;

    return nfoData;
  }

  static async writeNFOFile(filePath: string, data: NFOData): Promise<void> {
    const xml = this.generateNFOXML(data);
    await fs.writeFile(filePath, xml, 'utf-8');
  }

  private static generateNFOXML(data: NFOData): string {
    const escapeXML = (str: string): string => {
      return str
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    };

    let xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<movie>\n';

    if (data.title) xml += `  <title>${escapeXML(data.title)}</title>\n`;
    if (data.originalTitle) xml += `  <originaltitle>${escapeXML(data.originalTitle)}</originaltitle>\n`;
    if (data.plot) xml += `  <plot>${escapeXML(data.plot)}</plot>\n`;
    if (data.year) xml += `  <year>${data.year}</year>\n`;
    if (data.releaseDate) xml += `  <releasedate>${data.releaseDate}</releasedate>\n`;
    if (data.runtime) xml += `  <runtime>${data.runtime}</runtime>\n`;
    if (data.studio) xml += `  <studio>${escapeXML(data.studio)}</studio>\n`;
    if (data.director) xml += `  <director>${escapeXML(data.director)}</director>\n`;
    if (data.posterUrl) xml += `  <poster>${escapeXML(data.posterUrl)}</poster>\n`;
    if (data.trailerUrl) xml += `  <trailer>${escapeXML(data.trailerUrl)}</trailer>\n`;
    if (data.rating) xml += `  <rating>${data.rating}</rating>\n`;
    if (data.mpaa) xml += `  <mpaa>${escapeXML(data.mpaa)}</mpaa>\n`;
    if (data.country) xml += `  <country>${escapeXML(data.country)}</country>\n`;

    // Genres
    if (data.genres) {
      data.genres.forEach(genre => {
        xml += `  <genre>${escapeXML(genre)}</genre>\n`;
      });
    }

    // Tags
    if (data.tags) {
      data.tags.forEach(tag => {
        xml += `  <tag>${escapeXML(tag)}</tag>\n`;
      });
    }

    // Actors
    if (data.actors) {
      data.actors.forEach(actor => {
        xml += `  <actor>\n    <name>${escapeXML(actor)}</name>\n  </actor>\n`;
      });
    }

    xml += '</movie>';
    return xml;
  }

  static convertNFOToMovie(nfoData: NFOData): Partial<Movie> {
    return {
      title: nfoData.title,
      originalTitle: nfoData.originalTitle,
      plot: nfoData.plot,
      year: nfoData.year,
      releaseDate: nfoData.releaseDate,
      runtime: nfoData.runtime,
      studio: nfoData.studio,
      director: nfoData.director,
      posterUrl: nfoData.posterUrl,
      trailerUrl: nfoData.trailerUrl,
      genres: nfoData.genres,
      actors: nfoData.actors,
      tags: nfoData.tags,
    };
  }
}
