/**
 * 论坛A (x1080x) 采集器
 * 
 * 继承自BaseCollector，实现x1080x论坛的具体采集逻辑
 * 包含已验证的稳定功能和修复
 */

const BaseCollector = require('./BaseCollector');
const NodeNfoParser = require('../services/nodeNfoParser');
const path = require('path');
const fs = require('fs');

class ForumACollector extends BaseCollector {
  constructor(config) {
    super(config);
    this.forumName = 'x1080x';
    
    // 论坛A特有的配置
    this.currentBoardId = null;
    this.currentBoardConfig = null;
    this.currentPageUrl = null;
  }

  /**
   * 执行抓取逻辑 - 从已连接的页面开始抓取（支持多页面和日期限制）
   */
  async executeScrapingLogic(page, siteProfile, targetUrl, options) {
    this.log.info(`[${this.forumName}] 开始执行多页面抓取逻辑`);
    const results = [];

    try {
      // 🔧 帖子排序修正：自动添加排序参数
      let finalUrl = targetUrl;
      if (!finalUrl.includes('orderby=dateline')) {
        const separator = finalUrl.includes('?') ? '&' : '?';
        finalUrl += `${separator}filter=author&orderby=dateline`;
        this.log.info(`[${this.forumName}] 🔧 自动添加排序参数: ${finalUrl}`);
      }

      // 智能检测起始页面
      const detectedStartPage = this.detectStartPageFromUrl(finalUrl);
      const startPage = options.startPage || detectedStartPage;

      this.updateTaskStatus('refreshing', `准备从第 ${startPage} 页开始抓取...`);

      // 🔧 设置当前页面URL，用于板块名称提取
      this.currentPageUrl = finalUrl;
      this.log.info(`[${this.forumName}] 🔧 设置当前页面URL: ${this.currentPageUrl}`);

      // 🔧 智能标记与分类：从targetUrl中提取boardId
      this.extractBoardInfo(finalUrl);

      // 如果检测到的起始页面不是1，保持当前URL；否则导航到第一页
      if (startPage === 1 && detectedStartPage > 1) {
        // 用户想从第一页开始，但当前在其他页面
        let firstPageUrl = finalUrl.replace(/[&?]page=\d+/g, '').replace(/[&?]extra=[^&]*/g, '');
        // 确保第一页也有排序参数
        if (!firstPageUrl.includes('orderby=dateline')) {
          const separator = firstPageUrl.includes('?') ? '&' : '?';
          firstPageUrl += `${separator}filter=author&orderby=dateline`;
        }
        this.log.info(`[${this.forumName}] 导航到第一页: ${firstPageUrl}`);
        await page.goto(firstPageUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        await page.waitForTimeout(2000);
        // 🔧 更新当前页面URL
        this.currentPageUrl = firstPageUrl;
        this.log.info(`[${this.forumName}] 🔧 更新当前页面URL为第一页: ${this.currentPageUrl}`);
      } else {
        // 从当前页面开始抓取
        this.log.info(`[${this.forumName}] 从第 ${startPage} 页开始抓取: ${finalUrl}`);

        // 确保页面在正确的URL上
        const currentUrl = page.url();
        if (currentUrl !== finalUrl) {
          this.log.info(`[${this.forumName}] 当前页面URL不匹配，导航到目标URL: ${finalUrl}`);
          await page.goto(finalUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        }

        await page.waitForTimeout(1000); // 确保页面稳定
      }

      const maxPages = options.maxPages || 10; // 从选项获取要抓取的页面数量，默认为10
      const scrapeDays = options.scrapeDays || 0; // 获取天数设置，0为不限制

      let keepScraping = true;
      let currentPageNum = startPage;
      let pagesProcessed = 0; // 已处理的页面数量

      // 主循环，处理翻页
      while (keepScraping && pagesProcessed < maxPages) {
        this.updateTaskStatus('scraping', `正在分析第 ${currentPageNum} 页...`);

        try {
          // 步骤一：抓取当前页面的所有帖子URL
          this.log.info(`[${this.forumName}] 使用选择器: ${siteProfile.postLinkSelector}`);

          // 添加页面调试信息
          const pageInfo = await page.evaluate(() => {
            return {
              url: window.location.href,
              title: document.title,
              bodyText: document.body ? document.body.textContent.substring(0, 200) : '无body',
              hasLoginIndicator: document.querySelector('a[href*="action=logout"]') ? true : false
            };
          });
          this.log.info(`[${this.forumName}] 页面信息:`, pageInfo);

          let postLinksOnPage = await page.evaluate((selector) => {
            console.log(`[Browser] 查找选择器: ${selector}`);
            const elements = document.querySelectorAll(selector);
            console.log(`[Browser] 找到 ${elements.length} 个元素`);

            const links = [];
            elements.forEach((element, index) => {
              const href = element.href;
              if (href) {
                console.log(`[Browser] 元素 ${index + 1}: ${href}`);
                links.push(href);
              }
            });

            return links;
          }, siteProfile.postLinkSelector);

          this.log.info(`[${this.forumName}] 第 ${currentPageNum} 页发现 ${postLinksOnPage.length} 个帖子链接`);

          if (postLinksOnPage.length === 0) {
            this.log.warn(`[${this.forumName}] 第 ${currentPageNum} 页没有找到帖子链接，可能已到最后一页`);
            break;
          }

          // 步骤二：遍历每个帖子链接，抓取详细信息
          for (let i = 0; i < postLinksOnPage.length; i++) {
            if (this.shouldStop) {
              this.log.info(`[${this.forumName}] 收到停止指令，中断任务`);
              keepScraping = false;
              break;
            }

            const postUrl = postLinksOnPage[i];
            this.updateTaskStatus('scraping', `正在处理帖子 ${i + 1}/${postLinksOnPage.length} (第${currentPageNum}页)`);

            try {
              // 导航到帖子页面
              await page.goto(postUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
              await page.waitForTimeout(1000);

              // 解析帖子内容
              const postData = await this.parsePostContent(page, siteProfile, postUrl);
              if (postData) {
                // 检查日期限制
                if (!this.shouldContinueCollection(postData, options)) {
                  keepScraping = false;
                  break;
                }

                // 🔧 修复：检查是否有有效的数据（磁力链接、ed2k链接或附件链接）
                const hasValidData = postData.magnetLink || postData.ed2kLink || postData.attachmentUrl;

                // 🔧 修复：默认记录所有有效数据的帖子，不再依赖下载成功与否
                let shouldRecord = hasValidData; // 只要有有效数据就记录

                if (!hasValidData) {
                  this.log.info(`[${this.forumName}] 无有效数据: ${postData.postTitle} - 没有找到磁力链接、ed2k链接或附件链接`);
                  // 🔧 新逻辑：即使没有有效数据，也设置状态并生成档案文件
                  postData.downloadStatus = 'no_attachment';
                  shouldRecord = false; // 不记录到数据库，但会生成档案文件
                } else {
                  this.log.info(`[${this.forumName}] 发现有效数据: ${postData.postTitle}`);
                  this.log.info(`[${this.forumName}] 🔧 将记录到数据库（无论下载是否成功）`);
                }

                // 尝试下载附件（如果启用了下载功能）
                if (this.workspacePath && postData.attachmentUrl) {
                  try {
                    await this.downloadAttachments(page, postData, siteProfile);
                    this.stats.successCount++;
                  } catch (downloadError) {
                    this.log.warn(`[${this.forumName}] 下载失败: ${downloadError.message}`);
                    this.stats.failedCount++;
                    
                    // 检查是否需要停止任务
                    if (downloadError.message.includes('下载次数已达上限') || 
                        downloadError.message.includes('需要人机验证')) {
                      throw downloadError;
                    }
                  }
                }

                // 记录有效数据
                if (shouldRecord) {
                  results.push(postData);
                  this.stats.totalProcessed++;
                }
              }

            } catch (error) {
              this.log.warn(`[${this.forumName}] 跳过帖子 ${postUrl}: ${error.message}`);
              this.stats.failedCount++;
            }

            // 延迟
            if (options.delay && options.delay > 0) {
              await this.delay(options.delay);
            }
          }

          // 步骤三：翻页到下一页
          if (keepScraping && pagesProcessed + 1 < maxPages) {
            const hasNextPage = await this.goToNextPage(page, siteProfile);
            if (hasNextPage) {
              currentPageNum++;
              pagesProcessed++;
              await page.waitForTimeout(2000); // 等待页面加载
            } else {
              this.log.info(`[${this.forumName}] 已到最后一页，停止抓取`);
              break;
            }
          } else {
            pagesProcessed++;
            break;
          }

        } catch (error) {
          this.log.error(`[${this.forumName}] 处理第 ${currentPageNum} 页时出错: ${error.message}`);
          if (error.message.includes('下载次数已达上限') || 
              error.message.includes('需要人机验证')) {
            throw error;
          }
          break;
        }
      }

      this.log.info(`[${this.forumName}] 多页面抓取任务完成，共处理 ${results.length} 个帖子`);
      return results;

    } catch (error) {
      this.log.error(`[${this.forumName}] 抓取逻辑执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从URL中智能检测起始页面
   */
  detectStartPageFromUrl(url) {
    try {
      const pageMatch = url.match(/[&?]page=(\d+)/);
      if (pageMatch) {
        const page = parseInt(pageMatch[1]);
        this.log.info(`[${this.forumName}] 🔍 从URL检测到起始页面: ${page}`);
        return page;
      }
      return 1;
    } catch (error) {
      this.log.warn(`[${this.forumName}] 检测起始页面失败: ${error.message}`);
      return 1;
    }
  }

  /**
   * 提取板块信息
   */
  extractBoardInfo(url) {
    try {
      const fidMatch = url.match(/[&?]fid=(\d+)/);
      if (fidMatch) {
        this.currentBoardId = fidMatch[1];
        this.currentBoardConfig = this.siteProfile.boards?.[this.currentBoardId] || null;

        if (this.currentBoardConfig) {
          this.log.info(`[${this.forumName}] 🔧 检测到板块: ${this.currentBoardConfig.name} (ID: ${this.currentBoardId})`);
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 提取板块信息失败: ${error.message}`);
    }
  }

  /**
   * 解析帖子内容 - 增强版，支持全信息结构化归档
   */
  async parsePostContent(page, siteProfile, postUrl) {
    try {
      // 抓取帖子标题
      let postTitle = '';
      try {
        await page.waitForSelector(siteProfile.postTitleSelector, { timeout: 5000 });
        postTitle = await page.locator(siteProfile.postTitleSelector).first().textContent();
        postTitle = postTitle ? postTitle.trim() : '';
      } catch (error) {
        postTitle = await page.title() || `帖子`;
      }

      // 🔧 新增：提取帖子正文内容进行元数据解析
      let postBodyText = '';
      let extractedMetadata = {};
      let cloudLinks = []; // 🔧 新增：网盘链接数组

      try {
        if (siteProfile.postBodyContainerSelector) {
          this.log.info(`[${this.forumName}] 🔍 开始提取帖子正文内容...`);
          await page.waitForSelector(siteProfile.postBodyContainerSelector, { timeout: 5000 });

          // 获取主内容区域的纯文本
          const bodyElements = await page.locator(siteProfile.postBodyContainerSelector).all();
          if (bodyElements.length > 0) {
            postBodyText = await bodyElements[0].innerText();
            this.log.info(`[${this.forumName}] 📄 提取到正文内容长度: ${postBodyText.length} 字符`);

            // 解析元数据
            extractedMetadata = this.extractMetadataFromText(postBodyText);
            this.log.info(`[${this.forumName}] 📊 提取的元数据:`, extractedMetadata);

            // 🔧 新增：提取网盘链接与提取码
            cloudLinks = this.extractCloudLinksWithCodes(postBodyText);
            if (cloudLinks.length > 0) {
              this.log.info(`[${this.forumName}] 🌐 提取到 ${cloudLinks.length} 个网盘链接`);
              cloudLinks.forEach((link, index) => {
                this.log.info(`[${this.forumName}] 🌐 网盘链接 ${index + 1}: ${link.url} (提取码: ${link.code})`);
              });
            }
          }
        }
      } catch (error) {
        this.log.warn(`[${this.forumName}] ⚠️ 提取正文内容失败: ${error.message}`);
      }

      // 提取NFO ID（优先使用从正文提取的品番，其次使用标题）
      let nfoId = extractedMetadata.nfoId || NodeNfoParser.extractJavIdFromFilename(postTitle);

      // 抓取磁力链接 - 支持从li标签内提取，修复截断问题
      let magnetLinks = [];
      try {
        const magnetElements = await page.locator(siteProfile.magnetLinkSelector).all();
        this.log.info(`[${this.forumName}] 🧲 查找磁力链接，使用选择器: ${siteProfile.magnetLinkSelector}`);
        this.log.info(`[${this.forumName}] 🧲 找到 ${magnetElements.length} 个磁力链接元素`);

        for (const element of magnetElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('magnet:')) {
              // 使用更强大的正则表达式提取完整磁力链接
              const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
              if (magnetMatches) {
                magnetMatches.forEach(link => {
                  const cleanLink = link.trim();
                  magnetLinks.push(cleanLink);
                  this.log.info(`[${this.forumName}] 🧲 从li标签HTML提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('magnet:')) {
              magnetLinks.push(href.trim());
              this.log.info(`[${this.forumName}] 🧲 从href属性提取磁力链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('magnet:')) {
                const magnetMatches = html.match(/(magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s<>"]*)/g);
                if (magnetMatches) {
                  magnetMatches.forEach(link => {
                    const cleanLink = link.trim();
                    magnetLinks.push(cleanLink);
                    this.log.info(`[${this.forumName}] 🧲 从HTML内容提取磁力链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        this.log.info(`[${this.forumName}] 🧲 总共提取到 ${magnetLinks.length} 个磁力链接`);
      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法获取磁力链接: ${error.message}`);
      }

      // 抓取ED2K链接 - 支持从li标签内提取，修复截断问题
      let ed2kLinks = [];
      try {
        const ed2kElements = await page.locator(siteProfile.ed2kLinkSelector).all();
        this.log.info(`[${this.forumName}] 🔗 查找ED2K链接，使用选择器: ${siteProfile.ed2kLinkSelector}`);
        this.log.info(`[${this.forumName}] 🔗 找到 ${ed2kElements.length} 个ED2K链接元素`);

        for (const element of ed2kElements) {
          // 检查是否是li标签（98堂的格式）
          const tagName = await element.evaluate(el => el.tagName.toLowerCase());

          if (tagName === 'li') {
            // 🔧 修复截断问题：使用innerHTML获取完整内容
            const html = await element.innerHTML();
            if (html && html.includes('ed2k:')) {
              // 使用更强大的正则表达式提取完整ed2k链接
              const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/g);
              if (ed2kMatches) {
                ed2kMatches.forEach(link => {
                  const cleanLink = link.trim();
                  ed2kLinks.push(cleanLink);
                  this.log.info(`[${this.forumName}] 🔗 从li标签HTML提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                });
              }
            }
          } else {
            // 传统方式：直接从href属性或HTML内容获取
            const href = await element.getAttribute('href');
            if (href && href.startsWith('ed2k:')) {
              ed2kLinks.push(href.trim());
              this.log.info(`[${this.forumName}] 🔗 从href属性提取ED2K链接: ${href.substring(0, 50)}...`);
            } else {
              // 🔧 修复截断问题：使用innerHTML而不是textContent
              const html = await element.innerHTML();
              if (html && html.includes('ed2k:')) {
                const ed2kMatches = html.match(/(ed2k:\/\/\|file\|.*?\|\/)/g);
                if (ed2kMatches) {
                  ed2kMatches.forEach(link => {
                    const cleanLink = link.trim();
                    ed2kLinks.push(cleanLink);
                    this.log.info(`[${this.forumName}] 🔗 从HTML内容提取ED2K链接: ${cleanLink.substring(0, 50)}...`);
                  });
                }
              }
            }
          }
        }
        this.log.info(`[${this.forumName}] 🔗 总共提取到 ${ed2kLinks.length} 个ED2K链接`);
      } catch (error) {
        this.log.warn(`[${this.forumName}] 无法获取ED2K链接: ${error.message}`);
      }

      // 继续其他内容...
      return this.buildPostData(postUrl, postTitle, nfoId, magnetLinks, ed2kLinks, extractedMetadata, cloudLinks, postBodyText, page, siteProfile);

    } catch (error) {
      this.log.error(`[${this.forumName}] 解析帖子内容失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 构建帖子数据对象
   */
  async buildPostData(postUrl, postTitle, nfoId, magnetLinks, ed2kLinks, extractedMetadata, cloudLinks, postBodyText, page, siteProfile) {
    // 抓取附件链接
    let attachmentUrls = [];
    try {
      const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();
      this.log.info(`[${this.forumName}] 📎 查找附件链接，使用选择器: ${siteProfile.attachmentUrlSelector}`);
      this.log.info(`[${this.forumName}] 📎 找到 ${attachmentElements.length} 个附件元素`);

      for (const element of attachmentElements) {
        const href = await element.getAttribute('href');
        if (href) {
          const absoluteUrl = href.startsWith('http') ? href : new URL(href, page.url()).toString();
          attachmentUrls.push(absoluteUrl);
          this.log.info(`[${this.forumName}] 📎 提取附件链接: ${absoluteUrl}`);
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 无法获取附件链接: ${error.message}`);
    }

    // 抓取解压密码
    let decompressionPassword = '';
    try {
      const passwordElements = await page.locator(siteProfile.passwordSelector).all();
      for (const element of passwordElements) {
        const text = await element.textContent();
        if (text) {
          // 密码提取逻辑
          const passwordPatterns = [
            /密码[：:]\s*([^\s\n\r]+)/i,
            /解压密码[：:]\s*([^\s\n\r]+)/i,
            /password[：:]\s*([^\s\n\r]+)/i,
            /pwd[：:]\s*([^\s\n\r]+)/i,
            /pass[：:]\s*([^\s\n\r]+)/i,
            /【解压密码】[：:]\s*([^\s\n\r]+)/i,
            /【密码】[：:]\s*([^\s\n\r]+)/i,
            /\[解压密码\][：:]\s*([^\s\n\r]+)/i,
            /\[密码\][：:]\s*([^\s\n\r]+)/i
          ];

          for (const pattern of passwordPatterns) {
            const match = text.match(pattern);
            if (match && match[1]) {
              decompressionPassword = match[1].trim();
              decompressionPassword = decompressionPassword.replace(/[，。！？；：""''（）【】\[\]]/g, '');
              break;
            }
          }

          if (decompressionPassword) break;
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 无法获取解压密码: ${error.message}`);
    }

    // 抓取预览图
    let previewImageUrl = '';
    try {
      const previewElements = await page.locator(siteProfile.previewImageSelector).all();
      if (previewElements.length > 0) {
        const src = await previewElements[0].getAttribute('src');
        if (src) {
          previewImageUrl = src.startsWith('http') ? src : new URL(src, page.url()).toString();
        }
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 获取预览图失败: ${error.message}`);
    }

    // 获取帖子日期
    const postDate = await this.extractPostDate(page);

    // 检测回帖可见状态
    let status = 'normal';
    try {
      const hasReplyButton = siteProfile.replyToViewSelector &&
        await page.locator(siteProfile.replyToViewSelector).count() > 0;
      const hasDownloadLinks = magnetLinks.length > 0 || ed2kLinks.length > 0 || attachmentUrls.length > 0;

      if (hasReplyButton && !hasDownloadLinks) {
        status = 'requires_reply';
        this.log.info(`[${this.forumName}] 🔒 检测到回帖可见状态：${postTitle}`);
      }
    } catch (error) {
      this.log.warn(`[${this.forumName}] 检测回帖可见状态时出错: ${error.message}`);
    }

    // 构建板块信息
    const boardInfo = this.currentBoardConfig ? {
      boardId: this.currentBoardId,
      boardName: this.currentBoardConfig.name,
      boardTags: this.currentBoardConfig.tags
    } : null;

    return {
      postUrl,
      postTitle,
      nfoId,
      magnetLink: magnetLinks.join('\n'),
      ed2kLink: ed2kLinks.join('\n'),
      attachmentUrl: attachmentUrls.join('\n'),
      decompressionPassword,
      previewImageUrl,
      postDate,
      collectionDate: new Date().toISOString(),
      downloadStatus: 'pending',
      errorMessage: null,
      downloadPath: null,
      status,
      boardInfo,
      fileSize: extractedMetadata.fileSize || null,
      performers: extractedMetadata.performers || null,
      studio: extractedMetadata.studio || null,
      duration: extractedMetadata.duration || null,
      cloudLinks: cloudLinks,
      postBodyText: postBodyText.substring(0, 1000) // 限制长度
    };
  }

  /**
   * 提取帖子日期
   */
  async extractPostDate(page) {
    try {
      // 尝试从帖子页面提取日期
      const postRow = await page.locator(`a[href="${page.url()}"]`).locator('xpath=ancestor::tr[1]').first();
      const dateElement = postRow.locator(this.siteProfile.postDateSelector).first();

      // 优先尝试从title属性读取日期
      let dateText = await dateElement.getAttribute('title');

      // 如果title属性不存在，则读取元素的文本内容
      if (!dateText) {
        dateText = await dateElement.textContent();
      }

      return this.parseForumDate(dateText);
    } catch (error) {
      this.log.warn(`[${this.forumName}] 获取帖子日期失败: ${error.message}`);
      return new Date();
    }
  }

  /**
   * 解析论坛日期格式
   */
  parseForumDate(dateText) {
    if (!dateText) return new Date();

    const cleanText = dateText.trim();

    // 匹配 YYYY-MM-DD 格式
    const match = cleanText.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (match) {
      return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
    }

    // 匹配其他常见格式，如 YYYY/MM/DD
    const match2 = cleanText.match(/(\d{4})\/(\d{1,2})\/(\d{1,2})/);
    if (match2) {
      return new Date(parseInt(match2[1]), parseInt(match2[2]) - 1, parseInt(match2[3]));
    }

    // 解析相对日期格式
    const now = new Date();

    const hoursMatch = cleanText.match(/(\d+)\s*小时前/);
    if (hoursMatch) {
      const hours = parseInt(hoursMatch[1]);
      return new Date(now.getTime() - hours * 60 * 60 * 1000);
    }

    const minutesMatch = cleanText.match(/(\d+)\s*分钟前/);
    if (minutesMatch) {
      const minutes = parseInt(minutesMatch[1]);
      return new Date(now.getTime() - minutes * 60 * 1000);
    }

    const daysMatch = cleanText.match(/(\d+)\s*天前/);
    if (daysMatch) {
      const days = parseInt(daysMatch[1]);
      return new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    }

    if (cleanText.includes('昨天')) {
      return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    if (cleanText.includes('前天')) {
      return new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
    }

    if (cleanText.includes('今天')) {
      return new Date(now.getFullYear(), now.getMonth(), now.getDate());
    }

    return new Date();
  }

  /**
   * 翻页到下一页
   */
  async goToNextPage(page, siteProfile) {
    try {
      // 查找下一页链接
      const nextPageElement = await page.locator(siteProfile.nextPageSelector).first();
      if (await nextPageElement.count() > 0) {
        await nextPageElement.click();
        await page.waitForLoadState('domcontentloaded');
        return true;
      }
      return false;
    } catch (error) {
      this.log.warn(`[${this.forumName}] 翻页失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 下载附件 - 论坛A的稳定实现
   */
  async downloadAttachments(page, postData, siteProfile) {
    const { postTitle, decompressionPassword, attachmentUrl } = postData;

    if (!attachmentUrl) {
      this.log.info(`[${this.forumName}] 跳过下载: 没有附件链接`);
      return { success: false, message: '没有附件链接' };
    }

    const attachmentUrls = attachmentUrl.split('\n').filter(url => url.trim());
    this.log.info(`[${this.forumName}] 发现 ${attachmentUrls.length} 个附件链接`);

    // 🔧 修正：使用全局统一的attachments目录
    const attachmentsDir = path.join(this.workspacePath, 'attachments');

    // 确保目录存在
    if (!fs.existsSync(attachmentsDir)) {
      fs.mkdirSync(attachmentsDir, { recursive: true });
    }

    let downloadSuccess = false;

    for (let i = 0; i < attachmentUrls.length; i++) {
      const url = attachmentUrls[i].trim();

      try {
        this.updateTaskStatus('downloading', `正在下载附件 ${i + 1}/${attachmentUrls.length}: ${postTitle}`);

        // 导航到帖子页面
        await page.goto(postData.postUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
        await page.waitForTimeout(2000);

        // 查找附件链接
        const attachmentElements = await page.locator(siteProfile.attachmentUrlSelector).all();

        if (attachmentElements.length === 0) {
          this.log.warn(`[${this.forumName}] 未找到附件元素，跳过下载`);
          continue;
        }

        // 🔧 修正：遍历所有附件元素进行下载
        for (let attachmentIndex = 0; attachmentIndex < attachmentElements.length; attachmentIndex++) {
          this.log.info(`[${this.forumName}] 准备下载附件 ${attachmentIndex + 1}/${attachmentElements.length}`);

          // 设置popup事件监听器
          const popupPromise = page.waitForEvent('popup');

          // 点击附件链接
          await attachmentElements[attachmentIndex].click();

          // 等待并捕获弹窗
          const popupPage = await popupPromise;
          this.log.info(`[${this.forumName}] 成功捕获弹窗页面: ${popupPage.url()}`);

          // 监听下载事件 - 关键修复：将postData作为参数传递
          let downloadCompleted = false;
          let downloadPath = null;

          const downloadHandler = async (download) => {
            try {
              const originalFileName = download.suggestedFilename();
              this.log.info(`[${this.forumName}] 开始下载: ${originalFileName}`);

              // 🔧 关键修复：使用传入的postData而不是循环变量
              const fileNameResult = this.fileNameBuilder ?
                this.fileNameBuilder.buildStandardFileName(postData, originalFileName) :
                originalFileName;
              const newFileName = fileNameResult;
              const fullPath = path.join(attachmentsDir, newFileName);

              this.log.info(`[${this.forumName}] 文件名: ${originalFileName} -> ${newFileName}`);

              // 保存文件
              await download.saveAs(fullPath);
              await page.waitForTimeout(1000);

              // 验证文件是否存在
              if (fs.existsSync(fullPath)) {
                const stats = fs.statSync(fullPath);
                this.log.info(`[${this.forumName}] 下载成功: ${newFileName} (${stats.size} bytes)`);

                downloadCompleted = true;
                downloadPath = fullPath;

                // 更新数据库状态
                if (this.databaseService) {
                  this.databaseService.updateDownloadStatus(postData.postUrl, 'completed', fullPath);
                }
              } else {
                throw new Error('文件保存失败');
              }

            } catch (error) {
              this.log.error(`[${this.forumName}] 下载处理失败: ${error.message}`);
              if (this.databaseService) {
                this.databaseService.updateDownloadStatus(postData.postUrl, 'failed', null, error.message);
              }
            }
          };

          // 为主页面和弹窗页面都设置下载监听器
          page.on('download', downloadHandler);
          popupPage.on('download', downloadHandler);

          // 等待下载完成或超时
          const timeout = 30000;
          const startTime = Date.now();

          while (!downloadCompleted && (Date.now() - startTime) < timeout) {
            await page.waitForTimeout(1000);
          }

          // 清理事件监听器
          page.off('download', downloadHandler);
          popupPage.off('download', downloadHandler);

          // 关闭弹窗
          try {
            await popupPage.close();
          } catch (error) {
            this.log.warn(`[${this.forumName}] 关闭弹窗失败: ${error.message}`);
          }

          if (downloadCompleted) {
            this.log.info(`[${this.forumName}] 附件 ${attachmentIndex + 1} 下载成功: ${downloadPath}`);
            downloadSuccess = true;
          } else {
            this.log.warn(`[${this.forumName}] 附件 ${attachmentIndex + 1} 下载超时`);
          }

          // 多附件下载间隔
          if (attachmentIndex < attachmentElements.length - 1) {
            await page.waitForTimeout(2000);
          }
        }

      } catch (error) {
        this.log.error(`[${this.forumName}] 下载附件失败: ${error.message}`);

        if (error.message.includes('下载次数已达上限') ||
            error.message.includes('需要人机验证')) {
          throw error;
        }
      }

      // 下载间隔
      if (i < attachmentUrls.length - 1) {
        await this.delay(2000);
      }
    }

    return { success: downloadSuccess, message: downloadSuccess ? '下载成功' : '下载失败' };
  }

  /**
   * 从文本中提取元数据
   */
  extractMetadataFromText(text) {
    const metadata = {};

    // 提取文件大小
    const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(GB|MB|KB)/i);
    if (sizeMatch) {
      metadata.fileSize = `${sizeMatch[1]} ${sizeMatch[2].toUpperCase()}`;
    }

    // 提取演员信息
    const performerPatterns = [
      /演员[：:]\s*([^\n\r]+)/i,
      /主演[：:]\s*([^\n\r]+)/i,
      /女优[：:]\s*([^\n\r]+)/i
    ];

    for (const pattern of performerPatterns) {
      const match = text.match(pattern);
      if (match) {
        metadata.performers = match[1].trim();
        break;
      }
    }

    return metadata;
  }

  /**
   * 从文本中提取网盘链接与提取码
   */
  extractCloudLinksWithCodes(text) {
    const cloudLinks = [];

    const patterns = [
      /链接\d*[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
      /下载链接[：:]\s*(https?:\/\/[^\s]+)\s*密码[：:]\s*(\w+)/gi,
      /网盘[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi,
      /百度网盘[：:]\s*(https?:\/\/[^\s]+)\s*提取码[：:]\s*(\w+)/gi
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const url = match[1].trim();
        const code = match[2].trim();

        try {
          new URL(url);
          cloudLinks.push({
            url: url,
            code: code,
            type: this.detectCloudType(url)
          });
        } catch (e) {
          // 无效URL，跳过
        }
      }
    }

    return cloudLinks;
  }

  /**
   * 检测网盘类型
   */
  detectCloudType(url) {
    if (url.includes('pan.baidu.com')) return '百度网盘';
    if (url.includes('cloud.189.cn')) return '天翼云盘';
    if (url.includes('lanzou.com') || url.includes('lanzous.com')) return '蓝奏云';
    return '其他网盘';
  }
}

module.exports = ForumACollector;
