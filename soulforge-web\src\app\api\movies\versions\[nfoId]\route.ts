import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { nfoId: string } }
) {
  try {
    const nfoId = decodeURIComponent(params.nfoId);

    if (!nfoId) {
      return NextResponse.json(
        { success: false, error: 'NFO ID is required' },
        { status: 400 }
      );
    }

    // Get all versions of the movie with the same nfoId
    const versions = await prisma.movie.findMany({
      where: {
        nfoId: nfoId,
      },
      orderBy: [
        // Preferred versions first
        { preferredStatus: 'desc' },
        // Then by year (newest first)
        { year: 'desc' },
        // Then by file size (largest first)
        { fileSize: 'desc' },
        // Finally by ID (newest first)
        { id: 'desc' },
      ],
    });

    if (versions.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No versions found for this NFO ID' },
        { status: 404 }
      );
    }

    // Transform the data to include computed fields
    const transformedVersions = versions.map(version => ({
      ...version,
      // Parse JSON fields
      actors: version.actors ? JSON.parse(version.actors as string) : [],
      genres: version.genres ? JSON.parse(version.genres as string) : [],
      tags: version.tags ? JSON.parse(version.tags as string) : [],
      customFileTags: version.customFileTags ? JSON.parse(version.customFileTags as string) : [],
      versionCategories: version.versionCategories ? JSON.parse(version.versionCategories as string) : [],
      aiAnalyzedTags: version.aiAnalyzedTags ? JSON.parse(version.aiAnalyzedTags as string) : [],
      
      // Add version count for this nfoId
      versionCount: versions.length,
      
      // Calculate multi-CD count
      multiCdCountForNfoId: versions.filter(v => v.cdPartInfo && v.cdPartInfo.trim() !== '').length,
    }));

    return NextResponse.json({
      success: true,
      versions: transformedVersions,
      totalVersions: versions.length,
    });

  } catch (error) {
    console.error('Error fetching movie versions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch movie versions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
