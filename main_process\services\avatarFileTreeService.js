// soul-forge-electron/main_process/services/avatarFileTreeService.js
const fs = require('node:fs');
const fsp = require('node:fs').promises;
const path = require('node:path');
const http = require('node:http');
const https = require('node:https');
const crypto = require('node:crypto');


let log;
let settingsServiceInstance;
let fileUtilsInstance;
let databaseServiceInstance;
let electronAppInstance;

// let cachedFiletree = null; // No longer caching entire filetree globally in memory this way
// let lastLoadedFiletreePathOrUrl = null; // No longer needed for global cache
// let lastFiletreeTimestamp = null; // No longer needed for global cache

// const FILE_TREE_CACHE_DURATION_MS = 24 * 60 * 60 * 1000; // Retained for potential future use with specific remote fetches
const ACTOR_AVATAR_CACHE_DIR_NAME = 'actor_avatar_cache';

function getActorCacheDir() {
  if (!electronAppInstance) {
    log.error('[AvatarService] Electron App instance not available for cache directory.');
    return null;
  }
  const userDataPath = electronAppInstance.getPath('userData');
  const cacheDir = path.join(userDataPath, ACTOR_AVATAR_CACHE_DIR_NAME);
  if (!fs.existsSync(cacheDir)) {
    try {
      fs.mkdirSync(cacheDir, { recursive: true });
      log.info(`[AvatarService] Created actor avatar cache directory: ${cacheDir}`);
    } catch (error) {
      log.error(`[AvatarService] Failed to create actor avatar cache directory ${cacheDir}:`, error);
      return null;
    }
  }
  return cacheDir;
}

function sanitizeFilename(name) {
  return name.replace(/[<>:"/\\|?*\x00-\x1F]/g, '_').substring(0, 100);
}

async function fetchJsonOverHttp(urlToFetch, maxRedirects = 5) {
  return new Promise((resolve, reject) => {
    if (maxRedirects < 0) {
      return reject(new Error(`Too many redirects for ${urlToFetch}`));
    }

    const client = urlToFetch.startsWith('https:') ? https : http;
    const request = client.get(urlToFetch, { timeout: 15000 }, (response) => {
      if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
        log.info(`[AvatarService] fetchJsonOverHttp: Redirecting to ${response.headers.location}`);
        response.resume(); // consume data
        return fetchJsonOverHttp(response.headers.location, maxRedirects - 1).then(resolve).catch(reject);
      }
      
      let data = '';
      if (response.statusCode !== 200) {
        response.resume(); 
        return reject(new Error(`Request Failed. Status Code: ${response.statusCode} for ${urlToFetch}`));
      }
      response.setEncoding('utf8');
      response.on('data', (chunk) => { data += chunk; });
      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (e) {
          reject(new Error(`Failed to parse JSON from ${urlToFetch}: ${e.message}. Data (first 100): ${data.substring(0,100)}`));
        }
      });
    });
    request.on('error', (err) => reject(new Error(`Request error for ${urlToFetch}: ${err.message}`)));
    request.on('timeout', () => {
        request.destroy();
        reject(new Error(`Request timeout for ${urlToFetch}`));
    });
  });
}

function initializeAvatarFileTreeService(logger, settingsSvc, fileUtilsSvc, dbSvc, appInstance) {
  log = logger;
  settingsServiceInstance = settingsSvc;
  fileUtilsInstance = fileUtilsSvc;
  databaseServiceInstance = dbSvc;
  electronAppInstance = appInstance; 
  log.info('[AvatarService] Initialized. Filetree.json will be loaded on demand during scraping.');
  getActorCacheDir(); 
}

// REMOVED: loadAndCacheFiletree is no longer called on startup or settings change directly.
// It's implicitly handled within scrapeAndCacheAllAvatars.

async function getAvatarInfo(actorName) { // Removed bypassDbCache, DB is now primary
  if (!actorName || typeof actorName !== 'string' || actorName.trim() === '') {
    return null;
  }
  
  const dbMeta = await databaseServiceInstance.getActorMetadata(actorName);
  if (dbMeta && dbMeta.local_avatar_path && fs.existsSync(dbMeta.local_avatar_path)) {
    log.debug(`[AvatarService] DB Cache hit for ${actorName}: ${dbMeta.local_avatar_path}`);
    return { type: 'localPath', path: dbMeta.local_avatar_path, source: 'db_cache', actorName };
  }
  
  // If not in DB cache, we don't proactively look in Filetree.json anymore.
  // The scraping process is responsible for populating the DB cache.
  log.debug(`[AvatarService] Avatar for ${actorName} not found in DB cache. Trigger scraping to populate.`);
  return null; // Or return a specific "not_found_in_cache" status
}

async function scrapeAndCacheAllAvatars(webContentsSender) {
  const cacheDir = getActorCacheDir();
  if (!cacheDir) {
    const errorMsg = "演员头像缓存目录不可用，刮削操作中止。";
    log.error(`[AvatarService] ${errorMsg}`);
    if (webContentsSender) webContentsSender.send('scrape-avatars-error', errorMsg);
    return { success: false, error: errorMsg, totalActors: 0, processedCount: 0, failedCount: 0 };
  }

  log.info('[AvatarService] 开始刮削所有演员头像 (按需加载Filetree.json)...');
  if (webContentsSender) webContentsSender.send('scrape-avatars-log', '开始演员头像刮削任务 (按需加载Filetree.json)...');

  const settings = settingsServiceInstance.getSettings();
  const { avatarDataSourceType, localFileTreePath, remoteGfriendsFiletreeUrl, avatarPreferAiFixed, remoteGfriendsImageBaseUrl } = settings;
  
  let filetreeToProcess = null;
  let sourcePathOrUrlForFiletree = null;
  let isRemoteFiletree = false;

  if (avatarDataSourceType === 'localFileTree' && localFileTreePath) {
    sourcePathOrUrlForFiletree = path.join(localFileTreePath, 'Filetree.json');
  } else if (avatarDataSourceType === 'remoteGfriends' && remoteGfriendsFiletreeUrl) {
    sourcePathOrUrlForFiletree = remoteGfriendsFiletreeUrl;
    isRemoteFiletree = true;
  } else if (avatarDataSourceType === 'localSimple') {
    log.info('[AvatarService] 使用 "localSimple" 数据源，将直接扫描指定目录，不使用 Filetree.json。');
  } else {
    const msg = `没有配置有效的 Filetree.json 源 (类型: ${avatarDataSourceType})，或刮削源为 "none"。无法进行基于 Filetree 的刮削。`;
    log.warn(`[AvatarService] ${msg}`);
    if (webContentsSender) webContentsSender.send('scrape-avatars-log', msg);
    if (webContentsSender) webContentsSender.send('scrape-avatars-complete', { success: true, message: msg, totalActors: 0, processedCount: 0, failedCount: 0 });
    return { success: true, message: msg, totalActors: 0, processedCount: 0, failedCount: 0 };
  }

  if (sourcePathOrUrlForFiletree) { // If using localFileTree or remoteGfriends
    log.info(`[AvatarService] 刮削时加载 Filetree.json 从 ${sourcePathOrUrlForFiletree}`);
    try {
      if (isRemoteFiletree) {
        filetreeToProcess = await fetchJsonOverHttp(sourcePathOrUrlForFiletree);
      } else {
        if (fs.existsSync(sourcePathOrUrlForFiletree)) {
          const fileContent = await fsp.readFile(sourcePathOrUrlForFiletree, 'utf-8');
          filetreeToProcess = JSON.parse(fileContent);
        } else {
          throw new Error(`本地 Filetree.json 未找到于 ${sourcePathOrUrlForFiletree}`);
        }
      }
      const structureInfo = filetreeToProcess && filetreeToProcess.Content ? `${Object.keys(filetreeToProcess.Content).length} companies` : 'empty/invalid structure';
      log.info(`[AvatarService] 刮削时成功加载 Filetree.json。结构: ${structureInfo}`);
    } catch (error) {
      log.error(`[AvatarService] 刮削时加载或解析 Filetree.json 失败 from ${sourcePathOrUrlForFiletree}:`, error);
      if (webContentsSender) webContentsSender.send('scrape-avatars-error', `加载Filetree.json失败: ${error.message}`);
      return { success: false, error: `加载Filetree.json失败: ${error.message}`, totalActors: 0, processedCount: 0, failedCount: 0 };
    }
  }


  const actorNamesFromDb = await databaseServiceInstance.getUniqueActorNames();
  if (!actorNamesFromDb || actorNamesFromDb.length === 0) {
    const msg = "数据库中没有演员信息，无需刮削。";
    log.info(`[AvatarService] ${msg}`);
    if (webContentsSender) webContentsSender.send('scrape-avatars-log', msg);
    if (webContentsSender) webContentsSender.send('scrape-avatars-complete', { success: true, message: msg, totalActors: 0, processedCount: 0, failedCount: 0 });
    return { success: true, message: msg, totalActors: 0, processedCount: 0, failedCount: 0 };
  }

  let processedCount = 0;
  let failedCount = 0;
  const totalActors = actorNamesFromDb.length;

  for (let i = 0; i < totalActors; i++) {
    const actorName = actorNamesFromDb[i];
    if (webContentsSender) {
      webContentsSender.send('scrape-avatars-progress', {
        currentActorName: actorName,
        currentIndex: i,
        totalActors: totalActors,
        status: 'processing',
      });
    }

    try {
      const dbMeta = await databaseServiceInstance.getActorMetadata(actorName);
      // Check if DB cache is valid AND points to a file that still exists
      if (dbMeta && dbMeta.local_avatar_path && fs.existsSync(dbMeta.local_avatar_path)) {
         // Optional: Add a check here if dbMeta.filetree_source_path needs revalidation against current Filetree.json
         // For simplicity, if a valid local cache exists, we assume it's good enough for now.
         // A more robust solution might involve checking timestamps or checksums if Filetree.json indicates updates.
        log.info(`[AvatarService] 演员 ${actorName} 已有有效本地头像缓存，跳过。路径: ${dbMeta.local_avatar_path}`);
        if (webContentsSender) webContentsSender.send('scrape-avatars-progress', { currentActorName: actorName, currentIndex: i, totalActors, status: 'cached' });
        processedCount++;
        continue;
      }

      // --- Logic to find avatar source based on current settings and loaded filetree ---
      let avatarSourceDetails = null;
      if (avatarDataSourceType === 'localSimple' && settings.actorAvatarLibraryPath) {
        const extensions = ['.jpg', '.png', '.webp'];
        for (const ext of extensions) {
          const potentialPath = path.join(settings.actorAvatarLibraryPath, `${actorName}${ext}`);
          if (fs.existsSync(potentialPath)) {
            avatarSourceDetails = { type: 'localPath', path: potentialPath, source: 'local_simple', actorName, filetreeSourcePath: null };
            break;
          }
        }
      } else if ((avatarDataSourceType === 'localFileTree' || avatarDataSourceType === 'remoteGfriends') && filetreeToProcess?.Content) {
        // Search in the loaded filetreeToProcess
        for (const companyName in filetreeToProcess.Content) {
          if (!Object.prototype.hasOwnProperty.call(filetreeToProcess.Content, companyName)) continue;
          const companyFiles = filetreeToProcess.Content[companyName];
          if (typeof companyFiles !== 'object' || companyFiles === null) continue;

          const actorNameKeysToTry = [actorName, `${actorName}.jpg`, `${actorName}.png`, `${actorName}.webp`];
          let foundInFiletree = false;
          for (const searchKey of actorNameKeysToTry) {
            if (companyFiles.hasOwnProperty(searchKey)) {
              let canonicalFilenameWithPotentialTimestamp = companyFiles[searchKey];
              const baseFilenameFromValue = canonicalFilenameWithPotentialTimestamp.split('?')[0];
              const timestampSuffix = canonicalFilenameWithPotentialTimestamp.includes('?') ? canonicalFilenameWithPotentialTimestamp.substring(canonicalFilenameWithPotentialTimestamp.indexOf('?')) : '';
              let finalFilenameToUse = baseFilenameFromValue;
              if (avatarPreferAiFixed === false && baseFilenameFromValue.startsWith("AI-Fix-")) {
                finalFilenameToUse = baseFilenameFromValue.substring("AI-Fix-".length);
              }
              
              const filetreeSourcePathForDb = path.join(companyName, finalFilenameToUse);

              if (avatarDataSourceType === 'localFileTree' && localFileTreePath) {
                const fullLocalPath = path.join(localFileTreePath, 'Content', companyName, finalFilenameToUse);
                avatarSourceDetails = { type: 'localPath', path: fullLocalPath, source: 'local_filetree_direct_nocache', actorName, filetreeSourcePath: filetreeSourcePathForDb };
              } else if (avatarDataSourceType === 'remoteGfriends' && remoteGfriendsImageBaseUrl) {
                const filenameForUrl = finalFilenameToUse + timestampSuffix;
                const baseUrl = remoteGfriendsImageBaseUrl.endsWith('/') ? remoteGfriendsImageBaseUrl : remoteGfriendsImageBaseUrl + '/';
                const remoteUrl = `${baseUrl}${companyName}/${filenameForUrl}`;
                avatarSourceDetails = { type: 'url', url: remoteUrl, source: 'remote_gfriends', actorName, filetreeSourcePath: filetreeSourcePathForDb };
              }
              foundInFiletree = true;
              break; 
            }
          }
          if (foundInFiletree) break; 
        }
      }
      // --- End of avatar source finding logic ---


      if (!avatarSourceDetails) {
        log.warn(`[AvatarService] (刮削) 未找到演员 ${actorName} 的头像源。`);
        if (webContentsSender) webContentsSender.send('scrape-avatars-progress', { currentActorName: actorName, currentIndex: i, totalActors, status: 'skipped', error: '未找到源' });
        failedCount++;
        continue;
      }

      let localPathToSaveInDb = null;
      let sourceForDbUpdate = avatarSourceDetails.source; 

      if (avatarSourceDetails.type === 'url' && avatarSourceDetails.url) {
        log.info(`[AvatarService] (刮削) 下载演员 ${actorName} 的头像从 ${avatarSourceDetails.url}`);
        const urlObj = new URL(avatarSourceDetails.url);
        let extension = path.extname(urlObj.pathname.split('?')[0]);
        if (!extension || !['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(extension.toLowerCase())) {
            extension = '.jpg'; 
        }
        const targetFilenameInCache = sanitizeFilename(actorName) + extension;
        const cachedPath = path.join(cacheDir, targetFilenameInCache);
        
        await fileUtilsInstance.downloadImage(avatarSourceDetails.url, cachedPath);
        localPathToSaveInDb = cachedPath;
        sourceForDbUpdate = 'remote_gfriends_cached'; 
        if (webContentsSender) webContentsSender.send('scrape-avatars-progress', { currentActorName: actorName, currentIndex: i, totalActors, status: 'downloaded' });
      
      } else if (avatarSourceDetails.type === 'localPath' && avatarSourceDetails.path) {
          const originalSourcePath = avatarSourceDetails.path;
          if (!fs.existsSync(originalSourcePath)) {
            throw new Error(`源文件 ${originalSourcePath} 不存在。`);
          }
          const extension = path.extname(originalSourcePath) || '.jpg';
          const targetFilenameInCache = sanitizeFilename(actorName) + extension;
          const cachedPath = path.join(cacheDir, targetFilenameInCache);

          log.info(`[AvatarService] (刮削) 复制演员 ${actorName} 的本地头像从 ${originalSourcePath} 到 ${cachedPath}`);
          await fsp.copyFile(originalSourcePath, cachedPath);
          localPathToSaveInDb = cachedPath;
          sourceForDbUpdate = avatarSourceDetails.source === 'local_simple' ? 'local_simple_cached' : 'local_filetree_cached';
          if (webContentsSender) webContentsSender.send('scrape-avatars-progress', { currentActorName: actorName, currentIndex: i, totalActors, status: 'copied_to_cache' });
      }

      if (localPathToSaveInDb && fs.existsSync(localPathToSaveInDb)) {
        await databaseServiceInstance.upsertActorMetadata(
          actorName,
          localPathToSaveInDb,
          sourceForDbUpdate, 
          avatarSourceDetails.filetreeSourcePath || null
        );
        log.info(`[AvatarService] (刮削) 演员 ${actorName} 头像信息已更新到数据库: ${localPathToSaveInDb}`);
        processedCount++;
      } else {
        throw new Error(`(刮削) 下载或复制后文件未找到: ${localPathToSaveInDb || '未知路径'}`);
      }

    } catch (error) {
      log.error(`[AvatarService] (刮削) 演员 ${actorName} 头像失败: ${error.message}`);
      if (webContentsSender) webContentsSender.send('scrape-avatars-progress', { currentActorName: actorName, currentIndex: i, totalActors, status: 'error', error: error.message });
      failedCount++;
    }
  }

  const completionMessage = `头像刮削完成。总演员数: ${totalActors}, 成功处理: ${processedCount}, 失败: ${failedCount}.`;
  log.info(`[AvatarService] ${completionMessage}`);
  if (webContentsSender) webContentsSender.send('scrape-avatars-log', completionMessage);
  if (webContentsSender) webContentsSender.send('scrape-avatars-complete', { success: true, message: completionMessage, totalActors, processedCount, failedCount });
  return { success: true, message: completionMessage, totalActors, processedCount, failedCount };
}


module.exports = {
  initializeAvatarFileTreeService,
  // loadAndCacheFiletree, // No longer exposed for direct external call
  getAvatarInfo,
  scrapeAndCacheAllAvatars,
};

