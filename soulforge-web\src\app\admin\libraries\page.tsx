'use client';

import React, { useState, useEffect } from 'react';
import { AppLayout } from '@/components/layout/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FolderOpen, 
  RefreshCw, 
  Play,
  CheckCircle,
  AlertCircle,
  Database,
  Calendar,
  Film
} from 'lucide-react';

interface Library {
  id: string;
  name: string;
  path: string;
  lastScanned: string | null;
  movieCount: number;
  moviesWithNfoId: number;
  nfoIdCompleteness: number;
}

interface ScanResult {
  processed: number;
  added: number;
  updated: number;
  skipped: number;
  errors: number;
  errorDetails: string[];
}

export default function LibrariesPage() {
  const [libraries, setLibraries] = useState<Library[]>([]);
  const [loading, setLoading] = useState(false);
  const [scanning, setScanning] = useState<string | null>(null);
  const [scanResults, setScanResults] = useState<Map<string, ScanResult>>(new Map());

  useEffect(() => {
    loadLibraries();
  }, []);

  const loadLibraries = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/libraries');
      const data = await response.json();
      
      if (data.success) {
        // Load scan status for each library
        const librariesWithStatus = await Promise.all(
          data.libraries.map(async (lib: any) => {
            try {
              const statusResponse = await fetch(`/api/libraries/${lib.id}/scan`);
              const statusData = await statusResponse.json();
              return statusData.success ? statusData.library : lib;
            } catch {
              return lib;
            }
          })
        );
        setLibraries(librariesWithStatus);
      }
    } catch (error) {
      console.error('Failed to load libraries:', error);
    } finally {
      setLoading(false);
    }
  };

  const scanLibrary = async (libraryId: string, updateExisting = false) => {
    setScanning(libraryId);
    setScanResults(prev => {
      const newMap = new Map(prev);
      newMap.delete(libraryId);
      return newMap;
    });

    try {
      const response = await fetch(`/api/libraries/${libraryId}/scan`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          updateExisting,
          extractTechnicalInfo: false 
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setScanResults(prev => {
          const newMap = new Map(prev);
          newMap.set(libraryId, data.result);
          return newMap;
        });
        
        // Reload libraries to get updated counts
        await loadLibraries();
      } else {
        console.error('Scan failed:', data.error);
      }
    } catch (error) {
      console.error('Scan request failed:', error);
    } finally {
      setScanning(null);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '从未扫描';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const getCompletenessColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">媒体库管理</h1>
          <Button onClick={loadLibraries} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        {/* Libraries Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {libraries.map((library) => {
            const scanResult = scanResults.get(library.id);
            const isScanning = scanning === library.id;

            return (
              <Card key={library.id} className="relative">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <FolderOpen className="h-5 w-5" />
                      <span>{library.name}</span>
                    </div>
                    <Badge variant="outline">
                      {library.movieCount} 部电影
                    </Badge>
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Library Path */}
                  <div className="text-sm text-muted-foreground font-mono bg-muted p-2 rounded">
                    {library.path}
                  </div>

                  {/* Statistics */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{library.movieCount}</div>
                      <div className="text-sm text-muted-foreground">总电影数</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${getCompletenessColor(library.nfoIdCompleteness)}`}>
                        {library.moviesWithNfoId}
                      </div>
                      <div className="text-sm text-muted-foreground">有 NFO ID</div>
                    </div>
                  </div>

                  {/* NFO ID Completeness */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>NFO ID 完整性</span>
                      <span className={getCompletenessColor(library.nfoIdCompleteness)}>
                        {library.nfoIdCompleteness.toFixed(1)}%
                      </span>
                    </div>
                    <Progress value={library.nfoIdCompleteness} className="w-full" />
                  </div>

                  {/* Last Scan */}
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>上次扫描: {formatDate(library.lastScanned)}</span>
                  </div>

                  {/* Scan Actions */}
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => scanLibrary(library.id, false)}
                      disabled={isScanning}
                      variant="default"
                      size="sm"
                      className="flex-1"
                    >
                      {isScanning ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          扫描中...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          快速扫描
                        </>
                      )}
                    </Button>
                    
                    <Button
                      onClick={() => scanLibrary(library.id, true)}
                      disabled={isScanning}
                      variant="outline"
                      size="sm"
                      className="flex-1"
                    >
                      <Database className="h-4 w-4 mr-2" />
                      完整扫描
                    </Button>
                  </div>

                  {/* Scan Results */}
                  {scanResult && (
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                      <div className="text-sm font-medium mb-2">扫描结果:</div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="flex justify-between">
                          <span>已处理:</span>
                          <span>{scanResult.processed}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-600">新增:</span>
                          <span className="text-green-600">{scanResult.added}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-600">更新:</span>
                          <span className="text-blue-600">{scanResult.updated}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-yellow-600">跳过:</span>
                          <span className="text-yellow-600">{scanResult.skipped}</span>
                        </div>
                        {scanResult.errors > 0 && (
                          <div className="col-span-2 flex justify-between">
                            <span className="text-red-600">错误:</span>
                            <span className="text-red-600">{scanResult.errors}</span>
                          </div>
                        )}
                      </div>
                      
                      {scanResult.errorDetails.length > 0 && (
                        <div className="mt-2">
                          <details className="text-xs">
                            <summary className="cursor-pointer text-red-600">
                              查看错误详情 ({scanResult.errorDetails.length})
                            </summary>
                            <div className="mt-1 space-y-1 max-h-32 overflow-y-auto">
                              {scanResult.errorDetails.map((error, index) => (
                                <div key={index} className="text-red-600 font-mono">
                                  {error}
                                </div>
                              ))}
                            </div>
                          </details>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>

        {libraries.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-8">
              <FolderOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">没有找到媒体库</h3>
              <p className="text-muted-foreground">
                请先添加媒体库以开始扫描电影。
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
