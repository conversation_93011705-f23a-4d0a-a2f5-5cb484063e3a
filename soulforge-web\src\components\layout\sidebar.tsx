'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Home,
  Film,
  Library,
  Heart,
  Settings,
  Search,
  TrendingUp,
  Star,
  Clock,
  ChevronLeft,
  ChevronRight,
  Wrench
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const navigationItems = [
  {
    title: '首页',
    href: '/',
    icon: Home,
  },
  {
    title: '电影库',
    href: '/movies',
    icon: Film,
  },
  {
    title: '媒体库',
    href: '/libraries',
    icon: Library,
  },
  {
    title: '收藏',
    href: '/favorites',
    icon: Heart,
  },
  {
    title: '搜索',
    href: '/search',
    icon: Search,
  },
  {
    title: 'NFO 修复',
    href: '/admin/nfo-fix',
    icon: Wrench,
  },
  {
    title: '库管理',
    href: '/admin/libraries',
    icon: Settings,
  },
  {
    title: '调试工具',
    href: '/debug',
    icon: Wrench,
  },
];

const quickLinks = [
  {
    title: '最近添加',
    href: '/movies?sort=lastScanned&order=desc',
    icon: Clock,
  },
  {
    title: '高评分',
    href: '/movies?sort=personalRating&order=desc',
    icon: Star,
  },
  {
    title: '热门',
    href: '/movies?sort=views&order=desc',
    icon: TrendingUp,
  },
];

export function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const pathname = usePathname();

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-background border-r transition-transform duration-300 lg:relative lg:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full',
          isOpen ? 'w-64' : 'lg:w-16'
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            {isOpen && (
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Film className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="font-semibold">麟琅秘府</span>
              </div>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggle}
              className="lg:flex"
            >
              {isOpen ? (
                <ChevronLeft className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {/* Main navigation */}
            <div className="space-y-1">
              {navigationItems.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <Link key={item.href} href={item.href}>
                    <Button
                      variant={isActive ? 'secondary' : 'ghost'}
                      className={cn(
                        'w-full justify-start',
                        !isOpen && 'px-2'
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {isOpen && (
                        <span className="ml-2">{item.title}</span>
                      )}
                    </Button>
                  </Link>
                );
              })}
            </div>

            {/* Quick links */}
            {isOpen && (
              <div className="pt-4">
                <h3 className="px-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                  快速访问
                </h3>
                <div className="space-y-1">
                  {quickLinks.map((item) => (
                    <Link key={item.href} href={item.href}>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-sm"
                      >
                        <item.icon className="h-4 w-4" />
                        <span className="ml-2">{item.title}</span>
                      </Button>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t">
            <Link href="/settings">
              <Button
                variant="ghost"
                className={cn(
                  'w-full justify-start',
                  !isOpen && 'px-2'
                )}
              >
                <Settings className="h-4 w-4" />
                {isOpen && <span className="ml-2">设置</span>}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
