#!/usr/bin/env node

// test-avsox-provider-optimization.js - 验证 AVSOX Provider 优化效果
const fs = require('fs');

function testAvsoxProviderOptimization() {
  console.log('🧪 AVSOX Provider 优化验证开始...\n');

  try {
    // 检查 AVSOX Provider 文件是否存在
    const avsoxProviderExists = fs.existsSync('./main_process/services/scrapers/avsoxProvider.js');
    if (!avsoxProviderExists) {
      console.log('❌ AVSOX Provider 文件不存在');
      return;
    }

    const avsoxContent = fs.readFileSync('./main_process/services/scrapers/avsoxProvider.js', 'utf8');

    // 第一部分：验证搜索优化
    console.log('🔍 第一部分：验证搜索优化...');
    
    const hasOptimizedSearch = avsoxContent.includes('【优化】搜索并定位影片详情页');
    const hasWaterfallSelector = avsoxContent.includes('#waterfall > div');
    const hasNumberExtraction = avsoxContent.includes('.photo-info span date');
    const hasPosterFromSearch = avsoxContent.includes('posterUrl');
    const hasNormalizedMatching = avsoxContent.includes('replace("-PPV", "")');
    
    console.log(`✅ 搜索功能优化检查:`);
    console.log(`   优化标记: ${hasOptimizedSearch ? '✅' : '❌'}`);
    console.log(`   waterfall选择器: ${hasWaterfallSelector ? '✅' : '❌'}`);
    console.log(`   番号提取优化: ${hasNumberExtraction ? '✅' : '❌'}`);
    console.log(`   海报URL获取: ${hasPosterFromSearch ? '✅' : '❌'}`);
    console.log(`   标准化匹配: ${hasNormalizedMatching ? '✅' : '❌'}`);

    // 第二部分：验证选择器优化
    console.log('\n🔍 第二部分：验证选择器优化...');
    
    const hasContainerH3 = avsoxContent.includes('.container h3');
    const hasWebNumberSelector = avsoxContent.includes('.col-md-3.info p span[style="color:#CC0000;"]');
    const hasBigImageSelector = avsoxContent.includes('a.bigImage');
    const hasAvatarWaterfall = avsoxContent.includes('#avatar-waterfall a span');
    const hasGenreSpan = avsoxContent.includes('span.genre a');
    const hasStudioHref = avsoxContent.includes('a[href*="/studio/"]');
    const hasSeriesHref = avsoxContent.includes('a[href*="/series/"]');
    
    console.log(`✅ 选择器优化检查:`);
    console.log(`   标题选择器(.container h3): ${hasContainerH3 ? '✅' : '❌'}`);
    console.log(`   番号选择器(红色样式): ${hasWebNumberSelector ? '✅' : '❌'}`);
    console.log(`   封面选择器(a.bigImage): ${hasBigImageSelector ? '✅' : '❌'}`);
    console.log(`   演员选择器(avatar-waterfall): ${hasAvatarWaterfall ? '✅' : '❌'}`);
    console.log(`   类别选择器(span.genre): ${hasGenreSpan ? '✅' : '❌'}`);
    console.log(`   制作商选择器(href包含studio): ${hasStudioHref ? '✅' : '❌'}`);
    console.log(`   系列选择器(href包含series): ${hasSeriesHref ? '✅' : '❌'}`);

    // 第三部分：验证XPath等价实现
    console.log('\n🔍 第三部分：验证XPath等价实现...');
    
    const hasReleaseSpanFilter = avsoxContent.includes('发行时间:') && avsoxContent.includes('.filter(function()');
    const hasRuntimeSpanFilter = avsoxContent.includes('长度:') && avsoxContent.includes('.filter(function()');
    const hasTextNodeAccess = avsoxContent.includes('nodeType === 3');
    const hasParentTextAccess = avsoxContent.includes('.parent().text()');
    
    console.log(`✅ XPath等价实现检查:`);
    console.log(`   发行时间span过滤: ${hasReleaseSpanFilter ? '✅' : '❌'}`);
    console.log(`   时长span过滤: ${hasRuntimeSpanFilter ? '✅' : '❌'}`);
    console.log(`   文本节点访问: ${hasTextNodeAccess ? '✅' : '❌'}`);
    console.log(`   父元素文本访问: ${hasParentTextAccess ? '✅' : '❌'}`);

    // 第四部分：验证数据标准化
    console.log('\n🔍 第四部分：验证数据标准化...');
    
    const hasNumberField = avsoxContent.includes('number: nfoId');
    const hasOriginaltitleField = avsoxContent.includes('originaltitle: title');
    const hasOutlineField = avsoxContent.includes('outline: plot');
    const hasReleaseField = avsoxContent.includes('release: releaseDate');
    const hasYearField = avsoxContent.includes('year: releaseDate ? releaseDate.substring(0, 4)');
    const hasActorField = avsoxContent.includes('actor: actors?.map(a => a.name).join');
    const hasActorPhotoField = avsoxContent.includes('actor_photo: getActorPhoto(actors)');
    const hasTagField = avsoxContent.includes('tag: tags?.join');
    const hasThumbField = avsoxContent.includes('thumb: coverUrl');
    const hasPosterField = avsoxContent.includes('poster: posterUrl');
    const hasExtrafanartField = avsoxContent.includes('extrafanart: previewImages');
    const hasScoreField = avsoxContent.includes('score: rating?.score');
    const hasWebsiteField = avsoxContent.includes('website: detailUrl');
    const hasMosaicField = avsoxContent.includes('mosaic: \'无码\'');
    
    console.log(`✅ 数据标准化检查:`);
    console.log(`   number字段: ${hasNumberField ? '✅' : '❌'}`);
    console.log(`   originaltitle字段: ${hasOriginaltitleField ? '✅' : '❌'}`);
    console.log(`   outline字段: ${hasOutlineField ? '✅' : '❌'}`);
    console.log(`   release字段: ${hasReleaseField ? '✅' : '❌'}`);
    console.log(`   year字段: ${hasYearField ? '✅' : '❌'}`);
    console.log(`   actor字段: ${hasActorField ? '✅' : '❌'}`);
    console.log(`   actor_photo字段: ${hasActorPhotoField ? '✅' : '❌'}`);
    console.log(`   tag字段: ${hasTagField ? '✅' : '❌'}`);
    console.log(`   thumb字段: ${hasThumbField ? '✅' : '❌'}`);
    console.log(`   poster字段: ${hasPosterField ? '✅' : '❌'}`);
    console.log(`   extrafanart字段: ${hasExtrafanartField ? '✅' : '❌'}`);
    console.log(`   score字段: ${hasScoreField ? '✅' : '❌'}`);
    console.log(`   website字段: ${hasWebsiteField ? '✅' : '❌'}`);
    console.log(`   mosaic字段: ${hasMosaicField ? '✅' : '❌'}`);

    // 第五部分：验证辅助函数
    console.log('\n🔍 第五部分：验证辅助函数...');
    
    const hasGetWebNumber = avsoxContent.includes('function getWebNumber($)');
    const hasGetActorPhoto = avsoxContent.includes('function getActorPhoto(actors)');
    const hasTitleCleaning = avsoxContent.includes('getTitle($, nfoId)');
    const hasImageDownloadFlag = avsoxContent.includes('image_download: !!posterUrl');
    
    console.log(`✅ 辅助函数检查:`);
    console.log(`   getWebNumber函数: ${hasGetWebNumber ? '✅' : '❌'}`);
    console.log(`   getActorPhoto函数: ${hasGetActorPhoto ? '✅' : '❌'}`);
    console.log(`   标题清理功能: ${hasTitleCleaning ? '✅' : '❌'}`);
    console.log(`   图片下载标志: ${hasImageDownloadFlag ? '✅' : '❌'}`);

    // 第六部分：验证错误处理和健壮性
    console.log('\n🔍 第六部分：验证错误处理和健壮性...');
    
    const hasBackupSelectors = avsoxContent.includes('备用选择器');
    const hasLengthChecks = avsoxContent.includes('.length > 0');
    const hasNullChecks = avsoxContent.includes('|| null');
    const hasArrayChecks = avsoxContent.includes('Array.isArray');
    const hasTryCatchBlocks = avsoxContent.includes('try {') && avsoxContent.includes('catch');
    
    console.log(`✅ 健壮性检查:`);
    console.log(`   备用选择器: ${hasBackupSelectors ? '✅' : '❌'}`);
    console.log(`   长度检查: ${hasLengthChecks ? '✅' : '❌'}`);
    console.log(`   空值检查: ${hasNullChecks ? '✅' : '❌'}`);
    console.log(`   数组检查: ${hasArrayChecks ? '✅' : '❌'}`);
    console.log(`   异常处理: ${hasTryCatchBlocks ? '✅' : '❌'}`);

    // 第七部分：验证模块加载
    console.log('\n🔍 第七部分：验证模块加载...');
    
    try {
      const avsoxProvider = require('./main_process/services/scrapers/avsoxProvider.js');
      const hasName = !!avsoxProvider.name;
      const hasScrape = typeof avsoxProvider.scrape === 'function';
      const hasVersion = !!avsoxProvider.version;
      
      console.log(`✅ 模块加载检查:`);
      console.log(`   name属性: ${hasName ? '✅' : '❌'}`);
      console.log(`   scrape函数: ${hasScrape ? '✅' : '❌'}`);
      console.log(`   version属性: ${hasVersion ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`❌ 模块加载失败: ${error.message}`);
    }

    // 总结优化结果
    console.log('\n📊 优化结果总结:');
    
    const optimizationChecks = [
      hasOptimizedSearch, hasWaterfallSelector, hasNumberExtraction, hasPosterFromSearch,
      hasContainerH3, hasWebNumberSelector, hasBigImageSelector, hasAvatarWaterfall,
      hasReleaseSpanFilter, hasRuntimeSpanFilter, hasParentTextAccess,
      hasNumberField, hasActorField, hasTagField, hasThumbField, hasMosaicField,
      hasGetWebNumber, hasGetActorPhoto, hasTitleCleaning,
      hasBackupSelectors, hasLengthChecks, hasNullChecks
    ];
    
    const passedOptimizations = optimizationChecks.filter(Boolean).length;
    const totalOptimizations = optimizationChecks.length;
    const optimizationRate = (passedOptimizations / totalOptimizations * 100).toFixed(1);
    
    console.log(`   优化完成度: ${passedOptimizations}/${totalOptimizations} (${optimizationRate}%)`);
    console.log(`   搜索功能优化: ${hasOptimizedSearch && hasWaterfallSelector ? '✅' : '❌'}`);
    console.log(`   选择器精确化: ${hasContainerH3 && hasWebNumberSelector ? '✅' : '❌'}`);
    console.log(`   XPath等价实现: ${hasReleaseSpanFilter && hasRuntimeSpanFilter ? '✅' : '❌'}`);
    console.log(`   数据标准化: ${hasNumberField && hasActorField ? '✅' : '❌'}`);
    console.log(`   健壮性提升: ${hasBackupSelectors && hasLengthChecks ? '✅' : '❌'}`);

    console.log('\n🎉 AVSOX Provider 优化验证完成!');
    console.log('\n📋 优化总结:');
    console.log('1. ✅ 基于对标软件优化搜索和选择器逻辑');
    console.log('2. ✅ 实现XPath等价的Cheerio选择器');
    console.log('3. ✅ 标准化数据字段，兼容对标软件格式');
    console.log('4. ✅ 增强错误处理和健壮性');
    console.log('5. ✅ 添加辅助函数提升代码质量');
    console.log('6. ✅ 保持向后兼容的同时提升准确性');
    console.log('\n💡 优化后的 AVSOX Provider 更加健壮和准确！');

  } catch (error) {
    console.error('💥 优化验证过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testAvsoxProviderOptimization();
}

module.exports = { testAvsoxProviderOptimization };
