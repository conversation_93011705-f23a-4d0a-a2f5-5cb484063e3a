import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { NFOIdExtractor } from '@/lib/services/nfo-id-extractor';

export async function POST(request: NextRequest) {
  try {
    const { dryRun = true } = await request.json();

    console.log('Starting NFO ID fix process...');

    // Get all movies without nfoId
    const moviesWithoutNfoId = await prisma.movie.findMany({
      where: {
        OR: [
          { nfoId: null },
          { nfoId: '' },
        ],
      },
      select: {
        id: true,
        filePath: true,
        fileName: true,
        nfoId: true,
      },
    });

    console.log(`Found ${moviesWithoutNfoId.length} movies without nfoId`);

    if (moviesWithoutNfoId.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All movies already have nfoId',
        processed: 0,
        updated: 0,
        skipped: 0,
      });
    }

    const results = {
      processed: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
      details: [] as any[],
    };

    // Process movies in batches
    const batchSize = 20;
    for (let i = 0; i < moviesWithoutNfoId.length; i += batchSize) {
      const batch = moviesWithoutNfoId.slice(i, i + batchSize);
      
      for (const movie of batch) {
        results.processed++;
        
        try {
          // Extract NFO ID
          const extractionResult = await NFOIdExtractor.extractNfoId(movie.filePath);
          
          const detail = {
            id: movie.id,
            fileName: movie.fileName,
            currentNfoId: movie.nfoId,
            extractedNfoId: extractionResult.nfoId,
            source: extractionResult.source,
            confidence: extractionResult.confidence,
            action: 'none' as string,
          };

          if (extractionResult.nfoId) {
            if (!dryRun) {
              // Update the database
              await prisma.movie.update({
                where: { id: movie.id },
                data: { nfoId: extractionResult.nfoId },
              });
              detail.action = 'updated';
              results.updated++;
            } else {
              detail.action = 'would_update';
              results.updated++;
            }
          } else {
            detail.action = 'skipped';
            results.skipped++;
          }

          results.details.push(detail);
        } catch (error) {
          console.error(`Error processing movie ${movie.id}:`, error);
          results.errors++;
          results.details.push({
            id: movie.id,
            fileName: movie.fileName,
            error: error instanceof Error ? error.message : 'Unknown error',
            action: 'error',
          });
        }
      }
    }

    // Check for duplicates after processing
    if (!dryRun && results.updated > 0) {
      const duplicateCheck = await prisma.$queryRaw`
        SELECT nfoId, COUNT(*) as count
        FROM movies 
        WHERE nfoId IS NOT NULL AND nfoId != ''
        GROUP BY LOWER(TRIM(nfoId))
        HAVING COUNT(*) > 1
        ORDER BY count DESC
        LIMIT 10
      ` as any[];

      console.log(`Found ${duplicateCheck.length} duplicate nfoId groups after update`);
    }

    return NextResponse.json({
      success: true,
      dryRun,
      message: dryRun 
        ? `Dry run completed. ${results.updated} movies can be updated.`
        : `Updated ${results.updated} movies with extracted nfoIds.`,
      ...results,
    });

  } catch (error) {
    console.error('Error in NFO ID fix process:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fix NFO IDs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get statistics about NFO IDs
    const stats = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN nfoId IS NOT NULL AND nfoId != '' THEN 1 END) as with_nfo_id,
        COUNT(CASE WHEN nfoId IS NULL OR nfoId = '' THEN 1 END) as without_nfo_id
      FROM movies
    ` as any[];

    const duplicates = await prisma.$queryRaw`
      SELECT nfoId, COUNT(*) as count
      FROM movies 
      WHERE nfoId IS NOT NULL AND nfoId != ''
      GROUP BY LOWER(TRIM(nfoId))
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    ` as any[];

    return NextResponse.json({
      success: true,
      statistics: stats[0],
      duplicateGroups: duplicates,
    });

  } catch (error) {
    console.error('Error getting NFO ID statistics:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
