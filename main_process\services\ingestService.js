// main_process/services/ingestService.js
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const log = require('electron-log');

const execAsync = promisify(exec);

class IngestService {
  constructor() {
    this.log = log.scope('IngestService');
    this.progressCallback = null;
    this.log.info('情报中心服务已初始化');
  }

  /**
   * 设置进度回调函数
   * @param {Function} callback - 进度回调函数
   */
  setProgressCallback(callback) {
    this.progressCallback = callback;
  }

  /**
   * 发送进度更新
   * @param {string} type - 消息类型 (start, progress, complete, error)
   * @param {string} message - 消息内容
   */
  sendProgress(type, message) {
    if (this.progressCallback) {
      this.progressCallback({ type, message });
    }
    this.log.info(`[${type.toUpperCase()}] ${message}`);
  }

  /**
   * 扫描指定工作区路径，查找所有.md文件
   * @param {string} workspacePath - 工作区路径
   * @returns {Promise<Array<string>>} - 找到的.md文件路径数组
   */
  async scanForMdFiles(workspacePath) {
    this.log.info(`开始扫描工作区: ${workspacePath}`);
    
    if (!workspacePath || !fs.existsSync(workspacePath)) {
      throw new Error(`工作区路径不存在: ${workspacePath}`);
    }

    const mdFiles = [];
    const ignoredFolders = ['attachments', '_INCOMING'];
    
    try {
      await this._scanDirectoryRecursively(workspacePath, mdFiles, ignoredFolders);
      
      this.log.info(`扫描完成，共发现 ${mdFiles.length} 个.md文件`);
      return mdFiles;
    } catch (error) {
      this.log.error(`扫描过程中发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 递归扫描目录
   * @param {string} dirPath - 当前目录路径
   * @param {Array<string>} mdFiles - 存储找到的.md文件的数组
   * @param {Array<string>} ignoredFolders - 需要忽略的文件夹名称
   */
  async _scanDirectoryRecursively(dirPath, mdFiles, ignoredFolders) {
    try {
      const items = fs.readdirSync(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
          // 检查是否为需要忽略的文件夹
          if (ignoredFolders.includes(item.name)) {
            this.log.debug(`跳过忽略的文件夹: ${fullPath}`);
            continue;
          }
          
          // 递归扫描子目录
          await this._scanDirectoryRecursively(fullPath, mdFiles, ignoredFolders);
        } else if (item.isFile() && path.extname(item.name).toLowerCase() === '.md') {
          // 找到.md文件
          mdFiles.push(fullPath);
          this.log.debug(`发现.md文件: ${fullPath}`);
        }
      }
    } catch (error) {
      this.log.warn(`无法读取目录 ${dirPath}: ${error.message}`);
      // 继续处理其他目录，不抛出错误
    }
  }

  /**
   * 获取.md文件的基本信息
   * @param {string} filePath - .md文件路径
   * @returns {Object} - 文件信息对象
   */
  async getMdFileInfo(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const fileName = path.basename(filePath);
      const fileDir = path.dirname(filePath);
      
      return {
        path: filePath,
        name: fileName,
        directory: fileDir,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        isReadable: fs.constants.R_OK
      };
    } catch (error) {
      this.log.error(`获取文件信息失败 ${filePath}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量获取多个.md文件的信息
   * @param {Array<string>} filePaths - .md文件路径数组
   * @returns {Promise<Array<Object>>} - 文件信息对象数组
   */
  async getMdFilesInfo(filePaths) {
    const filesInfo = [];
    
    for (const filePath of filePaths) {
      try {
        const fileInfo = await this.getMdFileInfo(filePath);
        filesInfo.push(fileInfo);
      } catch (error) {
        // 记录错误但继续处理其他文件
        this.log.warn(`跳过无法处理的文件: ${filePath}`);
      }
    }
    
    return filesInfo;
  }

  /**
   * 验证工作区路径是否有效
   * @param {string} workspacePath - 工作区路径
   * @returns {boolean} - 是否有效
   */
  validateWorkspacePath(workspacePath) {
    if (!workspacePath || typeof workspacePath !== 'string') {
      return false;
    }

    try {
      return fs.existsSync(workspacePath) && fs.statSync(workspacePath).isDirectory();
    } catch (error) {
      this.log.warn(`验证工作区路径失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 开始情报汇入与解析工作流
   * @param {string} workspacePath - 工作区路径
   * @param {Array<Object>} mdFiles - .md文件信息数组
   * @returns {Promise<Object>} - 处理结果
   */
  async startIngestWorkflow(workspacePath, mdFiles) {
    this.sendProgress('start', '开始情报汇入与解析工作流...');

    const results = {
      total: mdFiles.length,
      processed: 0,
      successful: 0,
      failed: 0,
      quarantined: 0,
      recycled: 0,
      details: []
    };

    try {
      // 确保必要的目录存在
      await this._ensureDirectories(workspacePath);

      // 处理每个.md文件
      for (let i = 0; i < mdFiles.length; i++) {
        const mdFile = mdFiles[i];
        this.sendProgress('progress', `正在处理 ${i + 1}/${mdFiles.length}: ${mdFile.name}`);

        try {
          const result = await this._processMdFile(workspacePath, mdFile);
          results.details.push(result);
          results.processed++;

          if (result.status === 'success') {
            results.successful++;
            results.recycled++;
          } else {
            results.failed++;
            if (result.quarantined) {
              results.quarantined++;
            }
          }
        } catch (error) {
          this.log.error(`处理文件失败 ${mdFile.name}: ${error.message}`);
          results.details.push({
            file: mdFile.name,
            status: 'error',
            error: error.message
          });
          results.failed++;
        }
      }

      this.sendProgress('complete', `汇入完成！处理 ${results.processed} 个文件，成功 ${results.successful} 个，失败 ${results.failed} 个`);
      return results;

    } catch (error) {
      this.sendProgress('error', `汇入工作流失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 确保必要的目录存在
   * @param {string} workspacePath - 工作区路径
   */
  async _ensureDirectories(workspacePath) {
    const quarantineDir = path.join(workspacePath, '_QUARANTINE');
    const recycleDir = path.join(workspacePath, '_RECYCLE_BIN');

    if (!fs.existsSync(quarantineDir)) {
      fs.mkdirSync(quarantineDir, { recursive: true });
      this.log.info(`创建隔离区目录: ${quarantineDir}`);
    }

    if (!fs.existsSync(recycleDir)) {
      fs.mkdirSync(recycleDir, { recursive: true });
      this.log.info(`创建回收站目录: ${recycleDir}`);
    }
  }

  /**
   * 读取.md文件内容
   * @param {string} filePath - 文件路径
   * @returns {Promise<string>} - 文件内容
   */
  async _readMdFile(filePath) {
    try {
      return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
      throw new Error(`读取文件失败: ${error.message}`);
    }
  }

  /**
   * 解析.md文件的YAML元数据
   * @param {string} content - 文件内容
   * @returns {Object} - 解析后的元数据
   */
  _parseMdMetadata(content) {
    const metadata = {};

    // 简单的YAML解析，查找常见字段
    const lines = content.split('\n');
    let inYamlBlock = false;

    for (const line of lines) {
      if (line.trim() === '---') {
        inYamlBlock = !inYamlBlock;
        continue;
      }

      if (inYamlBlock && line.includes(':')) {
        const [key, ...valueParts] = line.split(':');
        const value = valueParts.join(':').trim();

        if (key.trim() === 'password') {
          metadata.password = value.replace(/['"]/g, ''); // 移除引号
        }
      }
    }

    return metadata;
  }

  /**
   * 查找对应的附件文件 - 支持压缩包和txt文件
   * @param {string} workspacePath - 工作区路径
   * @param {Object} mdFile - .md文件信息
   * @param {Object} metadata - 元数据
   * @returns {Promise<string|null>} - 附件文件路径
   */
  async _findAttachment(workspacePath, mdFile, metadata) {
    const attachmentsDir = path.join(workspacePath, 'attachments');

    this.log.info(`正在查找附件: ${mdFile.name}`);
    this.log.info(`附件目录: ${attachmentsDir}`);

    if (!fs.existsSync(attachmentsDir)) {
      this.log.warn(`附件目录不存在: ${attachmentsDir}`);
      return null;
    }

    // 列出所有附件文件用于调试
    try {
      const allFiles = fs.readdirSync(attachmentsDir);
      this.log.info(`附件目录中的所有文件: ${allFiles.join(', ')}`);
    } catch (error) {
      this.log.error(`无法读取附件目录: ${error.message}`);
      return null;
    }

    // 支持的附件类型：压缩包和txt文件
    const mdFileNameBase = path.basename(mdFile.name, '.md');
    const supportedExtensions = ['.rar', '.zip', '.7z', '.txt'];

    this.log.info(`正在查找基础名称: ${mdFileNameBase}`);

    try {
      const files = fs.readdirSync(attachmentsDir);
      const attachmentFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return supportedExtensions.includes(ext);
      });

      this.log.info(`找到的附件文件: ${attachmentFiles.join(', ')}`);

      // 1. 首先尝试精确匹配（搜集器标准）
      for (const file of attachmentFiles) {
        const fileExt = path.extname(file).toLowerCase();
        const fileNameWithoutExt = path.basename(file, fileExt);

        this.log.debug(`精确匹配比较: "${mdFileNameBase}" 与 "${fileNameWithoutExt}"`);

        if (fileNameWithoutExt === mdFileNameBase) {
          this.log.info(`找到精确匹配的附件: ${file}`);
          return path.join(attachmentsDir, file);
        }
      }

      // 2. 番号智能匹配
      this.log.info(`精确匹配失败，尝试番号智能匹配...`);

      for (const file of attachmentFiles) {
        const fileExt = path.extname(file).toLowerCase();
        const fileNameWithoutExt = path.basename(file, fileExt);

        if (this._isIntelligentMatch(mdFileNameBase, fileNameWithoutExt)) {
          this.log.info(`找到智能匹配的附件: ${file}`);
          return path.join(attachmentsDir, file);
        }
      }

    } catch (error) {
      this.log.warn(`扫描附件目录失败: ${error.message}`);
    }

    this.log.warn(`未找到匹配的附件文件: ${mdFile.name}`);
    return null;
  }

  /**
   * 智能匹配算法 - 基于搜集器的文件名规则
   * @param {string} mdFileName - .md文件名（无扩展名）
   * @param {string} attachmentFileName - 附件文件名（无扩展名）
   * @returns {boolean} - 是否匹配
   */
  _isIntelligentMatch(mdFileName, attachmentFileName) {
    // 1. 提取番号进行匹配
    const mdNfoId = this._extractNfoId(mdFileName);
    const attachmentNfoId = this._extractNfoId(attachmentFileName);

    if (mdNfoId && attachmentNfoId) {
      // 如果两个文件都有番号，比较番号
      const normalizedMdNfoId = this._normalizeNfoId(mdNfoId);
      const normalizedAttachmentNfoId = this._normalizeNfoId(attachmentNfoId);

      this.log.debug(`番号匹配: "${normalizedMdNfoId}" vs "${normalizedAttachmentNfoId}"`);

      if (normalizedMdNfoId === normalizedAttachmentNfoId) {
        this.log.info(`番号匹配成功: ${normalizedMdNfoId}`);
        return true;
      }
    }

    // 2. 标准化文件名进行模糊匹配
    const normalizedMd = this._normalizeForMatching(mdFileName);
    const normalizedAttachment = this._normalizeForMatching(attachmentFileName);

    this.log.debug(`标准化匹配: "${normalizedMd}" vs "${normalizedAttachment}"`);

    // 包含匹配
    if (normalizedMd.includes(normalizedAttachment) || normalizedAttachment.includes(normalizedMd)) {
      this.log.info(`标准化匹配成功`);
      return true;
    }

    // 3. 关键词匹配（如果文件名很长）
    if (mdFileName.length > 50 || attachmentFileName.length > 50) {
      const mdKeywords = this._extractKeywords(normalizedMd);
      const attachmentKeywords = this._extractKeywords(normalizedAttachment);

      const commonKeywords = mdKeywords.filter(keyword =>
        attachmentKeywords.some(ak => ak.includes(keyword) || keyword.includes(ak))
      );

      if (commonKeywords.length >= 2) {
        this.log.info(`关键词匹配成功: ${commonKeywords.join(', ')}`);
        return true;
      }
    }

    return false;
  }

  /**
   * 提取番号
   * @param {string} fileName - 文件名
   * @returns {string|null} - 番号
   */
  _extractNfoId(fileName) {
    // 匹配方括号中的番号：[HUNT-491]
    const bracketMatch = fileName.match(/\[([A-Z]+-?\d+[A-Z]*)\]/i);
    if (bracketMatch) {
      return bracketMatch[1];
    }

    // 匹配开头的番号：HUNT-491
    const startMatch = fileName.match(/^([A-Z]+-?\d+[A-Z]*)/i);
    if (startMatch) {
      return startMatch[1];
    }

    return null;
  }

  /**
   * 标准化番号格式
   * @param {string} nfoId - 原始番号
   * @returns {string} - 标准化后的番号
   */
  _normalizeNfoId(nfoId) {
    if (!nfoId) return '';

    // 统一转换为大写
    nfoId = nfoId.toUpperCase();

    // HUNT491 -> HUNT-491
    const match = nfoId.match(/^([A-Z]+)(\d+)([A-Z]*)$/);
    if (match) {
      const [, prefix, number, suffix] = match;
      return suffix ? `${prefix}-${number}-${suffix}` : `${prefix}-${number}`;
    }

    return nfoId;
  }

  /**
   * 标准化文件名用于匹配
   * @param {string} fileName - 原始文件名
   * @returns {string} - 标准化后的文件名
   */
  _normalizeForMatching(fileName) {
    return fileName
      .toLowerCase()
      .replace(/[_\-\s\[\]()（）【】]/g, '') // 移除分隔符
      .replace(/[^\w\u4e00-\u9fff]/g, ''); // 只保留字母数字和中文
  }

  /**
   * 提取关键词
   * @param {string} normalizedName - 标准化后的文件名
   * @returns {Array<string>} - 关键词数组
   */
  _extractKeywords(normalizedName) {
    // 提取长度大于2的连续字符作为关键词
    const keywords = [];
    const minLength = 3;

    for (let i = 0; i <= normalizedName.length - minLength; i++) {
      const keyword = normalizedName.substring(i, i + minLength);
      if (keyword.length >= minLength) {
        keywords.push(keyword);
      }
    }

    return [...new Set(keywords)]; // 去重
  }

  /**
   * 创建临时目录
   * @returns {Promise<string>} - 临时目录路径
   */
  async _createTempDirectory() {
    const tempDir = path.join(require('os').tmpdir(), `ingest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
    fs.mkdirSync(tempDir, { recursive: true });
    return tempDir;
  }

  /**
   * 处理单个.md文件
   * @param {string} workspacePath - 工作区路径
   * @param {Object} mdFile - .md文件信息
   * @returns {Promise<Object>} - 处理结果
   */
  async _processMdFile(workspacePath, mdFile) {
    const result = {
      file: mdFile.name,
      status: 'pending',
      attachmentFound: false,
      extractionSuccess: false,
      quarantined: false,
      recycled: false,
      error: null
    };

    try {
      // 1. 读取与定位
      this.sendProgress('progress', `正在读取 ${mdFile.name}...`);
      const mdContent = await this._readMdFile(mdFile.path);
      const metadata = this._parseMdMetadata(mdContent);

      // 2. 定位附件
      const attachmentPath = await this._findAttachment(workspacePath, mdFile, metadata);
      if (!attachmentPath) {
        result.status = 'failed';
        result.error = '未找到对应的附件文件';
        this.sendProgress('progress', `警告: ${mdFile.name} 未找到对应的附件文件`);
        return result;
      }

      result.attachmentFound = true;
      this.sendProgress('progress', `找到附件: ${path.basename(attachmentPath)}`);

      // 3. 判断附件类型并处理
      const attachmentExt = path.extname(attachmentPath).toLowerCase();

      if (attachmentExt === '.txt') {
        // 直接处理txt文件
        this.sendProgress('progress', `直接处理txt文件: ${path.basename(attachmentPath)}`);

        try {
          // 4. 直接分析txt内容
          const contentAnalysis = await this._analyzeTxtFile(attachmentPath);

          // 5. 情报增补
          await this._enrichMdFile(mdFile.path, contentAnalysis, null);

          // 6. 归入回收站
          await this._moveToRecycleBin(workspacePath, attachmentPath);
          result.recycled = true;
          result.status = 'success';

          this.sendProgress('progress', `${mdFile.name} 已成功增补txt内容，原始附件已移至回收站`);

        } catch (txtError) {
          this.sendProgress('progress', `警告: ${path.basename(attachmentPath)} 处理失败: ${txtError.message}`);
          result.status = 'failed';
          result.error = txtError.message;
        }

      } else {
        // 处理压缩包文件
        const tempDir = await this._createTempDirectory();
        const password = metadata.password || '';

        try {
          await this._extractArchive(attachmentPath, tempDir, password);
          result.extractionSuccess = true;
          this.sendProgress('progress', `附件解压成功: ${path.basename(attachmentPath)}`);

          // 4. 内容分析
          const contentAnalysis = await this._analyzeExtractedContent(tempDir);

          // 5. 情报增补
          await this._enrichMdFile(mdFile.path, contentAnalysis, tempDir);

          // 6. 归入回收站
          await this._moveToRecycleBin(workspacePath, attachmentPath);
          result.recycled = true;
          result.status = 'success';

          this.sendProgress('progress', `${mdFile.name} 已成功增补，原始附件已移至回收站`);

        } catch (extractError) {
          // 解压失败处理
          this.sendProgress('progress', `警告: ${path.basename(attachmentPath)} 解压失败，密码错误。已移至隔离区`);
          await this._quarantineAttachment(workspacePath, attachmentPath);
          await this._markMdAsFailed(mdFile.path, extractError.message);

          result.status = 'failed';
          result.quarantined = true;
          result.error = extractError.message;
        } finally {
          // 7. 清理现场
          await this._cleanupTempDirectory(tempDir);
        }
      }

    } catch (error) {
      result.status = 'error';
      result.error = error.message;
      this.log.error(`处理 ${mdFile.name} 时发生错误: ${error.message}`);
    }

    return result;
  }

  /**
   * 解压压缩包
   * @param {string} archivePath - 压缩包路径
   * @param {string} extractPath - 解压目标路径
   * @param {string} password - 解压密码
   */
  async _extractArchive(archivePath, extractPath, password) {
    const ext = path.extname(archivePath).toLowerCase();
    let command;

    // 构建解压命令
    if (ext === '.rar') {
      // 使用WinRAR或7z解压RAR文件
      if (password) {
        command = `7z x "${archivePath}" -o"${extractPath}" -p"${password}" -y`;
      } else {
        command = `7z x "${archivePath}" -o"${extractPath}" -y`;
      }
    } else if (ext === '.zip' || ext === '.7z') {
      // 使用7z解压ZIP/7Z文件
      if (password) {
        command = `7z x "${archivePath}" -o"${extractPath}" -p"${password}" -y`;
      } else {
        command = `7z x "${archivePath}" -o"${extractPath}" -y`;
      }
    } else {
      throw new Error(`不支持的压缩格式: ${ext}`);
    }

    try {
      const { stdout, stderr } = await execAsync(command);

      // 检查7z的输出，确认解压是否成功
      if (stderr && stderr.includes('Wrong password')) {
        throw new Error('密码错误');
      }
      if (stderr && stderr.includes('ERROR')) {
        throw new Error(`解压失败: ${stderr}`);
      }

      this.log.info(`解压成功: ${archivePath}`);
    } catch (error) {
      if (error.message.includes('密码错误') || error.message.includes('Wrong password')) {
        throw new Error('密码错误');
      }
      throw new Error(`解压失败: ${error.message}`);
    }
  }

  /**
   * 直接分析txt文件内容
   * @param {string} txtFilePath - txt文件路径
   * @returns {Promise<Object>} - 内容分析结果
   */
  async _analyzeTxtFile(txtFilePath) {
    const analysis = {
      textFiles: [],
      nonTextFiles: [],
      totalFiles: 1
    };

    try {
      const stats = fs.statSync(txtFilePath);
      const fileName = path.basename(txtFilePath);

      const fileInfo = {
        name: fileName,
        path: fileName,
        size: stats.size,
        type: 'text'
      };

      // 读取txt文件内容
      fileInfo.content = fs.readFileSync(txtFilePath, 'utf8');
      analysis.textFiles.push(fileInfo);

      this.log.info(`txt文件分析完成: ${fileName}, 大小: ${stats.size} 字节`);
      return analysis;

    } catch (error) {
      throw new Error(`txt文件分析失败: ${error.message}`);
    }
  }

  /**
   * 分析解压后的内容
   * @param {string} extractPath - 解压路径
   * @returns {Promise<Object>} - 内容分析结果
   */
  async _analyzeExtractedContent(extractPath) {
    const analysis = {
      textFiles: [],
      nonTextFiles: [],
      totalFiles: 0
    };

    try {
      const files = await this._getAllFiles(extractPath);
      analysis.totalFiles = files.length;

      for (const filePath of files) {
        const relativePath = path.relative(extractPath, filePath);
        const ext = path.extname(filePath).toLowerCase();
        const stats = fs.statSync(filePath);

        const fileInfo = {
          name: path.basename(filePath),
          path: relativePath,
          size: stats.size,
          type: this._getFileType(ext)
        };

        if (ext === '.txt') {
          // 读取文本文件内容
          try {
            fileInfo.content = fs.readFileSync(filePath, 'utf8');
            analysis.textFiles.push(fileInfo);
          } catch (error) {
            this.log.warn(`读取文本文件失败 ${filePath}: ${error.message}`);
            analysis.nonTextFiles.push(fileInfo);
          }
        } else {
          // 非.txt文件只记录文件信息
          analysis.nonTextFiles.push(fileInfo);
        }
      }

      this.log.info(`内容分析完成: ${analysis.totalFiles} 个文件，${analysis.textFiles.length} 个文本文件，${analysis.nonTextFiles.length} 个非文本文件`);
      return analysis;

    } catch (error) {
      throw new Error(`内容分析失败: ${error.message}`);
    }
  }

  /**
   * 递归获取目录下所有文件
   * @param {string} dirPath - 目录路径
   * @returns {Promise<Array<string>>} - 文件路径数组
   */
  async _getAllFiles(dirPath) {
    const files = [];

    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const fullPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        const subFiles = await this._getAllFiles(fullPath);
        files.push(...subFiles);
      } else if (item.isFile()) {
        files.push(fullPath);
      }
    }

    return files;
  }

  /**
   * 根据文件扩展名确定文件类型
   * @param {string} ext - 文件扩展名
   * @returns {string} - 文件类型
   */
  _getFileType(ext) {
    const typeMap = {
      '.txt': 'text',
      '.torrent': 'torrent',
      '.jpg': 'image',
      '.jpeg': 'image',
      '.png': 'image',
      '.gif': 'image',
      '.bmp': 'image',
      '.webp': 'image',
      '.mp4': 'video',
      '.avi': 'video',
      '.mkv': 'video',
      '.mov': 'video',
      '.wmv': 'video',
      '.flv': 'video',
      '.mp3': 'audio',
      '.wav': 'audio',
      '.flac': 'audio',
      '.aac': 'audio',
      '.pdf': 'document',
      '.doc': 'document',
      '.docx': 'document',
      '.xls': 'document',
      '.xlsx': 'document'
    };

    return typeMap[ext] || 'other';
  }

  /**
   * 增补.md文件内容
   * @param {string} mdFilePath - .md文件路径
   * @param {Object} contentAnalysis - 内容分析结果
   * @param {string} extractPath - 解压路径
   */
  async _enrichMdFile(mdFilePath, contentAnalysis, extractPath) {
    try {
      let originalContent = fs.readFileSync(mdFilePath, 'utf8');

      // 1. 处理.txt文件：直接追加内容到.md文件末尾
      if (contentAnalysis.textFiles.length > 0) {
        for (const textFile of contentAnalysis.textFiles) {
          const separator = '\n\n--- 附件文本内容 ---\n\n';
          originalContent += separator + textFile.content;
        }
      }

      // 2. 处理非.txt文件：更新YAML元数据
      if (contentAnalysis.nonTextFiles.length > 0) {
        originalContent = this._updateYamlMetadata(originalContent, contentAnalysis.nonTextFiles);
      }

      // 3. 如果有.txt文件，标记textEmbedded为true
      if (contentAnalysis.textFiles.length > 0) {
        originalContent = this._updateYamlMetadata(originalContent, [], true);
      }

      // 写入更新后的内容
      fs.writeFileSync(mdFilePath, originalContent, 'utf8');

      this.log.info(`已增补 ${path.basename(mdFilePath)}: ${contentAnalysis.textFiles.length} 个文本文件内容已嵌入，${contentAnalysis.nonTextFiles.length} 个非文本文件已记录到元数据`);

    } catch (error) {
      throw new Error(`增补文件失败: ${error.message}`);
    }
  }

  /**
   * 更新.md文件的YAML元数据
   * @param {string} content - 原始文件内容
   * @param {Array} nonTextFiles - 非文本文件列表
   * @param {boolean} markTextEmbedded - 是否标记文本已嵌入
   * @returns {string} - 更新后的文件内容
   */
  _updateYamlMetadata(content, nonTextFiles = [], markTextEmbedded = false) {
    const lines = content.split('\n');
    let inYamlBlock = false;
    let yamlEndIndex = -1;
    let hasYamlBlock = false;

    // 查找YAML块的结束位置
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim() === '---') {
        if (!inYamlBlock) {
          inYamlBlock = true;
          hasYamlBlock = true;
        } else {
          yamlEndIndex = i;
          break;
        }
      }
    }

    // 如果没有YAML块，在文件开头创建一个
    if (!hasYamlBlock) {
      const yamlContent = this._createYamlBlock(nonTextFiles, markTextEmbedded);
      return yamlContent + '\n' + content;
    }

    // 如果有YAML块，更新它
    if (yamlEndIndex > 0) {
      const beforeYaml = lines.slice(0, 1); // 保留第一个 ---
      const afterYaml = lines.slice(yamlEndIndex); // 保留第二个 --- 及之后的内容
      const existingYaml = lines.slice(1, yamlEndIndex);

      // 更新YAML内容
      const updatedYaml = this._updateExistingYaml(existingYaml, nonTextFiles, markTextEmbedded);

      return [...beforeYaml, ...updatedYaml, ...afterYaml].join('\n');
    }

    return content;
  }

  /**
   * 创建新的YAML块
   * @param {Array} nonTextFiles - 非文本文件列表
   * @param {boolean} markTextEmbedded - 是否标记文本已嵌入
   * @returns {string} - YAML块内容
   */
  _createYamlBlock(nonTextFiles, markTextEmbedded) {
    let yaml = '---\n';

    if (markTextEmbedded) {
      yaml += 'textEmbedded: true\n';
    }

    if (nonTextFiles.length > 0) {
      yaml += 'attachmentContents:\n';
      for (const file of nonTextFiles) {
        yaml += `  - type: "${file.type}"\n`;
        yaml += `    filename: "${file.name}"\n`;
      }
    }

    yaml += '---';
    return yaml;
  }

  /**
   * 更新现有的YAML内容
   * @param {Array} yamlLines - 现有YAML行
   * @param {Array} nonTextFiles - 非文本文件列表
   * @param {boolean} markTextEmbedded - 是否标记文本已嵌入
   * @returns {Array} - 更新后的YAML行
   */
  _updateExistingYaml(yamlLines, nonTextFiles, markTextEmbedded) {
    const updatedLines = [...yamlLines];

    // 添加或更新textEmbedded标记
    if (markTextEmbedded) {
      const textEmbeddedIndex = updatedLines.findIndex(line => line.trim().startsWith('textEmbedded:'));
      if (textEmbeddedIndex >= 0) {
        updatedLines[textEmbeddedIndex] = 'textEmbedded: true';
      } else {
        updatedLines.push('textEmbedded: true');
      }
    }

    // 添加或更新attachmentContents
    if (nonTextFiles.length > 0) {
      const attachmentIndex = updatedLines.findIndex(line => line.trim().startsWith('attachmentContents:'));

      if (attachmentIndex >= 0) {
        // 如果已存在，替换现有内容
        let nextIndex = attachmentIndex + 1;
        while (nextIndex < updatedLines.length && updatedLines[nextIndex].startsWith('  ')) {
          nextIndex++;
        }

        // 删除旧的attachmentContents内容
        updatedLines.splice(attachmentIndex + 1, nextIndex - attachmentIndex - 1);

        // 插入新的内容
        const newAttachments = [];
        for (const file of nonTextFiles) {
          newAttachments.push(`  - type: "${file.type}"`);
          newAttachments.push(`    filename: "${file.name}"`);
        }
        updatedLines.splice(attachmentIndex + 1, 0, ...newAttachments);
      } else {
        // 如果不存在，添加新的
        updatedLines.push('attachmentContents:');
        for (const file of nonTextFiles) {
          updatedLines.push(`  - type: "${file.type}"`);
          updatedLines.push(`    filename: "${file.name}"`);
        }
      }
    }

    return updatedLines;
  }

  /**
   * 移动附件到回收站
   * @param {string} workspacePath - 工作区路径
   * @param {string} attachmentPath - 附件路径
   */
  async _moveToRecycleBin(workspacePath, attachmentPath) {
    const recycleDir = path.join(workspacePath, '_RECYCLE_BIN');
    const fileName = path.basename(attachmentPath);
    const targetPath = path.join(recycleDir, fileName);

    try {
      fs.renameSync(attachmentPath, targetPath);
      this.log.info(`已移动到回收站: ${fileName}`);
    } catch (error) {
      throw new Error(`移动到回收站失败: ${error.message}`);
    }
  }

  /**
   * 隔离有问题的附件
   * @param {string} workspacePath - 工作区路径
   * @param {string} attachmentPath - 附件路径
   */
  async _quarantineAttachment(workspacePath, attachmentPath) {
    const quarantineDir = path.join(workspacePath, '_QUARANTINE');
    const fileName = path.basename(attachmentPath);
    const targetPath = path.join(quarantineDir, fileName);

    try {
      fs.renameSync(attachmentPath, targetPath);
      this.log.info(`已隔离: ${fileName}`);
    } catch (error) {
      this.log.error(`隔离失败: ${error.message}`);
    }
  }

  /**
   * 标记.md文件为处理失败
   * @param {string} mdFilePath - .md文件路径
   * @param {string} errorMessage - 错误信息
   */
  async _markMdAsFailed(mdFilePath, errorMessage) {
    try {
      const originalContent = fs.readFileSync(mdFilePath, 'utf8');
      const failureNote = `\n\n## ❌ 处理失败\n\n**错误信息**: ${errorMessage}\n**失败时间**: ${new Date().toLocaleString()}\n`;
      const updatedContent = originalContent + failureNote;
      fs.writeFileSync(mdFilePath, updatedContent, 'utf8');
    } catch (error) {
      this.log.error(`标记失败状态失败: ${error.message}`);
    }
  }

  /**
   * 清理临时目录
   * @param {string} tempDir - 临时目录路径
   */
  async _cleanupTempDirectory(tempDir) {
    try {
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
        this.log.debug(`已清理临时目录: ${tempDir}`);
      }
    } catch (error) {
      this.log.warn(`清理临时目录失败: ${error.message}`);
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} - 格式化后的大小
   */
  _formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 创建单例实例
const ingestService = new IngestService();

module.exports = ingestService;
