
// soul-forge-electron/src/components/common/ProgressBar.tsx
import React from 'react';

interface ProgressBarProps {
  progress: number;
  message: string | null;
  error: string | null;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress, message, error }) => {
  if (progress === 0 && !message && !error) return null;

  const hasContent = progress > 0 || message || error;

  return (
    <div 
      className={`fixed bottom-0 left-0 right-0 h-6 text-white text-xs transition-opacity duration-500 ${hasContent ? 'opacity-100' : 'opacity-0'}`}
      role="status"
      aria-live="polite"
      aria-atomic="true"
    >
      {error && (
        <div className="bg-red-700 h-full flex items-center justify-center px-2">
          {message || error}
        </div>
      )}
      {!error && message && progress === 0 && (
        <div className="bg-sky-700 h-full flex items-center justify-center px-2">
          {message}
        </div>
      )}
      {!error && progress > 0 && (
        <div className="bg-neutral-700 h-full w-full relative overflow-hidden">
          <div
            className="absolute top-0 left-0 h-full bg-sky-500 transition-all duration-300 ease-linear"
            style={{ width: `${progress}%` }}
            role="progressbar"
            aria-valuenow={progress}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label="Scan progress"
          />
          <span className="absolute inset-0 flex items-center justify-center px-2 z-10 truncate">
            {message || `${progress.toFixed(0)}%`}
          </span>
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
