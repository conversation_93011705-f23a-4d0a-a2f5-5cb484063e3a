// SoulForge Collector 页面组件 - 一体化标签页式数据管理中心
import React, { useState, useEffect } from 'react';
import { LuLink2, LuDownload, LuDatabase, LuSettings, LuPlay, LuPause, LuRotateCw, LuFileText, LuFolderOpen, LuSquare, LuClipboard, LuInfo, LuTrash2, LuChevronDown, LuChevronRight, LuSearch, LuFilter, LuArchive, LuActivity } from 'react-icons/lu';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from './ui/Tabs';
import IngestCenterPage from './IngestCenterPage';
import { ArchiveFilterBar } from './ArchiveFilterBar';
import { ArchiveDataGrid } from './ArchiveDataGrid';

import { LocalAssetProxyCreator } from './archive/LocalAssetProxyCreator';
import { LinkPreviewModal } from './LinkPreviewModal';
import { BatchAIProgressModal } from './BatchAIProgressModal';
import { useAppView } from '../hooks/useAppView';
import { useArchiveStore } from '../hooks/useArchiveStore';
import { toast } from 'react-hot-toast';

// 定义类型
interface Forum {
  key: string;
  name: string;
}

interface StatusUpdate {
  status: string;
  message: string;
  timestamp: Date;
}

interface CollectedLink {
  postUrl: string;
  postTitle: string;
  nfoId?: string;
  magnetLink?: string;
  ed2kLink?: string;
  collectionDate: string;
}

interface TaskResult {
  success: boolean;
  taskId?: number;
  result?: {
    collectedCount: number;
    pages: number;
    links: CollectedLink[];
    message?: string;
  };
  error?: string;
}

interface CollectorPageProps {
  onMovieDataChanged?: () => void;
}

const CollectorPage: React.FC<CollectorPageProps> = ({ onMovieDataChanged }) => {
  const { currentAppView, setCurrentAppView } = useAppView();
  const { fetchResults, setBatchStatus } = useArchiveStore();

  // 状态管理
  const [taskStatus, setTaskStatus] = useState<string>('idle'); // 'idle', 'running', 'stopped', 'completed', 'failed'
  const [statusLogs, setStatusLogs] = useState<StatusUpdate[]>([]);
  const [forums, setForums] = useState<Forum[]>([]);
  const [selectedForum, setSelectedForum] = useState<string>('');
  const [targetUrl, setTargetUrl] = useState<string>('');

  // 配置持久化键名
  const CONFIG_KEYS = {
    SELECTED_FORUM: 'collector_selected_forum',
    TARGET_URL: 'collector_target_url',
    WORKSPACE_PATH: 'collector_workspace_path',
    ENABLE_DOWNLOAD: 'collector_enable_download'
  };
  const [maxPages, setMaxPages] = useState<number>(5);
  const [pageDelay, setPageDelay] = useState<number>(2000);
  const [postDelay, setPostDelay] = useState<number>(1000);
  const [scrapeDays, setScrapeDays] = useState<number>(0);
  const [collectedLinks, setCollectedLinks] = useState<CollectedLink[]>([]);
  const [collectedCount, setCollectedCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentTaskId, setCurrentTaskId] = useState<number | null>(null);
  const [isStoppingTask, setIsStoppingTask] = useState<boolean>(false);
  const [isForceStoppingTask, setIsForceStoppingTask] = useState<boolean>(false);

  // 数据库数据状态
  const [collectedData, setCollectedData] = useState<any[]>([]);
  const [dataLoading, setDataLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 50,
    total: 0,
    totalPages: 0
  });

  // 工作区配置状态
  const [enableDownload, setEnableDownload] = useState<boolean>(true); // 默认启用
  const [workspacePath, setWorkspacePath] = useState<string>('');
  const [downloadConfigSaving, setDownloadConfigSaving] = useState<boolean>(false);

  // 历史档案管理状态
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sortField, setSortField] = useState<string>('collection_date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isPurging, setIsPurging] = useState<boolean>(false);



  // 配置持久化函数
  const loadConfig = () => {
    try {
      const savedForum = localStorage.getItem(CONFIG_KEYS.SELECTED_FORUM);
      const savedUrl = localStorage.getItem(CONFIG_KEYS.TARGET_URL);
      const savedWorkspace = localStorage.getItem(CONFIG_KEYS.WORKSPACE_PATH);
      const savedEnableDownload = localStorage.getItem(CONFIG_KEYS.ENABLE_DOWNLOAD);

      if (savedForum) setSelectedForum(savedForum);
      if (savedUrl) setTargetUrl(savedUrl);
      if (savedWorkspace) setWorkspacePath(savedWorkspace);
      if (savedEnableDownload !== null) setEnableDownload(savedEnableDownload === 'true');
    } catch (error) {
      console.warn('加载配置失败:', error);
    }
  };

  const saveConfig = () => {
    try {
      localStorage.setItem(CONFIG_KEYS.SELECTED_FORUM, selectedForum);
      localStorage.setItem(CONFIG_KEYS.TARGET_URL, targetUrl);
      localStorage.setItem(CONFIG_KEYS.WORKSPACE_PATH, workspacePath);
      localStorage.setItem(CONFIG_KEYS.ENABLE_DOWNLOAD, enableDownload.toString());
    } catch (error) {
      console.warn('保存配置失败:', error);
    }
  };

  // 组件加载时的初始化
  useEffect(() => {
    // 获取支持的论坛列表
    const loadForums = async () => {
      try {
        const result = await window.sfeElectronAPI.collectorGetForums();
        if (result.success && result.forums) {
          setForums(result.forums);
          console.log('✅ 论坛列表加载成功:', result.forums);
        } else {
          console.error('❌ 获取论坛列表失败:', result.error);
          addStatusLog('error', '获取论坛列表失败');
        }
      } catch (error) {
        console.error('❌ 获取论坛列表异常:', error);
        addStatusLog('error', '获取论坛列表异常');
      }
    };

    // 注册状态更新监听器
    const removeListener = window.sfeElectronAPI.onCollectorStatusUpdate((statusUpdate: StatusUpdate) => {
      console.log('📡 收到状态更新:', statusUpdate);
      setTaskStatus(statusUpdate.status);

      // 只有非完成状态才显示状态日志，完成状态由任务完成监听器处理
      if (statusUpdate.status !== 'completed') {
        addStatusLog(statusUpdate.status, statusUpdate.message);
      }

      // 如果任务完成，刷新数据
      if (statusUpdate.status === 'task-completed' || statusUpdate.status === 'completed') {
        console.log('🔄 任务完成，刷新数据...');
        setTimeout(() => {
          fetchCollectedData(1);
        }, 1000); // 延迟1秒刷新，确保数据已保存
      }
    });

    // 注册搜集任务完成监听器
    const removeTaskCompletedListener = window.sfeElectronAPI.onCollectorTaskCompleted?.((taskResult: any) => {
      console.log('🎉 收到搜集任务完成事件:', taskResult);

      if (taskResult.result) {
        setCollectedCount(taskResult.result.collectedCount || 0);
        if (taskResult.result.links) {
          setCollectedLinks(taskResult.result.links);
        }
        addStatusLog('completed', `搜集完成: 找到 ${taskResult.result.collectedCount || 0} 个链接`);

        // 刷新数据列表
        setTimeout(() => {
          fetchCollectedData(1);
        }, 1000);
      }
    });

    // 注册批量 AI 分析进度监听器
    const removeBatchAIListener = window.sfeElectronAPI.onBatchAIProgress?.((progressData: any) => {
      console.log('🧠 收到批量AI分析进度:', progressData);

      // 更新批量分析状态
      setBatchStatus(progressData);

      // 显示 toast 通知
      if (progressData.status === 'started') {
        toast.success(`已启动 ${progressData.total} 条记录的批量分析任务`);
      } else if (progressData.status === 'processing') {
        if (progressData.currentTitle) {
          toast.loading(`正在分析: ${progressData.currentTitle}`, {
            id: 'batch-ai-progress',
            duration: 2000
          });
        }
      } else if (progressData.status === 'completed') {
        toast.dismiss('batch-ai-progress');
        toast.success(
          `批量分析完成！成功 ${progressData.successCount} 条，失败 ${progressData.errorCount} 条`,
          { duration: 5000 }
        );
        // 刷新数据
        setTimeout(() => {
          fetchResults();
        }, 1000);
      } else if (progressData.status === 'error') {
        toast.error(progressData.message || '分析过程中出现错误', { duration: 3000 });
      }
    });

    // 获取当前服务状态
    const loadCurrentStatus = async () => {
      try {
        const result = await window.sfeElectronAPI.collectorGetStatus();
        if (result.success && result.status) {
          // 使用后端返回的详细状态信息
          const statusData = result.status;

          // 优先使用 currentStatus，如果没有则使用 status
          const currentStatus = statusData.currentStatus || statusData.status || 'idle';

          // 只有当状态真的发生变化时才更新，避免不必要的重渲染
          if (currentStatus !== taskStatus) {
            setTaskStatus(currentStatus);
          }
        }
      } catch (error) {
        console.error('❌ 获取服务状态失败:', error);
      }
    };

    loadForums();
    loadCurrentStatus();
    fetchCollectedData(1); // 加载初始数据
    loadConfig(); // 加载保存的配置

    // 定期获取状态
    const statusInterval = setInterval(loadCurrentStatus, 1000); // 每秒获取一次状态

    // 清理函数
    return () => {
      removeListener();
      if (removeTaskCompletedListener) {
        removeTaskCompletedListener();
      }
      if (removeBatchAIListener) {
        removeBatchAIListener();
      }
      clearInterval(statusInterval);
    };
  }, []);

  // 自动保存配置当设置改变时
  useEffect(() => {
    if (selectedForum || targetUrl || workspacePath) {
      saveConfig();
    }
  }, [selectedForum, targetUrl, workspacePath, enableDownload]);

  // 当搜索条件或排序条件变化时，重新获取数据
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchEnhancedCollectedData(1);
    }, 300); // 防抖，300ms后执行搜索

    return () => clearTimeout(timeoutId);
  }, [searchTerm, sortField, sortOrder]);

  // 处理标签页切换
  const handleTabChange = (value: string) => {
    if (value === 'archive-management') {
      // 切换到历史档案标签页时，立即加载数据
      fetchResults();
    }
  };

  // 将论坛标识符转换为真实论坛名称
  const getForumDisplayName = (forumKey: string) => {
    const forumMapping: { [key: string]: string } = {
      'forumA': 'x1080x',
      'forumB': '98堂'
    };
    return forumMapping[forumKey] || forumKey || '未知';
  };

  // 添加状态日志
  const addStatusLog = (status: string, message: string) => {
    const newLog: StatusUpdate = {
      status,
      message,
      timestamp: new Date()
    };
    setStatusLogs(prev => [...prev.slice(-19), newLog]); // 保留最近20条日志
  };

  // 快捷粘贴剪贴板内容到目标URL
  const pasteFromClipboard = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      if (clipboardText && clipboardText.trim()) {
        setTargetUrl(clipboardText.trim());
        addStatusLog('success', '已粘贴剪贴板内容到目标URL');

        // 如果是HTTP链接，尝试自动检测论坛
        if (clipboardText.startsWith('http')) {
          const forum = forums.find(f => clipboardText.includes(f.domain));
          if (forum && !selectedForum) {
            setSelectedForum(forum.key);
            addStatusLog('success', `已自动检测到${forum.name}论坛`);
          }
        }
        return true;
      } else {
        addStatusLog('warning', '剪贴板内容为空');
        return false;
      }
    } catch (error) {
      console.warn('无法读取剪贴板:', error);
      addStatusLog('error', '无法读取剪贴板内容，请手动粘贴');
      return false;
    }
  };

  // 选择论坛时的处理
  const handleForumChange = (forumKey: string) => {
    setSelectedForum(forumKey);
  };



  // 删除搜集记录
  const handleDeleteRecord = async (id: number) => {
    try {
      // 显示确认对话框
      const confirmed = window.confirm(
        '确定要删除此条记录吗？删除后，下次搜集将可能重新抓取此帖。'
      );

      if (!confirmed) {
        return;
      }

      const result = await window.sfeElectronAPI.collectorDeleteLink(id);

      if (result.success) {
        addStatusLog('success', '记录删除成功');
        // 刷新数据
        await fetchCollectedData(pagination.page);
      } else {
        addStatusLog('error', `删除失败: ${result.error}`);
      }
    } catch (error) {
      console.error('删除记录失败:', error);
      addStatusLog('error', `删除记录失败: ${error}`);
    }
  };

  // 打开档案文件
  const handleOpenArchiveFile = async (filePath: string) => {
    console.log('🔍 handleOpenArchiveFile 被调用，文件路径:', filePath);
    addStatusLog('info', `尝试打开档案文件: ${filePath}`);

    if (!filePath) {
      console.log('❌ 文件路径为空');
      addStatusLog('error', '文件路径为空，无法打开档案文件');
      return;
    }

    try {
      console.log('🔍 检查文件是否存在:', filePath);
      // 首先检查文件是否存在
      const fileExists = await window.sfeElectronAPI.fileExists(filePath);
      console.log('📁 文件存在性检查结果:', fileExists);

      if (!fileExists) {
        console.log('❌ 文件不存在');
        addStatusLog('error', `文件已移动或不存在: ${filePath}`);
        return;
      }

      console.log('🚀 尝试打开文件');
      // 文件存在，尝试打开
      const result = await window.sfeElectronAPI.openFileInDefaultApp(filePath);
      console.log('📂 打开文件结果:', result);

      if (result.success) {
        addStatusLog('success', `已打开档案文件: ${filePath}`);
      } else {
        addStatusLog('error', `打开文件失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 打开档案文件失败:', error);
      addStatusLog('error', `打开档案文件失败: ${error}`);
    }
  };

  // 打开md文档
  const handleOpenMdDocument = async (mdDocumentPath: string, postTitle: string) => {
    console.log('🔍 handleOpenMdDocument 被调用，mdDocumentPath:', mdDocumentPath, 'postTitle:', postTitle);
    addStatusLog('info', `尝试打开md文档: ${postTitle}`);

    if (!mdDocumentPath) {
      console.log('❌ md文档路径为空');
      addStatusLog('error', '该帖子没有关联的md文档');
      return;
    }

    try {
      console.log('🔍 检查md文档是否存在:', mdDocumentPath);
      // 首先检查文件是否存在
      const fileExists = await window.sfeElectronAPI.fileExists(mdDocumentPath);
      console.log('📁 md文档存在性检查结果:', fileExists);

      if (!fileExists) {
        console.log('❌ md文档不存在');
        addStatusLog('error', `md文档已移动或不存在: ${mdDocumentPath}`);
        return;
      }

      console.log('🚀 尝试打开md文档');
      // 文件存在，尝试打开
      const result = await window.sfeElectronAPI.openFileInDefaultApp(mdDocumentPath);
      console.log('📂 打开md文档结果:', result);

      if (result.success) {
        addStatusLog('success', `已打开md文档: ${mdDocumentPath}`);
      } else {
        addStatusLog('error', `打开md文档失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 打开md文档失败:', error);
      addStatusLog('error', `打开md文档失败: ${error}`);
    }
  };

  // 关联md文档
  const handleLinkMdDocument = async (id: number, postTitle: string) => {
    console.log('🔍 handleLinkMdDocument 被调用，id:', id, 'postTitle:', postTitle);

    try {
      // 打开文件选择对话框
      const result = await window.sfeElectronAPI.selectFile({
        title: `为 "${postTitle}" 选择md文档`,
        filters: [
          { name: 'Markdown文档', extensions: ['md'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      });

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        console.log('用户取消了文件选择');
        return;
      }

      const selectedPath = result.filePaths[0];
      console.log('🔍 用户选择的md文档路径:', selectedPath);

      // 更新数据库中的md文档路径
      const updateResult = await window.sfeElectronAPI.collectorUpdateMdPath(id, selectedPath);

      if (updateResult.success) {
        addStatusLog('success', `已关联md文档: ${selectedPath}`);
        console.log('✅ md文档路径更新成功');

        // 刷新数据列表
        await fetchCollectedData(pagination.page);
      } else {
        addStatusLog('error', `关联md文档失败: ${updateResult.error}`);
        console.error('❌ md文档路径更新失败:', updateResult.error);
      }
    } catch (error) {
      console.error('❌ 关联md文档失败:', error);
      addStatusLog('error', `关联md文档失败: ${error}`);
    }
  };

  // AI智能分析
  const handleAiAnalysis = async (id: number, postTitle: string) => {
    console.log('🤖 handleAiAnalysis 被调用，id:', id, 'postTitle:', postTitle);

    try {
      addStatusLog('info', `开始AI分析: ${postTitle}`);

      // 调用AI分析API
      const result = await window.sfeElectronAPI.collectorAnalyzeWithAi(id);

      if (result.success) {
        addStatusLog('success', `AI分析完成: ${result.tags?.join(', ') || '无标签'}`);
        console.log('✅ AI分析成功，生成标签:', result.tags);

        // 刷新数据列表以显示新的AI标签
        await fetchCollectedData(pagination.page);
      } else {
        addStatusLog('error', `AI分析失败: ${result.error}`);
        console.error('❌ AI分析失败:', result.error);
      }
    } catch (error) {
      console.error('❌ AI分析异常:', error);
      addStatusLog('error', `AI分析异常: ${error}`);
    }
  };

  // 获取搜集数据
  const fetchCollectedData = async (page: number = 1) => {
    try {
      setDataLoading(true);

      const options = {
        page,
        pageSize: 50, // 固定为50条每页
        sortField: 'collection_date',
        sortOrder: 'DESC'
      };

      const result = await window.sfeElectronAPI.collectorGetData(options);

      if (result.success) {
        setCollectedData(result.data || []);
        setPagination(result.pagination || {
          page: 1,
          pageSize: 50,
          total: 0,
          totalPages: 0
        });
        console.log('✅ 搜集数据加载成功:', result.data?.length || 0, '条记录');
      } else {
        console.error('❌ 获取搜集数据失败:', result.error);
        addStatusLog('error', `获取数据失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 获取搜集数据异常:', error);
      addStatusLog('error', `获取数据异常: ${error}`);
    } finally {
      setDataLoading(false);
    }
  };

  // 保存下载配置
  const saveDownloadConfig = async () => {
    try {
      setDownloadConfigSaving(true);

      const downloadConfig = {
        enableDownload,
        workspacePath: workspacePath.trim()
      };

      const result = await window.sfeElectronAPI.collectorConfigureDownload(downloadConfig);

      if (result.success) {
        addStatusLog('success', `下载配置已保存: ${enableDownload ? '启用' : '禁用'}`);
        console.log('✅ 下载配置保存成功:', downloadConfig);
      } else {
        addStatusLog('error', `保存下载配置失败: ${result.error}`);
        console.error('❌ 下载配置保存失败:', result.error);
      }
    } catch (error) {
      console.error('❌ 保存下载配置异常:', error);
      addStatusLog('error', `保存下载配置异常: ${error}`);
    } finally {
      setDownloadConfigSaving(false);
    }
  };

  // 选择工作区文件夹
  const selectWorkspaceFolder = async () => {
    try {
      const result = await window.sfeElectronAPI.selectFolder();
      if (result.success && !result.canceled) {
        setWorkspacePath(result.folderPath);
        addStatusLog('success', `已选择工作区文件夹: ${result.folderPath}`);
      }
    } catch (error) {
      console.error('❌ 选择工作区文件夹失败:', error);
      addStatusLog('error', `选择工作区文件夹失败: ${error}`);
    }
  };

  // 历史档案管理函数
  // 获取增强的搜集数据（支持搜索、排序、分页）
  const fetchEnhancedCollectedData = async (page: number = 1) => {
    try {
      setDataLoading(true);

      const options = {
        page,
        pageSize: 50,
        sortField,
        sortOrder: sortOrder.toUpperCase(),
        searchTerm: searchTerm.trim()
      };

      const result = await window.sfeElectronAPI.collectorGetData(options);

      if (result.success) {
        setCollectedData(result.data || []);
        setPagination(result.pagination || {
          page: 1,
          pageSize: 50,
          total: 0,
          totalPages: 0
        });
        console.log('✅ 增强搜集数据加载成功:', result.data?.length || 0, '条记录');
      } else {
        console.error('❌ 获取增强搜集数据失败:', result.error);
        addStatusLog('error', `获取数据失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 获取增强搜集数据异常:', error);
      addStatusLog('error', `获取数据异常: ${error}`);
    } finally {
      setDataLoading(false);
    }
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedItems.size === collectedData.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(collectedData.map(item => item.id)));
    }
  };

  // 单项选择
  const handleSelectItem = (id: number) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedItems(newSelected);
  };

  // 批量删除记录（用于重抓）
  const handleBatchDelete = async () => {
    if (selectedItems.size === 0) return;

    try {
      setIsDeleting(true);
      const ids = Array.from(selectedItems);

      const result = await window.sfeElectronAPI.collectorDeleteLinks(ids);

      if (result.success) {
        addStatusLog('success', `已删除 ${ids.length} 条记录`);
        setSelectedItems(new Set());
        await fetchEnhancedCollectedData(pagination.page);
      } else {
        addStatusLog('error', `删除记录失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 批量删除记录失败:', error);
      addStatusLog('error', `批量删除记录失败: ${error}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // 批量清除（记录+文件）
  const handleBatchPurge = async () => {
    if (selectedItems.size === 0) return;

    try {
      setIsPurging(true);
      const ids = Array.from(selectedItems);

      const result = await window.sfeElectronAPI.collectorPurgeLinks(ids);

      if (result.success) {
        addStatusLog('success', `已清除 ${ids.length} 条记录及相关文件`);
        setSelectedItems(new Set());
        await fetchEnhancedCollectedData(pagination.page);
      } else {
        addStatusLog('error', `清除失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 批量清除失败:', error);
      addStatusLog('error', `批量清除失败: ${error}`);
    } finally {
      setIsPurging(false);
    }
  };



  // 启动搜集任务
  const handleStartTask = async () => {
    if (!selectedForum) {
      addStatusLog('error', '请选择目标论坛');
      return;
    }

    if (!targetUrl.trim()) {
      addStatusLog('error', '请输入目标URL');
      return;
    }

    try {
      setIsLoading(true);
      setStatusLogs([]); // 清空之前的日志
      addStatusLog('info', '正在启动搜集任务...');

      // 首先配置下载设置
      if (workspacePath.trim()) {
        addStatusLog('info', '正在配置下载设置...');
        const downloadConfig = {
          enableDownload,
          workspacePath: workspacePath.trim()
        };

        console.log('🔧 配置下载设置:', downloadConfig);
        const configResult = await window.sfeElectronAPI.collectorConfigureDownload(downloadConfig);

        if (configResult.success) {
          addStatusLog('success', `下载配置已设置: ${enableDownload ? '启用' : '禁用'}, 路径: ${workspacePath}`);
        } else {
          addStatusLog('warning', `下载配置设置失败: ${configResult.error}`);
        }
      }

      const options = {
        maxPages,
        delay: postDelay,
        pageDelay,
        scrapeDays
      };

      console.log('🚀 启动搜集任务:', { selectedForum, targetUrl, options });

      const result: TaskResult = await window.sfeElectronAPI.collectorStartTask(selectedForum, targetUrl, options);

      if (result.success) {
        setCurrentTaskId(result.taskId || null);
        addStatusLog('success', '搜集任务启动成功');
        setIsLoading(false); // 启动成功后立即设置为false，让停止按钮可用

        // 不在这里显示搜集结果，等待任务完成事件
      } else {
        addStatusLog('error', `启动失败: ${result.error}`);
        setTaskStatus('failed');
        setIsLoading(false); // 启动失败后也要设置为false
      }
    } catch (error) {
      console.error('❌ 启动搜集任务失败:', error);
      addStatusLog('error', `启动异常: ${error}`);
      setTaskStatus('failed');
      setIsLoading(false); // 异常时也要设置为false
    }
  };

  // 停止搜集任务
  const handleStopTask = async () => {
    try {
      setIsLoading(true);
      addStatusLog('info', '正在停止搜集任务...');

      const result = await window.sfeElectronAPI.collectorStopTask();

      if (result.success) {
        addStatusLog('info', '搜集任务已停止');
        setTaskStatus('stopped');
        setCurrentTaskId(null);
      } else {
        addStatusLog('error', `停止失败: ${result.message}`);
      }
    } catch (error) {
      console.error('❌ 停止搜集任务失败:', error);
      addStatusLog('error', `停止异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取状态显示信息
  const getStatusInfo = () => {
    switch (taskStatus) {
      case 'running':
        return { text: '运行中', color: 'text-green-400', icon: LuRotateCw, spinning: true };
      case 'scraping':
        return { text: '搜集中', color: 'text-green-400', icon: LuRotateCw, spinning: true };
      case 'paging':
        return { text: '翻页中', color: 'text-green-400', icon: LuRotateCw, spinning: true };
      case 'initializing':
        return { text: '初始化', color: 'text-blue-400', icon: LuRotateCw, spinning: true };
      case 'refreshing':
        return { text: '刷新中', color: 'text-blue-400', icon: LuRotateCw, spinning: true };
      case 'downloading':
        return { text: '下载中', color: 'text-amber-400', icon: LuDownload, spinning: true };
      case 'download-completed':
        return { text: '下载完成', color: 'text-green-400', icon: LuDownload, spinning: false };
      case 'saving':
        return { text: '保存中', color: 'text-blue-400', icon: LuDatabase, spinning: true };
      case 'export-completed':
        return { text: '导出完成', color: 'text-green-400', icon: LuFileText, spinning: false };
      case 'completed':
        return { text: '已完成', color: 'text-blue-400', icon: LuDatabase, spinning: false };
      case 'failed':
        return { text: '失败', color: 'text-red-400', icon: LuPause, spinning: false };
      case 'stopped':
        return { text: '已停止', color: 'text-yellow-400', icon: LuPause, spinning: false };
      case 'force-stopped':
        return { text: '强制停止', color: 'text-orange-400', icon: LuPause, spinning: false };
      case 'paused':
        return { text: '已暂停', color: 'text-amber-400', icon: LuPause, spinning: false };
      default:
        return { text: '空闲', color: 'text-neutral-400', icon: LuPause, spinning: false };
    }
  };

  // 停止搜集任务
  const stopCollectionTask = async () => {
    try {
      setIsStoppingTask(true);
      addStatusLog('info', '正在停止搜集任务...');

      const result = await window.sfeElectronAPI.collectorStopTask();
      if (result.success) {
        addStatusLog('success', '搜集任务已停止');
      } else {
        addStatusLog('error', `停止任务失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 停止搜集任务失败:', error);
      addStatusLog('error', `停止任务异常: ${error}`);
    } finally {
      setIsStoppingTask(false);
    }
  };

  // 强制停止搜集任务
  const forceStopCollectionTask = async () => {
    try {
      setIsForceStoppingTask(true);
      addStatusLog('warning', '正在强制停止搜集任务...');

      const result = await window.sfeElectronAPI.collectorForceStopTask();
      if (result.success) {
        addStatusLog('success', '搜集任务已强制停止，现有数据已保存');
      } else {
        addStatusLog('error', `强制停止任务失败: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ 强制停止搜集任务失败:', error);
      addStatusLog('error', `强制停止任务异常: ${error}`);
    } finally {
      setIsForceStoppingTask(false);
    }
  };

  const statusInfo = getStatusInfo();
  // 修复：任务运行状态应该包括所有活跃状态
  const isTaskRunning = [
    'running', 'scraping', 'paging', 'initializing', 'refreshing',
    'downloading', 'saving', 'export-completed', 'download-completed', 'paused'
  ].includes(taskStatus);
  const canStartTask = !isTaskRunning && !isLoading;
  // 修复：停止按钮只要任务在运行就应该可用，不依赖isLoading状态
  const canStopTask = isTaskRunning;

  return (
    <div className="min-h-screen bg-[#1e1e1e] text-neutral-100 p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-amber-400 mb-2 flex items-center">
          <LuLink2 className="mr-3" size={32} />
          链接搜集器
        </h1>
        <p className="text-neutral-400 text-lg">
          一体化标签页式数据管理中心
        </p>
      </div>

      {/* 标签页导航 */}
      <Tabs defaultValue={currentAppView === 'ingestCenter' ? 'ingest-center' : 'task-control'} className="w-full" onValueChange={handleTabChange}>
        <TabsList className="mb-6">
          <TabsTrigger value="task-control" className="flex items-center">
            <LuActivity className="mr-2" size={16} />
            任务控制
          </TabsTrigger>

          <TabsTrigger value="archive-management" className="flex items-center">
            <LuArchive className="mr-2" size={16} />
            历史档案
          </TabsTrigger>
          <TabsTrigger value="ingest-center" className="flex items-center">
            <LuFileText className="mr-2" size={16} />
            MD数据归档
          </TabsTrigger>
        </TabsList>

        {/* 任务控制标签页 */}
        <TabsContent value="task-control">
          {/* 智能提示区域 */}
          {(!selectedForum || !targetUrl || !workspacePath) && (
        <div className="mb-6 p-4 bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-500/30 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="text-blue-400 mt-1">
              <LuClipboard size={20} />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-300 mb-2">快速开始指南</h3>
              <div className="space-y-2 text-sm text-neutral-300">
                {!selectedForum && (
                  <div className="flex items-center space-x-2">
                    <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
                    <span>选择目标论坛，或复制论坛URL到剪贴板后点击"检测"</span>
                  </div>
                )}
                {!workspacePath && (
                  <div className="flex items-center space-x-2">
                    <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                    <span>配置工作区路径，用于保存下载文件和搜集日志</span>
                  </div>
                )}
                {selectedForum && workspacePath && !targetUrl && (
                  <div className="flex items-center space-x-2">
                    <span className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">✓</span>
                    <span>配置完成！现在可以使用"快速启动"功能</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 功能状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* 搜集状态 */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-neutral-200">搜集状态</h3>
            <statusInfo.icon
              className={`${statusInfo.color} ${statusInfo.spinning ? 'animate-spin' : ''}`}
              size={20}
            />
          </div>
          <div className={`text-2xl font-bold ${statusInfo.color} mb-2`}>
            {statusInfo.text}
          </div>
          <p className="text-sm text-neutral-400">
            {currentTaskId ? `任务ID: ${currentTaskId}` : '等待启动搜集任务'}
          </p>
        </div>

        {/* 已搜集链接 */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-neutral-200">已搜集</h3>
            <LuDatabase className="text-blue-400" size={20} />
          </div>
          <div className="text-2xl font-bold text-blue-400 mb-2">{collectedCount}</div>
          <p className="text-sm text-neutral-400">个链接记录</p>
        </div>

        {/* 下载队列 */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-neutral-200">下载队列</h3>
            <LuDownload className="text-green-400" size={20} />
          </div>
          <div className="text-2xl font-bold text-green-400 mb-2">0</div>
          <p className="text-sm text-neutral-400">个待下载项目</p>
        </div>

        {/* 配置状态 */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-neutral-200">配置状态</h3>
            <LuSettings className="text-purple-400" size={20} />
          </div>
          <div className="text-2xl font-bold text-purple-400 mb-2">2</div>
          <p className="text-sm text-neutral-400">个论坛配置</p>
        </div>
      </div>

      {/* 主要功能区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 搜集控制面板 */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <h2 className="text-xl font-semibold text-neutral-200 mb-4 flex items-center">
            <LuPlay className="mr-2" size={20} />
            搜集控制
          </h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">
                目标论坛
              </label>
              <select
                value={selectedForum}
                onChange={(e) => handleForumChange(e.target.value)}
                disabled={isTaskRunning}
                className="w-full bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <option value="">选择论坛...</option>
                {Array.isArray(forums) && forums.map((forum) => (
                  <option key={forum.key} value={forum.key}>
                    {forum.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-300 mb-2">
                目标URL
              </label>
              <div className="flex space-x-2">
                <input
                  type="url"
                  value={targetUrl}
                  onChange={(e) => setTargetUrl(e.target.value)}
                  disabled={isTaskRunning}
                  className="flex-1 bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="输入目标板块的URL"
                />
                <button
                  onClick={pasteFromClipboard}
                  disabled={isTaskRunning}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium py-2 px-3 rounded-md transition-colors duration-200 flex items-center"
                  title="粘贴剪贴板内容到目标URL"
                >
                  <LuClipboard className="mr-1" size={16} />
                  粘贴
                </button>
              </div>
            </div>

            {/* 搜集限制配置 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  最多搜集页数
                </label>
                <input
                  type="number"
                  min="1"
                  value={maxPages}
                  onChange={(e) => setMaxPages(parseInt(e.target.value) || 5)}
                  disabled={isTaskRunning}
                  className="w-full bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="5"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  只搜集最近 N 天的帖子
                </label>
                <input
                  type="number"
                  min="0"
                  value={scrapeDays}
                  onChange={(e) => setScrapeDays(parseInt(e.target.value) || 0)}
                  disabled={isTaskRunning}
                  className="w-full bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="0 = 不限制"
                />
              </div>
            </div>



            {/* 延迟配置 */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  帖子间延迟 (毫秒)
                </label>
                <input
                  type="number"
                  min="0"
                  step="100"
                  value={postDelay}
                  onChange={(e) => setPostDelay(parseInt(e.target.value) || 1000)}
                  disabled={isTaskRunning}
                  className="w-full bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="1000"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral-300 mb-2">
                  翻页延迟 (毫秒)
                </label>
                <input
                  type="number"
                  min="0"
                  step="100"
                  value={pageDelay}
                  onChange={(e) => setPageDelay(parseInt(e.target.value) || 2000)}
                  disabled={isTaskRunning}
                  className="w-full bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="2000"
                />
              </div>
            </div>

            <div className="flex space-x-3 pt-4">
              {canStartTask ? (
                <>
                  <button
                    onClick={handleStartTask}
                    disabled={isLoading}
                    className="w-full bg-amber-600 hover:bg-amber-700 disabled:bg-amber-800 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                  >
                    {isLoading ? (
                      <LuRotateCw className="mr-2 animate-spin" size={16} />
                    ) : (
                      <LuPlay className="mr-2" size={16} />
                    )}
                    {isLoading ? '启动中...' : '开始搜集'}
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={stopCollectionTask}
                    disabled={!canStopTask || isStoppingTask || isForceStoppingTask}
                    className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                  >
                    {isStoppingTask ? (
                      <LuRotateCw className="mr-2 animate-spin" size={16} />
                    ) : (
                      <LuPause className="mr-2" size={16} />
                    )}
                    {isStoppingTask ? '停止中...' : '停止搜集'}
                  </button>
                  <button
                    onClick={forceStopCollectionTask}
                    disabled={!canStopTask || isStoppingTask || isForceStoppingTask}
                    className="bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                    title="强制停止并保存现有数据"
                  >
                    {isForceStoppingTask ? (
                      <LuRotateCw className="mr-2 animate-spin" size={16} />
                    ) : (
                      <LuSquare className="mr-2" size={16} />
                    )}
                    {isForceStoppingTask ? '强制停止中...' : '强制停止'}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 下载配置面板 */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <h2 className="text-xl font-semibold text-neutral-200 mb-4 flex items-center">
            <LuDownload className="mr-2" size={20} />
            下载配置
          </h2>

          <div className="space-y-4">
            {/* 启用下载开关 */}
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-neutral-300">
                启用自动下载附件
              </label>
              <button
                onClick={() => setEnableDownload(!enableDownload)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  enableDownload ? 'bg-amber-600' : 'bg-neutral-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    enableDownload ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* 工作区路径配置 */}
            {enableDownload && (
              <>
                <div>
                  <label className="block text-sm font-medium text-neutral-300 mb-2">
                    工作区文件夹路径
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={workspacePath}
                      onChange={(e) => setWorkspacePath(e.target.value)}
                      placeholder="请输入工作区文件夹路径，如: D:/SoulForge-Workspace"
                      className="flex-1 bg-[#1e1e1e] border border-neutral-600 rounded-md px-3 py-2 text-neutral-200 placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    />
                    <button
                      onClick={selectWorkspaceFolder}
                      className="bg-neutral-600 hover:bg-neutral-700 text-white font-medium py-2 px-3 rounded-md transition-colors duration-200"
                      title="选择文件夹"
                    >
                      📁
                    </button>
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">
                    下载的文件将保存为: [帖子标题]-[解压密码].扩展名
                  </p>

                  {/* 工作区状态显示 */}
                  {workspacePath && (
                    <div className="mt-3 p-3 bg-[#1e1e1e] rounded-md border border-neutral-700">
                      <h4 className="text-sm font-medium text-neutral-300 mb-2">工作区状态</h4>
                      <div className="space-y-1 text-xs text-neutral-400">
                        <div>📁 工作区: {workspacePath}</div>
                        <div>📦 附件目录: {workspacePath}/attachments/</div>
                        <div>📄 日志目录: {workspacePath}/logs/</div>
                        <div className="text-green-400">✅ 配置完成，可以开始搜集</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 保存配置按钮 */}
                <div>
                  <button
                    onClick={saveDownloadConfig}
                    disabled={downloadConfigSaving || !workspacePath.trim()}
                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
                  >
                    {downloadConfigSaving ? (
                      <LuRotateCw className="mr-2 animate-spin" size={16} />
                    ) : (
                      <LuSettings className="mr-2" size={16} />
                    )}
                    {downloadConfigSaving ? '保存中...' : '保存配置'}
                  </button>
                </div>
              </>
            )}

            {/* 下载状态说明 */}
            <div className="bg-[#1e1e1e] rounded-md p-3 border border-neutral-700">
              <h4 className="text-sm font-medium text-neutral-300 mb-2">下载功能说明</h4>
              <ul className="text-xs text-neutral-400 space-y-1">
                <li>• 启用后将自动下载帖子中的附件</li>
                <li>• 支持处理 Cloudflare 人机验证</li>
                <li>• 文件将自动重命名为规范格式</li>
                <li>• 下载状态会实时更新到数据库</li>
              </ul>
            </div>
          </div>
        </div>


      </div>

      {/* 状态日志区域 */}
      <div className="mt-8">
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-neutral-700">
          <h2 className="text-xl font-semibold text-neutral-200 mb-4 flex items-center">
            <LuDatabase className="mr-2" size={20} />
            状态日志
          </h2>

          <div className="bg-[#1e1e1e] rounded-md p-4 h-64 overflow-y-auto">
            {statusLogs.length === 0 ? (
              <div className="text-center py-8">
                <LuLink2 className="mx-auto text-neutral-500 mb-4" size={48} />
                <p className="text-neutral-400 text-lg mb-2">暂无日志记录</p>
                <p className="text-neutral-500 text-sm">
                  启动搜集任务后，状态更新将显示在这里
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {statusLogs.map((log, index) => (
                  <div key={index} className="flex items-start space-x-3 text-sm">
                    <span className="text-neutral-500 text-xs whitespace-nowrap">
                      {log.timestamp.toLocaleTimeString()}
                    </span>
                    <span className={`font-medium ${
                      log.status === 'error' ? 'text-red-400' :
                      log.status === 'success' || log.status === 'completed' ? 'text-green-400' :
                      log.status === 'running' ? 'text-blue-400' :
                      'text-neutral-300'
                    }`}>
                      [{log.status ? log.status.toUpperCase() : 'INFO'}]
                    </span>
                    <span className="text-neutral-300 flex-1">
                      {log.filePath ? (
                        <button
                          onClick={() => handleOpenArchiveFile(log.filePath)}
                          className="text-blue-400 hover:text-blue-300 underline transition-colors"
                          title={`点击打开档案文件: ${log.filePath}`}
                        >
                          {log.message}
                        </button>
                      ) : (
                        log.message
                      )}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

        </TabsContent>



        {/* 历史档案标签页 */}
        <TabsContent value="archive-management">
          <div className="space-y-6">
            {/* 筛选栏 */}
            <ArchiveFilterBar />

            {/* 数据网格 */}
            <ArchiveDataGrid />
          </div>
        </TabsContent>

        {/* MD数据归档标签页 */}
        <TabsContent value="ingest-center">
          <IngestCenterPage />
        </TabsContent>
      </Tabs>

      {/* 链接预览弹窗 */}
      <LinkPreviewModal />

      {/* 批量 AI 分析进度弹窗 */}
      <BatchAIProgressModal />
    </div>
  );
};

export default CollectorPage;
