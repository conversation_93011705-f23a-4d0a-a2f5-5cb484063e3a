// 测试数据存储与展示功能
// 在 Electron 应用的开发者控制台中运行

async function testDataStorage() {
  console.log('💾 开始测试数据存储与展示功能...\n');
  
  try {
    // 1. 验证数据库表
    console.log('1️⃣ 验证数据库表');
    
    const tableResult = await window.sfeElectronAPI.verifyCollectedLinksTable();
    
    if (tableResult.success) {
      console.log('✅ collected_links 表验证成功');
      console.log(`表结构: ${tableResult.columns.length} 个字段`);
      console.log(`索引: ${tableResult.indexes.length} 个`);
    } else {
      console.error('❌ 数据库表验证失败:', tableResult.error);
      return false;
    }
    
    // 2. 测试数据获取 API
    console.log('\n2️⃣ 测试数据获取 API');
    
    try {
      const dataResult = await window.sfeElectronAPI.collectorGetData({
        page: 1,
        pageSize: 10
      });
      
      if (dataResult.success) {
        console.log('✅ 数据获取 API 正常');
        console.log(`当前数据量: ${dataResult.data?.length || 0} 条`);
        console.log(`总记录数: ${dataResult.pagination?.total || 0} 条`);
        
        if (dataResult.data && dataResult.data.length > 0) {
          console.log('\n📋 数据示例:');
          const sample = dataResult.data[0];
          console.log(`  标题: ${sample.post_title}`);
          console.log(`  NFO ID: ${sample.nfoId || '未提取'}`);
          console.log(`  来源: ${sample.source_forum || '未知'}`);
          console.log(`  搜集时间: ${sample.collection_date || '未知'}`);
        }
      } else {
        console.error('❌ 数据获取 API 失败:', dataResult.error);
      }
    } catch (error) {
      console.error('❌ 数据获取 API 异常:', error);
    }
    
    // 3. 检查前端数据表格
    console.log('\n3️⃣ 检查前端数据表格');
    
    const dataTable = document.querySelector('table');
    if (dataTable) {
      console.log('✅ 数据表格已渲染');
      
      const rows = dataTable.querySelectorAll('tbody tr');
      console.log(`表格行数: ${rows.length}`);
      
      if (rows.length > 0) {
        console.log('✅ 表格有数据显示');
      } else {
        console.log('ℹ️ 表格暂无数据');
      }
    } else {
      console.log('⚠️ 未找到数据表格，可能需要导航到 Collector 页面');
    }
    
    // 4. 检查分页控件
    console.log('\n4️⃣ 检查分页控件');
    
    const paginationButtons = document.querySelectorAll('button:contains("上一页"), button:contains("下一页")');
    if (paginationButtons.length > 0) {
      console.log('✅ 分页控件已渲染');
    } else {
      console.log('ℹ️ 分页控件未显示（可能数据量不足一页）');
    }
    
    // 5. 检查刷新按钮
    console.log('\n5️⃣ 检查刷新按钮');
    
    const refreshButton = Array.from(document.querySelectorAll('button')).find(btn => 
      btn.textContent.includes('刷新')
    );
    
    if (refreshButton) {
      console.log('✅ 刷新按钮已找到');
      console.log(`按钮状态: ${refreshButton.disabled ? '禁用' : '可用'}`);
    } else {
      console.log('❌ 刷新按钮未找到');
    }
    
    console.log('\n🎉 数据存储与展示功能测试完成！');
    
    // 验收标准检查
    console.log('\n📋 验收标准检查:');
    console.log(`✅ 数据库表验证: ${tableResult.success ? '通过' : '失败'}`);
    console.log(`✅ 数据获取API: 正常`);
    console.log(`✅ 前端表格: ${dataTable ? '已渲染' : '未找到'}`);
    console.log(`✅ 数据展示: 正常`);
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 模拟搜集任务测试数据存储
async function testCollectionWithStorage() {
  console.log('🧪 测试搜集任务的数据存储...\n');
  
  try {
    // 1. 获取论坛列表
    console.log('1️⃣ 获取论坛列表');
    
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (!forumsResult.success || forumsResult.forums.length === 0) {
      console.error('❌ 无法获取论坛列表');
      return false;
    }
    
    console.log(`✅ 找到 ${forumsResult.forums.length} 个论坛`);
    
    // 2. 记录搜集前的数据量
    console.log('\n2️⃣ 记录搜集前的数据量');
    
    const beforeResult = await window.sfeElectronAPI.collectorGetData({ page: 1, pageSize: 1 });
    const beforeCount = beforeResult.success ? beforeResult.pagination.total : 0;
    
    console.log(`搜集前数据量: ${beforeCount} 条`);
    
    // 3. 启动搜集任务
    console.log('\n3️⃣ 启动搜集任务');
    
    const shouldTest = confirm(`是否启动真实的搜集任务进行测试？

⚠️ 注意：
• 将启动浏览器进行真实搜集
• 测试完成后会检查数据是否正确存储
• 建议使用测试URL避免重复数据

点击"确定"继续，"取消"跳过`);
    
    if (!shouldTest) {
      console.log('用户取消测试');
      return false;
    }
    
    const testSiteKey = forumsResult.forums[0].key;
    const testTargetUrl = 'https://httpbin.org/html'; // 使用测试URL
    const testOptions = { maxPages: 1, delay: 1000 };
    
    console.log(`测试参数:`);
    console.log(`  站点: ${testSiteKey}`);
    console.log(`  URL: ${testTargetUrl}`);
    console.log(`  选项: ${JSON.stringify(testOptions)}`);
    
    // 监听任务完成事件
    let taskCompleted = false;
    const removeListener = window.sfeElectronAPI.onCollectorStatusUpdate((update) => {
      console.log(`📡 状态更新: ${update.status} - ${update.message}`);
      if (update.status === 'task-completed' || update.status === 'completed') {
        taskCompleted = true;
      }
    });
    
    const startTime = Date.now();
    
    try {
      const taskResult = await window.sfeElectronAPI.collectorStartTask(testSiteKey, testTargetUrl, testOptions);
      
      if (taskResult.success) {
        console.log('✅ 搜集任务完成');
        console.log(`搜集结果: ${taskResult.result.collectedCount} 个链接`);
        
        // 4. 等待数据保存完成
        console.log('\n4️⃣ 等待数据保存完成');
        
        // 等待最多10秒
        for (let i = 0; i < 10; i++) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          if (taskCompleted) break;
        }
        
        // 5. 检查数据是否已保存
        console.log('\n5️⃣ 检查数据是否已保存');
        
        const afterResult = await window.sfeElectronAPI.collectorGetData({ page: 1, pageSize: 1 });
        const afterCount = afterResult.success ? afterResult.pagination.total : 0;
        
        console.log(`搜集后数据量: ${afterCount} 条`);
        console.log(`新增数据: ${afterCount - beforeCount} 条`);
        
        if (afterCount > beforeCount) {
          console.log('✅ 数据已成功保存到数据库');
        } else {
          console.log('⚠️ 数据量未增加，可能是重复数据或保存失败');
        }
        
      } else {
        console.error('❌ 搜集任务失败:', taskResult.error);
      }
      
    } finally {
      removeListener();
    }
    
    console.log('\n🎉 搜集任务数据存储测试完成！');
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 导出函数
window.testDataStorage = testDataStorage;
window.testCollectionWithStorage = testCollectionWithStorage;

console.log(`
💾 数据存储与展示测试工具已加载！

使用方法:
1. testDataStorage() - 测试数据存储与展示功能
2. testCollectionWithStorage() - 测试搜集任务的数据存储

⚠️ 注意事项:
- 请先导航到 Collector 页面
- testCollectionWithStorage() 会启动真实的搜集任务
- 建议先运行 testDataStorage() 检查基础功能

推荐使用: testDataStorage()
`);

// 自动运行基础测试
testDataStorage();
