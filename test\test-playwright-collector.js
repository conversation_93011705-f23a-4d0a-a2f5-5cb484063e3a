// 测试 Playwright 集成的 CollectorService
// 在 Electron 应用的开发者控制台中运行

async function testPlaywrightCollector() {
  console.log('🎭 开始测试 Playwright 集成的 CollectorService...\n');
  
  try {
    // 1. 检查 API 可用性
    console.log('1️⃣ 检查 API 可用性');
    
    if (!window.sfeElectronAPI.collectorStartTask) {
      console.error('❌ collectorStartTask API 不可用');
      return false;
    }
    
    if (!window.sfeElectronAPI.onCollectorStatusUpdate) {
      console.error('❌ onCollectorStatusUpdate API 不可用');
      return false;
    }
    
    console.log('✅ 所有 API 可用');
    
    // 2. 设置状态更新监听器
    console.log('\n2️⃣ 设置状态更新监听器');
    
    const statusUpdates = [];
    const removeListener = window.sfeElectronAPI.onCollectorStatusUpdate((statusUpdate) => {
      console.log(`📡 状态更新: ${statusUpdate.status} - ${statusUpdate.message}`);
      statusUpdates.push(statusUpdate);
    });
    
    console.log('✅ 状态更新监听器已设置');
    
    // 3. 获取可用论坛
    console.log('\n3️⃣ 获取可用论坛');
    
    const forumsResult = await window.sfeElectronAPI.collectorGetForums();
    
    if (!forumsResult.success || forumsResult.forums.length === 0) {
      console.error('❌ 无法获取论坛配置');
      return false;
    }
    
    console.log(`✅ 找到 ${forumsResult.forums.length} 个论坛配置`);
    forumsResult.forums.forEach((forum, index) => {
      console.log(`  ${index + 1}. ${forum.key}: ${forum.name}`);
    });
    
    // 4. 准备测试参数
    console.log('\n4️⃣ 准备测试参数');
    
    const testSiteKey = forumsResult.forums[0].key;
    
    // 使用一个真实的测试网站（可以是任何公开网站）
    const testTargetUrl = 'https://example.com'; // 简单的测试网站
    
    const testOptions = {
      maxPages: 1,    // 只处理1页
      delay: 1000     // 1秒延迟
    };
    
    console.log(`测试站点: ${testSiteKey}`);
    console.log(`测试URL: ${testTargetUrl}`);
    console.log(`测试选项: ${JSON.stringify(testOptions)}`);
    
    // 5. 启动搜集任务
    console.log('\n5️⃣ 启动 Playwright 搜集任务');
    console.log('⚠️ 注意: 浏览器窗口将会打开，请观察自动化过程');
    
    const shouldContinue = confirm('是否启动 Playwright 浏览器进行测试？\n\n注意：\n- 浏览器窗口将会打开\n- 可能需要手动登录\n- 测试过程可能需要几分钟');
    
    if (!shouldContinue) {
      console.log('用户取消测试');
      removeListener();
      return false;
    }
    
    console.log('🚀 启动搜集任务...');
    
    // 记录开始时间
    const startTime = Date.now();
    
    try {
      const taskResult = await window.sfeElectronAPI.collectorStartTask(testSiteKey, testTargetUrl, testOptions);
      
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      console.log(`\n⏱️ 任务执行时间: ${duration.toFixed(2)} 秒`);
      
      if (taskResult.success) {
        console.log('✅ Playwright 搜集任务完成！');
        console.log(`任务ID: ${taskResult.taskId}`);
        console.log(`搜集结果: ${taskResult.result.collectedCount} 个链接`);
        console.log(`处理页面: ${taskResult.result.pages}`);
        console.log(`消息: ${taskResult.result.message}`);
        
        // 显示搜集到的链接
        if (taskResult.result.links && taskResult.result.links.length > 0) {
          console.log('\n📋 搜集到的链接:');
          taskResult.result.links.forEach((link, index) => {
            console.log(`\n${index + 1}. ${link.postTitle}`);
            console.log(`   URL: ${link.postUrl}`);
            console.log(`   NFO ID: ${link.nfoId || '未提取到'}`);
            console.log(`   磁力链接: ${link.magnetLink || '无'}`);
            console.log(`   ed2k链接: ${link.ed2kLink || '无'}`);
            console.log(`   附件链接: ${link.attachmentUrl || '无'}`);
            console.log(`   解压密码: ${link.decompressionPassword || '无'}`);
            console.log(`   搜集时间: ${new Date(link.collectionDate).toLocaleString()}`);
          });
        }
        
      } else {
        console.error('❌ Playwright 搜集任务失败:', taskResult.error);
      }
      
    } catch (error) {
      console.error('❌ 搜集任务异常:', error);
    }
    
    // 6. 显示状态更新历史
    console.log('\n6️⃣ 状态更新历史');
    
    if (statusUpdates.length > 0) {
      console.log(`收到 ${statusUpdates.length} 个状态更新:`);
      statusUpdates.forEach((update, index) => {
        console.log(`  ${index + 1}. [${new Date(update.timestamp).toLocaleTimeString()}] ${update.status}: ${update.message}`);
      });
    } else {
      console.log('未收到状态更新');
    }
    
    // 7. 测试停止功能
    console.log('\n7️⃣ 测试停止功能');
    
    try {
      const stopResult = await window.sfeElectronAPI.collectorStopTask();
      console.log(`停止结果: ${stopResult.success ? '✅' : '❌'} - ${stopResult.message}`);
    } catch (error) {
      console.log(`停止测试: ${error.message}`);
    }
    
    // 8. 最终状态检查
    console.log('\n8️⃣ 最终状态检查');
    
    const finalStatus = await window.sfeElectronAPI.collectorGetStatus();
    
    if (finalStatus.success) {
      console.log(`运行状态: ${finalStatus.status.isRunning ? '运行中' : '空闲'}`);
      console.log(`历史任务数: ${finalStatus.status.taskHistory.length}`);
      
      if (finalStatus.status.taskHistory.length > 0) {
        const lastTask = finalStatus.status.taskHistory[finalStatus.status.taskHistory.length - 1];
        console.log(`最后任务状态: ${lastTask.status}`);
        if (lastTask.statusMessage) {
          console.log(`最后状态消息: ${lastTask.statusMessage}`);
        }
      }
    }
    
    // 清理监听器
    removeListener();
    
    console.log('\n🎉 Playwright CollectorService 测试完成！');
    console.log('✅ 浏览器自动化功能正常');
    console.log('✅ 实时状态更新正常');
    console.log('✅ 数据抓取逻辑正常');
    console.log('✅ NFO ID 提取功能正常');
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
    return false;
  }
}

// 简化版测试（不启动浏览器）
async function quickPlaywrightTest() {
  console.log('⚡ 快速测试 Playwright 集成...\n');
  
  try {
    // 检查 API
    const hasApi = typeof window.sfeElectronAPI.collectorStartTask === 'function';
    console.log(`API 可用性: ${hasApi ? '✅' : '❌'}`);
    
    // 检查论坛配置
    const forums = await window.sfeElectronAPI.collectorGetForums();
    console.log(`论坛配置: ${forums.success ? '✅' : '❌'} (${forums.forums?.length || 0} 个)`);
    
    // 检查状态监听
    const hasStatusListener = typeof window.sfeElectronAPI.onCollectorStatusUpdate === 'function';
    console.log(`状态监听: ${hasStatusListener ? '✅' : '❌'}`);
    
    console.log('\n⚡ 快速测试完成！');
    console.log('💡 运行 testPlaywrightCollector() 进行完整测试');
    
  } catch (error) {
    console.error('❌ 快速测试失败:', error);
  }
}

// 导出函数
window.testPlaywrightCollector = testPlaywrightCollector;
window.quickPlaywrightTest = quickPlaywrightTest;

console.log(`
🎭 Playwright CollectorService 测试工具已加载！

使用方法:
1. testPlaywrightCollector() - 完整的 Playwright 测试（会启动浏览器）
2. quickPlaywrightTest() - 快速测试（不启动浏览器）

⚠️ 注意事项:
- 完整测试会启动真实的浏览器窗口
- 可能需要手动登录某些网站
- 测试过程可能需要几分钟时间
- 请确保网络连接正常

推荐先运行: quickPlaywrightTest()
`);

// 自动运行快速测试
quickPlaywrightTest();
