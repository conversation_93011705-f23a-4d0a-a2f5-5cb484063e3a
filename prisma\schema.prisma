// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../soulforge-web/node_modules/.prisma/client"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Movie {
  id                            String   @id @default(cuid())
  filePath                      String   @unique
  fileName                      String
  title                         String?
  originalTitle                 String?
  nfoId                         String?
  year                          Int?
  releaseDate                   String?
  runtime                       Int?
  plot                          String?
  plotJa                        String?
  plotZh                        String?
  studio                        String?
  series                        String?
  director                      String?
  trailerUrl                    String?
  posterUrl                     String?
  coverUrl                      String?
  localCoverPath                String?
  watched                       Boolean  @default(false)
  personalRating                Int?
  actors                        String?  // JSON string
  genres                        String?  // JSON string
  tags                          String?  // JSON string
  lastScanned                   DateTime @default(now())
  nfoLastModified               Int?
  resolution                    String?
  fileSize                      Int?
  videoCodec                    String?
  audioCodec                    String?
  preferredStatus               String?
  customFileTags                String?  // JSON string
  versionCategories             String?  // JSON string
  autoDetectedFileNameTags      String?  // JSON string
  fps                           Float?
  videoCodecFull                String?
  videoBitrate                  String?
  audioCodecFull                String?
  audioChannelsDesc             String?
  audioSampleRate               Int?
  audioBitrate                  String?
  videoHeight                   String?
  aiAnalyzedTags                String?  // JSON string
  aiRecommendationType          String?
  aiRecommendationScore         Int?
  aiRecommendationJustification String?
  hasExternalSubtitles          Boolean  @default(false)
  cdPartInfo                    String?
  assetStatus                   String   @default("AVAILABLE") // 'VIRTUAL' | 'AVAILABLE' | 'MISSING'
  createdAt                     DateTime @default(now())
  updatedAt                     DateTime @updatedAt

  // Relations
  libraryLinks MovieLibraryLink[]
  snapshots    Snapshot[]

  @@index([nfoId])
  @@index([title])
  @@index([lastScanned])
  @@map("movies")
}

model MovieLibrary {
  id        String   @id @default(cuid())
  name      String
  paths     String   // JSON string array
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  movieLinks MovieLibraryLink[]

  @@index([name])
  @@map("movie_libraries")
}

model MovieLibraryLink {
  movieId   String
  libraryId String

  movie   Movie        @relation(fields: [movieId], references: [id], onDelete: Cascade)
  library MovieLibrary @relation(fields: [libraryId], references: [id], onDelete: Cascade)

  @@id([movieId, libraryId])
  @@index([libraryId])
  @@map("movie_library_links")
}

model ActorMetadata {
  actorName           String   @id
  localAvatarPath     String?
  avatarUrlSource     String?
  filetreeSourcePath  String?
  lastUpdated         DateTime @default(now())

  @@index([actorName])
  @@map("actor_metadata")
}

model Favorite {
  id          String   @id @default(cuid())
  itemType    String   // 'movie', 'actor', 'genre', 'studio'
  itemValue   String
  favoritedAt DateTime @default(now())

  @@unique([itemType, itemValue])
  @@map("favorites")
}

model Snapshot {
  id        String   @id @default(cuid())
  movieId   String
  filePath  String
  timestamp Float    // Video timestamp in seconds
  quality   String
  createdAt DateTime @default(now())

  movie Movie @relation(fields: [movieId], references: [id], onDelete: Cascade)

  @@index([movieId])
  @@map("snapshots")
}
