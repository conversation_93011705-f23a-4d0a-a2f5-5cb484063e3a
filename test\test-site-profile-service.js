// 测试站点配置服务的示例脚本
const siteProfileService = require('./main_process/services/siteProfileService');

// 模拟日志服务
const mockLogger = {
  info: (msg) => console.log(`[INFO] ${msg}`),
  error: (msg) => console.error(`[ERROR] ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${msg}`)
};

function testSiteProfileService() {
  console.log('🧪 开始测试站点配置服务...\n');
  
  try {
    // 1. 初始化服务
    console.log('1️⃣ 初始化站点配置服务');
    siteProfileService.initializeSiteProfileService(mockLogger, __dirname);
    
    // 2. 检查配置文件是否存在
    console.log('\n2️⃣ 检查配置文件是否存在');
    const fileExists = siteProfileService.configFileExists();
    console.log(`配置文件存在: ${fileExists ? '✅' : '❌'}`);
    
    if (!fileExists) {
      console.error('❌ 配置文件不存在，请先创建 site-profiles.json');
      return false;
    }
    
    // 3. 加载配置文件
    console.log('\n3️⃣ 加载站点配置文件');
    const loadResult = siteProfileService.loadSiteProfiles();
    console.log(`加载结果: ${loadResult.success ? '✅ 成功' : '❌ 失败'}`);
    
    if (!loadResult.success) {
      console.error(`加载失败原因: ${loadResult.error}`);
      return false;
    }
    
    // 4. 获取所有论坛列表
    console.log('\n4️⃣ 获取可用论坛列表');
    const forums = siteProfileService.getAvailableForums();
    console.log(`可用论坛数量: ${forums.length}`);
    forums.forEach((forum, index) => {
      console.log(`  ${index + 1}. ${forum.key}: ${forum.name}`);
    });
    
    // 5. 获取特定论坛配置
    console.log('\n5️⃣ 获取特定论坛配置');
    for (const forum of forums) {
      console.log(`\n📂 ${forum.key} (${forum.name}):`);
      const config = siteProfileService.getSiteProfile(forum.key);
      
      if (config) {
        console.log(`  登录URL: ${config.loginUrl}`);
        console.log(`  帖子链接选择器: ${config.postLinkSelector}`);
        console.log(`  标题选择器: ${config.postTitleSelector}`);
        console.log(`  磁力链接选择器: ${config.magnetLinkSelector}`);
        console.log(`  ed2k链接选择器: ${config.ed2kLinkSelector}`);
        console.log(`  附件URL选择器: ${config.attachmentUrlSelector}`);
        console.log(`  密码选择器: ${config.passwordSelector}`);
      } else {
        console.error(`  ❌ 无法获取配置`);
      }
    }
    
    // 6. 测试获取不存在的论坛配置
    console.log('\n6️⃣ 测试获取不存在的论坛配置');
    const nonExistentConfig = siteProfileService.getSiteProfile('nonExistentForum');
    console.log(`不存在的论坛配置: ${nonExistentConfig ? '❌ 意外获取到' : '✅ 正确返回null'}`);
    
    // 7. 获取完整配置
    console.log('\n7️⃣ 获取完整站点配置');
    const allProfiles = siteProfileService.getAllSiteProfiles();
    console.log(`完整配置获取: ${allProfiles ? '✅ 成功' : '❌ 失败'}`);
    
    if (allProfiles) {
      console.log('完整配置结构:');
      console.log(JSON.stringify(allProfiles, null, 2));
    }
    
    console.log('\n🎉 站点配置服务测试完成！');
    console.log('✅ 所有功能正常工作');
    console.log('✅ 配置文件可以被正确加载和解析');
    console.log('✅ 服务接口功能完整');
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
    return false;
  }
}

// 运行测试
const success = testSiteProfileService();
process.exit(success ? 0 : 1);
