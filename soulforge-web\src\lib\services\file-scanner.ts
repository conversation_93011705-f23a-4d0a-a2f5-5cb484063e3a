import { glob } from 'glob';
import { promises as fs } from 'fs';
import path from 'path';
import { Movie } from '@/lib/types';

// Video file extensions
const VIDEO_EXTENSIONS = [
  '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
  '.mpg', '.mpeg', '.3gp', '.ogv', '.ts', '.m2ts', '.mts', '.vob',
  '.rmvb', '.rm', '.asf', '.divx', '.xvid', '.strm'
];

// Image extensions for covers
const IMAGE_EXTENSIONS = [
  '.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff'
];

export interface ScanResult {
  movies: Partial<Movie>[];
  errors: string[];
}

export interface ScanProgress {
  current: number;
  total: number;
  currentFile: string;
}

export class FileScanner {
  private onProgress?: (progress: ScanProgress) => void;

  constructor(onProgress?: (progress: ScanProgress) => void) {
    this.onProgress = onProgress;
  }

  async scanDirectories(directories: string[]): Promise<ScanResult> {
    const movies: Partial<Movie>[] = [];
    const errors: string[] = [];

    // Find all video files
    const videoFiles: string[] = [];
    
    for (const directory of directories) {
      try {
        const pattern = path.join(directory, '**', `*{${VIDEO_EXTENSIONS.join(',')}}`);
        const files = await glob(pattern, { nocase: true });
        videoFiles.push(...files);
      } catch (error) {
        errors.push(`Failed to scan directory ${directory}: ${error}`);
      }
    }

    // Process each video file
    for (let i = 0; i < videoFiles.length; i++) {
      const filePath = videoFiles[i];
      
      if (this.onProgress) {
        this.onProgress({
          current: i + 1,
          total: videoFiles.length,
          currentFile: path.basename(filePath),
        });
      }

      try {
        const movieData = await this.processVideoFile(filePath);
        movies.push(movieData);
      } catch (error) {
        errors.push(`Failed to process ${filePath}: ${error}`);
      }
    }

    return { movies, errors };
  }

  private async processVideoFile(filePath: string): Promise<Partial<Movie>> {
    const fileName = path.basename(filePath);
    const fileDir = path.dirname(filePath);
    
    // Get file stats
    const stats = await fs.stat(filePath);
    
    // Basic movie data
    const movieData: Partial<Movie> = {
      filePath,
      fileName,
      fileSize: stats.size,
      lastScanned: new Date(),
    };

    // Extract title from filename
    movieData.title = this.extractTitleFromFilename(fileName);

    // Look for NFO file
    const nfoPath = await this.findNfoFile(filePath);
    if (nfoPath) {
      try {
        const nfoData = await this.parseNfoFile(nfoPath);
        Object.assign(movieData, nfoData);
      } catch (error) {
        console.warn(`Failed to parse NFO file ${nfoPath}:`, error);
      }
    }

    // Look for local cover
    const coverPath = await this.findLocalCover(filePath);
    if (coverPath) {
      movieData.localCoverPath = coverPath;
    }

    // Extract technical info (placeholder - would need ffprobe)
    movieData.resolution = this.extractResolutionFromFilename(fileName);
    
    // Extract tags from filename
    movieData.autoDetectedFileNameTags = this.extractTagsFromFilename(fileName);

    return movieData;
  }

  private extractTitleFromFilename(fileName: string): string {
    // Remove extension
    let title = path.parse(fileName).name;
    
    // Remove common patterns
    title = title.replace(/\[.*?\]/g, ''); // Remove [tags]
    title = title.replace(/\(.*?\)/g, ''); // Remove (year) etc
    title = title.replace(/\d{3,4}p/gi, ''); // Remove resolution
    title = title.replace(/x264|x265|h264|h265/gi, ''); // Remove codecs
    title = title.replace(/\.(mp4|avi|mkv|mov|wmv|flv|webm|m4v)$/i, '');
    
    // Clean up
    title = title.replace(/[._-]+/g, ' ').trim();
    
    return title || fileName;
  }

  private extractResolutionFromFilename(fileName: string): string | undefined {
    const resolutionMatch = fileName.match(/(\d{3,4}p)/i);
    return resolutionMatch ? resolutionMatch[1] : undefined;
  }

  private extractTagsFromFilename(fileName: string): string[] {
    const tags: string[] = [];
    
    // Extract tags in brackets
    const bracketMatches = fileName.match(/\[([^\]]+)\]/g);
    if (bracketMatches) {
      bracketMatches.forEach(match => {
        const tag = match.slice(1, -1); // Remove brackets
        tags.push(tag);
      });
    }

    // Extract common quality indicators
    if (/4k|2160p/i.test(fileName)) tags.push('4K');
    if (/1080p/i.test(fileName)) tags.push('1080p');
    if (/720p/i.test(fileName)) tags.push('720p');
    if (/x264/i.test(fileName)) tags.push('x264');
    if (/x265|h265/i.test(fileName)) tags.push('x265');
    if (/hdr/i.test(fileName)) tags.push('HDR');
    if (/dolby/i.test(fileName)) tags.push('Dolby');

    return tags;
  }

  private async findNfoFile(videoPath: string): Promise<string | null> {
    const videoDir = path.dirname(videoPath);
    const videoName = path.parse(videoPath).name;
    
    // Common NFO file patterns
    const nfoPatterns = [
      path.join(videoDir, `${videoName}.nfo`),
      path.join(videoDir, 'movie.nfo'),
      path.join(videoDir, 'tvshow.nfo'),
    ];

    for (const nfoPath of nfoPatterns) {
      try {
        await fs.access(nfoPath);
        return nfoPath;
      } catch {
        // File doesn't exist, continue
      }
    }

    return null;
  }

  private async findLocalCover(videoPath: string): Promise<string | null> {
    const videoDir = path.dirname(videoPath);
    const videoName = path.parse(videoPath).name;
    
    // Common cover file patterns
    const coverPatterns = [
      path.join(videoDir, `${videoName}-poster.*`),
      path.join(videoDir, `${videoName}-cover.*`),
      path.join(videoDir, `${videoName}.*`),
      path.join(videoDir, 'poster.*'),
      path.join(videoDir, 'cover.*'),
      path.join(videoDir, 'folder.*'),
    ];

    for (const pattern of coverPatterns) {
      try {
        const files = await glob(pattern);
        const imageFile = files.find(file => 
          IMAGE_EXTENSIONS.some(ext => 
            file.toLowerCase().endsWith(ext)
          )
        );
        if (imageFile) {
          return imageFile;
        }
      } catch {
        // Continue to next pattern
      }
    }

    return null;
  }

  private async parseNfoFile(nfoPath: string): Promise<Partial<Movie>> {
    const content = await fs.readFile(nfoPath, 'utf-8');
    
    // Simple XML parsing (would be better with xml2js)
    const movieData: Partial<Movie> = {};

    // Extract basic fields
    const titleMatch = content.match(/<title>(.*?)<\/title>/s);
    if (titleMatch) movieData.title = titleMatch[1].trim();

    const originalTitleMatch = content.match(/<originaltitle>(.*?)<\/originaltitle>/s);
    if (originalTitleMatch) movieData.originalTitle = originalTitleMatch[1].trim();

    const plotMatch = content.match(/<plot>(.*?)<\/plot>/s);
    if (plotMatch) movieData.plot = plotMatch[1].trim();

    const yearMatch = content.match(/<year>(\d{4})<\/year>/);
    if (yearMatch) movieData.year = parseInt(yearMatch[1]);

    const runtimeMatch = content.match(/<runtime>(\d+)<\/runtime>/);
    if (runtimeMatch) movieData.runtime = parseInt(runtimeMatch[1]);

    const studioMatch = content.match(/<studio>(.*?)<\/studio>/s);
    if (studioMatch) movieData.studio = studioMatch[1].trim();

    const directorMatch = content.match(/<director>(.*?)<\/director>/s);
    if (directorMatch) movieData.director = directorMatch[1].trim();

    // Extract genres
    const genreMatches = content.match(/<genre>(.*?)<\/genre>/gs);
    if (genreMatches) {
      movieData.genres = genreMatches.map(match => 
        match.replace(/<\/?genre>/g, '').trim()
      );
    }

    // Extract actors
    const actorMatches = content.match(/<actor>.*?<name>(.*?)<\/name>.*?<\/actor>/gs);
    if (actorMatches) {
      movieData.actors = actorMatches.map(match => {
        const nameMatch = match.match(/<name>(.*?)<\/name>/);
        return nameMatch ? nameMatch[1].trim() : '';
      }).filter(Boolean);
    }

    return movieData;
  }
}
