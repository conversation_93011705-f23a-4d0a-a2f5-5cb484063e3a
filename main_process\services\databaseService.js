
// soul-forge-electron/main_process/services/databaseService.js
const Database = require('better-sqlite3');
const path = require('node:path');
const fs =require('node:fs');
const { v4: uuidv4 } = require('uuid'); // For generating library IDs

let db;
let log;
let dbPathInternal;
let defaultThumbnailCacheBasePathInternal; 
let isDevInternal;

const DB_VERSION = 41; // Incremented for actor_profiles table

// Define all columns for the movies table explicitly
const MOVIE_TABLE_COLUMNS = [
  'db_id', 'filePath', 'fileName', 'title', 'originalTitle', 'nfoId',
  'year', 'releaseDate', 'runtime', 'plot', 'studio', 'series',
  'director', 'trailerUrl', 'posterUrl', 'coverUrl', 'localCoverPath',
  'watched', 'personalRating', 'actors', 'genres', 'tags', 'lastScanned',
  'nfoLastModified', 'resolution', 'fileSize', 'videoCodec', 'audioCodec',
  'preferredStatus', 'customFileTags', 'versionCategories',
  'autoDetectedFileNameTags', 'fps', 'videoCodecFull', 'videoBitrate',
  'audioCodecFull', 'audioChannelsDesc', 'audioSampleRate', 'audioBitrate',
  'videoHeight', 'aiAnalyzedTags', 'aiRecommendationType',
  'aiRecommendationScore', 'aiRecommendationJustification',
  'hasExternalSubtitles', 'cdPartInfo', 'plotJa', 'plotZh', 'asset_status', 'asset_root_path', 'version_status', 'release_status'
];
const MOVIE_TABLE_COLUMNS_M_SUB = MOVIE_TABLE_COLUMNS.map(col => `m_sub.${col}`).join(', ');
const MOVIE_TABLE_COLUMNS_M_FINAL = MOVIE_TABLE_COLUMNS.map(col => `m_final.${col}`).join(', ');
const MOVIE_TABLE_COLUMNS_M = MOVIE_TABLE_COLUMNS.map(col => `m.${col}`).join(', ');


function initializeDatabaseService(logger, dbPath, defaultThumbnailCacheBasePath, isDev) {
  log = logger;
  dbPathInternal = dbPath;
  defaultThumbnailCacheBasePathInternal = defaultThumbnailCacheBasePath;
  isDevInternal = isDev;
  log.info(`[数据库服务] 初始化. 数据库路径: ${dbPathInternal}, 默认缩略图缓存: ${defaultThumbnailCacheBasePathInternal}`);
}

function connectAndSetupDatabase() {
  try {
    const dbDir = path.dirname(dbPathInternal);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
      log.info(`[数据库服务] 已创建数据库目录: ${dbDir}`);
    }

    db = new Database(dbPathInternal, { verbose: isDevInternal ? log.info : null });
    log.info('[数据库服务] 数据库连接已成功建立.');
    
    db.pragma('journal_mode = WAL');
    db.pragma('busy_timeout = 5000'); 
    log.info('[数据库服务] PRAGMA journal_mode=WAL, busy_timeout=5000 设置完成.');

    initializeSchema();
  } catch (error) {
    log.error('[数据库服务] 连接或设置数据库时发生严重错误:', error);
    throw error; 
  }
}

function initializeSchema() {
  log.info('[数据库服务] 开始初始化/检查数据库结构.');
  try {
    db.exec(`CREATE TABLE IF NOT EXISTS db_info (key TEXT PRIMARY KEY, value TEXT);`);
    log.info('[数据库服务] db_info 表检查/创建完成.');

    let currentVersion = 0;
    const versionRow = db.prepare("SELECT value FROM db_info WHERE key = 'version'").get();
    if (versionRow) currentVersion = parseInt(versionRow.value, 10);
    log.info(`[数据库服务] 当前数据库版本: ${currentVersion}, 目标版本: ${DB_VERSION}`);

    if (currentVersion < DB_VERSION) {
      log.info(`[数据库服务] 需要数据库迁移. 从版本 ${currentVersion} 到 ${DB_VERSION}.`);
      
      if (currentVersion < 21) { 
        const desiredColumnsV21 = [
          { name: 'db_id', type: 'INTEGER PRIMARY KEY AUTOINCREMENT' }, { name: 'filePath', type: 'TEXT UNIQUE NOT NULL' },
          { name: 'fileName', type: 'TEXT NOT NULL' }, { name: 'title', type: 'TEXT' }, { name: 'originalTitle', type: 'TEXT' },
          { name: 'nfoId', type: 'TEXT' }, { name: 'year', type: 'INTEGER' }, { name: 'releaseDate', type: 'TEXT' },
          { name: 'runtime', type: 'INTEGER' }, { name: 'plot', type: 'TEXT' }, { name: 'studio', type: 'TEXT' },
          { name: 'series', type: 'TEXT' }, { name: 'director', type: 'TEXT' }, { name: 'trailerUrl', type: 'TEXT' },
          { name: 'posterUrl', type: 'TEXT' }, { name: 'coverUrl', type: 'TEXT' }, { name: 'localCoverPath', type: 'TEXT' },
          { name: 'watched', type: 'BOOLEAN DEFAULT 0' }, { name: 'personalRating', type: 'INTEGER' },
          { name: 'actors', type: 'TEXT' }, { name: 'genres', type: 'TEXT' }, { name: 'tags', type: 'TEXT' },
          { name: 'lastScanned', type: 'DATETIME DEFAULT CURRENT_TIMESTAMP' }, { name: 'nfoLastModified', type: 'INTEGER' },
          { name: 'resolution', type: 'TEXT' }, { name: 'fileSize', type: 'INTEGER' }, { name: 'videoCodec', type: 'TEXT' },
          { name: 'audioCodec', type: 'TEXT' }, { name: 'preferredStatus', type: 'TEXT' }, { name: 'customFileTags', type: 'TEXT' },
          { name: 'versionCategories', type: 'TEXT' }, { name: 'autoDetectedFileNameTags', type: 'TEXT' },
          { name: 'fps', type: 'REAL' }, { name: 'videoCodecFull', type: 'TEXT' }, { name: 'videoBitrate', type: 'TEXT' },
          { name: 'audioCodecFull', type: 'TEXT' }, { name: 'audioChannelsDesc', type: 'TEXT' },
          { name: 'audioSampleRate', type: 'INTEGER' }, { name: 'audioBitrate', type: 'TEXT' }, { name: 'videoHeight', type: 'TEXT' },
          { name: 'aiAnalyzedTags', type: 'TEXT' }, { name: 'aiRecommendationType', type: 'TEXT' },
          { name: 'aiRecommendationScore', type: 'INTEGER' }, { name: 'aiRecommendationJustification', type: 'TEXT' }
        ];
        migrateTable('movies', desiredColumnsV21);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_movies_nfoId ON movies (nfoId);`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_movies_title ON movies (LOWER(title));`);
        db.exec(`CREATE TABLE IF NOT EXISTS favorites (id INTEGER PRIMARY KEY AUTOINCREMENT, item_type TEXT NOT NULL, item_value TEXT NOT NULL, favorited_at DATETIME DEFAULT CURRENT_TIMESTAMP, UNIQUE(item_type, item_value));`);
        log.info('[数据库服务] 迁移到版本 21 完成.');
      }
      
      if (currentVersion < 22) {
        db.exec(`CREATE TABLE IF NOT EXISTS actor_metadata (actor_name TEXT PRIMARY KEY, local_avatar_path TEXT, avatar_url_source TEXT, filetree_source_path TEXT, last_updated DATETIME DEFAULT CURRENT_TIMESTAMP);`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_actor_metadata_name ON actor_metadata (actor_name);`);
        log.info('[数据库服务] 迁移到版本 22: actor_metadata 表创建完成.');
      }

      if (currentVersion < 23) {
        const moviesColsV23 = db.pragma('table_info(movies)').map(col => ({ name: col.name, type: col.type }));
        if (!moviesColsV23.find(c => c.name === 'hasExternalSubtitles')) {
          db.exec('ALTER TABLE movies ADD COLUMN hasExternalSubtitles BOOLEAN DEFAULT 0');
        }
        if (!moviesColsV23.find(c => c.name === 'cdPartInfo')) {
          db.exec('ALTER TABLE movies ADD COLUMN cdPartInfo TEXT');
        }
        log.info('[数据库服务] 迁移到版本 23: movies 表添加 hasExternalSubtitles 和 cdPartInfo 列完成.');
      }
      
      if (currentVersion < 24) {
        const moviesColsV24 = db.pragma('table_info(movies)').map(col => ({ name: col.name, type: col.type }));
        if (!moviesColsV24.find(c => c.name === 'plotJa')) {
          db.exec('ALTER TABLE movies ADD COLUMN plotJa TEXT');
        }
        if (!moviesColsV24.find(c => c.name === 'plotZh')) {
          db.exec('ALTER TABLE movies ADD COLUMN plotZh TEXT');
        }
        log.info('[数据库服务] 迁移到版本 24: movies 表添加 plotJa 和 plotZh 列完成.');
      }

      if (currentVersion < 25) {
        db.exec(`CREATE TABLE IF NOT EXISTS movie_libraries (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            paths TEXT NOT NULL, -- JSON string array of paths
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_movie_libraries_name ON movie_libraries (name);`);

        db.exec(`CREATE TABLE IF NOT EXISTS movie_library_links (
            movie_db_id INTEGER NOT NULL,
            library_id TEXT NOT NULL,
            PRIMARY KEY (movie_db_id, library_id),
            FOREIGN KEY (movie_db_id) REFERENCES movies(db_id) ON DELETE CASCADE,
            FOREIGN KEY (library_id) REFERENCES movie_libraries(id) ON DELETE CASCADE
        );`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_movie_library_links_library_id ON movie_library_links (library_id);`);
        log.info('[数据库服务] 迁移到版本 25: movie_libraries 和 movie_library_links 表创建完成.');
      }

      if (currentVersion < 26) {
        // 用于存储搜集到的链接信息的新表 (Collector 模块)
        db.exec(`CREATE TABLE IF NOT EXISTS collected_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_forum TEXT,                          -- 来源论坛标识 (例如: 'ForumA', 'ForumB')
            post_url TEXT UNIQUE NOT NULL,              -- 原始帖子URL，用于防止重复搜集
            post_title TEXT NOT NULL,                   -- 帖子标题
            nfoId TEXT,                                 -- 从帖子标题或内容中提取的番号，用于未来联动
            magnet_link TEXT,                           -- 磁力链接
            ed2k_link TEXT,                             -- ed2k链接
            attachment_url TEXT,                        -- 附件的直接下载链接
            decompression_password TEXT,                -- 解压密码
            collection_date DATETIME DEFAULT CURRENT_TIMESTAMP, -- 搜集日期
            download_status TEXT DEFAULT 'pending' NOT NULL -- 下载状态: pending, downloading, completed, failed
        );`);

        // 为常用查询字段创建索引，提升性能
        db.exec(`CREATE INDEX IF NOT EXISTS idx_collected_links_nfoId ON collected_links (nfoId);`);
        db.exec(`CREATE INDEX IF NOT EXISTS idx_collected_links_download_status ON collected_links (download_status);`);
        log.info('[数据库服务] 迁移到版本 26: collected_links 表创建完成 (Collector 模块).');
      }

      if (currentVersion < 27) {
        // 添加 error_message 字段用于存储错误信息
        try {
          db.exec(`ALTER TABLE collected_links ADD COLUMN error_message TEXT;`);
          log.info('[数据库服务] 迁移到版本 27: 添加 error_message 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          log.info('[数据库服务] error_message 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 28) {
        // 添加 download_path 和 download_date 字段用于存储下载信息
        try {
          db.exec(`ALTER TABLE collected_links ADD COLUMN download_path TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN download_date DATETIME;`);
          log.info('[数据库服务] 迁移到版本 28: 添加 download_path 和 download_date 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          log.info('[数据库服务] download_path 和 download_date 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 29) {
        // 添加 archive_path 字段用于存储档案文件路径
        try {
          db.exec(`ALTER TABLE collected_links ADD COLUMN archive_path TEXT;`);
          log.info('[数据库服务] 迁移到版本 29: 添加 archive_path 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          log.info('[数据库服务] archive_path 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 30) {
        // 添加完整帖子内容相关字段
        try {
          db.exec(`ALTER TABLE collected_links ADD COLUMN full_post_html TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN full_post_text TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN post_body_text TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN all_images TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN all_links TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN cloud_links TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN extracted_metadata TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN board_info TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN status TEXT DEFAULT 'normal';`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN preview_image_url TEXT;`);
          db.exec(`ALTER TABLE collected_links ADD COLUMN post_date TEXT;`);
          log.info('[数据库服务] 迁移到版本 30: 添加完整帖子内容字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          log.info('[数据库服务] 完整帖子内容字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 31) {
        // 添加 md_document_path 字段用于存储对应的md文档路径
        try {
          db.exec(`ALTER TABLE collected_links ADD COLUMN md_document_path TEXT;`);
          log.info('[数据库服务] 迁移到版本 31: 添加 md_document_path 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          log.info('[数据库服务] md_document_path 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 32) {
        // 添加 ai_tags_json 字段用于存储 AI 分析生成的标签
        try {
          db.exec(`ALTER TABLE collected_links ADD COLUMN ai_tags_json TEXT;`);
          log.info('[数据库服务] 迁移到版本 32: 添加 ai_tags_json 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          log.info('[数据库服务] ai_tags_json 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 33) {
        // 创建 ai_categories 表用于管理AI分类标签
        try {
          db.exec(`
            CREATE TABLE IF NOT EXISTS ai_categories (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT UNIQUE NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
          `);
          log.info('[数据库服务] 迁移到版本 33: 创建 ai_categories 表完成.');

          // 插入默认分类标签
          const defaultCategories = [
            '国产精品', '人物合集', 'VR', '4K', '动画', '无码', '有码',
            '中文字幕', '破解版', '高清', '素人', '制服', '丝袜', '熟女',
            '萝莉', '巨乳', '美腿', '口交', '肛交', '群交', 'SM', '户外',
            '偷拍', '自拍', '直播', '网红', '明星', '欧美', '日韩', '亚洲', '其他'
          ];

          const insertStmt = db.prepare('INSERT OR IGNORE INTO ai_categories (name) VALUES (?)');
          for (const category of defaultCategories) {
            insertStmt.run(category);
          }
          log.info(`[数据库服务] 迁移到版本 33: 插入 ${defaultCategories.length} 个默认分类标签完成.`);
        } catch (error) {
          log.error(`[数据库服务] 迁移到版本 33 失败: ${error.message}`);
          throw error;
        }
      }

      if (currentVersion < 34) {
        try {
          // 添加 asset_status 字段到 movies 表
          db.exec(`ALTER TABLE movies ADD COLUMN asset_status TEXT NOT NULL DEFAULT 'AVAILABLE';`);
          log.info('[数据库服务] 迁移到版本 34: 添加 asset_status 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            log.error(`[数据库服务] 迁移到版本 34 失败: ${error.message}`);
            throw error;
          }
          log.info('[数据库服务] asset_status 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 36) {
        try {
          // 添加 asset_root_path 字段到 movies 表
          db.exec(`ALTER TABLE movies ADD COLUMN asset_root_path TEXT;`);
          log.info('[数据库服务] 迁移到版本 36: 添加 asset_root_path 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            log.error(`[数据库服务] 迁移到版本 36 失败: ${error.message}`);
            throw error;
          }
          log.info('[数据库服务] asset_root_path 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 37) {
        try {
          // 添加 version_status 字段到 movies 表
          db.exec(`ALTER TABLE movies ADD COLUMN version_status TEXT;`);
          log.info('[数据库服务] 迁移到版本 37: 添加 version_status 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            log.error(`[数据库服务] 迁移到版本 37 失败: ${error.message}`);
            throw error;
          }
          log.info('[数据库服务] version_status 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 38) {
        try {
          // 创建智能片库表
          db.exec(`CREATE TABLE IF NOT EXISTS smart_libraries (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            rules TEXT NOT NULL, -- 存储筛选规则的 JSON 字符串
            icon TEXT, -- (可选) 用于在 UI 上显示的图标名 (e.g., 'LuSparkles')
            sort_order INTEGER DEFAULT 0 -- 用于排序
          );`);
          db.exec(`CREATE INDEX IF NOT EXISTS idx_smart_libraries_sort_order ON smart_libraries (sort_order);`);
          log.info('[数据库服务] 迁移到版本 38: 创建 smart_libraries 表完成.');
        } catch (error) {
          log.error(`[数据库服务] 迁移到版本 38 失败: ${error.message}`);
          throw error;
        }
      }

      if (currentVersion < 39) {
        try {
          // 【数据天桥建设】创建 Collector 桥接安全视图
          db.exec(`CREATE VIEW IF NOT EXISTS collector_movie_data AS
            SELECT
              nfoId,
              post_title as title,
              post_url as source_url,
              magnet_link,
              ed2k_link,
              cloud_links,
              extracted_metadata,
              all_images,
              all_links,
              post_date,
              collection_date as collected_at,
              source_forum,
              decompression_password,
              preview_image_url,
              md_document_path
            FROM collected_links
            WHERE nfoId IS NOT NULL
              AND nfoId != ''
              AND status != 'deleted'
            ORDER BY collection_date DESC;`);

          log.info('[数据库服务] 迁移到版本 39: 创建 collector_movie_data 安全视图完成.');
        } catch (error) {
          log.error(`[数据库服务] 迁移到版本 39 失败: ${error.message}`);
          throw error;
        }
      }

      if (currentVersion < 40) {
        try {
          // 添加 release_status 字段到 movies 表，用于区分影片发售状态
          db.exec(`ALTER TABLE movies ADD COLUMN release_status TEXT DEFAULT 'released';`);
          log.info('[数据库服务] 迁移到版本 40: 添加 release_status 字段完成.');
        } catch (error) {
          // 如果字段已存在，忽略错误
          if (!error.message.includes('duplicate column name')) {
            log.error(`[数据库服务] 迁移到版本 40 失败: ${error.message}`);
            throw error;
          }
          log.info('[数据库服务] release_status 字段已存在，跳过添加.');
        }
      }

      if (currentVersion < 41) {
        try {
          // 创建 actor_profiles 表，用于存储人物档案信息
          db.exec(`CREATE TABLE IF NOT EXISTS actor_profiles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            aliases TEXT,
            avatar_remote_url TEXT,
            avatar_local_path TEXT,
            stats TEXT,
            bio TEXT,
            tags TEXT,
            source_url TEXT,
            external_ids TEXT,
            last_scraped_at TEXT
          );`);

          // 创建索引以提升查询性能
          db.exec(`CREATE INDEX IF NOT EXISTS idx_actor_profiles_name ON actor_profiles (name);`);
          db.exec(`CREATE INDEX IF NOT EXISTS idx_actor_profiles_last_scraped ON actor_profiles (last_scraped_at);`);

          log.info('[数据库服务] 迁移到版本 41: 创建 actor_profiles 表完成.');
        } catch (error) {
          log.error(`[数据库服务] 迁移到版本 41 失败: ${error.message}`);
          throw error;
        }
      }

      db.prepare("INSERT OR REPLACE INTO db_info (key, value) VALUES ('version', ?)").run(String(DB_VERSION));
      log.info(`[数据库服务] 数据库已成功迁移到版本 ${DB_VERSION}.`);
    } else {
      log.info('[数据库服务] 数据库结构已是最新版本.');
    }
  } catch (error) {
    log.error('[数据库服务] 初始化数据库结构时出错:', error);
    throw error;
  }
}

function migrateTable(tableName, desiredColumns) {
    const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}';`).get();
    let existingColumns = [];
    if (tableExists) {
        existingColumns = db.pragma(`table_info(${tableName})`).map(col => col.name);
    }
    
    const tempTableName = `${tableName}_new`;
    db.exec(`CREATE TABLE IF NOT EXISTS ${tempTableName} (${desiredColumns.map(c => `${c.name} ${c.type}`).join(', ')});`);
    
    if (tableExists) {
        const commonColumns = desiredColumns.filter(dc => existingColumns.includes(dc.name)).map(dc => dc.name).join(', ');
        if (commonColumns) {
             try {
                db.exec(`INSERT INTO ${tempTableName} (${commonColumns}) SELECT ${commonColumns} FROM ${tableName};`);
             } catch(e) {
                log.error(`[数据库服务] 迁移数据 ${tableName} -> ${tempTableName} 失败: ${e.message}`);
             }
        }
        db.exec(`DROP TABLE ${tableName};`);
    }
    db.exec(`ALTER TABLE ${tempTableName} RENAME TO ${tableName};`);
    log.info(`[数据库服务] 表 ${tableName} 迁移完成.`);
}


function getDb() {
  if (!db) {
    log.error('[数据库服务] 尝试在初始化/连接前获取数据库实例.');
    connectAndSetupDatabase(); 
  }
  return db;
}

function closeDatabase() {
  if (db) {
    db.close();
    log.info('[数据库服务] 数据库连接已关闭.');
    db = null;
  }
}

function determineSpecialTags(movie, filenameSuffixRules = []) {
    const tags = new Set();
    if (movie.videoHeight && parseInt(movie.videoHeight, 10) >= 2000) tags.add("4K");
    else if (movie.resolution && (movie.resolution.includes("3840") || movie.resolution.includes("4096"))) tags.add("4K");
    else if (movie.fileName && /(4k|2160p|uhd)/i.test(movie.fileName)) tags.add("4K");

    const genresAndTags = [...(movie.genres || []), ...(movie.tags || [])];
    if (genresAndTags.some(gt => gt.toLowerCase() === 'vr') || (movie.fileName && /vr/i.test(movie.fileName))) {
        tags.add("VR");
    }
    if (movie.fileName && /(bluray|blu-ray|bdmv|bd25|bd50|remux)/i.test(movie.fileName)) {
        tags.add("BD原盘");
    }

    const fn = movie.fileName ? movie.fileName.toLowerCase() : '';
    let isChineseSubtitled = false;
    if (/-c\.|\-c\-|\bch\.|\-字幕|\-c\]/i.test(fn)) {
        tags.add("中文字幕");
        isChineseSubtitled = true;
    }
    
    let isUncensored = false;
    if (/\-u\.|\-uncensored/i.test(fn)) {
        tags.add("破解版");
        isUncensored = true;
    }

    if (/\-uc/i.test(fn)) {
        if (!isChineseSubtitled) tags.add("中文字幕");
        if (!isUncensored) tags.add("破解版");
    }

    if (/\-leaked/i.test(fn)) {
        tags.add("流出版");
    }
    
    (filenameSuffixRules || []).forEach(rule => {
      if (movie.fileName && movie.fileName.toLowerCase().includes(rule.suffix.toLowerCase())) { 
        rule.tags.forEach(tag => tags.add(tag));
      }
    });

    if (movie.hasExternalSubtitles) {
        tags.add("外挂字幕");
    }
    
    return Array.from(tags).slice(0, 4); 
}

function getMoviesFromDb(params) {
    const {
        sortField = 'releaseDate', sortOrder = 'desc', filterText = '',
        advancedFilters = {}, pageNumber = 1, pageSize = 50, privacySettings = null,
        libraryId = null // Added libraryId
    } = params;
    const settings = require('./settingsService').getSettings(); 

    let whereClauses = [];
    let queryParams = [];
    let fromClause = "FROM movies m_sub";

    if (libraryId) {
        fromClause += " INNER JOIN movie_library_links mll_sub ON m_sub.db_id = mll_sub.movie_db_id";
        whereClauses.push("mll_sub.library_id = ?");
        queryParams.push(libraryId);
    }

    if (filterText) {
        const text = `%${filterText}%`;
        whereClauses.push(`(
            m_sub.title LIKE ? OR m_sub.originalTitle LIKE ? OR m_sub.nfoId LIKE ? OR m_sub.fileName LIKE ? OR 
            (json_valid(m_sub.actors) AND EXISTS (SELECT 1 FROM json_each(m_sub.actors) WHERE json_each.value LIKE ?)) OR
            (m_sub.director LIKE ?) 
        )`);
        queryParams.push(text, text, text, text, text, text);
    }
    
    if (advancedFilters) {
        if (advancedFilters.selectedActors && advancedFilters.selectedActors.length > 0) {
            const actorPlaceholders = advancedFilters.selectedActors.map(() => '?').join(',');
            whereClauses.push(`EXISTS (SELECT 1 FROM json_each(m_sub.actors) ja WHERE ja.value IN (${actorPlaceholders}))`);
            queryParams.push(...advancedFilters.selectedActors);
        }
        if (advancedFilters.selectedGenres && advancedFilters.selectedGenres.length > 0) {
            const genrePlaceholders = advancedFilters.selectedGenres.map(() => '?').join(',');
            whereClauses.push(`EXISTS (SELECT 1 FROM json_each(m_sub.genres) jg WHERE jg.value IN (${genrePlaceholders}))`);
            queryParams.push(...advancedFilters.selectedGenres);
        }
        if (advancedFilters.selectedTags && advancedFilters.selectedTags.length > 0) {
            const tagConditions = [];
            if (advancedFilters.selectedTags.some(tag => tag.toLowerCase().includes('(ai)'))) { 
                const aiTagsOnly = advancedFilters.selectedTags
                    .filter(tag => tag.toLowerCase().includes('(ai)'))
                    .map(tag => tag.replace(/\s*\(ai\)/i, '').trim());
                if (aiTagsOnly.length > 0) {
                    const aiTagPlaceholders = aiTagsOnly.map(() => '?').join(',');
                    tagConditions.push(`EXISTS (SELECT 1 FROM json_each(m_sub.aiAnalyzedTags) jt_ai WHERE jt_ai.value IN (${aiTagPlaceholders}))`);
                    queryParams.push(...aiTagsOnly);
                }
            }
            const normalTags = advancedFilters.selectedTags.filter(tag => !tag.toLowerCase().includes('(ai)'));
            if (normalTags.length > 0) {
                 const normalTagPlaceholders = normalTags.map(() => '?').join(',');
                 tagConditions.push(`EXISTS (SELECT 1 FROM json_each(m_sub.tags) jt WHERE jt.value IN (${normalTagPlaceholders}))`);
                 queryParams.push(...normalTags);
            }
            if (tagConditions.length > 0) {
                whereClauses.push(`(${tagConditions.join(' OR ')})`);
            }
        }
        
        if (advancedFilters.resolution && Array.isArray(advancedFilters.resolution) && advancedFilters.resolution.length > 0) {
            const resolutionConditions = advancedFilters.resolution.map(res => {
                if (res.includes("4K") || res.includes("2160p")) {
                    queryParams.push('%2160%', '%4k%'); 
                    return "(m_sub.videoHeight LIKE ? OR LOWER(m_sub.resolution) LIKE ?)";
                } else if (res.includes("1440p")) {
                    queryParams.push('%1440%');
                    return "(m_sub.videoHeight LIKE ?)";
                } else if (res.includes("1080p")) {
                    queryParams.push('%1080%');
                    return "(m_sub.videoHeight LIKE ?)";
                } else if (res.includes("720p")) {
                    queryParams.push('%720%');
                    return "(m_sub.videoHeight LIKE ?)";
                } else if (res.includes("SD") || res.includes("480p")) {
                    return "(CAST(m_sub.videoHeight AS INTEGER) <= 480)";
                } else { 
                    queryParams.push(`%${res}%`, `%${res}%`);
                    return "(m_sub.videoHeight LIKE ? OR LOWER(m_sub.resolution) LIKE ?)";
                }
            });
            if (resolutionConditions.length > 0) {
                whereClauses.push(`(${resolutionConditions.join(' OR ')})`);
            }
        } else if (advancedFilters.resolution && typeof advancedFilters.resolution === 'string' && advancedFilters.resolution.trim() !== '') {
            const resVal = `%${advancedFilters.resolution.trim()}%`;
            whereClauses.push(`(m_sub.videoHeight LIKE ? OR LOWER(m_sub.resolution) LIKE ?)`);
            queryParams.push(resVal, resVal);
        }

        if (advancedFilters.hasSpecialTag) {
           log.warn("[数据库服务] 按特殊标签筛选的功能尚未在SQL层面完全实现，将进行后续处理。");
        }
        if (advancedFilters.selectedStudio) { whereClauses.push(`m_sub.studio = ?`); queryParams.push(advancedFilters.selectedStudio); }
        if (advancedFilters.selectedSeries) { whereClauses.push(`m_sub.series = ?`); queryParams.push(advancedFilters.selectedSeries); }
        if (advancedFilters.director) { whereClauses.push(`m_sub.director = ?`); queryParams.push(advancedFilters.director); }
        if (advancedFilters.year) { whereClauses.push(`m_sub.year = ?`); queryParams.push(advancedFilters.year); }
        if (advancedFilters.filterWatchedStatus && advancedFilters.filterWatchedStatus !== 'all') {
            whereClauses.push(`m_sub.watched = ?`);
            queryParams.push(advancedFilters.filterWatchedStatus === 'watched' ? 1 : 0);
        }
        if (advancedFilters.filterRating && advancedFilters.filterRating !== 'any') {
            if (advancedFilters.filterRating === 'unrated') {
                whereClauses.push(`(m_sub.personalRating IS NULL OR m_sub.personalRating = 0)`);
            } else {
                whereClauses.push(`m_sub.personalRating >= ?`);
                queryParams.push(parseInt(advancedFilters.filterRating, 10));
            }
        }
    }
    
    if (privacySettings && privacySettings.hideTags && privacySettings.hideTags.length > 0) {
       const privacyTagPlaceholders = privacySettings.hideTags.map(() => '?').join(',');
        whereClauses.push(`NOT EXISTS (
            SELECT 1 FROM json_each(m_sub.tags) pt 
            WHERE pt.value IN (${privacyTagPlaceholders})
        )`);
        queryParams.push(...privacySettings.hideTags);
    }

    const offset = (pageNumber - 1) * pageSize;
    const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

    const validSortFields = ['db_id', 'filePath', 'fileName', 'title', 'originalTitle', 'nfoId', 'year', 'releaseDate', 'runtime', 'plot', 'studio', 'series', 'director', 'watched', 'personalRating', 'lastScanned', 'nfoLastModified', 'resolution', 'videoHeight', 'fileSize', 'videoCodec', 'audioCodec', 'preferredStatus', 'fps', 'videoBitrate', 'aiRecommendationScore', 'versionCount', 'multiCdCountForNfoId'];
    const safeSortField = validSortFields.includes(sortField) ? sortField : 'releaseDate';
    const safeSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    let orderBySql = `ORDER BY m_final.${safeSortField === 'aiRecommendationScore' ? 'aiRecommendationScoreToUse' : safeSortField} ${safeSortOrder}, m_final.db_id ${safeSortOrder}`;
     if (safeSortField === 'aiRecommendationScore') {
        orderBySql = `ORDER BY CASE m_final.aiRecommendationType WHEN 'avoidance' THEN m_final.aiRecommendationScoreToUse * -1 ELSE m_final.aiRecommendationScoreToUse END ${safeSortOrder}, m_final.db_id ${safeSortOrder}`;
    }

    const moviesQuery = `
      SELECT ${MOVIE_TABLE_COLUMNS_M_FINAL}, m_final.aiRecommendationScoreToUse, m_final.multiCdCountForNfoId, m_final.versionCountComputed
      FROM (
          SELECT 
            ${MOVIE_TABLE_COLUMNS_M},
            COALESCE(m.aiRecommendationScore, 0) as aiRecommendationScoreToUse, 
            (SELECT COUNT(DISTINCT mc.cdPartInfo) FROM movies mc WHERE mc.nfoId = m.nfoId AND mc.cdPartInfo IS NOT NULL AND mc.cdPartInfo != '') as multiCdCountForNfoId,
            (SELECT COUNT(DISTINCT v.filePath) FROM movies v WHERE v.nfoId = m.nfoId AND v.nfoId IS NOT NULL AND v.nfoId != '') as versionCountComputed
          FROM (
              SELECT 
                  ${MOVIE_TABLE_COLUMNS_M_SUB},
                  ROW_NUMBER() OVER (
                      PARTITION BY CASE WHEN m_sub.nfoId IS NOT NULL AND TRIM(m_sub.nfoId) != '' THEN LOWER(TRIM(m_sub.nfoId)) ELSE m_sub.filePath END
                      ORDER BY
                          CASE m_sub.preferredStatus WHEN 'preferred' THEN 1 ELSE 2 END,
                          m_sub.year DESC NULLS LAST,
                          m_sub.fileSize DESC NULLS LAST,
                          m_sub.db_id DESC
                  ) as rn
              ${fromClause} ${whereSql} 
          ) m
          WHERE m.rn = 1
      ) m_final
      ${orderBySql}
      LIMIT ? OFFSET ?
    `;
    
    const countQuery = `
        SELECT COUNT(*) as total
        FROM (
            SELECT 1
            FROM (
                SELECT m_sub.db_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY CASE WHEN m_sub.nfoId IS NOT NULL AND TRIM(m_sub.nfoId) != '' THEN LOWER(TRIM(m_sub.nfoId)) ELSE m_sub.filePath END
                        ORDER BY CASE m_sub.preferredStatus WHEN 'preferred' THEN 1 ELSE 2 END, m_sub.year DESC NULLS LAST, m_sub.fileSize DESC NULLS LAST, m_sub.db_id DESC
                    ) as rn
                ${fromClause} ${whereSql} 
            ) m
            WHERE m.rn = 1
        )
    `;

    try {
        const moviesResult = db.prepare(moviesQuery).all(...queryParams, pageSize, offset);
        const totalResult = db.prepare(countQuery).get(...queryParams);
        
        const moviesWithParsedArrays = moviesResult.map(movie => {
          const specialTags = determineSpecialTags(movie, settings.filenameSuffixRules);
          return {
            ...movie,
            versionCount: movie.versionCountComputed > 1 ? movie.versionCountComputed : undefined,
            specialTags,
            actors: movie.actors && json_valid(movie.actors) ? JSON.parse(movie.actors) : [],
            genres: movie.genres && json_valid(movie.genres) ? JSON.parse(movie.genres) : [],
            tags: movie.tags && json_valid(movie.tags) ? JSON.parse(movie.tags) : [],
            aiAnalyzedTags: movie.aiAnalyzedTags && json_valid(movie.aiAnalyzedTags) ? JSON.parse(movie.aiAnalyzedTags) : [],
            watched: Boolean(movie.watched)
          };
        });

        // 【新增】为每个影片附加 A区 displayData
        const moviesWithDisplayData = moviesWithParsedArrays.map(movie => {
          let displayData = null;

          try {
            // 计算 .meta.json 文件路径
            let metaJsonPath = null;

            if (movie.asset_root_path && typeof movie.asset_root_path === 'string') {
              // 如果有 asset_root_path，直接使用
              metaJsonPath = path.join(movie.asset_root_path, '.meta.json');
            } else if (movie.nfoId) {
              // 否则通过 pathResolverService 重新计算
              try {
                const pathResolverService = require('./pathResolverService');
                const mockScrapedData = { nfoId: movie.nfoId };
                const assetPaths = pathResolverService.resolveAssetPaths(mockScrapedData);
                if (assetPaths && assetPaths.movieRootPath) {
                  metaJsonPath = path.join(assetPaths.movieRootPath, '.meta.json');
                }
              } catch (pathError) {
                log.debug(`[数据库服务] 无法解析 ${movie.nfoId} 的路径: ${pathError.message}`);
              }
            }

            // 安全读取并解析 .meta.json 文件
            if (metaJsonPath && fs.existsSync(metaJsonPath)) {
              const metaJsonContent = fs.readFileSync(metaJsonPath, 'utf8');
              const metaJson = JSON.parse(metaJsonContent);

              if (metaJson.display_data) {
                displayData = metaJson.display_data;
                log.debug(`[数据库服务] 成功加载 ${movie.nfoId} 的 displayData`);
              }
            }
          } catch (error) {
            // 静默处理错误，不影响整个查询
            log.warn(`[数据库服务] 读取 ${movie.nfoId} 的 .meta.json 失败: ${error.message}`);
          }

          return {
            ...movie,
            displayData: displayData
          };
        });

        return { success: true, movies: moviesWithDisplayData, totalMovies: totalResult.total };
    } catch (error) {
        log.error('[数据库服务] 获取影片列表时出错:', error);
        return { success: false, movies: [], totalMovies: 0, error: error.message };
    }
}

function json_valid(str) {
    if (typeof str !== 'string') return false;
    if (str.trim() === '') return false; 
    try { JSON.parse(str); return true; } catch (e) { return false; }
}


function updateMovieCoverPath(videoFilePath, localCoverPath) { /* ... */ }
async function saveNfoDataAndUpdateDb(videoFilePath, movieData, pythonRunnerService, pythonExecutablePath, imagePathToDataUrlFunc) {
  const NodeNfoWriter = require('./nodeNfoWriter');

  try {
    log.info(`[数据库服务] 开始保存 NFO 数据: ${videoFilePath}`);

    // 使用 Node.js NFO 写入器
    const nfoResult = await NodeNfoWriter.createOrUpdateNfo(videoFilePath, movieData);

    if (!nfoResult.success) {
      log.error(`[数据库服务] NFO 文件写入失败: ${nfoResult.error}`);
      return { success: false, error: `NFO 文件写入失败: ${nfoResult.error}` };
    }

    log.info(`[数据库服务] NFO 文件写入成功: ${nfoResult.nfoFilePath}`);

    // 更新数据库
    const updateFields = [];
    const updateValues = [];

    // 构建更新字段
    const fieldsToUpdate = [
      'title', 'originalTitle', 'year', 'releaseDate', 'runtime', 'plot', 'plotJa', 'plotZh',
      'studio', 'series', 'director', 'trailerUrl', 'posterUrl', 'coverUrl', 'personalRating',
      'actors', 'genres', 'tags', 'nfoId', 'preferredStatus', 'customFileTags', 'versionCategories'
    ];

    fieldsToUpdate.forEach(field => {
      if (movieData.hasOwnProperty(field)) {
        updateFields.push(`${field} = ?`);
        updateValues.push(movieData[field]);
      }
    });

    // 添加最后扫描时间
    updateFields.push('lastScanned = datetime(\'now\', \'localtime\')');
    updateFields.push('nfoLastModified = datetime(\'now\', \'localtime\')');

    // 添加 WHERE 条件
    updateValues.push(videoFilePath);

    if (updateFields.length > 0) {
      const updateSql = `UPDATE movies SET ${updateFields.join(', ')} WHERE filePath = ?`;
      const stmt = db.prepare(updateSql);
      const info = stmt.run(...updateValues);

      if (info.changes > 0) {
        log.info(`[数据库服务] 数据库更新成功: ${videoFilePath}`);

        // 获取更新后的电影数据
        const updatedMovie = db.prepare('SELECT * FROM movies WHERE filePath = ?').get(videoFilePath);

        if (updatedMovie) {
          // 处理封面数据
          const coverDataUrl = updatedMovie.localCoverPath ? imagePathToDataUrlFunc(updatedMovie.localCoverPath) : null;

          return {
            success: true,
            updatedMovie: {
              ...updatedMovie,
              coverDataUrl,
              actors: updatedMovie.actors && json_valid(updatedMovie.actors) ? JSON.parse(updatedMovie.actors) : [],
              genres: updatedMovie.genres && json_valid(updatedMovie.genres) ? JSON.parse(updatedMovie.genres) : [],
              tags: updatedMovie.tags && json_valid(updatedMovie.tags) ? JSON.parse(updatedMovie.tags) : [],
              watched: Boolean(updatedMovie.watched)
            },
            message: 'NFO 数据保存成功'
          };
        }
      }
    }

    return { success: false, error: '数据库更新失败' };

  } catch (error) {
    log.error(`[数据库服务] 保存 NFO 数据失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}
function updateMovieAiTags(db_id, aiTags) { /* ... */ }
function updateMovieAiRecommendation(db_id, recommendation) { /* ... */ }
function deleteMovieByFilePath(filePath) { /* ... */ }

/**
 * 根据 nfoId 获取所有关联的本地版本和虚拟版本
 * @param {string} nfoId
 * @returns {Array<object>} 统一格式的版本对象数组
 */
function getUnifiedVersionsByNfoId(nfoId) {
  if (!nfoId) return [];

  try {
    // 1. 查询本地已拥有的版本 (从 movies 表)
    const localVersionsStmt = db.prepare(`SELECT ${MOVIE_TABLE_COLUMNS.join(', ')} FROM movies WHERE nfoId = ? ORDER BY preferredStatus DESC, fileSize DESC, filePath ASC`);
    const localVersions = localVersionsStmt.all(nfoId).map(m => {
      // 【新增逻辑】文件大小单位为字节 (Bytes)，小于 200MB 的标记为预告片
      const isTrailer = m.fileSize && m.fileSize < 200 * 1024 * 1024;
      const sub_type = isTrailer ? 'trailer' : 'main';

      return {
        type: 'local',
        sub_type: sub_type, // 【新增字段】
        ...m // 包含所有 movies 表的字段
      };
    });

    // 2. 查询情报中心发现的虚拟版本 (从 collected_links 表)
    const virtualVersionsStmt = db.prepare(`
      SELECT id, source_forum, post_url, post_title, nfoId,
             magnet_link, ed2k_link, attachment_url, decompression_password,
             collection_date, download_status, error_message, download_path, download_date,
             archive_path, full_post_html, full_post_text, post_body_text, all_images, all_links,
             cloud_links, extracted_metadata, board_info, status, preview_image_url, post_date,
             md_document_path, ai_tags_json
      FROM collected_links
      WHERE nfoId = ? AND nfoId IS NOT NULL AND nfoId != ''
      ORDER BY collection_date DESC
    `);
    const virtualVersions = virtualVersionsStmt.all(nfoId).map(v => ({
      type: 'virtual',
      // 手动映射字段以匹配 movie 对象的大致结构，方便前端处理
      db_id: v.id,
      title: v.post_title,
      nfoId: v.nfoId,
      fileName: v.post_title, // 使用帖子标题作为文件名
      fileSize: null, // 虚拟版本没有实际文件大小
      resolution: null, // 可以从 extracted_metadata 中解析
      ai_tags_json: v.ai_tags_json,
      source_forum: v.source_forum,
      post_url: v.post_url,
      magnet_link: v.magnet_link,
      ed2k_link: v.ed2k_link,
      attachment_url: v.attachment_url,
      decompression_password: v.decompression_password,
      collection_date: v.collection_date,
      download_status: v.download_status,
      preview_image_url: v.preview_image_url,
      // ... 映射其他需要的字段 ...
      raw_data: v // 保留原始数据以备后用
    }));

    // 3. 合并并返回
    // 在这里可以加入逻辑，避免重复（例如，一个虚拟版本如果已经被下载，理论上就不该再显示）
    // 但初期我们可以简单合并，让前端处理
    return [...localVersions, ...virtualVersions];
  } catch (error) {
    log.error(`[数据库服务] 获取统一版本数据失败: ${error.message}`, error);
    return [];
  }
}

function getMovieVersionsFromDb(nfoId, imagePathToDataUrlFunc) {
  if (!nfoId) return { success: false, versions: [], error: "NFO ID 无效." };
  const settings = require('./settingsService').getSettings();
  try {
    const stmt = db.prepare(`SELECT ${MOVIE_TABLE_COLUMNS.join(', ')} FROM movies WHERE nfoId = ? ORDER BY preferredStatus DESC, fileSize DESC, filePath ASC`);
    const versionsData = stmt.all(nfoId);
    
    const versions = versionsData.map(v => {
      const coverDataUrl = v.localCoverPath ? imagePathToDataUrlFunc(v.localCoverPath) : null;
      const specialTags = determineSpecialTags(v, settings.filenameSuffixRules);
      return {
        ...v,
        coverDataUrl,
        specialTags,
        actors: v.actors && json_valid(v.actors) ? JSON.parse(v.actors) : [],
        genres: v.genres && json_valid(v.genres) ? JSON.parse(v.genres) : [],
        tags: v.tags && json_valid(v.tags) ? JSON.parse(v.tags) : [],
        aiAnalyzedTags: v.aiAnalyzedTags && json_valid(v.aiAnalyzedTags) ? JSON.parse(v.aiAnalyzedTags) : [],
        watched: Boolean(v.watched)
      };
    });
    
    log.info(`[数据库服务] 成功获取 NFO ID "${nfoId}" 的 ${versions.length} 个版本.`);
    return { success: true, versions };
  } catch (error) {
    log.error(`[数据库服务] 获取影片版本信息失败 (NFO ID: ${nfoId}):`, error);
    return { success: false, versions: [], error: error.message };
  }
}

function getMovieCdPartsFromDb(nfoId, imagePathToDataUrlFunc) {
  if (!nfoId) return { success: false, cdParts: [], error: "NFO ID 无效." };
  const settings = require('./settingsService').getSettings();
  try {
    const stmt = db.prepare(`SELECT ${MOVIE_TABLE_COLUMNS.join(', ')} FROM movies WHERE nfoId = ? AND cdPartInfo IS NOT NULL AND cdPartInfo != '' ORDER BY cdPartInfo ASC, filePath ASC`);
    const cdPartsData = stmt.all(nfoId);
    
    const cdParts = cdPartsData.map(cd => {
      const coverDataUrl = cd.localCoverPath ? imagePathToDataUrlFunc(cd.localCoverPath) : null;
      const specialTags = determineSpecialTags(cd, settings.filenameSuffixRules);
      return {
        ...cd,
        coverDataUrl,
        specialTags,
        actors: cd.actors && json_valid(cd.actors) ? JSON.parse(cd.actors) : [],
        genres: cd.genres && json_valid(cd.genres) ? JSON.parse(cd.genres) : [],
        tags: cd.tags && json_valid(cd.tags) ? JSON.parse(cd.tags) : [],
        aiAnalyzedTags: cd.aiAnalyzedTags && json_valid(cd.aiAnalyzedTags) ? JSON.parse(cd.aiAnalyzedTags) : [],
        watched: Boolean(cd.watched)
      };
    });
    
    log.info(`[数据库服务] 成功获取 NFO ID "${nfoId}" 的 ${cdParts.length} 个CD分集.`);
    return { success: true, cdParts };
  } catch (error) {
    log.error(`[数据库服务] 获取影片CD分集信息失败 (NFO ID: ${nfoId}):`, error);
    return { success: false, cdParts: [], error: error.message };
  }
}


function saveVersionMarksToDb(params, imagePathToDataUrlFunc) { /* ... */ }
async function renameFilesByNfoIdInDb(nfoId, renameTemplate, pythonRunnerService, pythonExecutablePath) { /* ... */ }
function backupDatabase(userDataPath) { /* ... */ }
function restoreDatabase(backupFilePath, currentDbPath) { /* ... */ }
function getMovieCleanupStats() { /* ... */ }

function toggleFavorite(itemType, itemValue) {
  const isCurrentlyFavorite = isFavoriteItem(itemType, itemValue).isFavorite;
  if (isCurrentlyFavorite) {
    return removeFavoriteItem(itemType, itemValue);
  } else {
    return addFavoriteItem(itemType, itemValue);
  }
}

function addFavoriteItem(itemType, itemValue) {
  try {
    const stmt = db.prepare("INSERT OR IGNORE INTO favorites (item_type, item_value) VALUES (?, ?)");
    const info = stmt.run(itemType, itemValue);
    return { success: true, isFavorite: true, changed: info.changes > 0 };
  } catch (error) {
    log.error(`[数据库服务] 添加收藏失败 for ${itemType} - ${itemValue}:`, error);
    return { success: false, isFavorite: false, error: error.message };
  }
}

function removeFavoriteItem(itemType, itemValue) {
  try {
    const stmt = db.prepare("DELETE FROM favorites WHERE item_type = ? AND item_value = ?");
    const info = stmt.run(itemType, itemValue);
    return { success: true, isFavorite: false, changed: info.changes > 0 };
  } catch (error) {
    log.error(`[数据库服务] 移除收藏失败 for ${itemType} - ${itemValue}:`, error);
    return { success: false, isFavorite: true, error: error.message };
  }
}

function isFavoriteItem(itemType, itemValue) {
  try {
    const stmt = db.prepare("SELECT COUNT(*) as count FROM favorites WHERE item_type = ? AND item_value = ?");
    const result = stmt.get(itemType, itemValue);
    return { success: true, isFavorite: result.count > 0 };
  } catch (error) {
    log.error(`[数据库服务] 检查收藏状态失败 for ${itemType} - ${itemValue}:`, error);
    return { success: false, isFavorite: false, error: error.message };
  }
}

// 新增：批量检查收藏状态
function batchCheckFavoriteItems(items) {
  try {
    if (!items || items.length === 0) {
      return { success: true, favoriteStatus: {} };
    }

    // 构建批量查询
    const placeholders = items.map(() => "(?, ?)").join(", ");
    const query = `SELECT item_type, item_value FROM favorites WHERE (item_type, item_value) IN (${placeholders})`;

    const params = [];
    items.forEach(item => {
      params.push(item.type, item.value);
    });

    const stmt = db.prepare(query);
    const favoriteItems = stmt.all(...params);

    // 构建结果对象
    const favoriteStatus = {};
    items.forEach(item => {
      if (!favoriteStatus[item.type]) {
        favoriteStatus[item.type] = {};
      }
      favoriteStatus[item.type][item.value] = false;
    });

    // 标记收藏的项目
    favoriteItems.forEach(fav => {
      if (!favoriteStatus[fav.item_type]) {
        favoriteStatus[fav.item_type] = {};
      }
      favoriteStatus[fav.item_type][fav.item_value] = true;
    });

    return { success: true, favoriteStatus };
  } catch (error) {
    log.error(`[数据库服务] 批量检查收藏状态失败:`, error);
    return { success: false, favoriteStatus: {}, error: error.message };
  }
}

function getFavoriteItems(itemType = null) {
  try {
    let query = "SELECT id, item_type, item_value, favorited_at FROM favorites";
    const params = [];
    if (itemType) {
      query += " WHERE item_type = ?";
      params.push(itemType);
    }
    query += " ORDER BY item_type, favorited_at DESC";
    const stmt = db.prepare(query);
    const favorites = stmt.all(...params);
    return { success: true, favorites };
  } catch (error) {
    log.error(`[数据库服务] 获取收藏列表失败 (类型: ${itemType || 'all'}):`, error);
    return { success: false, favorites: [], error: error.message };
  }
}


function getRecommendationsByFavorites(params = {}) {
  const { limitPerCategory = 3, maxTotal = 20 } = params;
  log.info(`[数据库服务] 开始获取基于收藏的推荐。分类上限: ${limitPerCategory}, 总上限: ${maxTotal}`);
  try {
    const favoriteActorsStmt = db.prepare("SELECT item_value FROM favorites WHERE item_type = 'actress' ORDER BY favorited_at DESC LIMIT ?");
    const favoriteActors = favoriteActorsStmt.all(limitPerCategory).map(row => row.item_value);

    const favoriteGenresStmt = db.prepare("SELECT item_value FROM favorites WHERE item_type = 'genre' ORDER BY favorited_at DESC LIMIT ?");
    const favoriteGenres = favoriteGenresStmt.all(limitPerCategory).map(row => row.item_value);
    
    let recommendedMovies = [];
    const recommendedMovieDbIds = new Set();

    if (favoriteActors.length > 0) {
      let actorMoviesQuery = `
        SELECT ${MOVIE_TABLE_COLUMNS_M}
        FROM movies m
        WHERE EXISTS (SELECT 1 FROM json_each(m.actors) WHERE json_each.value = ?)
          AND (m.watched = 0 OR m.personalRating IS NULL) 
        ORDER BY m.releaseDate DESC, m.nfoId DESC
        LIMIT ?;`;
      for (const actor of favoriteActors) {
        if (recommendedMovies.length >= maxTotal) break;
        const moviesForActor = db.prepare(actorMoviesQuery).all(actor, limitPerCategory * 2); 
        moviesForActor.forEach(movie => {
          if (recommendedMovies.length < maxTotal && !recommendedMovieDbIds.has(movie.db_id)) {
            recommendedMovies.push(movie);
            recommendedMovieDbIds.add(movie.db_id);
          }
        });
      }
    }

    if (favoriteGenres.length > 0 && recommendedMovies.length < maxTotal) {
      let genreMoviesQuery = `
        SELECT ${MOVIE_TABLE_COLUMNS_M}
        FROM movies m
        WHERE EXISTS (SELECT 1 FROM json_each(m.genres) WHERE json_each.value = ?)
          AND (m.watched = 0 OR m.personalRating IS NULL)
          AND m.db_id NOT IN (${recommendedMovieDbIds.size > 0 ? Array.from(recommendedMovieDbIds).map(()=>'?').join(',') : 'NULL'}) 
        ORDER BY m.releaseDate DESC, m.nfoId DESC
        LIMIT ?;`;
      for (const genre of favoriteGenres) {
        if (recommendedMovies.length < maxTotal) break;
        const currentDbIds = recommendedMovieDbIds.size > 0 ? Array.from(recommendedMovieDbIds) : [];
        const moviesForGenre = db.prepare(genreMoviesQuery).all(genre, ...currentDbIds, limitPerCategory * 2);
        moviesForGenre.forEach(movie => {
          if (recommendedMovies.length < maxTotal && !recommendedMovieDbIds.has(movie.db_id)) {
            recommendedMovies.push(movie);
            recommendedMovieDbIds.add(movie.db_id);
          }
        });
      }
    }
    
    if (recommendedMovies.length < maxTotal) {
        const fallbackQuery = `
            SELECT ${MOVIE_TABLE_COLUMNS_M}
            FROM movies m
            WHERE (m.watched = 0 OR m.personalRating IS NULL)
              AND m.db_id NOT IN (${recommendedMovieDbIds.size > 0 ? Array.from(recommendedMovieDbIds).map(()=>'?').join(',') : 'NULL'})
            ORDER BY m.personalRating DESC, m.releaseDate DESC
            LIMIT ?;
        `;
        const currentDbIds = recommendedMovieDbIds.size > 0 ? Array.from(recommendedMovieDbIds) : [];
        const fallbackMovies = db.prepare(fallbackQuery).all(...currentDbIds, maxTotal - recommendedMovies.length);
        fallbackMovies.forEach(movie => {
            if (recommendedMovies.length < maxTotal && !recommendedMovieDbIds.has(movie.db_id)) {
                recommendedMovies.push(movie);
                recommendedMovieDbIds.add(movie.db_id);
            }
        });
    }
    
    const settings = require('./settingsService').getSettings();
    const finalMovies = recommendedMovies.slice(0, maxTotal).map(movie => {
      const specialTags = determineSpecialTags(movie, settings.filenameSuffixRules);
      return {
        ...movie,
        specialTags,
        actors: movie.actors && json_valid(movie.actors) ? JSON.parse(movie.actors) : [],
        genres: movie.genres && json_valid(movie.genres) ? JSON.parse(movie.genres) : [],
        tags: movie.tags && json_valid(movie.tags) ? JSON.parse(movie.tags) : [],
        aiAnalyzedTags: movie.aiAnalyzedTags && json_valid(movie.aiAnalyzedTags) ? JSON.parse(movie.aiAnalyzedTags) : [],
        watched: Boolean(movie.watched),
      };
    });

    log.info(`[数据库服务] 找到 ${finalMovies.length} 条基于收藏的推荐.`);
    return { success: true, recommendedMovies: finalMovies };
  } catch (error) {
    log.error('[数据库服务] 获取基于收藏的推荐时发生错误:', error);
    return { success: false, recommendedMovies: [], error: error.message };
  }
}

function queryMoviesForLinLuo(userInputString) { /* ... */ }
function getActorMetadata(actorName) { /* ... */ }
function upsertActorMetadata(actorName, localAvatarPath, avatarUrlSource, filetreeSourcePath) { /* ... */ }

function getUniqueActorNames() {
  try {
    const rows = db.prepare(`
      SELECT DISTINCT value AS actor_name
      FROM movies, json_each(movies.actors)
      WHERE json_valid(movies.actors) AND movies.actors != '[]' AND value IS NOT NULL AND value != ''
    `).all();
    return rows.map(row => row.actor_name);
  } catch (error) {
    log.error('[数据库服务] 获取所有唯一演员名称失败:', error);
    return [];
  }
}

function manageMovieLibrary(params) {
  try {
    if (params.delete && params.id) {
      // Begin transaction
      const deleteLibraryTx = db.transaction(() => {
        db.prepare("DELETE FROM movie_library_links WHERE library_id = ?").run(params.id);
        const info = db.prepare("DELETE FROM movie_libraries WHERE id = ?").run(params.id);
        if (info.changes === 0) {
          throw new Error('未找到要删除的片库。');
        }
        log.info(`[数据库服务] 已删除片库 ID: ${params.id} 及其关联影片。`);
      });
      deleteLibraryTx();
      return { success: true, message: '片库已删除。' };

    } else if (params.id && params.name !== undefined && params.paths !== undefined) { // Update
      const { id, name, paths } = params;
      const pathsJson = JSON.stringify(paths || []);
      const info = db.prepare("UPDATE movie_libraries SET name = ?, paths = ?, updated_at = datetime('now', 'localtime') WHERE id = ?")
                     .run(name, pathsJson, id);
      if (info.changes > 0) {
        log.info(`[数据库服务] 已更新片库 ID: ${id}`);
        const updatedLibraryRaw = db.prepare("SELECT * FROM movie_libraries WHERE id = ?").get(id);
        const updatedLibrary = {
          ...updatedLibraryRaw,
          paths: JSON.parse(updatedLibraryRaw.paths || '[]'),
        };
        return { success: true, library: updatedLibrary };
      } else {
        return { success: false, error: '未找到要更新的片库。' };
      }
    } else if (params.name !== undefined && params.paths !== undefined) { // Create
      const { name, paths } = params;
      const newId = uuidv4();
      const pathsJson = JSON.stringify(paths || []);
      db.prepare("INSERT INTO movie_libraries (id, name, paths, created_at, updated_at) VALUES (?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))")
        .run(newId, name, pathsJson);
      log.info(`[数据库服务] 已创建新片库: ${name} (ID: ${newId})`);
      const createdLibraryRaw = db.prepare("SELECT * FROM movie_libraries WHERE id = ?").get(newId);
      const createdLibrary = {
          ...createdLibraryRaw,
          paths: JSON.parse(createdLibraryRaw.paths || '[]'),
        };
      return { success: true, library: createdLibrary };
    } else {
        return { success: false, error: '无效的片库管理参数。' };
    }
  } catch (error) {
    log.error(`[数据库服务] 管理片库失败 (参数: ${JSON.stringify(params)}):`, error);
    return { success: false, error: error.message };
  }
}

function getMovieLibraries() {
    const settings = require('./settingsService').getSettings();
    const fileUtils = require('../utils/fileUtils'); 
    try {
        const libraries = db.prepare("SELECT * FROM movie_libraries ORDER BY name ASC").all();
        const resultLibraries = libraries.map(lib => {
            const paths = JSON.parse(lib.paths || '[]');
            const movieCountQuery = db.prepare("SELECT COUNT(DISTINCT movie_db_id) as count FROM movie_library_links WHERE library_id = ?");
            const movieCountResult = movieCountQuery.get(lib.id);
            
            const coverMoviesQuery = db.prepare(`
                SELECT m.db_id, m.filePath, m.fileName, m.title, m.localCoverPath, m.posterUrl, m.coverUrl, m.videoHeight, m.resolution
                FROM movies m
                JOIN movie_library_links mll ON m.db_id = mll.movie_db_id
                WHERE mll.library_id = ? AND m.localCoverPath IS NOT NULL AND m.localCoverPath != ''
                ORDER BY m.releaseDate DESC, m.personalRating DESC
                LIMIT 5
            `);
            const coverMoviesData = coverMoviesQuery.all(lib.id);
            const coverMovies = coverMoviesData.map(movie => {
                const specialTags = determineSpecialTags(movie, settings.filenameSuffixRules);
                const coverDataUrl = movie.localCoverPath ? fileUtils.imagePathToDataUrl(movie.localCoverPath) : null;
                return {
                    ...movie,
                    specialTags,
                    coverDataUrl,
                    actors: [], genres: [], tags: [], aiAnalyzedTags: [],
                    watched: Boolean(movie.watched)
                };
            });

            return { ...lib, paths, movieCount: movieCountResult.count, coverMovies };
        });
        return { success: true, libraries: resultLibraries };
    } catch (error) {
        log.error('[数据库服务] 获取所有片库失败:', error);
        return { success: false, libraries: [], error: error.message };
    }
}

function getRecentMovies(fetchType, limit) {
  const settings = require('./settingsService').getSettings();
  const fileUtils = require('../utils/fileUtils'); 
  let baseQuery = `
    SELECT ${MOVIE_TABLE_COLUMNS_M_FINAL}, m_final.aiRecommendationScoreToUse, m_final.multiCdCountForNfoId, m_final.versionCountComputed
    FROM (
        SELECT 
            ${MOVIE_TABLE_COLUMNS_M},
            COALESCE(m.aiRecommendationScore, 0) as aiRecommendationScoreToUse, 
            (SELECT COUNT(DISTINCT mc.cdPartInfo) FROM movies mc WHERE mc.nfoId = m.nfoId AND mc.cdPartInfo IS NOT NULL AND mc.cdPartInfo != '') as multiCdCountForNfoId,
            (SELECT COUNT(DISTINCT v.filePath) FROM movies v WHERE v.nfoId = m.nfoId AND v.nfoId IS NOT NULL AND v.nfoId != '') as versionCountComputed
        FROM (
            SELECT 
                ${MOVIE_TABLE_COLUMNS_M_SUB},
                ROW_NUMBER() OVER (
                    PARTITION BY CASE WHEN m_sub.nfoId IS NOT NULL AND TRIM(m_sub.nfoId) != '' THEN LOWER(TRIM(m_sub.nfoId)) ELSE m_sub.filePath END
                    ORDER BY
                        CASE m_sub.preferredStatus WHEN 'preferred' THEN 1 ELSE 2 END,
                        m_sub.year DESC NULLS LAST,
                        m_sub.fileSize DESC NULLS LAST,
                        m_sub.db_id DESC
                ) as rn
            FROM movies m_sub
            ${fetchType === 'recent_played' ? 'WHERE m_sub.watched = 1' : ''}
        ) m
        WHERE m.rn = 1
    ) m_final
  `;
  
  let orderByClause;
  if (fetchType === 'recent_played') {
    // For recently played, we now sort by lastScanned, which is updated on play.
    orderByClause = "ORDER BY m_final.lastScanned DESC"; 
  } else { // 'recent_added'
    // For recently added, db_id is a good proxy if lastScanned isn't specifically for add date.
    // Or, if lastScanned is updated on initial scan, this is also fine.
    orderByClause = "ORDER BY m_final.db_id DESC"; 
  }
  
  const query = `${baseQuery} ${orderByClause} LIMIT ?`;
  
  try {
    const moviesResult = db.prepare(query).all(limit);
    const moviesWithParsedArrays = moviesResult.map(movie => {
      const specialTags = determineSpecialTags(movie, settings.filenameSuffixRules);
      const coverDataUrl = movie.localCoverPath ? fileUtils.imagePathToDataUrl(movie.localCoverPath) : null;
      return {
        ...movie,
        versionCount: movie.versionCountComputed > 1 ? movie.versionCountComputed : undefined,
        specialTags,
        coverDataUrl,
        actors: movie.actors && json_valid(movie.actors) ? JSON.parse(movie.actors) : [],
        genres: movie.genres && json_valid(movie.genres) ? JSON.parse(movie.genres) : [],
        tags: movie.tags && json_valid(movie.tags) ? JSON.parse(movie.tags) : [],
        aiAnalyzedTags: movie.aiAnalyzedTags && json_valid(movie.aiAnalyzedTags) ? JSON.parse(movie.aiAnalyzedTags) : [],
        watched: Boolean(movie.watched)
      };
    });

    // 【新增】为每个影片附加 A区 displayData
    const moviesWithDisplayData = moviesWithParsedArrays.map(movie => {
      let displayData = null;

      try {
        // 计算 .meta.json 文件路径
        let metaJsonPath = null;

        if (movie.asset_root_path && typeof movie.asset_root_path === 'string') {
          // 如果有 asset_root_path，直接使用
          metaJsonPath = path.join(movie.asset_root_path, '.meta.json');
        } else if (movie.nfoId) {
          // 否则通过 pathResolverService 重新计算
          try {
            const pathResolverService = require('./pathResolverService');
            const mockScrapedData = { nfoId: movie.nfoId };
            const assetPaths = pathResolverService.resolveAssetPaths(mockScrapedData);
            if (assetPaths && assetPaths.movieRootPath) {
              metaJsonPath = path.join(assetPaths.movieRootPath, '.meta.json');
            }
          } catch (pathError) {
            log.debug(`[数据库服务] 无法解析 ${movie.nfoId} 的路径 (getRecentMovies): ${pathError.message}`);
          }
        }

        // 安全读取并解析 .meta.json 文件
        if (metaJsonPath && fs.existsSync(metaJsonPath)) {
          const metaJsonContent = fs.readFileSync(metaJsonPath, 'utf8');
          const metaJson = JSON.parse(metaJsonContent);

          if (metaJson.display_data) {
            displayData = metaJson.display_data;
            log.debug(`[数据库服务] 成功加载 ${movie.nfoId} 的 displayData (getRecentMovies)`);
          }
        }
      } catch (error) {
        // 静默处理错误，不影响整个查询
        log.warn(`[数据库服务] 读取 ${movie.nfoId} 的 .meta.json 失败 (getRecentMovies): ${error.message}`);
      }

      return {
        ...movie,
        displayData: displayData
      };
    });

    return { success: true, movies: moviesWithDisplayData, totalMovies: moviesWithDisplayData.length };
  } catch (error) {
    log.error(`[数据库服务] 获取最近影片失败 (类型: ${fetchType}):`, error);
    return { success: false, movies: [], totalMovies: 0, error: error.message };
  }
}

function markMovieAsPlayed(filePath) {
  try {
    const stmt = db.prepare("UPDATE movies SET watched = 1, lastScanned = datetime('now', 'localtime') WHERE filePath = ?");
    const info = stmt.run(filePath);
    if (info.changes > 0) {
      log.info(`[数据库服务] 已标记影片为已播放并更新时间戳: ${filePath}`);
      return { success: true };
    }
    log.warn(`[数据库服务] 标记已播放时未找到影片或无需更新: ${filePath}`);
    return { success: false, error: '影片未找到或无需更新。' };
  } catch (error) {
    log.error(`[数据库服务] 标记影片为已播放时出错 (${filePath}):`, error);
    return { success: false, error: error.message };
  }
}

function updateMovieNfoId(movieId, nfoId) {
  try {
    const stmt = db.prepare("UPDATE movies SET nfoId = ?, lastScanned = datetime('now', 'localtime') WHERE db_id = ?");
    const info = stmt.run(nfoId, movieId);
    if (info.changes > 0) {
      log.info(`[数据库服务] 已更新影片 NFO ID: ${movieId} -> ${nfoId}`);
      return { success: true };
    }
    log.warn(`[数据库服务] 更新 NFO ID 时未找到影片: ${movieId}`);
    return { success: false, error: '影片未找到。' };
  } catch (error) {
    log.error(`[数据库服务] 更新影片 NFO ID 时出错 (${movieId}):`, error);
    return { success: false, error: error.message };
  }
}


// Collector 模块相关函数
function verifyCollectedLinksTable() {
  try {
    // 检查表是否存在
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='collected_links'").get();

    if (!tableExists) {
      return { success: false, error: 'collected_links 表不存在' };
    }

    // 获取表结构
    const columns = db.prepare("PRAGMA table_info(collected_links)").all();

    // 获取索引信息
    const indexes = db.prepare("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='collected_links'").all();

    log.info('[数据库服务] collected_links 表验证成功');
    return {
      success: true,
      tableExists: true,
      columns: columns.map(col => ({
        name: col.name,
        type: col.type,
        notNull: Boolean(col.notnull),
        defaultValue: col.dflt_value
      })),
      indexes: indexes.map(idx => idx.name)
    };
  } catch (error) {
    log.error('[数据库服务] 验证 collected_links 表失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 批量插入搜集到的链接数据
 * @param {Array} linksData - 链接数据数组
 * @param {string} sourceForum - 来源论坛标识
 * @returns {Object} 插入结果
 */
function insertCollectedLinks(linksData, sourceForum) {
  try {
    if (!Array.isArray(linksData) || linksData.length === 0) {
      return { success: true, insertedCount: 0, skippedCount: 0, message: '没有数据需要插入' };
    }

    log.info(`[数据库服务] 开始批量插入 ${linksData.length} 条搜集数据`);

    // 准备插入语句
    const insertStmt = db.prepare(`
      INSERT OR IGNORE INTO collected_links (
        source_forum, post_url, post_title, nfoId,
        magnet_link, ed2k_link, attachment_url, decompression_password,
        full_post_html, full_post_text, post_body_text, all_images, all_links,
        cloud_links, extracted_metadata, board_info, status, preview_image_url, post_date,
        md_document_path, ai_tags_json
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    // 使用事务确保原子性和性能
    const insertTransaction = db.transaction((links, forum) => {
      let insertedCount = 0;
      let skippedCount = 0;

      for (const link of links) {
        try {
          const result = insertStmt.run(
            forum,
            link.postUrl,
            link.postTitle || '',
            link.nfoId || null,
            link.magnetLink || null,
            link.ed2kLink || null,
            link.attachmentUrl || null,
            link.decompressionPassword || null,
            link.fullPostHtml || null,
            link.fullPostText || null,
            link.postBodyText || null,
            link.allImages || null,
            link.allLinks || null,
            link.cloudLinks || null,
            link.extractedMetadata || null,
            link.boardInfo || null,
            link.status || 'normal',
            link.previewImageUrl || null,
            link.postDate || null,
            link.mdDocumentPath || null,
            link.aiTagsJson || null
          );

          if (result.changes > 0) {
            insertedCount++;
          } else {
            skippedCount++;
          }
        } catch (error) {
          log.warn(`[数据库服务] 插入单条数据失败: ${error.message}, URL: ${link.postUrl}`);
          skippedCount++;
        }
      }

      return { insertedCount, skippedCount };
    });

    // 执行事务
    const result = insertTransaction(linksData, sourceForum);

    log.info(`[数据库服务] 批量插入完成: 新增 ${result.insertedCount} 条，跳过 ${result.skippedCount} 条`);

    return {
      success: true,
      insertedCount: result.insertedCount,
      skippedCount: result.skippedCount,
      totalProcessed: linksData.length,
      message: `成功插入 ${result.insertedCount} 条新数据，跳过 ${result.skippedCount} 条重复数据`
    };

  } catch (error) {
    log.error('[数据库服务] 批量插入搜集数据失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 获取搜集到的链接数据
 * @param {Object} options - 查询选项
 * @returns {Object} 查询结果
 */
function getCollectedLinks(options = {}) {
  try {
    const {
      page = 1,
      pageSize = 50,
      sourceForum = null,
      downloadStatus = null,
      searchText = null,
      searchTerm = null, // 新增支持searchTerm参数
      sortField = 'collection_date',
      sortOrder = 'DESC'
    } = options;

    // 构建查询条件
    let whereClause = '';
    const params = [];

    const conditions = [];

    if (sourceForum) {
      conditions.push('source_forum = ?');
      params.push(sourceForum);
    }

    if (downloadStatus) {
      conditions.push('download_status = ?');
      params.push(downloadStatus);
    }

    // 支持searchText或searchTerm参数
    const searchQuery = searchText || searchTerm;
    if (searchQuery) {
      conditions.push('(post_title LIKE ? OR nfoId LIKE ? OR post_url LIKE ?)');
      const searchPattern = `%${searchQuery}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    // 验证排序字段
    const validSortFields = ['id', 'post_title', 'nfoId', 'collection_date', 'download_status', 'source_forum'];
    const validSortField = validSortFields.includes(sortField) ? sortField : 'collection_date';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // 计算总数
    const countQuery = `SELECT COUNT(*) as total FROM collected_links ${whereClause}`;
    const totalResult = db.prepare(countQuery).get(...params);
    const total = totalResult.total;

    // 计算分页
    const offset = (page - 1) * pageSize;

    // 查询数据
    const dataQuery = `
      SELECT
        id, source_forum, post_url, post_title, nfoId,
        magnet_link, ed2k_link, attachment_url, decompression_password,
        collection_date, download_status, error_message, download_path, download_date, archive_path,
        full_post_html, full_post_text, post_body_text, all_images, all_links,
        cloud_links, extracted_metadata, board_info, status, preview_image_url, post_date,
        md_document_path, ai_tags_json
      FROM collected_links
      ${whereClause}
      ORDER BY ${validSortField} ${validSortOrder}
      LIMIT ? OFFSET ?
    `;

    const dataParams = [...params, pageSize, offset];
    const links = db.prepare(dataQuery).all(...dataParams);

    // 格式化日期
    const formattedLinks = links.map(link => ({
      ...link,
      collection_date: link.collection_date ? new Date(link.collection_date).toISOString() : null
    }));

    log.info(`[数据库服务] 查询搜集数据: 第${page}页，每页${pageSize}条，共${total}条`);

    return {
      success: true,
      data: formattedLinks,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };

  } catch (error) {
    log.error('[数据库服务] 查询搜集数据失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 获取已存在的URL集合（用于防重复下载）
 * @param {string[]} urls - 要检查的URL数组
 * @returns {Set<string>} 已存在于数据库中的URL集合
 */
function getExistingUrls(urls) {
  try {
    if (!urls || urls.length === 0) {
      return new Set();
    }

    // 构建查询语句，使用参数化查询防止SQL注入
    const placeholders = urls.map(() => '?').join(',');
    const query = `SELECT post_url FROM collected_links WHERE post_url IN (${placeholders})`;

    const existingUrls = db.prepare(query).all(...urls);
    const urlSet = new Set(existingUrls.map(row => row.post_url));

    log.info(`[数据库服务] 检查 ${urls.length} 个URL，发现 ${urlSet.size} 个已存在`);
    return urlSet;
  } catch (error) {
    log.error('[数据库服务] 获取已存在URL时发生错误:', error);
    return new Set(); // 发生错误时返回空集合，允许继续处理
  }
}

/**
 * 根据ID获取搜集记录
 * @param {number} id - 记录ID
 * @returns {Object|null} 搜集记录或null
 */
function getCollectedLinkById(id) {
  try {
    const selectStmt = db.prepare('SELECT * FROM collected_links WHERE id = ?');
    const result = selectStmt.get(id);

    if (result) {
      log.info(`[数据库服务] 获取搜集记录成功: ID ${id}`);
    } else {
      log.warn(`[数据库服务] 搜集记录不存在: ID ${id}`);
    }

    return result || null;
  } catch (error) {
    log.error(`[数据库服务] 获取搜集记录异常: ${error.message}`);
    return null;
  }
}

/**
 * 更新搜集记录的md文档路径
 * @param {number} id - 记录ID
 * @param {string} mdDocumentPath - md文档路径
 * @returns {Object} 更新结果
 */
function updateCollectedLinkMdPath(id, mdDocumentPath) {
  try {
    const updateStmt = db.prepare('UPDATE collected_links SET md_document_path = ? WHERE id = ?');
    const result = updateStmt.run(mdDocumentPath, id);

    if (result.changes > 0) {
      log.info(`[数据库服务] 更新md文档路径成功: ID ${id}, 路径: ${mdDocumentPath}`);
      return { success: true, message: '更新成功' };
    } else {
      log.warn(`[数据库服务] 未找到要更新的记录: ID ${id}`);
      return { success: false, error: '记录不存在' };
    }
  } catch (error) {
    log.error(`[数据库服务] 更新md文档路径失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 更新搜集记录的AI标签
 * @param {number} id - 记录ID
 * @param {string[]} aiTags - AI生成的标签数组
 * @returns {Object} 更新结果
 */
function updateCollectedLinkAiTags(id, aiTags) {
  try {
    const aiTagsJson = JSON.stringify(aiTags);
    const updateStmt = db.prepare('UPDATE collected_links SET ai_tags_json = ? WHERE id = ?');
    const result = updateStmt.run(aiTagsJson, id);

    if (result.changes > 0) {
      log.info(`[数据库服务] 更新AI标签成功: ID ${id}, 标签: ${aiTagsJson}`);
      return { success: true, message: '更新成功', tags: aiTags };
    } else {
      log.warn(`[数据库服务] 未找到要更新的记录: ID ${id}`);
      return { success: false, error: '记录不存在' };
    }
  } catch (error) {
    log.error(`[数据库服务] 更新AI标签失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 获取数据库实例
 * @returns {Object} 数据库实例
 */
function getDatabase() {
  return db;
}

/**
 * 构建查询条件的辅助函数
 * @param {object} filters - 筛选条件对象
 * @returns {{whereClauses: string[], params: any[], whereString: string}}
 */
function buildQueryConditions(filters) {
  const {
    topCategory,
    aiTags,
    sourceForum,
    dateRange,
    keyword,
    hasAiTags
  } = filters;

  let whereClauses = [];
  let params = [];

  // 1. 顶级分类筛选 (作为 AI 标签的一种)
  if (topCategory && topCategory !== '全部') {
    whereClauses.push(`EXISTS (SELECT 1 FROM json_each(c.ai_tags_json) WHERE json_each.value = ?)`);
    params.push(topCategory);
  }

  // 2. AI 标签筛选 (核心功能)
  if (aiTags && aiTags.length > 0) {
    aiTags.forEach(tag => {
      // 为每个标签创建一个子查询，确保记录包含所有指定的标签
      whereClauses.push(`EXISTS (SELECT 1 FROM json_each(c.ai_tags_json) WHERE json_each.value = ?)`);
      params.push(tag);
    });
  }

  // 3. 来源论坛筛选
  if (sourceForum && sourceForum !== '全部') {
    whereClauses.push('c.source_forum = ?');
    params.push(sourceForum);
  }

  // 4. 日期范围筛选
  if (dateRange && dateRange.startDate && dateRange.endDate) {
    whereClauses.push('c.collection_date BETWEEN ? AND ?');
    params.push(dateRange.startDate, dateRange.endDate);
  }

  // 5. 关键词筛选
  if (keyword && keyword.trim()) {
    whereClauses.push('c.post_title LIKE ?');
    params.push(`%${keyword.trim()}%`);
  }

  // 6. 是否有AI标签筛选
  if (hasAiTags !== undefined) {
    if (hasAiTags === false) {
      // 筛选没有AI标签的记录
      whereClauses.push('(c.ai_tags_json IS NULL OR c.ai_tags_json = "")');
    } else {
      // 筛选有AI标签的记录
      whereClauses.push('(c.ai_tags_json IS NOT NULL AND c.ai_tags_json != "")');
    }
  }

  const whereString = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

  return { whereClauses, params, whereString };
}

/**
 * 对历史档案进行复杂的多条件查询
 * @param {object} filters - 包含所有筛选条件的对象
 * @param {string} [filters.topCategory] - 顶级分类 (例如 '国产精品', '欧美')
 * @param {string[]} [filters.aiTags] - 需要匹配的 AI 标签数组
 * @param {string} [filters.sourceForum] - 来源论坛
 * @param {{startDate: string, endDate: string}} [filters.dateRange] - 采集日期范围
 * @param {string} [filters.keyword] - 帖子标题的模糊搜索关键词
 * @param {number} [filters.page = 1] - 页码
 * @param {number} [filters.pageSize = 50] - 每页数量
 * @returns {{data: any[], total: number}}
 */
function queryArchive(filters) {
  try {
    const { page = 1, pageSize = 50 } = filters;

    // 1. 查询 collected_links 表（情报中心数据）
    const { params, whereString } = buildQueryConditions(filters);
    const countSql = `SELECT COUNT(DISTINCT c.id) as total FROM collected_links c ${whereString}`;
    const collectedTotal = db.prepare(countSql).get(params).total;

    const offset = (page - 1) * pageSize;
    const dataSql = `
        SELECT DISTINCT c.*, 'collected' as source_type FROM collected_links c
        ${whereString}
        ORDER BY c.collection_date DESC
        LIMIT ? OFFSET ?
    `;
    const collectedData = db.prepare(dataSql).all(...params, pageSize, offset);

    // 2. 查询 movies 表中的虚拟资产
    let virtualData = [];
    let virtualTotal = 0;

    // 构建虚拟资产的查询条件
    const virtualConditions = [];
    const virtualParams = [];

    // 基本条件：只查询虚拟资产
    virtualConditions.push("asset_status = 'VIRTUAL'");

    // 如果有番号筛选
    if (filters.nfoId) {
      virtualConditions.push("nfoId LIKE ?");
      virtualParams.push(`%${filters.nfoId}%`);
    }

    // 如果有关键词筛选
    if (filters.keyword) {
      virtualConditions.push("(title LIKE ? OR plot LIKE ?)");
      virtualParams.push(`%${filters.keyword}%`, `%${filters.keyword}%`);
    }

    const virtualWhereString = virtualConditions.length > 0
      ? `WHERE ${virtualConditions.join(' AND ')}`
      : '';

    // 查询虚拟资产总数
    const virtualCountSql = `SELECT COUNT(*) as total FROM movies ${virtualWhereString}`;
    virtualTotal = db.prepare(virtualCountSql).get(virtualParams).total;

    // 查询虚拟资产数据
    const virtualDataSql = `
        SELECT *, 'virtual' as source_type FROM movies
        ${virtualWhereString}
        ORDER BY lastScanned DESC
        LIMIT ? OFFSET ?
    `;
    virtualData = db.prepare(virtualDataSql).all(...virtualParams, pageSize, offset);

    // 将虚拟资产数据转换为与 collected_links 兼容的格式
    const formattedVirtualData = virtualData.map(movie => ({
      id: `virtual_${movie.db_id}`,
      source_forum: 'virtual_asset',
      post_url: `virtual://${movie.nfoId}`,
      post_title: movie.title,
      nfoId: movie.nfoId,
      magnet_link: null,
      ed2k_link: null,
      attachment_url: null,
      decompression_password: null,
      collection_date: movie.lastScanned,
      download_status: 'virtual',
      error_message: null,
      download_path: movie.filePath,
      download_date: movie.lastScanned,
      archive_path: null,
      full_post_html: null,
      full_post_text: movie.plot,
      post_body_text: movie.plot,
      all_images: movie.coverUrl ? [movie.coverUrl] : [],
      all_links: [],
      cloud_links: null,
      extracted_metadata: JSON.stringify({
        title: movie.title,
        year: movie.year,
        runtime: movie.runtime,
        director: movie.director,
        studio: movie.studio,
        series: movie.series,
        actors: movie.actors ? JSON.parse(movie.actors) : [],
        genres: movie.genres ? JSON.parse(movie.genres) : []
      }),
      board_info: null,
      status: 'virtual',
      preview_image_url: movie.coverUrl,
      post_date: movie.releaseDate,
      md_document_path: null,
      ai_tags_json: null,
      source_type: 'virtual'
    }));

    // 3. 合并数据
    const totalRecords = collectedTotal + virtualTotal;
    const allData = [...collectedData, ...formattedVirtualData];

    // 按时间排序
    allData.sort((a, b) => {
      const dateA = new Date(a.collection_date || 0);
      const dateB = new Date(b.collection_date || 0);
      return dateB - dateA;
    });

    // 应用分页到合并后的数据
    const paginatedData = allData.slice(0, pageSize);

    log.info(`[数据库服务] 复杂查询完成: 情报中心 ${collectedTotal}, 虚拟资产 ${virtualTotal}, 总数 ${totalRecords}, 页码 ${page}, 每页 ${pageSize}`);
    return { data: paginatedData, total: totalRecords };

  } catch (error) {
    log.error(`[数据库服务] 复杂查询失败: ${error.message}`);
    return { data: [], total: 0 };
  }
}

/**
 * 获取符合查询条件的所有记录中的所有下载链接
 * @param {object} filters - 与 queryArchive 使用的筛选条件对象完全相同
 * @returns {string[]} - 一个包含所有提取链接的纯文本数组
 */
function getAllLinksForQuery(filters) {
  try {
    const { params, whereString } = buildQueryConditions(filters);

    // 构建查询链接的 SQL，只选择成品链接字段，不使用分页
    const sql = `
        SELECT DISTINCT
            c.magnet_link,
            c.ed2k_link,
            c.cloud_links
        FROM collected_links c
        ${whereString}
        ORDER BY c.collection_date DESC
    `;

    const records = db.prepare(sql).all(params);

    // 提取和整理所有成品链接
    const allLinks = [];
    for (const record of records) {
      // 磁力链接
      if (record.magnet_link && record.magnet_link.trim()) {
        allLinks.push(record.magnet_link.trim());
      }

      // ed2k链接
      if (record.ed2k_link && record.ed2k_link.trim()) {
        allLinks.push(record.ed2k_link.trim());
      }

      // 网盘链接 (JSON格式)
      if (record.cloud_links && record.cloud_links.trim()) {
        try {
          const cloudLinks = JSON.parse(record.cloud_links);
          if (Array.isArray(cloudLinks)) {
            cloudLinks.forEach(link => {
              if (link.url && link.url.trim()) {
                // 如果有密码，拼接在一起
                const linkText = link.password ?
                  `${link.url.trim()} (密码: ${link.password})` :
                  link.url.trim();
                allLinks.push(linkText);
              }
            });
          }
        } catch (e) {
          // 忽略解析失败的 JSON，但记录日志
          log.warn(`[数据库服务] 解析网盘链接JSON失败: ${e.message}`);
        }
      }
    }

    // 返回去重后的链接数组，如果为空则返回空数组
    const uniqueLinks = [...new Set(allLinks)];

    if (uniqueLinks.length === 0) {
      log.info(`[数据库服务] 提取链接完成: 共 ${records.length} 条记录, 但未找到任何成品链接`);
      return [];
    }

    log.info(`[数据库服务] 提取链接完成: 共 ${records.length} 条记录, ${uniqueLinks.length} 个唯一成品链接`);
    return uniqueLinks;

  } catch (error) {
    log.error(`[数据库服务] 提取链接失败: ${error.message}`);
    return [];
  }
}

/**
 * 删除搜集记录
 * @param {number} id - 记录ID
 * @returns {boolean} 是否删除成功
 */
function deleteCollectedLink(id) {
  try {
    const deleteStmt = db.prepare('DELETE FROM collected_links WHERE id = ?');
    const result = deleteStmt.run(id);

    const success = result.changes > 0;
    if (success) {
      log.info(`[数据库服务] 成功删除搜集记录 ID: ${id}`);
    } else {
      log.warn(`[数据库服务] 未找到要删除的记录 ID: ${id}`);
    }

    return success;
  } catch (error) {
    log.error(`[数据库服务] 删除搜集记录失败 ID: ${id}`, error);
    return false;
  }
}

/**
 * 更新下载状态
 * @param {string} postUrl - 帖子URL
 * @param {string} status - 下载状态 ('pending', 'downloading', 'completed', 'failed')
 * @param {string} downloadPath - 下载文件路径（可选）
 * @param {string} errorMessage - 错误信息（可选）
 * @returns {Object} 更新结果
 */
function updateDownloadStatus(postUrl, status, downloadPath = null, errorMessage = null) {
  try {
    if (!postUrl || !status) {
      return { success: false, error: '缺少必要参数: postUrl 和 status' };
    }

    // 验证状态值
    const validStatuses = ['pending', 'downloading', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
      return { success: false, error: `无效的状态值: ${status}` };
    }

    log.info(`[数据库服务] 更新下载状态: ${postUrl} -> ${status}`);

    // 准备更新语句
    let updateQuery;
    let params;

    if (downloadPath && errorMessage) {
      updateQuery = `
        UPDATE collected_links
        SET download_status = ?, download_path = ?, download_date = CURRENT_TIMESTAMP, error_message = ?
        WHERE post_url = ?
      `;
      params = [status, downloadPath, errorMessage, postUrl];
    } else if (downloadPath) {
      updateQuery = `
        UPDATE collected_links
        SET download_status = ?, download_path = ?, download_date = CURRENT_TIMESTAMP, error_message = NULL
        WHERE post_url = ?
      `;
      params = [status, downloadPath, postUrl];
    } else if (errorMessage) {
      updateQuery = `
        UPDATE collected_links
        SET download_status = ?, error_message = ?
        WHERE post_url = ?
      `;
      params = [status, errorMessage, postUrl];
    } else {
      updateQuery = `
        UPDATE collected_links
        SET download_status = ?, error_message = NULL
        WHERE post_url = ?
      `;
      params = [status, postUrl];
    }

    const updateStmt = db.prepare(updateQuery);
    const result = updateStmt.run(...params);

    if (result.changes > 0) {
      log.info(`[数据库服务] 下载状态更新成功: ${postUrl} -> ${status}`);
      return {
        success: true,
        updatedRows: result.changes,
        message: `成功更新 ${result.changes} 条记录的下载状态`
      };
    } else {
      log.warn(`[数据库服务] 未找到匹配的记录: ${postUrl}`);
      return {
        success: false,
        error: `未找到URL为 ${postUrl} 的记录`
      };
    }

  } catch (error) {
    log.error('[数据库服务] 更新下载状态失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 批量更新下载状态
 * @param {Array} updates - 更新数组，每个元素包含 {postUrl, status, downloadPath}
 * @returns {Object} 批量更新结果
 */
function batchUpdateDownloadStatus(updates) {
  try {
    if (!Array.isArray(updates) || updates.length === 0) {
      return { success: true, updatedCount: 0, message: '没有需要更新的数据' };
    }

    log.info(`[数据库服务] 开始批量更新下载状态: ${updates.length} 条记录`);

    // 准备更新语句
    const updateStmt = db.prepare(`
      UPDATE collected_links
      SET download_status = ?, download_path = ?, download_date = CURRENT_TIMESTAMP
      WHERE post_url = ?
    `);

    // 使用事务确保原子性
    const updateTransaction = db.transaction((updateList) => {
      let updatedCount = 0;
      let failedCount = 0;

      for (const update of updateList) {
        try {
          const { postUrl, status, downloadPath = null } = update;

          if (!postUrl || !status) {
            log.warn(`[数据库服务] 跳过无效的更新记录: ${JSON.stringify(update)}`);
            failedCount++;
            continue;
          }

          const result = updateStmt.run(status, downloadPath, postUrl);

          if (result.changes > 0) {
            updatedCount++;
          } else {
            failedCount++;
          }
        } catch (error) {
          log.warn(`[数据库服务] 更新单条记录失败: ${error.message}, 数据: ${JSON.stringify(update)}`);
          failedCount++;
        }
      }

      return { updatedCount, failedCount };
    });

    // 执行事务
    const result = updateTransaction(updates);

    log.info(`[数据库服务] 批量更新完成: 成功 ${result.updatedCount} 条，失败 ${result.failedCount} 条`);

    return {
      success: true,
      updatedCount: result.updatedCount,
      failedCount: result.failedCount,
      totalProcessed: updates.length,
      message: `批量更新完成: 成功 ${result.updatedCount} 条，失败 ${result.failedCount} 条`
    };

  } catch (error) {
    log.error('[数据库服务] 批量更新下载状态失败:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 获取一个 nfoId 下所有本地版本的快照，并按版本分组
 * @param {string} nfoId
 * @returns {Promise<Array<{versionInfo: object, snapshots: string[]}>>}
 */
async function getAllSnapshotsForNfoId(nfoId) {
  if (!nfoId) {
    log.warn('[数据库服务] getAllSnapshotsForNfoId: nfoId 无效');
    return [];
  }

  try {
    // 获取该 nfoId 下所有本地版本（包括预告片和主版本）
    const localVersionsStmt = db.prepare(`SELECT db_id, filePath, fileName, fileSize, resolution FROM movies WHERE nfoId = ? ORDER BY preferredStatus DESC, fileSize DESC, filePath ASC`);
    const localVersions = localVersionsStmt.all(nfoId); // 获取所有版本

    const groupedSnapshots = [];
    const settingsService = require('./settingsService');
    const snapshotUtils = require('../utils/snapshotUtils');
    const fileUtils = require('../utils/fileUtils');

    // 获取快照缓存路径
    const settings = settingsService.getSettings();
    const cacheBasePath = settings.snapshotCachePath || require('../../main').defaultThumbnailCacheBasePathInternal;

    for (const version of localVersions) {
      try {
        // 获取该版本的快照
        const snapshots = snapshotUtils.getExistingSnapshotsInfo(version.db_id, cacheBasePath, fileUtils.imagePathToDataUrl);

        // 添加版本类型信息
        const isTrailer = version.fileSize && version.fileSize < 200 * 1024 * 1024;
        const versionWithType = {
          ...version,
          sub_type: isTrailer ? 'trailer' : 'main'
        };

        // 无论是否有快照都添加到结果中
        groupedSnapshots.push({
          versionInfo: versionWithType,
          snapshots: snapshots || [] // 如果没有快照，使用空数组
        });
      } catch (error) {
        log.warn(`[数据库服务] 获取版本 ${version.db_id} 的快照失败: ${error.message}`);
        // 即使出错也添加版本信息，但快照为空
        const isTrailer = version.fileSize && version.fileSize < 200 * 1024 * 1024;
        const versionWithType = {
          ...version,
          sub_type: isTrailer ? 'trailer' : 'main'
        };
        groupedSnapshots.push({
          versionInfo: versionWithType,
          snapshots: []
        });
      }
    }

    log.info(`[数据库服务] 为 nfoId ${nfoId} 获取到 ${groupedSnapshots.length} 个版本的快照数据`);
    return groupedSnapshots;
  } catch (error) {
    log.error(`[数据库服务] getAllSnapshotsForNfoId 失败: ${error.message}`, error);
    return [];
  }
}

/**
 * 确定预告片URL的存储值（优先使用本地路径）
 * @param {object} actualData - 刮削到的数据
 * @param {object} resolvedPaths - 解析出的本地文件路径
 * @returns {string} 要存储到数据库的预告片URL或路径
 */
function getTrailerUrlForStorage(actualData, resolvedPaths) {
  const fs = require('fs');

  // 1. 如果有本地预告片路径且文件存在，优先使用本地路径
  if (resolvedPaths && resolvedPaths.trailerPath) {
    try {
      if (fs.existsSync(resolvedPaths.trailerPath)) {
        log.info(`[数据库服务] 使用本地预告片路径: ${resolvedPaths.trailerPath}`);
        return resolvedPaths.trailerPath;
      }
    } catch (error) {
      log.warn(`[数据库服务] 检查本地预告片文件失败: ${error.message}`);
    }
  }

  // 2. 否则使用原始的网络URL
  const originalUrl = actualData.trailerUrl || '';
  if (originalUrl) {
    log.info(`[数据库服务] 使用原始预告片URL: ${originalUrl}`);
  }

  return originalUrl;
}

/**
 * 使用刮削到的数据，在 movies 表中创建一条新的虚拟资产记录
 * @param {ScrapedMovieData|object} scrapedData 从 scraperManager 返回的标准数据对象或包装对象
 * @param {string} assetRootPath 媒体资产根路径（可选）
 * @param {object} resolvedPaths 解析出的本地文件路径（可选）
 * @returns {object} 包含成功状态和新创建的电影对象的结果
 */
function createVirtualAsset(scrapedData, assetRootPath = null, resolvedPaths = null) {
  // 处理不同的数据格式
  let actualData = scrapedData;

  // 如果传入的是包装对象（如 {success: true, data: {...}}），则提取实际数据
  if (scrapedData && typeof scrapedData === 'object' && scrapedData.data) {
    actualData = scrapedData.data;
    // 如果包装对象中有 resolvedPaths，使用它
    if (scrapedData.resolvedPaths && !resolvedPaths) {
      resolvedPaths = scrapedData.resolvedPaths;
    }
  }

  if (!actualData || !actualData.nfoId) {
    const error = '刮削数据无效：缺少必要的 nfoId 字段';
    log.error(`[数据库服务] ${error}`);
    return { success: false, error: error };
  }

  log.info(`[数据库服务] 开始创建虚拟资产: ${actualData.nfoId}`);

  try {
    // 首先，检查该 nfoId 是否已存在（无论是虚拟还是本地）
    const existingMovie = db.prepare('SELECT db_id, asset_status FROM movies WHERE nfoId = ?').get(actualData.nfoId);
    if (existingMovie) {
      const statusText = existingMovie.asset_status === 'VIRTUAL' ? '虚拟资产' : '本地资产';
      const error = `番号 ${actualData.nfoId} 已存在于资料库中 (${statusText})`;
      log.warn(`[数据库服务] ${error}`);
      return { success: false, error: error, existing: true };
    }

    // 为虚拟资产生成特殊的 filePath 和 fileName
    const virtualFilePath = `VIRTUAL://${actualData.nfoId}`;
    const virtualFileName = `${actualData.nfoId}.virtual`;

    // 确定资产根路径
    const finalAssetRootPath = assetRootPath ||
                               (resolvedPaths && resolvedPaths.movieRootPath) ||
                               null;

    // 将 actualData 映射到 movies 表的字段
    const sql = `
      INSERT INTO movies (
        nfoId, title, plot, releaseDate, year, runtime, director,
        studio, series, actors, genres, posterUrl, coverUrl, localCoverPath,
        trailerUrl, asset_status, lastScanned, watched, personalRating,
        filePath, fileName, asset_root_path
      ) VALUES (
        @nfoId, @title, @plot, @releaseDate, @year, @runtime, @director,
        @studio, @series, @actors, @genres, @posterUrl, @coverUrl, @localCoverPath,
        @trailerUrl, 'VIRTUAL', CURRENT_TIMESTAMP, 0, NULL,
        @filePath, @fileName, @assetRootPath
      )
    `;

    const stmt = db.prepare(sql);
    const result = stmt.run({
      nfoId: actualData.nfoId,
      title: actualData.title || '',
      plot: actualData.plot || '',
      releaseDate: actualData.releaseDate || null,
      year: actualData.year || null,
      runtime: actualData.runtime || null,
      director: actualData.director || '',
      studio: actualData.studio || '',
      series: actualData.series || '',
      actors: actualData.actors ? JSON.stringify(actualData.actors) : '[]',
      genres: actualData.tags ? JSON.stringify(actualData.tags) : '[]', // 映射 tags 到 genres
      posterUrl: actualData.posterUrl || actualData.coverUrl || '',
      coverUrl: actualData.coverUrl || '',
      localCoverPath: resolvedPaths && resolvedPaths.posterPath ? resolvedPaths.posterPath : null,
      trailerUrl: getTrailerUrlForStorage(actualData, resolvedPaths),
      filePath: virtualFilePath,
      fileName: virtualFileName,
      assetRootPath: finalAssetRootPath
    });

    // 返回新插入的记录
    const newMovie = db.prepare('SELECT * FROM movies WHERE db_id = ?').get(result.lastInsertRowid);

    log.info(`[数据库服务] 虚拟资产创建成功: ${actualData.nfoId}, DB ID: ${newMovie.db_id}`);

    return {
      success: true,
      movie: newMovie,
      message: `虚拟资产 ${actualData.nfoId} 创建成功`
    };

  } catch (error) {
    log.error(`[数据库服务] 创建虚拟资产失败: ${actualData.nfoId}`, error.message);
    return {
      success: false,
      error: error.message,
      nfoId: actualData.nfoId
    };
  }
}

/**
 * 根据 nfoId 获取电影记录
 * @param {string} nfoId - 番号
 * @returns {object|null} 电影记录
 */
function getMovieByNfoId(nfoId) {
  if (!nfoId) {
    return null;
  }

  try {
    const sql = `SELECT * FROM movies WHERE nfoId = ? LIMIT 1`;
    const stmt = db.prepare(sql);
    const movie = stmt.get(nfoId);

    if (movie) {
      log.debug(`[数据库服务] 找到电影记录: ${nfoId}`);
      return movie;
    } else {
      log.debug(`[数据库服务] 未找到电影记录: ${nfoId}`);
      return null;
    }
  } catch (error) {
    log.error(`[数据库服务] 查询电影记录失败: ${nfoId}`, error.message);
    return null;
  }
}

/**
 * 添加新的电影记录
 * @param {object} movieData - 电影数据
 * @returns {object} 新创建的电影记录
 */
function addMovie(movieData) {
  try {
    const sql = `
      INSERT INTO movies (
        nfoId, title, filePath, fileName, fileSize, localCoverPath,
        asset_status, lastScanned, watched, personalRating
      ) VALUES (
        @nfoId, @title, @filePath, @fileName, @fileSize, @localCoverPath,
        @asset_status, @lastScanned, @watched, @personalRating
      )
    `;

    const stmt = db.prepare(sql);
    const result = stmt.run({
      nfoId: movieData.nfoId,
      title: movieData.title || '',
      filePath: movieData.filePath || null,
      fileName: movieData.fileName || null,
      fileSize: movieData.fileSize || null,
      localCoverPath: movieData.localCoverPath || null,
      asset_status: movieData.asset_status || 'AVAILABLE',
      lastScanned: movieData.lastScanned || new Date().toISOString(),
      watched: movieData.watched || false,
      personalRating: movieData.personalRating || null
    });

    // 返回新插入的记录
    const newMovie = db.prepare('SELECT * FROM movies WHERE db_id = ?').get(result.lastInsertRowid);
    log.info(`[数据库服务] 新增电影记录: ${movieData.nfoId}, ID: ${newMovie.db_id}`);
    return newMovie;

  } catch (error) {
    log.error(`[数据库服务] 添加电影记录失败: ${movieData.nfoId}`, error.message);
    throw error;
  }
}

/**
 * 使用版本优先逻辑更新电影记录
 * @param {number} movieId - 电影ID
 * @param {object} newData - 新数据
 * @param {object} options - 更新选项
 * @param {boolean} options.forceOverwrite - 是否强制覆盖所有字段
 * @returns {boolean} 更新是否成功
 */
function updateMovieWithVersionPriority(movieId, newData, options = {}) {
  try {
    // 获取现有记录
    const existingMovie = db.prepare('SELECT * FROM movies WHERE db_id = ?').get(movieId);

    if (!existingMovie) {
      log.warn(`[数据库服务] 未找到要更新的电影记录: ID ${movieId}`);
      return false;
    }

    // 应用版本优先逻辑决定哪些字段需要更新
    const updateData = {};

    for (const [key, newValue] of Object.entries(newData)) {
      if (newValue !== undefined) {
        const existingValue = existingMovie[key];

        if (options.forceOverwrite || shouldUpdateField(key, existingValue, newValue)) {
          updateData[key] = newValue;
        }
      }
    }

    // 总是更新扫描时间
    updateData.lastScanned = new Date().toISOString();

    if (Object.keys(updateData).length === 0) {
      log.debug(`[数据库服务] 版本优先逻辑：无需更新任何字段: ID ${movieId}`);
      return true; // 认为是成功的，因为数据已经是最新的
    }

    // 执行更新
    return updateMovie(movieId, updateData);

  } catch (error) {
    log.error(`[数据库服务] 版本优先更新失败: ID ${movieId}`, error.message);
    throw error;
  }
}

/**
 * 判断是否应该更新字段（版本优先逻辑）
 * @param {string} fieldName - 字段名
 * @param {any} existingValue - 现有值
 * @param {any} newValue - 新值
 * @returns {boolean} 是否应该更新
 */
function shouldUpdateField(fieldName, existingValue, newValue) {
  // 如果现有值为空或null，则更新
  if (!existingValue || existingValue === '' || existingValue === null) {
    return true;
  }

  // 如果新值为空，则不更新（保留现有值）
  if (!newValue || newValue === '' || newValue === null) {
    return false;
  }

  // 特殊字段的版本优先逻辑
  switch (fieldName) {
    case 'title':
      // 如果新标题更长或更详细，则更新
      return newValue.length > existingValue.length;

    case 'localCoverPath':
    case 'filePath':
      // 文件路径字段：如果新路径存在且文件更新，则更新
      return true; // 简化处理，总是更新文件路径

    case 'fileSize':
      // 文件大小：如果新文件更大，则更新
      return parseInt(newValue) > parseInt(existingValue);

    case 'asset_status':
      // 资产状态：AVAILABLE > VIRTUAL > MISSING
      const statusPriority = { 'AVAILABLE': 3, 'VIRTUAL': 2, 'MISSING': 1 };
      const existingPriority = statusPriority[existingValue] || 0;
      const newPriority = statusPriority[newValue] || 0;
      return newPriority > existingPriority;

    case 'watched':
    case 'personalRating':
      // 用户数据：不自动覆盖
      return false;

    default:
      // 其他字段：如果新值更长或更详细，则更新
      if (typeof newValue === 'string' && typeof existingValue === 'string') {
        return newValue.length > existingValue.length;
      }
      return true;
  }
}

/**
 * 更新电影记录
 * @param {number} movieId - 电影ID
 * @param {object} updateData - 更新数据
 * @returns {boolean} 更新是否成功
 */
function updateMovie(movieId, updateData) {
  try {
    const fields = [];
    const values = {};

    // 构建动态更新语句
    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined) {
        fields.push(`${key} = @${key}`);
        values[key] = value;
      }
    }

    if (fields.length === 0) {
      log.warn(`[数据库服务] 没有需要更新的字段: ${movieId}`);
      return false;
    }

    const sql = `UPDATE movies SET ${fields.join(', ')} WHERE db_id = @movieId`;
    values.movieId = movieId;

    const stmt = db.prepare(sql);
    const result = stmt.run(values);

    if (result.changes > 0) {
      log.info(`[数据库服务] 更新电影记录成功: ID ${movieId}`);
      return true;
    } else {
      log.warn(`[数据库服务] 未找到要更新的电影记录: ID ${movieId}`);
      return false;
    }

  } catch (error) {
    log.error(`[数据库服务] 更新电影记录失败: ID ${movieId}`, error.message);
    throw error;
  }
}

// ==================== 智能片库 CRUD 函数 ====================

/**
 * 获取所有智能片库
 * @returns {Array} 智能片库列表
 */
function getSmartLibraries() {
  try {
    const stmt = db.prepare('SELECT * FROM smart_libraries ORDER BY sort_order ASC, name ASC');
    const libraries = stmt.all();

    // 解析 rules JSON 字符串
    return libraries.map(lib => ({
      ...lib,
      rules: lib.rules ? JSON.parse(lib.rules) : {}
    }));
  } catch (error) {
    log.error('[数据库服务] 获取智能片库失败:', error);
    throw error;
  }
}

/**
 * 创建智能片库
 * @param {Object} libraryData - 片库数据
 * @param {string} libraryData.id - 片库ID
 * @param {string} libraryData.name - 片库名称
 * @param {Object} libraryData.rules - 筛选规则对象
 * @param {string} [libraryData.icon] - 图标名称
 * @param {number} [libraryData.sort_order] - 排序顺序
 * @returns {Object} 创建结果
 */
function createSmartLibrary(libraryData) {
  try {
    const { id, name, rules, icon, sort_order = 0 } = libraryData;

    if (!id || !name || !rules) {
      throw new Error('智能片库的 id、name 和 rules 字段是必需的');
    }

    const stmt = db.prepare(`
      INSERT INTO smart_libraries (id, name, rules, icon, sort_order)
      VALUES (?, ?, ?, ?, ?)
    `);

    const rulesJson = JSON.stringify(rules);
    stmt.run(id, name, rulesJson, icon || null, sort_order);

    log.info(`[数据库服务] 智能片库 "${name}" 创建成功`);
    return { success: true, id };
  } catch (error) {
    log.error('[数据库服务] 创建智能片库失败:', error);
    throw error;
  }
}

/**
 * 更新智能片库
 * @param {string} id - 片库ID
 * @param {Object} updateData - 更新数据
 * @returns {Object} 更新结果
 */
function updateSmartLibrary(id, updateData) {
  try {
    const { name, rules, icon, sort_order } = updateData;

    // 构建动态更新语句
    const updateFields = [];
    const updateValues = [];

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }

    if (rules !== undefined) {
      updateFields.push('rules = ?');
      updateValues.push(JSON.stringify(rules));
    }

    if (icon !== undefined) {
      updateFields.push('icon = ?');
      updateValues.push(icon);
    }

    if (sort_order !== undefined) {
      updateFields.push('sort_order = ?');
      updateValues.push(sort_order);
    }

    if (updateFields.length === 0) {
      throw new Error('没有提供要更新的字段');
    }

    updateValues.push(id);

    const stmt = db.prepare(`
      UPDATE smart_libraries
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...updateValues);

    if (result.changes === 0) {
      throw new Error(`智能片库 ID "${id}" 不存在`);
    }

    log.info(`[数据库服务] 智能片库 "${id}" 更新成功`);
    return { success: true, id };
  } catch (error) {
    log.error('[数据库服务] 更新智能片库失败:', error);
    throw error;
  }
}

/**
 * 删除智能片库
 * @param {string} id - 片库ID
 * @returns {Object} 删除结果
 */
function deleteSmartLibrary(id) {
  try {
    const stmt = db.prepare('DELETE FROM smart_libraries WHERE id = ?');
    const result = stmt.run(id);

    if (result.changes === 0) {
      throw new Error(`智能片库 ID "${id}" 不存在`);
    }

    log.info(`[数据库服务] 智能片库 "${id}" 删除成功`);
    return { success: true, id };
  } catch (error) {
    log.error('[数据库服务] 删除智能片库失败:', error);
    throw error;
  }
}

/**
 * 根据演员姓名获取相关影片列表
 * @param {string} actorName - 演员姓名
 * @returns {Array} 影片列表
 */
function getMoviesByActor(actorName) {
  try {
    if (!db) {
      throw new Error('数据库未初始化');
    }

    if (!actorName || typeof actorName !== 'string') {
      log.warn('[数据库服务] getMoviesByActor: 无效的演员姓名');
      return [];
    }

    log.debug(`[数据库服务] 查询演员 "${actorName}" 的相关影片`);

    // 使用LIKE操作查询actors字段中包含指定演员姓名的影片
    // actors字段存储的是逗号分隔的字符串，所以使用LIKE '%演员名%'
    const stmt = db.prepare(`
      SELECT ${MOVIE_TABLE_COLUMNS.join(', ')}
      FROM movies
      WHERE actors LIKE ?
      ORDER BY releaseDate DESC, title ASC
    `);

    const searchPattern = `%${actorName}%`;
    const movies = stmt.all(searchPattern);

    // 智能匹配：支持精确匹配、部分匹配和别名匹配
    const filteredMovies = movies.filter(movie => {
      if (!movie.actors) return false;

      // 将actors字符串分割为数组，然后检查是否包含演员姓名
      const actorList = movie.actors.split(',').map(actor => actor.trim());

      // 1. 精确匹配
      if (actorList.includes(actorName)) {
        return true;
      }

      // 2. 智能模糊匹配
      for (const actor of actorList) {
        // 移除括号内容进行比较
        const cleanActorName = actor.replace(/[（(].*?[）)]/g, '').trim();
        const cleanSearchName = actorName.replace(/[（(].*?[）)]/g, '').trim();

        // 2.1 双向包含匹配
        if (cleanActorName.includes(cleanSearchName) || cleanSearchName.includes(cleanActorName)) {
          log.debug(`[数据库服务] 模糊匹配成功: "${actorName}" 匹配 "${actor}"`);
          return true;
        }

        // 2.2 别名匹配：检查括号内的别名
        const aliasMatch = actor.match(/[（(](.*?)[）)]/);
        if (aliasMatch) {
          const alias = aliasMatch[1].trim();
          if (alias === cleanSearchName || cleanSearchName.includes(alias) || alias.includes(cleanSearchName)) {
            log.debug(`[数据库服务] 别名匹配成功: "${actorName}" 匹配别名 "${alias}" (来自 "${actor}")`);
            return true;
          }
        }

        // 2.3 反向别名匹配：检查搜索名称中的别名
        const searchAliasMatch = actorName.match(/[（(](.*?)[）)]/);
        if (searchAliasMatch) {
          const searchAlias = searchAliasMatch[1].trim();
          if (cleanActorName.includes(searchAlias) || searchAlias.includes(cleanActorName)) {
            log.debug(`[数据库服务] 反向别名匹配成功: 搜索别名 "${searchAlias}" 匹配 "${actor}"`);
            return true;
          }
        }

        // 2.4 部分字符匹配（至少3个字符）
        if (cleanSearchName.length >= 3 && cleanActorName.length >= 3) {
          // 检查是否有连续的3个字符匹配
          for (let i = 0; i <= cleanSearchName.length - 3; i++) {
            const searchSubstr = cleanSearchName.substring(i, i + 3);
            if (cleanActorName.includes(searchSubstr)) {
              log.debug(`[数据库服务] 部分字符匹配成功: "${searchSubstr}" 在 "${actor}" 中找到`);
              return true;
            }
          }
        }
      }

      return false;
    });

    log.debug(`[数据库服务] 找到演员 "${actorName}" 的相关影片: ${filteredMovies.length} 部`);
    return filteredMovies;

  } catch (error) {
    log.error(`[数据库服务] getMoviesByActor 失败: ${error.message}`);
    return [];
  }
}

module.exports = {
  initializeDatabaseService,
  connectAndSetupDatabase,
  getDb,
  getDatabase: () => db, // 添加getDatabase方法供其他服务使用
  closeDatabase,
  getMoviesFromDb,
  getMoviesByActor,
  updateMovieCoverPath,
  saveNfoDataAndUpdateDb,
  updateMovieAiTags,
  updateMovieAiRecommendation,
  deleteMovieByFilePath,
  getUnifiedVersionsByNfoId,
  getMovieVersionsFromDb,
  getMovieCdPartsFromDb,
  saveVersionMarksToDb,
  renameFilesByNfoIdInDb,
  backupDatabase,
  restoreDatabase,
  getMovieCleanupStats,
  toggleFavorite,
  addFavoriteItem,
  removeFavoriteItem,
  isFavoriteItem,
  batchCheckFavoriteItems,
  getFavoriteItems,
  getRecommendationsByFavorites,
  queryMoviesForLinLuo,
  getActorMetadata,
  upsertActorMetadata,
  getUniqueActorNames,
  manageMovieLibrary,
  getMovieLibraries,
  getRecentMovies,
  markMovieAsPlayed,
  updateMovieNfoId,
  verifyCollectedLinksTable,
  insertCollectedLinks,
  getCollectedLinks,
  getExistingUrls,
  getCollectedLinkById,
  updateCollectedLinkMdPath,
  updateCollectedLinkAiTags,
  deleteCollectedLink,
  updateDownloadStatus,
  batchUpdateDownloadStatus,
  getDatabase,
  queryArchive,
  getAllLinksForQuery,
  getAllSnapshotsForNfoId,
  createVirtualAsset,
  getMovieByNfoId,
  addMovie,
  updateMovie,
  updateMovieWithVersionPriority,
  // 智能片库 CRUD 函数
  getSmartLibraries,
  createSmartLibrary,
  updateSmartLibrary,
  deleteSmartLibrary,
};
