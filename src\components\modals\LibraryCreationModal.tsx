// soul-forge-electron/src/components/modals/LibraryCreationModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import { MovieLibrary, ManageMovieLibraryParams, ManageMovieLibraryResult } from '../../types';
import { LuFolderPlus, LuSave, LuTrash2 } from 'react-icons/lu';

interface LibraryCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (params: ManageMovieLibraryParams, operation: 'create' | 'update') => Promise<ManageMovieLibraryResult>;
  existingLibrary?: MovieLibrary | null; // For editing
}

const LibraryCreationModal: React.FC<LibraryCreationModalProps> = ({
  isOpen,
  onClose,
  onSave,
  existingLibrary,
}) => {
  const [name, setName] = useState('');
  const [paths, setPaths] = useState<string[]>([]);
  const [newPath, setNewPath] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const nameInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen) {
      if (existingLibrary) {
        setName(existingLibrary.name);
        setPaths(existingLibrary.paths);
      } else {
        setName('');
        setPaths([]);
      }
      setNewPath('');
      setIsLoading(false);
      if (nameInputRef.current) { // Always try to focus if ref is available
        setTimeout(() => nameInputRef.current?.focus(), 0); 
      }
    }
  }, [isOpen, existingLibrary]);

  if (!isOpen) return null;

  const handleAddPath = async () => {
    if (newPath.trim() && !paths.includes(newPath.trim())) {
      setPaths(prev => [...prev, newPath.trim()]);
      setNewPath('');
    } else if (newPath.trim() === '') {
        const selectedPaths = await window.sfeElectronAPI.selectDirectory(); 
        if (selectedPaths && selectedPaths.length > 0) {
            const pathsToAdd = selectedPaths.filter(p => !paths.includes(p));
            setPaths(prev => [...prev, ...pathsToAdd]);
        }
    } else {
      alert("该路径已添加或路径无效。");
    }
  };

  const handleRemovePath = (pathToRemove: string) => {
    setPaths(prev => prev.filter(p => p !== pathToRemove));
  };

  const handleSubmit = async () => {
    if (!name.trim()) {
      alert('片库名称不能为空。');
      return;
    }
    if (paths.length === 0) {
      alert('请至少为片库添加一个扫描路径。');
      return;
    }
    setIsLoading(true);
    const params: ManageMovieLibraryParams = {
      id: existingLibrary?.id,
      name: name.trim(),
      paths,
    };
    const operation = existingLibrary ? 'update' : 'create';
    const result = await onSave(params, operation);
    setIsLoading(false);
    if (result.success) {
      onClose();
    } else {
      alert(`保存片库失败: ${result.error}`);
    }
  };

  return (
    <div className="fixed inset-0 z-[80] flex items-center justify-center p-4 bg-black/80 backdrop-blur-md" onClick={onClose}>
      <div
        className="bg-[#232323] text-neutral-200 rounded-xl shadow-2xl w-full max-w-lg max-h-[90vh] flex flex-col border border-[#4f4f4f]"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-5 border-b border-[#3a3a3a] bg-[#2a2a2a]">
          <h2 className="text-xl font-bold text-amber-400">{existingLibrary ? '编辑片库' : '新建片库'}</h2>
          <button onClick={onClose} className="text-neutral-400 hover:text-white p-1 rounded-full hover:bg-[#3a3a3a]" aria-label="关闭">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>

        <div className="flex-grow overflow-y-auto p-5 space-y-4 settings-scroll-container">
          <div>
            <label htmlFor="libraryName" className="settings-label">片库名称</label>
            <input
              ref={nameInputRef}
              type="text"
              id="libraryName"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="例如：无码珍藏、VR秘境"
              className="form-input-app"
              required
            />
          </div>
          <div>
            <label className="settings-label">包含的文件夹路径</label>
            {paths.length === 0 && <p className="text-xs text-neutral-500 italic mb-1">暂无路径。请添加扫描文件夹。</p>}
            <div className="space-y-1 max-h-40 overflow-y-auto pr-1 settings-scroll-container">
              {paths.map(p => (
                <div key={p} className="flex items-center justify-between bg-[#2d2d2d] p-1.5 rounded-md border border-[#4f4f4f] text-sm">
                  <span className="text-neutral-100 truncate" title={p}>{p}</span>
                  <button onClick={() => handleRemovePath(p)} className="text-red-400 hover:text-red-300 font-semibold ml-2 p-0.5 text-xs"><LuTrash2 size={14}/></button>
                </div>
              ))}
            </div>
            <div className="flex items-center gap-2 mt-2">
              <input
                type="text"
                value={newPath}
                onChange={e => setNewPath(e.target.value)}
                placeholder="输入新路径或点击浏览..."
                className="form-input-app flex-grow text-sm"
              />
              <button onClick={handleAddPath} className="button-secondary-app px-3 py-1.5 text-sm flex items-center">
                <LuFolderPlus size={16} className="mr-1"/> 添加/浏览
              </button>
            </div>
             <p className="settings-description mt-1">提示: 点击“添加/浏览”按钮不输入路径时，可直接调起文件夹选择器选择多个文件夹。</p>
          </div>
        </div>

        <div className="p-4 border-t border-[#3a3a3a] bg-[#2a2a2a] flex justify-end space-x-3">
          <button onClick={onClose} className="button-neutral-app px-4 py-2 text-sm">取消</button>
          <button onClick={handleSubmit} disabled={isLoading} className="button-primary-app px-6 py-2 text-sm flex items-center">
            <LuSave size={16} className="mr-1.5"/> {isLoading ? '保存中...' : (existingLibrary ? '更新片库' : '创建片库')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LibraryCreationModal;