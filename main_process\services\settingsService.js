// soul-forge-electron/main_process/services/settingsService.js
const Store = require('electron-store');
const path = require('node:path');
const fs = require('node:fs');

let store;
let log;
let lastSettings = null; // To store the state before a save

const DEFAULT_SETTINGS = {
  defaultScanPaths: [], // This will be deprecated/managed by libraries
  pythonExecutablePath: null,
  defaultSortField: 'releaseDate',
  defaultSortOrder: 'desc',
  defaultPageSize: 50,
  defaultViewMode: 'card', 
  customDefaultCoverDataUrl: null,
  defaultActorAvatarDataUrl: null, // For default fallback avatar image
  
  avatarDataSourceType: 'none', 
  actorAvatarLibraryPath: null,   
  localFileTreePath: null,        
  avatarPreferAiFixed: true,      
  remoteGfriendsFiletreeUrl: "https://cdn.jsdelivr.net/gh/xinxin8816/gfriends/Filetree.json",
  remoteGfriendsImageBaseUrl: "https://cdn.jsdelivr.net/gh/xinxin8816/gfriends/Content/",

  ffmpegPath: null,
  ffprobePath: null,
  snapshotCachePath: null,
  filenameSuffixRules: [],
  presetVersionCategories: ["4K", "1080p", "720p", "HDR", "BluRay", "Remux", "WEB-DL", "Extended Cut", "Director's Cut", "Uncut", "Theatrical Cut", "IMAX Enhanced", "3D", "H265", "H264", "AV1", "DTS-HD MA", "TrueHD Atmos", "AAC"],
  filenameRenameTemplate: "{original_basename} [{nfoId}] [{year}].{extension}",
  snapshotQuality: 'hd_640p',
  autoUpdateNfoWatchedRating: true,
  autoCreateNfoOnSave: true,
  aiProvider: null,
  customGptEndpoint: null,
  customGptApiKey: null,
  customGptModel: null,
  grokApiKey: null,
  grokModel: 'grok-3-mini-fast', // Added Grok model with new default

  // Gemini API 设置
  geminiApiKey: null,
  geminiModel: 'gemini-2.5-flash', // 默认使用最新的Gemini 2.5 Flash模型

  // Ollama 本地AI 设置
  ollamaApiEndpoint: 'http://localhost:11434',
  ollamaModelName: 'llama3', // 默认使用llama3模型

  // AI分类引擎设置
  aiClassificationEnabled: true,
  aiClassificationCategories: [
    '国产精品', '人物合集', 'VR', '4K', '动画', '无码', '有码',
    '中文字幕', '破解版', '高清', '素人', '制服', '丝袜', '熟女',
    '萝莉', '巨乳', '美腿', '口交', '肛交', '群交', 'SM', '户外',
    '偷拍', '自拍', '直播', '网红', '明星', '欧美', '日韩', '亚洲', '其他'
  ],

  privacyModeEnabled: false,
  privacyModePassword: null,
  privacyHideTags: [],
  imagesGloballyVisible: true,      // Added for SFW toggle
  customSfwPlaceholderDataUrl: null, // Added for SFW toggle
  activeLibraryId: null, // Added for active library
  homePageSettings: {
    showRecentAdded: true,
    showRecentPlayed: true,
    recentAddedCount: 12,
    recentPlayedCount: 8
  }, // Added for homepage display settings

  // 刮削器设置
  javbusBaseUrl: 'https://www.javbus.com',
  javbusCookie: '', // JavBus 访问所需的 Cookie
  javdbBaseUrl: 'https://javdb.com',
  javdbCookie: '', // JavDB 访问所需的 Cookie

  // 三位一体的分离式媒体资产存储设置
  assetsPath: null,   // "元数据仓库" (SoulForge_Assets) - 用于存放图片, NFO, JSON, STRM 等
  trailersPath: null, // "预告片仓库" (SoulForge_Trailers) - 用于存放预告片视频文件
  mediaPath: null,    // "正片仓库" (SoulForge_Media) - 用于存放主要视频文件
  categoryDirectoryRules: JSON.stringify({
    "jav_censored": ["有码"],
    "jav_uncensored": ["无码"],
    "jav_vr": ["VR"],
    "chinese": ["国产"],
    "western": ["欧美"],
    "anime": ["动画"],
    "live": ["直播"]
  }),

  // 下载中转站设置
  downloadStagingPath: null, // 下载完成文件的中转目录
  uploadQueuePath: null, // 待上传到云盘的队列目录
  cloud115RootPath: '/', // 115网盘根目录路径前缀

  // 双源入库设置
  importPath: null, // 待导入 (To Import) 目录，用于云端资产导入

  // 回收站设置
  recycleBinPath: null, // 回收站目录，用于存储被回收的资产 // 分类目录规则，JSON 字符串格式

  // 【新增】刮削源优先级规则 - 数据精炼厂的择优配置
  scraperPriorityRules: {
    title: ['dmm', 'javbus', 'javdb', 'avsox'],
    plot: ['dmm', 'javdb', 'javbus', 'avsox'],
    cover: ['javbus', 'dmm', 'javdb', 'avsox'],
    releaseDate: ['dmm', 'javbus', 'avsox'],
    runtime: ['dmm', 'javbus', 'avsox'],
    display_id: ['javbus', 'dmm', 'avsox'],
    year: ['javbus', 'dmm', 'avsox'],
    studio: ['javbus', 'dmm', 'avsox'],
    publisher: ['javbus', 'dmm', 'avsox'],
    series: ['javbus', 'dmm', 'avsox'],
    director: ['dmm', 'javbus', 'avsox'],
    rating: ['javbus', 'javdb', 'avsox'],
    actresses: ['javbus', 'dmm', 'avsox'],
    actors_male: ['dmm', 'javbus', 'avsox'],
    cover_path: ['dmm', 'javbus', 'avsox'],
    cover_orientation: ['dmm', 'javbus', 'avsox'],
    nfo_prefix: ['javbus', 'dmm', 'avsox'],
    preview_image_paths: ['dmm', 'javbus', 'avsox'],
    user_reviews: ['javbus', 'javdb', 'avsox'],
    similar_movies: ['dmm', 'javbus', 'avsox'],
    // 合并字段（需要特殊处理）
    tags: ['dmm', 'javbus', 'javdb', 'avsox'], // 合并去重
    // 布尔字段（需要逻辑判断）
    has_4k: ['dmm', 'javbus', 'avsox'],
    has_bluray: ['dmm', 'javbus', 'avsox'],
    has_subtitles: ['avsox', 'javdb', 'javbus'], // AVSOX 磁力链接表有字幕信息
    is_uncensored_cracked: ['javdb', 'javbus', 'avsox'],
    is_leaked: ['javdb', 'javbus', 'avsox'],
    // 计算字段
    version_count: 'calculated', // 需要计算
    is_watched: 'calculated',    // 需要从数据库读取
    type: ['dmm', 'javbus', 'avsox']      // 影片类型
  }
};

function initializeSettings(logger, userDataPath) {
  log = logger;
  const defaults = { ...DEFAULT_SETTINGS };
  
  if (!defaults.snapshotCachePath) {
    defaults.snapshotCachePath = path.join(userDataPath, 'thumbnail_cache');
    log.info(`[设置服务] 默认快照缓存路径设置为: ${defaults.snapshotCachePath}`);
  }
  
  if (defaults.snapshotCachePath && !fs.existsSync(defaults.snapshotCachePath)) {
    try {
      fs.mkdirSync(defaults.snapshotCachePath, { recursive: true });
      log.info(`[设置服务] 已成功创建快照缓存目录: ${defaults.snapshotCachePath}`);
    } catch (error) {
      log.error(`[设置服务] 创建快照缓存目录失败: ${defaults.snapshotCachePath}`, error);
    }
  }

  // 设置三位一体分离式媒体资产存储的默认值

  // 1. 元数据仓库 (Assets) - 图片, NFO, JSON, STRM 等
  let assetsPath = defaults.assetsPath;
  if (!assetsPath) {
    assetsPath = path.join(userDataPath, 'SoulForge_Assets');
    defaults.assetsPath = assetsPath;
    log.info(`[设置服务] 默认元数据仓库路径设置为: ${assetsPath}`);
  }

  // 2. 预告片仓库 (Trailers) - 预告片视频文件
  let trailersPath = defaults.trailersPath;
  if (!trailersPath) {
    trailersPath = path.join(userDataPath, 'SoulForge_Trailers');
    defaults.trailersPath = trailersPath;
    log.info(`[设置服务] 默认预告片仓库路径设置为: ${trailersPath}`);
  }

  // 3. 正片仓库 (Media) - 主要视频文件
  let mediaPath = defaults.mediaPath;
  if (!mediaPath) {
    mediaPath = path.join(userDataPath, 'SoulForge_Media');
    defaults.mediaPath = mediaPath;
    log.info(`[设置服务] 默认正片仓库路径设置为: ${mediaPath}`);
  }

  // 确保三个仓库目录都存在
  const repositories = [
    { name: '元数据仓库', path: assetsPath },
    { name: '预告片仓库', path: trailersPath },
    { name: '正片仓库', path: mediaPath }
  ];

  repositories.forEach(repo => {
    if (repo.path && !fs.existsSync(repo.path)) {
      try {
        fs.mkdirSync(repo.path, { recursive: true });
        log.info(`[设置服务] 已成功创建${repo.name}: ${repo.path}`);
      } catch (error) {
        log.error(`[设置服务] 创建${repo.name}失败: ${repo.path}`, error);
      }
    }
  });

  // 设置回收站目录的默认值
  let recycleBinPath = defaults.recycleBinPath;
  if (!recycleBinPath) {
    recycleBinPath = path.join(userDataPath, 'SoulForge', '_RecycleBin');
    log.info(`[设置服务] 默认回收站目录设置为: ${recycleBinPath}`);
  }

  // 确保回收站目录存在
  if (recycleBinPath && !fs.existsSync(recycleBinPath)) {
    try {
      fs.mkdirSync(recycleBinPath, { recursive: true });
      log.info(`[设置服务] 已成功创建回收站目录: ${recycleBinPath}`);
    } catch (error) {
      log.error(`[设置服务] 创建回收站目录失败: ${recycleBinPath}`, error);
    }
  }

  store = new Store({
    name: 'linlang-mifu-settings', // Changed from 'soul-forge-settings'
    defaults: { appSettings: defaults },
    configFileMode: 0o660,
    clearInvalidConfig: true,
  });
  log.info('[设置服务] Electron Store 已初始化 (linlang-mifu-settings)。');
  
  let currentStoredSettings = store.get('appSettings', {});
  let settingsModified = false;

  for (const key in defaults) {
    if (!Object.prototype.hasOwnProperty.call(currentStoredSettings, key)) {
      // 为三位一体的路径设置计算出的默认值
      if (key === 'assetsPath') {
        currentStoredSettings[key] = assetsPath;
      } else if (key === 'trailersPath') {
        currentStoredSettings[key] = trailersPath;
      } else if (key === 'mediaPath') {
        currentStoredSettings[key] = mediaPath;
      } else if (key === 'recycleBinPath') {
        currentStoredSettings[key] = recycleBinPath;
      } else {
        currentStoredSettings[key] = defaults[key];
      }
      settingsModified = true;
    }
  }

  // 向后兼容性：迁移旧的 mediaAssetsRootPath 到新的三位一体架构
  if (currentStoredSettings.mediaAssetsRootPath && !currentStoredSettings.assetsPath) {
    const oldPath = currentStoredSettings.mediaAssetsRootPath;
    log.info(`[设置服务] 检测到旧的媒体资产路径，开始迁移: ${oldPath}`);

    // 将旧路径作为元数据仓库的基础
    currentStoredSettings.assetsPath = oldPath;

    // 在同级目录创建预告片和正片仓库
    const parentDir = path.dirname(oldPath);
    currentStoredSettings.trailersPath = path.join(parentDir, 'SoulForge_Trailers');
    currentStoredSettings.mediaPath = path.join(parentDir, 'SoulForge_Media');

    // 删除旧的设置项
    delete currentStoredSettings.mediaAssetsRootPath;

    settingsModified = true;
    log.info(`[设置服务] 路径迁移完成:`);
    log.info(`  元数据仓库: ${currentStoredSettings.assetsPath}`);
    log.info(`  预告片仓库: ${currentStoredSettings.trailersPath}`);
    log.info(`  正片仓库: ${currentStoredSettings.mediaPath}`);
  }

  // 确保三个路径都有值
  if (!currentStoredSettings.assetsPath) {
    currentStoredSettings.assetsPath = assetsPath;
    settingsModified = true;
  }
  if (!currentStoredSettings.trailersPath) {
    currentStoredSettings.trailersPath = trailersPath;
    settingsModified = true;
  }
  if (!currentStoredSettings.mediaPath) {
    currentStoredSettings.mediaPath = mediaPath;
    settingsModified = true;
  }

  // 如果 recycleBinPath 为 null，设置为计算出的默认值
  if (!currentStoredSettings.recycleBinPath) {
    currentStoredSettings.recycleBinPath = recycleBinPath;
    settingsModified = true;
  }
  if (Object.prototype.hasOwnProperty.call(currentStoredSettings, 'googleGeminiApiKey')) {
    delete currentStoredSettings.googleGeminiApiKey;
    settingsModified = true;
  }

  if (settingsModified) {
    store.set('appSettings', currentStoredSettings);
    log.info('[设置服务] 已向现有配置中添加/移除设置键以匹配最新定义。');
  }
  lastSettings = store.get('appSettings'); 
}

function getStore() {
  if (!store) {
    log.error('[设置服务] 尝试在初始化前获取 Store 实例。');
    throw new Error("Settings store not initialized.");
  }
  return store;
}

function getSettings() {
  if (!store) {
    log.error('[设置服务] 尝试在初始化前获取设置。');
    return { ...DEFAULT_SETTINGS };
  }

  // 先获取存储的设置，不使用默认值覆盖
  const storedSettings = store.get('appSettings', {});

  // 只为缺失的字段设置默认值，不覆盖已存在的用户设置
  const currentAppSettings = {};
  for (const key in DEFAULT_SETTINGS) {
    if (storedSettings.hasOwnProperty(key)) {
      // 使用用户设置的值
      currentAppSettings[key] = storedSettings[key];
    } else {
      // 只有当字段不存在时才使用默认值
      currentAppSettings[key] = DEFAULT_SETTINGS[key];
    }
  }

  // 保留用户设置中可能存在但不在 DEFAULT_SETTINGS 中的字段
  for (const key in storedSettings) {
    if (!DEFAULT_SETTINGS.hasOwnProperty(key)) {
      currentAppSettings[key] = storedSettings[key];
    }
  }

  if (currentAppSettings.snapshotCachePath && !path.isAbsolute(currentAppSettings.snapshotCachePath)) {
    log.warn(`[设置服务] 快照缓存路径不是绝对路径: ${currentAppSettings.snapshotCachePath}. 这可能导致问题.`);
  }
  // Remove googleGeminiApiKey if it somehow still exists
  const { googleGeminiApiKey, ...restOfSettings } = currentAppSettings;
  return restOfSettings;
}

function saveSettings(newSettings) {
  if (!store) {
    log.error('[设置服务] 尝试在初始化前保存设置。');
    return { success: false, error: '设置服务未初始化。' };
  }
  try {
    lastSettings = store.get('appSettings'); 

    // 从当前存储的设置开始，而不是从 DEFAULT_SETTINGS 开始
    // 这样可以保持用户已设置的值，避免被默认值覆盖
    const currentStoredSettings = store.get('appSettings', {});
    const settingsToSave = { ...DEFAULT_SETTINGS, ...currentStoredSettings };

    for (const key in DEFAULT_SETTINGS) {
      if (Object.prototype.hasOwnProperty.call(newSettings, key) && newSettings[key] !== undefined) {
        if (typeof DEFAULT_SETTINGS[key] === 'boolean') {
          settingsToSave[key] = Boolean(newSettings[key]);
        } else if (typeof DEFAULT_SETTINGS[key] === 'number') {
          const numVal = Number(newSettings[key]);
          settingsToSave[key] = isNaN(numVal) ? DEFAULT_SETTINGS[key] : numVal;
        } else if (Array.isArray(DEFAULT_SETTINGS[key])) {
          settingsToSave[key] = Array.isArray(newSettings[key]) ? newSettings[key] : DEFAULT_SETTINGS[key];
        }
        else {
          settingsToSave[key] = newSettings[key];
        }
      }
    }
    
    if (Object.prototype.hasOwnProperty.call(settingsToSave, 'googleGeminiApiKey')) {
        delete settingsToSave.googleGeminiApiKey;
    }
    
    const nullableStringKeys = [
        'pythonExecutablePath', 'ffmpegPath', 'ffprobePath', 'snapshotCachePath',
        'customGptEndpoint', 'customGptApiKey', 'grokApiKey', 'customGptModel', 'grokModel', // Added grokModel
        'customDefaultCoverDataUrl', 'defaultActorAvatarDataUrl',
        'actorAvatarLibraryPath', 'localFileTreePath',
        'remoteGfriendsFiletreeUrl', 'remoteGfriendsImageBaseUrl',
        'customSfwPlaceholderDataUrl', 'activeLibraryId', // Added activeLibraryId
        'mediaAssetsRootPath', // Added mediaAssetsRootPath for media asset storage
        'downloadStagingPath', // Added downloadStagingPath for download staging
        'uploadQueuePath', // Added uploadQueuePath for upload queue
        'cloud115RootPath', // Added cloud115RootPath for 115 cloud storage
        'importPath', // Added importPath for cloud asset import
        'recycleBinPath', // Added recycleBinPath for recycle bin
        // 三位一体分离式存储路径
        'assetsPath', // Added assetsPath for assets repository
        'trailersPath', // Added trailersPath for trailers repository
        'mediaPath' // Added mediaPath for media repository
    ];

    nullableStringKeys.forEach(key => {
      if (newSettings[key] === '' || newSettings[key] === undefined) {
        settingsToSave[key] = null;
      } else if (newSettings[key] !== undefined) { 
        settingsToSave[key] = newSettings[key];
      }
    });
     if (newSettings.aiProvider === '') {
        settingsToSave.aiProvider = null;
    }
    if (newSettings.avatarDataSourceType === '') {
        settingsToSave.avatarDataSourceType = 'none';
    }
    // Ensure boolean for imagesGloballyVisible is correctly handled
    if (typeof newSettings.imagesGloballyVisible === 'boolean') {
        settingsToSave.imagesGloballyVisible = newSettings.imagesGloballyVisible;
    } else {
        settingsToSave.imagesGloballyVisible = DEFAULT_SETTINGS.imagesGloballyVisible; // Fallback to default if type is wrong
    }
    
    // Ensure grokModel is handled correctly, defaulting if empty string is passed
    if (newSettings.grokModel === '') {
        settingsToSave.grokModel = DEFAULT_SETTINGS.grokModel; // or null if you want provider to use its internal default
    } else if (newSettings.grokModel !== undefined) {
        settingsToSave.grokModel = newSettings.grokModel;
    }

    // defaultScanPaths is deprecated but kept for now to avoid breaking old configs immediately.
    // It should not be actively used by new library logic.
    if (newSettings.defaultScanPaths === undefined) {
        settingsToSave.defaultScanPaths = DEFAULT_SETTINGS.defaultScanPaths;
    } else {
        settingsToSave.defaultScanPaths = newSettings.defaultScanPaths;
    }

    // Handle homePageSettings object
    if (newSettings.homePageSettings !== undefined) {
        settingsToSave.homePageSettings = {
            ...DEFAULT_SETTINGS.homePageSettings,
            ...newSettings.homePageSettings
        };
    } else {
        settingsToSave.homePageSettings = DEFAULT_SETTINGS.homePageSettings;
    }

    store.set('appSettings', settingsToSave);
    log.info('[设置服务] 设置已成功保存。');
    return { success: true, message: '设置已成功保存！', newSettings: settingsToSave };
  } catch (error) {
    log.error('[设置服务] 保存设置失败:', error);
    return { success: false, error: `保存设置时出错: ${error.message}` };
  }
}

function getLastSettings() {
  const currentLastSettings = lastSettings || store.get('appSettings');
  const { googleGeminiApiKey, ...restOfLastSettings } = currentLastSettings;
  return restOfLastSettings;
}

module.exports = {
  initializeSettings,
  getStore,
  getSettings,
  saveSettings,
  getLastSettings,
};