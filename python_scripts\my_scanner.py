import os
import json
import sys
import argparse
import xml.etree.ElementTree as ET
from datetime import datetime
import subprocess
import re

# Supported video extensions
VIDEO_EXTENSIONS = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mpg', '.mpeg', '.m4v', '.ts', '.vob']
# Common cover filenames
COVER_FILENAMES = ['folder.jpg', 'poster.jpg', 'fanart.jpg', 'cover.jpg', 'movie.jpg']

def parse_nfo(nfo_path):
    """Parses an NFO file and extracts movie metadata."""
    movie_data = {}
    try:
        tree = ET.parse(nfo_path)
        root = tree.getroot()

        # Common fields
        movie_data['title'] = root.findtext('title')
        movie_data['originalTitle'] = root.findtext('originaltitle')
        
        # ID: Try <uniqueid type="primary"> or specific types, then generic <id>, then <num>
        uniqueids = root.findall('uniqueid')
        primary_id = None
        specific_ids = {}
        for uid_tag in uniqueids:
            type_attr = uid_tag.get('type', '').lower()
            value = uid_tag.text
            if value:
                if uid_tag.get('default') == 'true' or type_attr == 'primary': # Prioritize default/primary
                    primary_id = value
                    break
                if type_attr in ['imdb', 'tmdb', 'tvdb']: # Store common specific IDs
                     specific_ids[type_attr] = value
        
        if primary_id:
             movie_data['nfoId'] = primary_id
        elif specific_ids: # Use a specific one if primary not found
            movie_data['nfoId'] = specific_ids.get('imdb') or specific_ids.get('tmdb') or specific_ids.get('tvdb') or next(iter(specific_ids.values()), None)
        else: # Fallback to simple <id> or <num>
            movie_data['nfoId'] = root.findtext('id') or root.findtext('num')


        movie_data['year'] = root.findtext('year')
        movie_data['releaseDate'] = root.findtext('premiered') or root.findtext('releasedate') or root.findtext('aired')
        
        runtime_str = root.findtext('runtime')
        if runtime_str and runtime_str.isdigit():
            movie_data['runtime'] = int(runtime_str)
        else: # Sometimes runtime includes " min" or similar
            match = re.search(r'(\d+)', runtime_str or "")
            if match:
                 movie_data['runtime'] = int(match.group(1))

        movie_data['plot'] = root.findtext('plot') or root.findtext('overview')
        movie_data['studio'] = root.findtext('studio') or root.findtext('maker') or root.findtext('publisher') or root.findtext('label')
        
        series_tag = root.find('set')
        if series_tag is not None:
            movie_data['series'] = series_tag.findtext('name') or series_tag.text # Jellyfin uses <set>Name of Set</set>
        else:
            movie_data['series'] = root.findtext('series') # Kodi might use <series>

        movie_data['trailerUrl'] = root.findtext('trailer')
        
        # Poster and Cover URLs (from NFO, not local files)
        # Prefer art/poster, then poster, then art/thumb, then thumb
        art_node = root.find('art')
        if art_node is not None:
            poster_art = art_node.find("./poster[@preview]") or art_node.find("./poster") or \
                         art_node.find("./thumb[@preview]") or art_node.find("./thumb")
            if poster_art is not None and poster_art.text:
                 movie_data['posterUrl'] = poster_art.text.strip()
            
            fanart_art = art_node.find("./fanart[@preview]") or art_node.find("./fanart")
            if fanart_art is not None and fanart_art.text:
                movie_data['coverUrl'] = fanart_art.text.strip() # Using coverUrl for fanart for consistency
        
        if not movie_data.get('posterUrl'):
            movie_data['posterUrl'] = root.findtext('poster')
        if not movie_data.get('coverUrl'):
            movie_data['coverUrl'] = root.findtext('cover') or root.findtext('fanart') # fanart often used for background

        # Actors
        actors = []
        for actor_tag in root.findall('actor'):
            name = actor_tag.findtext('name')
            if name:
                actors.append(name)
        movie_data['actors'] = actors

        # Genres and Tags
        movie_data['genres'] = [genre.text for genre in root.findall('genre') if genre.text]
        movie_data['tags'] = [tag.text for tag in root.findall('tag') if tag.text]
        
        # Watched status and personal rating (these are usually user-set, but respect if in NFO)
        playcount_str = root.findtext('playcount')
        if playcount_str and playcount_str.isdigit() and int(playcount_str) > 0:
            movie_data['watched'] = True
        
        userrating_str = root.findtext('userrating')
        if userrating_str:
            try:
                # NFO user rating is often 0-10, SoulForge uses 1-5.
                # Basic scaling: if > 5, cap at 5. If 0, map to null.
                # This is a rough approximation and can be refined.
                rating_val = int(userrating_str)
                if rating_val >= 9: movie_data['personalRating'] = 5
                elif rating_val >= 7: movie_data['personalRating'] = 4
                elif rating_val >= 5: movie_data['personalRating'] = 3
                elif rating_val >= 3: movie_data['personalRating'] = 2
                elif rating_val >= 1: movie_data['personalRating'] = 1
                else: movie_data['personalRating'] = None # 0 or invalid as unrated
            except ValueError:
                pass # Invalid rating format
        
        nfo_mtime = os.path.getmtime(nfo_path)
        movie_data['nfoLastModified'] = int(nfo_mtime * 1000) # Convert to milliseconds

    except ET.ParseError:
        # Could log this error if a logging mechanism is added
        pass
    except Exception:
        # General error handling
        pass
    return movie_data

def find_local_cover(video_filepath):
    """Finds a local cover image for the video file."""
    video_dir = os.path.dirname(video_filepath)
    video_filename_no_ext = os.path.splitext(os.path.basename(video_filepath))[0]

    # Check for [video_filename].jpg/png etc.
    for ext in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
        specific_cover = os.path.join(video_dir, video_filename_no_ext + ext)
        if os.path.exists(specific_cover):
            return specific_cover
    
    # Check for common cover filenames in the same directory
    for cover_name in COVER_FILENAMES:
        common_cover = os.path.join(video_dir, cover_name)
        if os.path.exists(common_cover):
            return common_cover
    
    return None

def get_technical_info(video_filepath, ffprobe_path='ffprobe'):
    """Extracts technical video and audio information using ffprobe."""
    tech_info = {
        'resolution': None,
        'fileSize': None,
        'fps': None,
        'videoCodecFull': None,
        'videoBitrate': None,
        'audioCodecFull': None,
        'audioChannelsDesc': None,
        'audioSampleRate': None,
        'audioBitrate': None,
        'videoCodec': None, # Basic codec for backwards compatibility/simplicity if needed
        'audioCodec': None, # Basic codec
    }
    try:
        command = [
            ffprobe_path,
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_filepath
        ]
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        stdout, stderr = process.communicate(timeout=60) # Added timeout

        if process.returncode != 0:
            # sys.stderr.write(f"FFprobe error for {video_filepath}: {stderr}\n")
            return tech_info # Return defaults on error

        data = json.loads(stdout)

        # File size from format section
        if 'format' in data and 'size' in data['format']:
            tech_info['fileSize'] = int(data['format']['size'])

        video_stream = next((s for s in data.get('streams', []) if s.get('codec_type') == 'video'), None)
        audio_stream = next((s for s in data.get('streams', []) if s.get('codec_type') == 'audio'), None) # Simplistic: takes first audio stream

        if video_stream:
            if 'width' in video_stream and 'height' in video_stream:
                tech_info['resolution'] = f"{video_stream['width']}x{video_stream['height']}"
            
            if 'r_frame_rate' in video_stream:
                try:
                    num, den = map(int, video_stream['r_frame_rate'].split('/'))
                    if den != 0:
                        tech_info['fps'] = round(num / den, 3)
                except ValueError:
                    pass # Could not parse r_frame_rate

            tech_info['videoCodec'] = video_stream.get('codec_name')
            tech_info['videoCodecFull'] = video_stream.get('codec_long_name')
            
            # Bitrate: ffprobe often gives it in bps. Convert to kbps or Mbps.
            v_bit_rate_str = video_stream.get('bit_rate') or data.get('format', {}).get('bit_rate') # Fallback to overall if stream specific is missing
            if v_bit_rate_str and v_bit_rate_str.isdigit():
                v_bit_rate = int(v_bit_rate_str)
                if v_bit_rate > 1000000: # If > 1 Mbps
                    tech_info['videoBitrate'] = f"{round(v_bit_rate / 1000000, 1)} Mbps"
                elif v_bit_rate > 1000: # If > 1 kbps
                    tech_info['videoBitrate'] = f"{round(v_bit_rate / 1000)} kbps"
                elif v_bit_rate > 0:
                     tech_info['videoBitrate'] = f"{v_bit_rate} bps"


        if audio_stream:
            tech_info['audioCodec'] = audio_stream.get('codec_name')
            tech_info['audioCodecFull'] = audio_stream.get('codec_long_name')
            
            if 'channels' in audio_stream:
                channels = audio_stream['channels']
                channel_layout = audio_stream.get('channel_layout', '').lower()
                if channel_layout == 'stereo' or channels == 2:
                    tech_info['audioChannelsDesc'] = 'Stereo'
                elif channel_layout == 'mono' or channels == 1:
                    tech_info['audioChannelsDesc'] = 'Mono'
                elif '5.1' in channel_layout or channels == 6: # common 5.1 has 6 channels
                    tech_info['audioChannelsDesc'] = '5.1'
                elif '7.1' in channel_layout or channels == 8: # common 7.1 has 8 channels
                    tech_info['audioChannelsDesc'] = '7.1'
                elif channels > 0:
                    tech_info['audioChannelsDesc'] = f"{channels} Channels"

            if 'sample_rate' in audio_stream and audio_stream['sample_rate'].isdigit():
                tech_info['audioSampleRate'] = int(audio_stream['sample_rate'])
            
            a_bit_rate_str = audio_stream.get('bit_rate')
            if a_bit_rate_str and a_bit_rate_str.isdigit():
                a_bit_rate = int(a_bit_rate_str)
                if a_bit_rate > 1000: # If > 1 kbps
                    tech_info['audioBitrate'] = f"{round(a_bit_rate / 1000)} kbps"
                elif a_bit_rate > 0:
                     tech_info['audioBitrate'] = f"{a_bit_rate} bps"
        
    except FileNotFoundError:
        # sys.stderr.write(f"FFprobe not found at path: {ffprobe_path}. Technical info will be missing.\n")
        pass # ffprobe not found
    except subprocess.TimeoutExpired:
        # sys.stderr.write(f"FFprobe command timed out for {video_filepath}.\n")
        pass
    except Exception as e:
        # sys.stderr.write(f"Error running FFprobe for {video_filepath}: {str(e)}\n")
        pass # Other errors
    return tech_info

def apply_filename_suffix_rules(filename, rules):
    """Applies filename suffix rules to extract auto-tags."""
    detected_tags = set()
    name_part, _ = os.path.splitext(filename)
    if not rules or not isinstance(rules, list):
        return []

    for rule in rules:
        suffix = rule.get('suffix')
        tags_to_add = rule.get('tags')
        if suffix and isinstance(tags_to_add, list):
            # Ensure suffix matching is case-insensitive or as per design
            if name_part.endswith(suffix): # Can make this case-insensitive: name_part.lower().endswith(suffix.lower())
                for tag in tags_to_add:
                    if isinstance(tag, str):
                         detected_tags.add(tag)
    return sorted(list(detected_tags))


def scan_folder(folder_path, suffix_rules_json_str=None, ffprobe_path='ffprobe'):
    """Scans a folder for video files and their metadata."""
    movies_found = []
    suffix_rules = []
    if suffix_rules_json_str:
        try:
            suffix_rules = json.loads(suffix_rules_json_str)
        except json.JSONDecodeError:
            # sys.stderr.write(f"Warning: Could not parse suffix_rules JSON: {suffix_rules_json_str}\n")
            pass


    for root_dir, _, files in os.walk(folder_path):
        for file in files:
            if any(file.lower().endswith(ext) for ext in VIDEO_EXTENSIONS):
                video_filepath = os.path.join(root_dir, file)
                video_filename = file
                
                movie_entry = {
                    'filePath': video_filepath,
                    'fileName': video_filename,
                    'title': None, # Default title to filename if NFO not found/parsed
                    'originalTitle': None,
                    'nfoId': None,
                    'year': None,
                    'releaseDate': None,
                    'runtime': None,
                    'plot': None,
                    'studio': None,
                    'series': None,
                    'trailerUrl': None,
                    'posterUrl': None,
                    'coverUrl': None,
                    'localCoverPath': None,
                    'ssrRating': 'R', # Default SSR rating
                    'watched': False,
                    'personalRating': None,
                    'actors': [],
                    'genres': [],
                    'tags': [],
                    'lastScanned': datetime.now().isoformat(),
                    'nfoLastModified': None,
                    # V1.32 technical fields (will be populated by get_technical_info)
                    'resolution': None,
                    'fileSize': None,
                    'videoCodec': None, 
                    'audioCodec': None, 
                    'fps': None,
                    'videoCodecFull': None,
                    'videoBitrate': None,
                    'audioCodecFull': None,
                    'audioChannelsDesc': None,
                    'audioSampleRate': None,
                    'audioBitrate': None,
                    'autoDetectedFileNameTags': [],
                }

                # Attempt to parse NFO
                nfo_filename = os.path.splitext(video_filename)[0] + '.nfo'
                nfo_filepath = os.path.join(root_dir, nfo_filename)
                if os.path.exists(nfo_filepath):
                    nfo_data = parse_nfo(nfo_filepath)
                    movie_entry.update(nfo_data)

                # If title still not set (no NFO or no title in NFO), use filename
                if not movie_entry['title']:
                    movie_entry['title'] = os.path.splitext(video_filename)[0]

                # Find local cover
                movie_entry['localCoverPath'] = find_local_cover(video_filepath)
                
                # Get technical info using ffprobe
                technical_details = get_technical_info(video_filepath, ffprobe_path)
                movie_entry.update(technical_details) # This will overwrite defaults if info is found
                
                # Apply filename suffix rules
                movie_entry['autoDetectedFileNameTags'] = apply_filename_suffix_rules(video_filename, suffix_rules)

                # Fallback for file size if ffprobe didn't get it (e.g., ffprobe error)
                if movie_entry['fileSize'] is None:
                    try:
                        movie_entry['fileSize'] = os.path.getsize(video_filepath)
                    except OSError:
                        pass


                movies_found.append(movie_entry)
    return movies_found

def main():
    parser = argparse.ArgumentParser(description="Scan a folder for movies and NFO files.")
    parser.add_argument("folder_path", help="The path to the folder to scan.")
    parser.add_argument("--suffix_rules", help="JSON string of filename suffix rules.", default="[]")
    parser.add_argument("--ffprobe_path", help="Path to the ffprobe executable.", default="ffprobe")
    
    args = parser.parse_args()

    if not os.path.isdir(args.folder_path):
        result = {"movies": [], "error": f"Error: Provided path '{args.folder_path}' is not a valid directory."}
        print(json.dumps(result, indent=2, ensure_ascii=False))
        sys.exit(1)

    try:
        movies = scan_folder(args.folder_path, args.suffix_rules, args.ffprobe_path)
        result = {"movies": movies, "error": None}
    except Exception as e:
        # This is a fallback for unexpected errors during the scan process itself
        # sys.stderr.write(f"An unexpected error occurred during scan: {str(e)}\n")
        result = {"movies": [], "error": f"An unexpected error occurred: {str(e)}"}
        # In a real app, you might want to exit with a non-zero code here too,
        # but for now, we'll let Electron handle the JSON error message.

    # Ensure output is UTF-8 for Electron
    sys.stdout.reconfigure(encoding='utf-8')
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == '__main__':
    main()